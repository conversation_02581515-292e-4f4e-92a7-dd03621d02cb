<template>
	<view>
    <loading-cover ref="loadingCover"></loading-cover>
	</view>
</template>

<script>
	const plugin = requirePlugin("logisticsPlugin");

	export default {
		data() {
			return {
				pageStatus: false
			}
		},
		onLoad(option) {
			this.pluginFn(option)

		},
		onShow() {

			// if(this.pageStatus){
			// 	uni.navigateBack();
			// }

		},
		onHide() {
			// this.pageStatus = true

			uni.navigateBack();

		},
		methods: {
			// 物流查询插件
			async pluginFn(option) {
				// this.pageStatus = false
				plugin.openWaybillTracking({
					waybillToken: option.waybillToken
				});
			},
      /**
       *分享参数组装
       */
      getSharePageParams() {
        return this.$util.unifySharePageParams('/otherpages/shop/home/<USER>', this.shareTitle || '先迈商城',
            '',{}, this.$util.img(this.shareImg) || this.$util.img('public/static/youpin/home_share.jpg'))
      }
		},
    /**
     * 自定义分享内容
     * @param {Object} res
     */
    onShareAppMessage(res) {
      let { title, link, imageUrl, query } = this.getSharePageParams()
      return this.$buriedPoint.pageShare(link , imageUrl, title);
    },
	}
</script>

<style>

</style>
