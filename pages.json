{
  //在pages.json文件中新加insetLoader属性 （微信小程序模式全局引入组件）
  "insetLoader": {
    //配置
    "config": {
      //将需要引入的组件名起了个confirm的名字在下面label中使用
      //右侧"<test ref='confirm' />"为需要插入的组件标签
      //			"ypWechatPrivacyPopup": "<yp-wechat-privacy-popup ref='ypWechatPrivacyPopup'></yp-wechat-privacy-popup>",
      "ypAddEnterpriseWechatFriendButton": "<yp-add-enterprise-wechat-friend-button ref='ypAddEnterpriseWechatFriendButton'></yp-add-enterprise-wechat-friend-button>",
      "ypWechatVerificationCode": "<yp-wechat-verification-code ref='ypWechatVerificationCode'></yp-wechat-verification-code>",
      "diyRealNamePopup": "<diy-real-name-popup ref='diyRealNamePopup'></diy-real-name-popup>"
    },
    // 全局配置
    //需要挂在的组件名
    "label": [
      "ypAddEnterpriseWechatFriendButton",
      "ypWechatVerificationCode",
      "diyRealNamePopup"
    ],
    //根元素的标签类型 也就是插入到页面哪个根元素下默认为div 但是uniapp中需要写为view
    "rootEle": "view"
  },
  "pages": [
    //pages数组中第一项表示应用启动页，参考：https://uniapp.dcloud.io/collocation/pages
    {
      "path": "pages/index/index/index",
      "style": {
        "enablePullDownRefresh": true,
        "navigationStyle": "custom"
      }
    },
    //******************登录模块（3）******************
    // #ifdef H5
    {
      "path": "pages/login/login/login",
      "style": {
        "navigationStyle": "custom",
        "navigationBarTitleText": ""
      }
    },
    {
      "path": "pages/login/register/register",
      "style": {
        "navigationBarTitleText": "注册"
        // "navigationBarBackgroundColor": "transparent"
      }
    },
    // #endif
    //******************柚品协议******************
    {
      "path": "pages/agreement/list/list",
      "style": {}
    },
    {
      "path": "pages/agreement/detail/detail",
      "style": {}
    },
    //******************商品模块（11）******************
    {
      "path": "pages/goods/cart/cart",
      "style": {
        "navigationStyle": "custom"
      }
    },
    // {
    // 	"path": "pages/goods/category/category",
    // 	"style": {
    // 		"disableScroll": true
    // 	}
    // },
    // 商品详情、限时折扣、预售
    {
      "path": "pages/goods/detail/detail",
      "style": {
        "h5": {
          "titleNView": false
        },
        "mp-weixin": {
          "usingComponents": {
            "guarantee-bar": "plugin://shoppingGuarantee/guarantee-bar"
          }
        }
      }
    },
    // 商品详情--周期购
    //		{
    //			"path": "pages/goods/periodbuy-detail/periodbuy-detail",
    //			"style": {}
    //		},
    //		{
    //			"path": "pages/goods/list/list",
    //			"style": {}
    //		},
    //******************会员模块（20）******************
    {
      "path": "pages/member/index/index",
      "style": {
        "enablePullDownRefresh": true,
        "navigationBarTextStyle": "white",
        "navigationStyle": "custom"
      }
    },
    //******************订单模块（12）******************
    //		{
    //			"path": "pages/order/upgrade_payment/upgrade_payment",
    //			"style": {}
    //		},
    {
      "path": "pages/order/payment/payment",
      "style": {
        "navigationStyle": "custom"
      }
    },
    // 周期购确认订单star
    //		{
    //			"path": "pages/order/cycle_payment/cycle_payment",
    //			"style": {
    //				"navigationStyle": "custom"
    //			}
    //		},
    // 周期购确认订单end
    {
      "path": "pages/order/list/list",
      "style": {
        "navigationStyle": "custom"
      }
    },
    {
      "path": "pages/order/detail/detail",
      "style": {
        "enablePullDownRefresh": true,
        "navigationStyle": "custom"
      }
    },
    //		{
    //			"path": "pages/order/list_cycle/list_cycle",
    //			"style": {}
    //		},
    //		{
    //			"path": "pages/order/manage_cycle/manage_cycle",
    //			"style": {
    //				"navigationStyle": "custom"
    //			}
    //		},
    //		{
    //			"path": "pages/order/detail/forPayButton"
    //		},
    // 本地配送订单详情
    // {
    // 	"path": "pages/order/detail_local_delivery/detail_local_delivery",
    // 	"style": {}
    // },
    // 自提订单详情
    // {
    // 	"path": "pages/order/detail_pickup/detail_pickup",
    // 	"style": {}
    // },
    // 虚拟订单详情
    // {
    // 	"path": "pages/order/detail_virtual/detail_virtual",
    // 	"style": {}
    // },
    {
      "path": "pages/order/logistics/logistics",
      "style": {}
    },
    {
      "path": "pages/order/activist/activist",
      "style": {
        "navigationBarTitleText": "退款/售后"
      }
    },
    // {
    // 	"path": "pages/order/complain/complain",
    // 	"style": {}
    // },
    //******************支付模块（2）******************
    //		{
    //			"path": "pages/pay/index/index",
    //			"style": {}
    //		},
    {
      "path": "pages/pay/result/result",
      "style": {}
    },
    //****************** 先迈跳转路径 ******************
    {
      "path": "pages/payment/payment",
      "style": {}
    },
    {
      "path": "pages/home/<USER>",
      "style": {}
    },
    {
      "path": "pages/productDetail/productDetail",
      "style": {}
    },
    {
      "path": "pages/singalProductDetail/singalProductDetail",
      "style": {}
    },
    //		{
    //			"path": "pages/shareProductDetail/shareProductDetail",
    //			"style": {}
    //		},
    //		{
    //			"path": "pages/shareSingalGoodsDetail/shareSingalGoodsDetail",
    //			"style": {}
    //		},
    {
      "path": "pages/live-player-plugin",
      "style": {
        "navigationStyle": "custom"
      }
    }
  ],
  "subPackages": [
    {
      //******************营销活动模块（26）******************
      "root": "promotionpages",
      "pages": [
        //----------组合套餐模块（2）----------
        {
          "path": "combo/detail/detail",
          "style": {
            "h5": {
              "titleNView": false
            }
          }
        },
        // {
        // 	"path": "combo/payment/payment",
        // 	"style": {}
        // },
        //----------专题活动模块（4）----------
        // {
        // 	"path": "topics/list/list",
        // 	"style": {}
        // },
        // {
        // 	"path": "topics/detail/detail",
        // 	"style": {}
        // },
        // {
        // 	"path": "topics/goods_detail/goods_detail",
        // 	"style": {}
        // },
        // {
        // 	"path": "topics/payment/payment",
        // 	"style": {}
        // },
        //----------秒杀模块（3）----------
        //				{
        //					"path": "seckill/list/list",
        //					"style": {}
        //				},
        {
          "path": "new_seckill/list/list",
          "style": {
            "navigationBarTitleText": "秒杀活动"
          }
        },
        {
          "path": "new_seckill/detail/detail",
          "style": {
            "h5": {
              "titleNView": false
            },
            "mp-weixin": {
              "usingComponents": {
                "guarantee-bar": "plugin://shoppingGuarantee/guarantee-bar"
              }
            }
          }
        },
        {
          "path": "new_seckill/payment/payment",
          "style": {
            "navigationStyle": "custom"
          }
        },
        //----------迈豆模块----------
        {
          "path": "maidou/list/list",
          "style": {
            "navigationBarTitleText": "迈豆专区"
          }
        },
        //----------拼团模块（5）----------
        {
          "path": "pintuan/list/list",
          "style": {
            "navigationBarTitleText": "拼团好物"
          }
        },
        {
          "path": "pintuan/detail/detail",
          "style": {
            "h5": {
              "titleNView": false
            },
            "mp-weixin": {
              "navigationStyle": "custom",
              "usingComponents": {
                "guarantee-bar": "plugin://shoppingGuarantee/guarantee-bar"
              }
            }
          }
        },
        // 拼团订单列表
        {
          "path": "pintuan/order/list/list",
          "style": {}
        },
        // 拼团订单详情
        {
          "path": "pintuan/order/detail/detail",
          "style": {}
        },
        // 参团人员
        {
          "path": "pintuan/join_member/join_member",
          "style": {}
        },
        //邀请有礼
        {
          "path": "pintuan/gift_invitation/gift_invitation",
          "style": {}
        },
        //邀请有礼人员
        {
          "path": "pintuan/gift_invitation_person/gift_invitation_person",
          "style": {}
        },
        // {
        // 	"path": "pintuan/my_spell/my_spell",
        // 	"style": {}
        // },
        {
          "path": "pintuan/share/share",
          "style": {
            // "enablePullDownRefresh": true
          }
        },
        {
          "path": "pintuan/payment/payment",
          "style": {
            "navigationStyle": "custom"
          }
        },
        //----------砍价模块（5）----------
        // {
        // 	"path": "bargain/list/list",
        // 	"style": {}
        // },
        //				{
        //					"path": "bargain/detail/detail",
        //					"style": {}
        //				},
        // {
        // 	"path": "bargain/activity-detail/activity-detail",
        // 	"style": {}
        // },
        // {
        // 	"path": "bargain/launch/launch",
        // 	"style": {}
        // },
        // {
        // 	"path": "bargain/my_bargain/my_bargain",
        // 	"style": {}
        // },
        //				{
        //					"path": "bargain/payment/payment",
        //					"style": {
        //						"navigationStyle": "custom"
        //					}
        //				},
        //----------团购模块（3）----------
        // {
        // 	"path": "groupbuy/list/list",
        // 	"style": {}
        // },
        // {
        // 	"path": "groupbuy/detail/detail",
        // 	"style": {}
        // },
        // {
        // 	"path": "groupbuy/payment/payment",
        // 	"style": {}
        // },
        //----------小游戏模块（3）----------
        // {
        // 	"path": "game/scratch_ticket/scratch_ticket",
        // 	"style": {}
        // },
        // 大转盘
        //				{
        //					"path": "game/turntable/turntable",
        //					"style": {}
        //				},
        // {
        // 	"path": "game/smash_eggs/smash_eggs",
        // 	"style": {}
        // },
        //				{
        //					"path": "game/gift/gift",
        //					"style": {}
        //				},
        //----------积分模块（2）----------
        // {
        // 	"path": "point/list/list",
        // 	"style": {}
        // },
        // {
        // 	"path": "point/detail/detail",
        // 	"style": {}
        // },
        // {
        // 	"path": "point/payment/payment",
        // 	"style": {}
        // },
        // {
        // 	"path": "point/order_list/order_list",
        // 	"style": {}
        // },
        // {
        // 	"path": "point/order_detail/order_detail",
        // 	"style": {}
        // },
        // {
        // 	"path": "point/result/result",
        // 	"style": {}
        // },
        // {
        // 	"path" : "wholesale/list/list",
        // 	"style" : {}
        // }
        // ,{
        // 	"path" : "wholesale/detail/detail",
        // 	"style" : {}
        // }
        // ,{
        // 		"path" : "wholesale/payment/payment",
        // 		"style" : {}
        // }
        // ,{
        // 		"path" : "wholesale/cartList/cartList",
        // 		"style" : {}
        // }
        // ,{
        //     "path" : "wholesale/order/order",
        //     "style" : {}
        // }
        //****************** 分销助力详情 ******************
        //				{
        //					"path": "assist/detail/detail",
        //					"style": {}
        //				},
        //***************分享赚活动**************************
        //				{
        //					"path": "fenxiangzhuan/activityDetails/activityDetails",
        //					"style": {}
        //				},
        //				{
        //					"path": "fenxiangzhuan/apply/apply",
        //					"style": {}
        //				},
        //				{
        //					"path": "fenxiangzhuan/goodsDetail/goodsDetail",
        //					"style": {}
        //				},
        {
          "path": "new_people/list/list",
          "style": {}
        },
        {
          "path": "preferred_list/list/list",
          "style": {}
        },
        {
          "path": "task/list/list",
          "style": {
            "h5": {
              "titleNView": false
            }
          }
        },
        {
          "path": "articlemessage/detail/detail",
          "style": {
            "navigationBarTitleText": "用户分享体验详情",
            "enablePullDownRefresh": true
          }
        },
        {
          "path": "articlemessage/list/list",
          "style": {
            "navigationBarTitleText": "用户分享体验",
            "enablePullDownRefresh": true
          }
        },
        {
          "path": "special_offers/special_offers",
          "style": {
            "navigationBarTitleText": "优惠活动",
            "enablePullDownRefresh": false
          }
        },
        {
          "path": "seeding/seeding-list/seeding-list",
          "style": {
            "enablePullDownRefresh": true,
            "navigationStyle": "custom",
            "h5": {
              "titleNView": false
            }
          }
        },
        {
          "path": "seeding/seeding_detail/seeding_detail",
          "style": {
            "navigationStyle": "custom",
            "h5": {
              "titleNView": false
            }
          }
        },
        {
          "path": "seeding/seeding_home_page/seeding_home_page",
          "style": {
            "navigationStyle": "custom"
          }
        },
        {
          "path": "seeding/seeding-add/seeding-add",
          "style": {
            "navigationBarTitleText": "发布种草",
            "enablePullDownRefresh": false
          }
        },
        {
          "path": "seeding/seeding-select-good/seeding-select-good",
          "style": {
            "navigationBarTitleText": "关联商品",
            "enablePullDownRefresh": false
          }
        },
        {
          "path": "material/detail/detail",
          "style": {
            "navigationBarTitleText": "素材详情"
          }
        },
        {
          "path": "questionnaire/qform/qform",
          "style": {
            "navigationBarTitleText": "问券活动",
            "h5": {
              "titleNView": false
            },
            "mp-weixin": {
              "navigationStyle": "custom"
            }
          }
        },
        {
          "path": "questionnaire/records/records",
          "style": {
            "navigationBarTitleText": "问卷提交记录",
            "onReachBottomDistance": 50
          }
        },
        {
          "path": "new_product_area/list/list",
          "style": {
            "navigationBarTitleText": "新品专区列表",
            "enablePullDownRefresh": false,
            "h5": {
              "titleNView": false
            }
          }
        }
      ]
    },
    {
      //*****************其他模块（26）******************
      "root": "otherpages",
      "pages": [
        //**************************************
        //				{
        //					"path": "order/refund/refund",
        //					"style": {}
        //				},
        {
          "path": "order/refund_detail/refund_detail",
          "style": {}
        },
        {
          "path": "order/weixin_receipt/weixin_receipt",
          "style": {
            "navigationStyle": "custom"
          }
        },
        //				{
        //					"path": "order/evaluate/evaluate",
        //					"style": {}
        //				},
        //				{
        //					"path": "index/city/city",
        //					"style": {
        //						"disableScroll": true
        //					}
        //				},
        {
          "path": "web/web",
          "style": {
            //						"navigationStyle": "custom"
          }
        },
        //******************微页面******************
        {
          "path": "diy/diy/diy",
          "style": {
            "navigationStyle": "custom",
            "mp-weixin": {
              "usingComponents": {
                "subscribe": "plugin-private://wx2b03c6e691cd7370/components/subscribe/subscribe",
                "player-component": "plugin://player/video"
              }
            }
          }
        },
        //******************会员模块（20）******************
        // 旧的银行卡账号管理页面
        // {
        // 	"path": "member/account/account",
        // 	"style": {}
        // },
        // {
        // 	"path": "member/account_edit/account_edit",
        // 	"style": {}
        // },
        //******************迈豆模块******************
        {
          "path": "member/myMaidou/myMaidou",
          "style": {
            "navigationBarTitleText": "我的迈豆"
          }
        },
        {
          "path": "member/my_shop/my_shop",
          "style": {
            "navigationBarTitleText": "我的店铺"
          }
        },
        {
          "path": "member/setting/setting",
          "style": {
            "navigationBarTitleText": "设置"
          }
        },
        {
          "path": "member/setting/setting_password",
          "style": {
            "navigationBarTitleText": "支付密码设置"
          }
        },
        {
          "path": "member/up_to_shopkeeper/up_to_shopkeeper",
          "style": {
            "navigationBarTitleText": "升级成为店主"
          }
        },
        {
          "path": "member/up_to_shopkeeper/form_up_to_shopkeeper",
          "style": {
            "navigationBarTitleText": "升级成为店主"
          }
        },
        {
          "path": "member/up_to_shopkeeper/show_result",
          "style": {
            "navigationBarTitleText": "升级成为店主"
          }
        },
        {
          "path": "member/up_to_shopkeeper/upgrade_invitation",
          "style": {
            "navigationBarTitleText": "升级邀请"
          }
        },
        //				{
        //					"path": "member/modify_face/modify_face",
        //					"style": {}
        //				},
        //				{
        //					"path": "member/info/info",
        //					"style": {
        //						"navigationStyle": "custom",
        //						"navigationBarTitleText": "个人资料"
        //					}
        //				},
        //				{
        //					"path": "member/earnings/earnings",
        //					"style": {}
        //				},
        {
          "path": "member/apply_withdrawal/apply_withdrawal",
          "style": {}
        },
        {
          "path": "member/balance/balance",
          "style": {
            "navigationStyle": "custom"
          }
        },
        {
          "path": "member/balance_detail/balance_detail",
          "style": {}
        },
        {
          "path": "member/collection/collection",
          "style": {}
        },
        //				{
        //					"path": "member/event_application_list/event_application_list",
        //					"style": {
        //						"navigationBarTitleText": "活动申请列表"
        //					}
        //				},
        {
          "path": "member/coupon/coupon",
          "style": {
            "navigationBarTitleText": "我的优惠券"
            // "disableScroll": true
          }
        },
        {
          "path": "member/footprint/footprint",
          "style": {}
        },
        //				{
        //					"path": "member/level/level",
        //					"style": {}
        //				},
        //				{
        //					"path": "member/message/message",
        //					"style": {}
        //				},
        //				{
        //					"path": "member/point/point",
        //					"style": {}
        //				},
        //				{
        //					"path": "member/signin/signin",
        //					"style": {}
        //				},
        {
          "path": "member/signin/sign_in_product_rewards",
          "style": {
            "mp-weixin": {
              "navigationStyle": "custom"
            }
          }
        },
        {
          "path": "member/signin/sign_in_product_rewards_records",
          "style": {}
        },
        // {
        // 	"path": "member/gift/gift",
        // 	"style": {}
        // },
        // {
        // 	"path": "member/gift_detail/gift_detail",
        // 	"style": {}
        // },
        {
          "path": "member/withdrawal/withdrawal",
          "style": {}
        },
        {
          "path": "member/withdrawal_detail/withdrawal_detail",
          "style": {}
        },
        {
          "path": "member/address/address",
          "style": {}
        },
        {
          "path": "member/address/setWechatAddress",
          "style": {}
        },
        {
          "path": "member/address_edit/address_edit",
          "style": {}
        },
        //				{
        //					"path": "member/pay_password/pay_password",
        //					"style": {}
        //				},
        {
          "path": "member/bank_card_list/bank_card_list",
          "style": {}
        },
        {
          "path": "member/bank_card_detail/bank_card_detail",
          "style": {}
        },
        {
          "path": "member/bank_list/bank_list",
          "style": {}
        },
        {
          "path": "member/real_name_authentication/real_name_authentication",
          "style": {}
        },
        {
          "path": "member/open_shopkeeper/open_shopkeeper",
          "style": {}
        },
        {
          "path": "member/open_shopkeeper/result",
          "style": {}
        },
        //******************登录模块******************
        // #ifdef H5
        {
          "path": "login/find/find",
          "style": {
            //				 		"navigationStyle": "custom",
            //				 		"navigationBarTitleText": ""
          }
        },
        // #endif
        //******************商品模块******************
        //				{
        //					"path": "goods/brand/brand",
        //					"style": {}
        //				},
        //				{
        //					"path": "goods/consult/consult",
        //					"style": {}
        //				},
        //				{
        //					"path": "goods/consult_edit/consult_edit",
        //					"style": {}
        //				},
        //				{
        //					"path": "goods/coupon/coupon",
        //					"style": {}
        //				},
        {
          "path": "goods/coupon_receive/coupon_receive",
          "style": {
            // "enablePullDownRefresh": true
          }
        },
        {
          "path": "goods/coupon_goods_list/coupon_goods_list",
          "style": {
            "navigationStyle": "custom"
          }
        },
        //				{
        //					"path": "goods/evaluate/evaluate",
        //					"style": {}
        //				},
        {
          "path": "goods/search/search",
          "style": {
            "navigationStyle": "custom"
          }
        },
        //******************CMS模块（6）******************
        // {
        // 	"path": "help/list/list",
        // 	"style": {}
        // },
        // {
        // 	"path": "help/detail/detail",
        // 	"style": {}
        // },
        // {
        // 	"path": "notice/list/list",
        // 	"style": {}
        // },
        // {
        // 	"path": "notice/detail/detail",
        // 	"style": {}
        // },
        //******************店铺模块（7）******************
        //				{
        //					"path": "shop/index/index",
        //					"style": {
        //						"enablePullDownRefresh": true,
        //						"navigationStyle": "custom"
        //					}
        //				},
        //				{
        //					"path": "shop/introduce/introduce",
        //					"style": {}
        //				},
        //				{
        //					"path": "shop/search/search",
        //					"style": {}
        //				},
        //				{
        //					"path": "shop/street/street",
        //					"style": {}
        //				},
        {
          "path": "shop/category/category",
          "style": {
            "disableScroll": true,
            "navigationStyle": "custom"
          }
        },
        //				{
        //					"path": "shop/message/message",
        //					"style": {}
        //				},
        {
          "path": "shop/list/list",
          "style": {
            "navigationStyle": "custom"
          }
        },
        //				{
        //					"path": "shop/store_detail/store_detail",
        //					"style": {}
        //				},
        {
          "path": "shop/home/<USER>",
          "style": {
            "navigationStyle": "custom",
            "mp-weixin": {
              "usingComponents": {
                "subscribe": "plugin-private://wx2b03c6e691cd7370/components/subscribe/subscribe",
                "player-component": "plugin://player/video"
              }
            }
          }
        },
        {
          "path": "shop/cycle_purchase/cycle_purchase",
          "style": {}
        },
        //******************核销模块（4）******************
        // {
        // 	"path": "verification/index/index",
        // 	"style": {}
        // },
        // {
        // 	"path": "verification/list/list",
        // 	"style": {}
        // },
        // {
        // 	"path": "verification/detail/detail",
        // 	"style": {}
        // },
        //******************会员充值（4）******************
        //				{
        //					"path": "recharge/list/list",
        //					"style": {}
        //				},
        //				{
        //					"path": "recharge/detail/detail",
        //					"style": {}
        //				},
        //				{
        //					"path": "recharge/order_list/order_list",
        //					"style": {}
        //				},
        //****************** 分销（二开系统自带的） ******************
        //				{
        //					"path": "fenxiao/index/index",
        //					"style": {}
        //				},
        //				{
        //					"path": "fenxiao/apply/apply",
        //					"style": {}
        //				},
        //				{
        //					"path": "fenxiao/order/order",
        //					"style": {}
        //				},
        //				{
        //					"path": "fenxiao/order_detail/order_detail",
        //					"style": {}
        //				},
        //				{
        //					"path": "fenxiao/team/team",
        //					"style": {}
        //				},
        //				{
        //					"path": "fenxiao/withdraw_apply/withdraw_apply",
        //					"style": {}
        //				},
        //				{
        //					"path": "fenxiao/withdraw_list/withdraw_list",
        //					"style": {}
        //				},
        //				{
        //					"path": "fenxiao/promote_code/promote_code",
        //					"style": {}
        //				},
        //				{
        //					"path": "fenxiao/level/level",
        //					"style": {
        //						"navigationStyle": "custom"
        //					}
        //				},
        //				{
        //					"path": "fenxiao/goods_list/goods_list",
        //					"style": {}
        //				},
        //				{
        //					"path": "fenxiao/follow/follow",
        //					"style": {}
        //				},
        //				{
        //					"path": "fenxiao/bill/bill",
        //					"style": {}
        //				},
        {
          "path": "live/list/list",
          "style": {}
        },
        {
          "path": "live/end/end",
          "style": {
            // "navigationStyle": "custom",
            "navigationBarTitleText": "直播回放"
          }
        },
        {
          "path": "live/list/liveList",
          "style": {
            "mp-weixin": {
              "usingComponents": {
                "subscribe": "plugin-private://wx2b03c6e691cd7370/components/subscribe/subscribe"
              }
            }
          }
        },
        //				{
        //					"path": "chat/room/room",
        //					"style": {
        //						"enablePullDownRefresh": true
        //					}
        //				},
        //				{
        //					"path": "chat/list/list",
        //					"style": {}
        //				},
        {
          "path": "store/store_empty/store_empty",
          "style": {
            "navigationStyle": "custom"
          }
        },
        //****************** 分销客（新增加的） ******************
        //				{
        //					"path": "fenxiaoke/apply/apply",
        //					"style": {}
        //				},
        //*******************本地生活h5调用小程序支付的页面****************
        {
          "path": "local_life_spa/pay/pay",
          "style": {}
        },
        // ********************（退换模块）****************
        {
          "path": "order/return_and_exchange/select_service",
          "style": {
            "navigationBarTitleText": "选择服务"
          }
        },
        {
          "path": "order/return_and_exchange/refund_form",
          "style": {
            "navigationBarTitleText": ""
          }
        },
        {
          "path": "order/return_and_exchange/refund_progress",
          "style": {
            "navigationBarTitleText": ""
          }
        },
        {
          "path": "order/parcel/parcel",
          "style": {
            "navigationBarTitleText": "物流详情"
          }
        },
        {
          "path": "shop/app-home/app-home",
          "style": {
            "navigationStyle": "custom",
            "navigationBarTitleText": "",
            "enablePullDownRefresh": false
          }
        },
        {
          "path": "member/update_headimg_nickname/update_headimg_nickname",
          "style": {
            "navigationBarTitleText": "头像昵称修改",
            "enablePullDownRefresh": false
          }
        },
        {
          "path": "member/biometric_authentication/biometric_authentication",
          "style": {
            "navigationBarTitleText": "",
            "enablePullDownRefresh": false
          }
        },
        {
          "path": "member/app_sms_verification/app_sms_verification",
          "style": {
            "navigationBarTitleText": "",
            "enablePullDownRefresh": false,
            "navigationStyle": "custom"
          }
        },
        {
          "path": "member/certification/certification",
          "style": {
            "navigationBarTitleText": "",
            "enablePullDownRefresh": false
          }
        },
        {
          "path": "member/shop_manage/shop_manage",
          "style": {
            "navigationBarTitleText": "店铺管理"
          }
        },
        {
          "path": "member/shop_manage/fans_detail",
          "style": {
            "navigationBarTitleText": "我的粉丝",
            "navigationStyle": "custom"
          }
        },
        {
          "path": "member/shop_manage/performance_center",
          "style": {
            "navigationBarTitleText": "业绩中心"
          }
        },
        {
          "path": "member/join/contribution_list/contribution_list",
          "style": {
            "navigationStyle": "custom",
            "navigationBarTitleText": "我的贡献值",
            "enablePullDownRefresh": false
          }
        },
        {
          "path": "member/join/redeem_mall/redeem_mall",
          "style": {
            "navigationBarTitleText": "贡献值兑换",
            "navigationStyle": "custom",
            "enablePullDownRefresh": false,
            "onReachBottomDistance": 50
          }
        },
        {
          "path": "member/join/contribution_records/contribution_records",
          "style": {
            "navigationBarTitleText": "贡献值记录",
            "enablePullDownRefresh": false,
            "onReachBottomDistance": 50
          }
        },
        {
          "path": "member/cross_border_real_name_authentication/cross_border_real_name_authentication",
          "style": {
            "navigationBarTitleText": "跨境实名认证",
            "enablePullDownRefresh": false
          }
        }
      ],
      "plugins": {
        "player": {
          "version": "2.6.1",
          "provider": "wxa75efa648b60994b"
        }
      }
    },
    {
      "root": "pluginspages",
      "pages": [
        {
          "path": "logisticsPlugin/logisticsPlugin",
          "style": {}
        }
      ],
      "plugins": {
        "logisticsPlugin": {
          "version": "2.3.0",
          "provider": "wx9ad912bf20548d92"
        }
      }
    }
  ],
  "globalStyle": {
    "navigationBarTextStyle": "black",
    "navigationBarTitleText": "",
    "navigationBarBackgroundColor": "#ffffff",
    "backgroundColor": "#F7f7f7",
    "backgroundColorTop": "#f7f7f7",
    "backgroundColorBottom": "#f7f7f7",
    "mp-weixin": {
      "usingComponents": {
        "t-captcha": "plugin://captcha/t-captcha"
      }
    }
  },
  "condition": {
    //模式配置，仅开发期间生效
    "current": 0,
    //当前激活的模式(list 的索引项)
    "list": [
      {
        "name": "店铺首页new",
        //模式名称
        "path": "otherpages/shop/home/<USER>",
        //启动页面，必选
        "query": "shop_id=168"
        //启动参数，在页面的onLoad函数里面得到
      },
      {
        "name": "个人中心",
        //模式名称
        "path": "pages/member/index/index",
        //启动页面，必选
        "query": ""
        //启动参数，在页面的onLoad函数里面得到
      },
      {
        "name": "搜索结果",
        //模式名称
        "path": "pages/goods/list/list",
        //启动页面，必选
        "query": "keyword=ff"
        //启动参数，在页面的onLoad函数里面得到
      },
      {
        "name": "待付款",
        //模式名称
        "path": "pages/order/list/list",
        //启动页面，必选
        "query": "status=waitpay"
        //启动参数，在页面的onLoad函数里面得到
      },
      {
        "name": "模拟分享进入商品详情页",
        //模式名称
        "path": "pages/goods/detail/detail",
        //启动页面，必选
        "query": "sku_id=32&shop_id=1"
        //启动参数，在页面的onLoad函数里面得到
      },
      {
        "name": "开通vip掌柜h5",
        //模式名称
        "path": "otherpages/member/open_shopkeeper/open_shopkeeper",
        //启动页面，必选
        "query": "invitation_shop_id=1&open_type=0"
        //启动参数，在页面的onLoad函数里面得到
      },
      {
        "name": "开通vip掌柜结果页面",
        //模式名称
        "path": "otherpages/member/open_shopkeeper/result",
        //启动页面，必选
        "query": "mobile=13560041293"
        //启动参数，在页面的onLoad函数里面得到
      },
      {
        "name": "大转盘游戏",
        //模式名称
        "path": "promotionpages/game/turntable/turntable",
        //启动页面，必选
        "query": "share_member_id=32"
        //启动参数，在页面的onLoad函数里面得到
      },
      {
        "name": "我的礼品",
        //模式名称
        "path": "promotionpages/game/gift/gift",
        //启动页面，必选
        "query": ""
        //启动参数，在页面的onLoad函数里面得到
      },
      {
        "name": "实名认证",
        //模式名称
        "path": "otherpages/member/real_name_authentication/real_name_authentication",
        //启动页面，必选
        "query": ""
        //启动参数，在页面的onLoad函数里面得到
      },
      {
        "name": "分销客申请",
        //模式名称
        "path": "otherpages/fenxiaoke/apply/apply",
        //启动页面，必选
        "query": "shop_id=1"
        //启动参数，在页面的onLoad函数里面得到
      },
      {
        "name": "提现申请",
        //模式名称
        "path": "otherpages/member/apply_withdrawal/apply_withdrawal",
        //启动页面，必选
        "query": "shop_id=1"
        //启动参数，在页面的onLoad函数里面得到
      },
      {
        "name": "资金明细",
        //模式名称
        "path": "otherpages/member/balance/balance",
        //启动页面，必选
        "query": "shop_id=1"
        //启动参数，在页面的onLoad函数里面得到
      },
      {
        "name": "资金明细详情",
        //模式名称
        "path": "otherpages/member/balance_detail/balance_detail",
        //启动页面，必选
        "query": "shop_id=1"
        //启动参数，在页面的onLoad函数里面得到
      },
      {
        "name": "我的优惠券",
        //模式名称
        "path": "otherpages/member/coupon/coupon",
        //启动页面，必选
        "query": ""
        //启动参数，在页面的onLoad函数里面得到
      },
      {
        "name": "领取优惠劵",
        //模式名称
        "path": "otherpages/goods/coupon_receive/coupon_receive",
        //启动页面，必选
        "query": "shop_id=1&coupon_type_id=5"
        //启动参数，在页面的onLoad函数里面得到
      },
      {
        "name": "测试支付接口",
        //模式名称
        "path": "pages/order/detail/forPayButton",
        //启动页面，必选
        "query": ""
        //启动参数，在页面的onLoad函数里面得到
      },
      {
        "name": "周期购列表",
        //模式名称
        "path": "otherpages/shop/cycle_purchase/cycle_purchase",
        //启动页面，必选
        "query": ""
        //启动参数，在页面的onLoad函数里面得到
      },
      {
        "name": "周期购确认订单页",
        //模式名称
        "path": "pages/order/cycle_payment/cycle_payment",
        //启动页面，必选
        "query": "sku_id=52"
        //启动参数，在页面的onLoad函数里面得到
      },
      {
        "name": "新秒杀专区",
        //模式名称
        "path": "promotionpages/new_seckill/list/list",
        //启动页面，必选
        "query": "type=1"
        //启动参数，在页面的onLoad函数里面得到
      },
      {
        "name": "新秒杀专区详情",
        //模式名称
        "path": "promotionpages/new_seckill/detail/detail",
        //启动页面，必选
        "query": "sku_id=96"
        //启动参数，在页面的onLoad函数里面得到
      },
      {
        "name": "活动申请",
        //模式名称
        "path": "otherpages/member/event_application_list/event_application_list",
        //启动页面，必选
        "query": ""
        //启动参数，在页面的onLoad函数里面得到
      },
      {
        "name": "分享赚活动申请商品详情",
        //模式名称
        "path": "promotionpages/fenxiangzhuan/goodsDetail/goodsDetail",
        //启动页面，必选
        "query": "activity_id=1&shop_id=110"
        //启动参数，在页面的onLoad函数里面得到
      },
      {
        "name": "砍价活动详情",
        //模式名称
        "path": "promotionpages/bargain/detail/detail",
        "query": "activity_id=15"
        //启动参数，在页面的onLoad函数里面得到
      },
      {
        "name": "先迈店铺首页",
        //模式名称
        "path": "pages/home/<USER>",
        "query": "shopId=37127&uid=268206"
        //启动参数，在页面的onLoad函数里面得到
      },
      {
        "name": "先迈商品详情页面",
        //模式名称
        "path": "pages/productDetail/productDetail",
        "query": "goods_id=12&shopId=37192&uid=268887"
        //启动参数，在页面的onLoad函数里面得到
      },
      {
        "name": "先迈单个商品详情页面",
        //模式名称
        "path": "pages/singalProductDetail/singalProductDetail",
        "query": "singalProductId=12&shopId=37235&uid=269170"
        //启动参数，在页面的onLoad函数里面得到
      },
      {
        "name": "新人专享",
        //模式名称
        "path": "promotionpages/new_people/list/list",
        "query": ""
        //启动参数，在页面的onLoad函数里面得到
      },
      {
        "name": "优选单品",
        //模式名称
        "path": "promotionpages/preferred_list/list/list",
        "query": ""
        //启动参数，在页面的onLoad函数里面得到
      },
      {
        "name": "任务专区",
        //模式名称
        "path": "promotionpages/task/list/list",
        "query": "topic_id=3"
        //启动参数，在页面的onLoad函数里面得到
      },
      {
        "name": "拼团商品详情页",
        //模式名称
        "path": "promotionpages/pintuan/detail/detail",
        "query": "id=1"
        //启动参数，在页面的onLoad函数里面得到
      },
      {
        "name": "拼团订单列表",
        //模式名称
        "path": "promotionpages/pintuan/order/list/list",
        "query": ""
        //启动参数，在页面的onLoad函数里面得到
      },
      {
        "name": "拼团订单详情页",
        //模式名称
        "path": "promotionpages/pintuan/order/detail/detail",
        "query": "order_id=1"
        //启动参数，在页面的onLoad函数里面得到
      },
      {
        "name": "参团详情页",
        //模式名称
        "path": "promotionpages/pintuan/share/share",
        "query": "group_id=6"
        //启动参数，在页面的onLoad函数里面得到
      },
      {
        "name": "物流信息",
        //模式名称
        "path": "pages/order/logistics/logistics",
        "query": "order_id=1453"
        //启动参数，在页面的onLoad函数里面得到
      },
      {
        "name": "我的足迹",
        //模式名称
        "path": "otherpages/member/footprint/footprint",
        //启动页面，必选
        "query": ""
        //启动参数，在页面的onLoad函数里面得到
      },
      {
        "name": "用户分享体验列表",
        //模式名称
        "path": "promotionpages/articlemessage/list/list",
        //启动页面，必选
        "query": ""
        //启动参数，在页面的onLoad函数里面得到
      },
      {
        "name": "组合商品详情页面",
        //模式名称
        "path": "promotionpages/combo/detail/detail",
        "query": ""
        //启动参数，在页面的onLoad函数里面得到
      },
      {
        "name": "签到商品奖励",
        //模式名称
        "path": "otherpages/member/signin/sign_in_product_rewards",
        "query": ""
        //启动参数，在页面的onLoad函数里面得到
      }
    ]
  }
}
