## 1.0.15（2023-12-06）
1. 优化
## 1.0.14（2023-12-06）
1. 阻止事件冒泡问题
## 1.0.13（2023-11-15）
1. 修复webp之前未使用的BUG
## 1.0.12（2023-10-11）
1. 修复懒加载报错：https://gitee.com/climblee/uv-ui/issues/I869JS
## 1.0.11（2023-08-31）
1. 修复设置widthFix时出现显示不全的BUG
2. 修复抖音等平台在width和height属性改变时出现不显示的BUG
## 1.0.10（2023-08-29）
1. 修复异步修改宽高不生效的问题，问题来源：https://gitee.com/climblee/uv-ui/issues/I7WUQ3
## 1.0.9（2023-08-21）
1. 修复设置宽高为百分比不生效的BUG
## 1.0.8（2023-07-24）
1. 优化 nvue模式下增加cellChild参数，是否在list中cell节点下，nvue中cell下建议设置成true
## 1.0.7（2023-07-02）
修复VUE3模式下可能不显示的BUG
## 1.0.6（2023-07-02）
优化修改
## 1.0.5（2023-06-28）
修复duration属性不生效的BUG
## 1.0.4（2023-05-27）
1. 修复可能报错的问题
## 1.0.3（2023-05-24）
1. 去掉template中存在的this.导致头条小程序编译警告
## 1.0.2（2023-05-23）
1. 优化
## 1.0.1（2023-05-16）
1. 优化组件依赖，修改后无需全局引入，组件导入即可使用
2. 优化部分功能
## 1.0.0（2023-05-10）
uv-image 图片
