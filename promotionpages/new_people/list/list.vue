<template>
	<view class="new_people" :class="themeStyle" :style="{'margin-top': top + 'px'}">
		<view class="banner" v-if="banner.length>0">
			<image v-for="(row,index) in banner" v-if="index==0" :src="row.image_url" mode=""
				v-on:click="toAd(row)"></image>
		</view>
		<view class="container">
			<view class="nav_tab">
				<view class="tab_item" :class="{active:sf == 'all'}" @click="changTab('all')">综合</view>
				<view class="tab_item" :class="{active:sf == 'sale_num'}" @click="changTab('sale_num')">销量</view>
				<view class="tab_item" :class="{active:sf == 'price'}" @click="changTab('price')">价格</view>
			</view>
			<mescroll-uni ref="mescroll" v-bind:top="containerHas ? headerTop :100" @getData="getData" :size="4" @scroll="scrollTouch">
				<block slot="list">
					<view class="goods_list" v-if="dataList.length">
						<view class="goods_item" v-for="(row,index) in dataList" :key="row.goods_id"
							@click="toProductDetail(row)">
							<view class="thumbImage">
								<image class="expose_goods_index" :data-expose_goods_sku="row.sku_id"
									:src="$util.img(row.goods_image)" @error="imageError(index)" mode='aspectFit'>
								</image>
								<image v-if="row.goods_stock==0"
									:src="$util.img('public/static/youpin/product-sell-out.png')" class="over"></image>
							</view>
							<view class="goods_info">
								<view class="goods_name"><text v-for="(tag, key) in row.tags" :key="key"
										class="tag">{{tag.tag_name}}</text>{{row.goods_name}}</view>
								<view class="bottom">
									<view class="sale_price"><text>￥</text>{{row.retail_price}}</view>
									<view class="market_price">￥{{row.market_price}}</view>
									<view class="buy_btn">立即购买</view>
								</view>
							</view>
						</view>

					</view>
					<view v-if="!loading && !dataList.length">
						<ns-empty :fixed="false"></ns-empty>
					</view>
				</block>
			</mescroll-uni>
		</view>
		<loading-cover ref="loadingCover"></loading-cover>
	</view>
</template>

<script>
	import system from "@/common/js/system.js";
	import wx_expose_goods from '@/common/mixins/wx_expose_goods.js';
	// #ifdef H5
	import {isOnXianMaiApp} from "@/common/js/h5/appOP";
	// #endif
	export default {
		mixins: [wx_expose_goods],
		data() {
			return {
				dataList: [],
				sf: 'all',
				banner: [],
				tag_key: 'newhand',
				loading: true,
				// #ifdef H5
				isOnXianMaiApp:isOnXianMaiApp,
				// #endif
        headerTop: 360,
				top: 0
			}
		},
		computed: {
			themeStyle() {
				return 'theme-' + this.$store.state.themeStyle
			},
			containerHas() {
				return this.banner.length > 0;
			}
		},
		onLoad(options) {
			this.getBanner()
		},
		async onShow() {
			this.$langConfig.refresh();

			await system.wait_staticLogin_success();

			// #ifdef H5
			let share_data = this.$util.deepClone(this.getSharePageParams())
			let link = window.location.origin + this.$router.options.base + share_data.link.slice(1)
			share_data.link = link
			share_data.desc = share_data.title
			share_data.title = '先迈商城'
			await this.$util.publicShare(share_data);

			if(isOnXianMaiApp){
        this.headerTop = 420;
				this.top = 44;
      }
			// #endif
		},
		methods: {
			getBanner() {
				this.$api.sendRequest({
					url: this.$apiUrl.specialBannerUrl,
					data: {
						sign: this.tag_key + '-1'
					},
					success: (res) => {
						if (res.code != 0) {
							this.$util.showToast({
								title: res.message
							})
							return
						}
						this.banner = res.data.list
					},
					fail: (err) => {
						this.$util.showToast({
							title: err.message
						})
					}
				})
			},
			getData(mescroll) {
				this.mescroll = mescroll;
				if (mescroll.num == 1) this.dataList = [];
				this.loading = true;
				this.$api.sendRequest({
					url: this.$apiUrl.goodsListUrl,
					data: {
						page_size: mescroll.size,
						page: mescroll.num,
						tag_key: this.tag_key,
						sf: this.sf
					},
					success: (res) => {
						this.loading = false
						if (res.code != 0) {
							this.$util.showToast({
								title: res.message
							})
							return
						}
						mescroll.endSuccess(res.data.list.length);
						this.dataList = this.dataList.concat(res.data.list)
						if (this.$refs.loadingCover) this.$refs.loadingCover.hide();
					},
					fail: (err) => {
						this.loading = false
						mescroll.endErr();
						this.$util.showToast({
							title: err.message
						})
						if (this.$refs.loadingCover) this.$refs.loadingCover.hide();
					}
				})
			},
			changTab(type) {
				this.sf = type;
				this.$refs.mescroll.refresh();
				this.againDealWith(true)
			},
			toProductDetail(item) {
				this.$util.redirectTo(`/pages/goods/detail/detail?sku_id=${item.sku_id}`)
			},
			toAd(item) {
        this.$buriedPoint.diyReportAdEvent(
            {diy_ad_location_type:'list',diy_material_path:item.image_url,diy_ad_type:'image',diy_target_page:item.banner_url,diy_ad_id:item.id,diy_action_type:'click'})
        this.$util.specialBannerReportByClick(item.id)
        if (item.banner_url) {
          this.$util.diyCompateRedirectTo({
            wap_url: item.banner_url
          })
        }
			},
			imageError(index) {
				this.dataList[index].goods_image = this.$util.getDefaultImage().default_goods_img;
				this.$forceUpdate();
			},
			getSharePageParams() {
        let share_data=this.$util.unifySharePageParams('/promotionpages/new_people/list/list','百元商品，首单1元起！',
            '',{},this.$util.img('public/static/youpin/new_share.jpg'))
        return share_data;
			}
		},
		// 分享朋友
		onShareAppMessage(res) {
      let share_data = this.getSharePageParams();
      return this.$buriedPoint.pageShare(share_data.link, share_data.imageUrl,share_data.title);
		},
		// 分享到微信
		onShareTimeline(res) {
			let {
				title,
				imageUrl,
				query
			} = this.getSharePageParams()
			return {
				title,
				imageUrl,
				query,
				success: res => {},
				fail: res => {}
			};
		}
	}
</script>

<style lang="scss" scoped>
	.new_people{
		position: fixed;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		display: flex;
		flex-direction: column;
		.container{
			position: initial;
			flex: 1;
			margin-top: -10px;
		}
	}
	.banner {
		width: 100%;
		height: 280rpx;

		image {
			width: 100%;
			height: 100%;
		}
	}

	.container {
		width: 100%;
		height: calc(100vh - 0rpx);
		background: linear-gradient(0deg, #F5F5F5 0%, #FFFFFF 100%);
		border-radius: 20rpx 20rpx 0px 0px;
		overflow: hidden;
		position: absolute;
		top: 0;

		&-has {
			top: 240rpx;
			height: calc(100vh - 260rpx);
		}

		z-index: 1;

		.nav_tab {
			width: 100%;
			height: 100rpx;
			line-height: 100rpx;
			display: flex;
			align-items: center;

			.tab_item {
				width: 33%;
				text-align: center;
				color: #999;
				font-size: 28rpx;

				&.active {
					color: $base-color
				}
			}
		}
		.app-good-list{
			padding-top: 50rpx;
		}
		.goods_list {
			.goods_item {
				width: 690rpx;
				box-sizing: border-box;
				background: #fff;
				margin: 0 auto;
				margin-bottom: 20rpx;
				padding: 20rpx 24rpx;
				display: flex;
				align-items: center;
				justify-content: space-between;
				border-radius: 20rpx;

				.thumbImage {
					position: relative;
					width: 240rpx;
					height: 240rpx;
					border-radius: 8rpx;
					margin-right: 26rpx;
					overflow: hidden;

					image {
						width: 100%;
						height: 100%;
					}

					.over {
						width: 120rpx;
						height: 120rpx;
						position: absolute;
						left: 50%;
						top: 50%;
						transform: translate(-50%, -50%);
					}
				}

				.goods_info {
					width: calc(100% - 270rpx);
					height: 240rpx;
					position: relative;

					.goods_name {
						font-size: 26rpx;
						color: #333;
						display: -webkit-box;
						-webkit-box-orient: vertical;
						-webkit-line-clamp: 2;
						overflow: hidden;

						.tag {
							padding: 5rpx;
							background: linear-gradient(270deg, #FE5838 0%, #FB331D 100%);
							border-radius: 8rpx;
							font-size: 20rpx;
							color: #fff;
							text-align: center;
							line-height: 24rpx;
							margin-right: 10rpx;
						}
					}

					.bottom {
						position: absolute;
						bottom: 0;
						width: 100%;
						line-height: 1;

						.sale_price {
							font-size: 36rpx;
							color: $base-color;
							font-weight: bold;
							line-height: 1;

							text {
								font-size: 26rpx;
								font-weight: normal;
							}
						}

						.market_price {
							line-height: 1;
							text-decoration: line-through;
							color: #999;
							font-size: 24rpx;
						}

						.buy_btn {
							position: absolute;
							right: 0;
							bottom: 0;
							background: linear-gradient(-90deg, #FF2127 0%, #FF5A2F 100%);
							height: 56rpx;
							line-height: 56rpx;
							border-radius: 28rpx;
							width: 148rpx;
							text-align: center;
							font-size: 24rpx;
							color: #fff;
						}
					}
				}
			}
		}
	}

	/deep/.mescroll-upwarp {
		padding: 0 !important;
		margin-bottom: 0;
		min-height: 0;
		line-height: 0;
	}

	/deep/.mescroll-uni {
		height: auto !important;
	}
</style>
