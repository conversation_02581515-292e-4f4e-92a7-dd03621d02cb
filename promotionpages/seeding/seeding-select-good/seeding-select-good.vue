<template>
  <div class="seeding-good" :style="[themeColorVar]">
    <div class="select-box">
      <div :class="select_type == 1 ? 'active':''" @click="selectType(1)">已购商品</div>
      <div :class="select_type == 2 ? 'active':''" @click="selectType(2)">足迹</div>
    </div>
    <view class="content">
      <view class="good-box">
        <view class="good-list" v-for="(item, index) in list" :key="index" @click="selectGoodFun(item.goods_id)">
          <image class="img" :src="item.goods_image" alt="" @error="errorFun(list,index)"/>
          <view class="good-info">
            <view class="title">{{item.goods_name}}</view>
            <view class="price">￥{{item.retail_price}}</view>
          </view>
          <image class="selectImage" :src="item.status == 1 ? selected_img :$util.img('public/static/youpin/select.png')" mode=""/>
        </view>
        <view class="empty-list-text" v-if="scrollLoading">没有更多内容</view>
      </view>
      <ns-loading v-if="showLoading"></ns-loading>
    </view>
    <view class="footer">
      <view class="text">已选择 （{{num}}/3）</view>
      <view class="btn" @click="comfirmFun">确认</view>
    </view>
    <loading-cover ref="loadingCover"></loading-cover>
  </div>
</template>

<script>
  import scrollLoading from '@/common/mixins/scroll-loading.js'
  import golbalConfig from "../../../common/mixins/golbalConfig";
  export default {
    mixins: [scrollLoading,golbalConfig],
    data() {
      return {
        select_type: 1,
        list: [],
        num: 0,
        goodInfo: [],
        selected_img:''
      }
    },
    onLoad(){
      let color = this.$util.colorToHex(this.$store.state.themeColorVar['--custom-brand-color']).slice(1);
      this.selected_img = encodeURI(this.$util.img( `api/website/svgChangeFillColor?svg_name=selected&color=${color}`))
    },
    onShow() {
      if(uni.getStorageSync('selectGoodType')) this.select_type = uni.getStorageSync('selectGoodType')
      this.getInit()
    },
    methods: {
      selectType(type) {
        if( this.select_type == type) return false
        this.select_type = type
        this.list = []
        this.page = 1
        this.num = 0
        this.scrollLoading = false
        this.showLoading = true
        // uni.removeStorageSync('selectGood');
        // uni.removeStorageSync('selectGoodType');
        this.getInit()
      },
      selectGoodFun(goods_id) {
        let num = 0
        this.list.map(v => {
          if(v.goods_id == goods_id) {
            if(v.status) {
              v.status = 0
            }else if(v.status == 0 && this.num < 3){
              v.status = 1
            }else{
              this.$util.showToast({
                title: '不能选择超过三件商品'
              })
            }
          }
          if(v.status == 1) {
            num ++
          }
        })
        this.num = num
      },
      getInit() {
        this.$api.sendRequest({
					url: this.select_type == 1 ? this.$apiUrl.buyList:this.$apiUrl.footprintList,
					data: {
            page: this.page,
            page_size: this.page_size,
          },
					success:(res)=>{
            this.showLoading = false
						if(res.code == 0){
              let data = res.data.list
              let goodInfo = uni.getStorageSync('selectGood') ? JSON.parse(uni.getStorageSync('selectGood')):[]
              if(goodInfo.length && this.select_type == uni.getStorageSync('selectGoodType')) {
                let arr = [], num = 0
                goodInfo.forEach(v => {
                  arr.push(v.goods_id)
                })
                data.map(v => {
                  v.status = arr.includes(v.goods_id) ? 1:0
                  if(v.status) num ++
                })
                this.num = num
              }else{
                data.map(v => {
                  v.status = 0
                })
              }
              if(data.length) {
                this.page ++;
                this.list = this.list.concat(data); //追加新数据
                if(data.length < this.page_size) {
                  this.scrollLoading = true
                }
              }else{
                // this.scrollLoading = true
              }
              if (this.$refs.loadingCover) this.$refs.loadingCover.hide();
            }
          }
        })
      },
      errorFun(dataList,index) {
        dataList[index] && (dataList[index].goods_image = this.$util.getDefaultImage().default_goods_img);
        this.$forceUpdate();
      },
      comfirmFun() {
        let arr = []
        this.list.forEach(v => {
          if(v.status) {
            arr.push({
              goods_id: v.goods_id,
              goods_image: v.goods_image,
              goods_name: v.goods_name,
              price:v.price
            })
          }
        })
        uni.setStorageSync('selectGood', JSON.stringify(arr))
        uni.setStorageSync('selectGoodType', this.select_type)
        uni.navigateBack({
          delta: 1
        });
      },
      /**
       *分享参数组装
       */
      getSharePageParams() {
        return this.$util.unifySharePageParams('/otherpages/shop/home/<USER>', this.shareTitle || '先迈商城',
            '',{}, this.$util.img(this.shareImg) || this.$util.img('public/static/youpin/home_share.jpg'))
      }
    },
    /**
     * 自定义分享内容
     * @param {Object} res
     */
    onShareAppMessage(res) {
      let { title, link, imageUrl, query } = this.getSharePageParams()
      return this.$buriedPoint.pageShare(link , imageUrl, title);
    },
  }
</script>

<style>
page{
    background:#FFFFFF;
}
</style>
<style lang="scss" scoped>
.seeding-good{
  padding: 0 24rpx 0 30rpx;
  background-color: #fff;
  .select-box{
    position: fixed;
    top: 0;
    left: 30rpx;
    right: 0;
    display: flex;
    align-items: center;
    height: 104rpx;
    background-color: #fff;
    border-bottom: 1px solid #EEEEEE;
    width: 100%;
    z-index: 111;
    div{
      display: flex;
      align-items: center;
      font-size: 28rpx;
      color: #999;
      height: 104rpx;
      &:last-child{
        margin-left: 48rpx;
      }
    }
    .active{
      position: relative;
      font-size: 32rpx;
      color: #333;
      &::after{
        content: '';
        position: absolute;
        bottom: 12rpx;
        left: 50%;
        transform: translateX(-50%);
        height: 6rpx;
        width: 32rpx;
        border-radius: 4rpx;
        background-color: var(--custom-brand-color);
      }
    }
  }
  .content{
    padding: 104rpx 0 100rpx;
    .good-box{
      margin-top: 24rpx;
      .good-list{
        position: relative;
        display: flex;
        margin-bottom: 30rpx;
        .img{
          width: 120rpx;
          height: 120rpx;
          border-radius: 8rpx;
        }
        .good-info{
          position: relative;
          margin-left: 12rpx;
          .title{
            width: 438rpx;
            font-size: 28rpx;
            line-height: 38rpx;
            color: #333;
            word-break: break-all;
            text-overflow: ellipsis;
            overflow: hidden;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
          }
          .price{
            position: absolute;
            bottom: 4rpx;
            left: 0;
            font-size: 30rpx;
            line-height: 38rpx;
            color: var(--custom-brand-color);
          }
        }
        .selectImage{
          position: absolute;
          right: 0;
          top: 50%;
          transform: translateY(-50%);
          width: 48rpx;
          height: 48rpx;
        }
      }
    }
  }
  .footer{
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    height: 100rpx;
    display: flex;
    align-items: center;
    padding: 0 30rpx;
    background-color: #fff;
    z-index: 111;
    .text{
      font-size: 28rpx;
      color: #999999;
    }
    .btn{
      position: absolute;
      right: 30rpx;
      bottom: 6rpx;
      width: 240rpx;
      height: 88rpx;
      background-color: var(--custom-brand-color);
      display: flex;
      justify-content: center;
      align-items: center;
      font-size: 32rpx;
      color: #fff;
      border-radius: 50rpx;
    }
  }
  .empty-list-text{
    color: #666;
  }
}
</style>
