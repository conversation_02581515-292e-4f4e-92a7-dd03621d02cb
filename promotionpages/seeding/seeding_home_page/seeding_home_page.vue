<template>
  <view class="main" :style="[themeColorVar]">
    <view class="main-head">
      <image :src="userInfo.headimg" class="main-head-bg" mode="widthFix" @error="headimgError(userInfo)"></image>
      <view class="main-head-clo"></view>
      <view class="custom" :style="{paddingTop:statusBarHeight+'px',height:navHeight+'px'}">
        <view class="iconfont iconback_light" @click="navigateBack"></view>
        <view class="custom-navbar">
          <view class="navbar-item">个人主页</view>
        </view>
      </view>
      <view class="main-head-info">
        <view class="main-head-info-row">
          <image :src="userInfo.headimg" class="main-head-info-image" @click="changeAvatar"></image>
          <view class="main-head-info-right">
            <view class="main-head-info-right-name" :style="{'color' : is_dark ? '#fff' : ''}">{{userInfo.nickname}}</view>
            <view class="main-head-info-right-content">
              <text class="main-head-info-right-content-one" :style="{'color' : is_dark ? '#fff' : ''}">获赞<text class="main-head-info-right-content-one-num" :style="{'color' : is_dark ? '#fff' : ''}">{{userInfo.total_like_num}}</text></text>
              <text class="main-head-info-right-content-one" :style="{'color' : is_dark ? '#fff' : ''}">发现<text class="main-head-info-right-content-one-num" :style="{'color' : is_dark ? '#fff' : ''}">{{userInfo.experience_num}}</text></text>
            </view>
          </view>
        </view>
        <text class="main-head-info-pub" @click="findFun" v-if="!other_mid">发布</text>
      </view>
    </view>
    <view class="main-tab">
      <text class="main-tab-text" :class="{'main-tab-text-active':type=='pub'}" @click="changeTab('pub')">发现</text>
      <text class="main-tab-text" :class="{'main-tab-text-active':type=='like'}" @click="changeTab('like')">喜欢</text>
    </view>
    <scroll-view scroll-y="true" class="main-scroll" @scrolltolower="scrolltolower" @refresherrefresh="onRefresh"
                 @refresherpulling="onPulling"
                 :refresher-enabled="true"
                 :refresher-triggered="triggered">
      <view class="main-list" v-if="list.length>0">
        <view class="main-list-item" v-for="(item,index) in list" :key="index" @longtap="longtap(item)">
          <view class="main-list-item-image" @click="toSeeding(item)">
            <image :src="item.image" class="main-list-item-image-img" mode="widthFix">
              <view class="main-list-item-image-status" v-if="item.status == 0 || item.status == 2">{{statusText[item.status]}}</view>
            </image>
            <view class="main-list-item-image-footer">
              <uni-icons type="image" size="14" class="main-list-item-image-footer-icon" color="#fff" v-if="item.content_type==3"></uni-icons>
<!--              <image :src="$util.img('public/static/youpin/video-play-icon.png')" class="main-list-item-image-footer-play" v-if="item.content_type==4"></image>-->
              <text class="main-list-item-image-footer-play iconfont iconarrow-" v-if="item.content_type==4"></text>
              <text class="main-list-item-image-footer-num" v-if="item.content_type == 3">{{(item.share_resource && item.share_resource.length) || 0}}</text>
              <text class="main-list-item-image-footer-num" v-if="item.content_type == 4">{{item.play_time}}</text>
            </view>
          </view>
          <view class="main-list-item-text">{{item.title}}</view>
          <view class="main-list-item-info">
            <image :src="item.headimg" class="main-list-item-info-head" @click="seedHome(event,`${item.member_id}|${item.nickname}`)"></image>
            <text class="main-list-item-info-name">{{ item.nickname }}</text>
            <view class="main-list-item-info-like" @click="likeFun(item.id)">
<!--              <text class="iconfont" :class="item.is_like ?　'iconlikefill':'iconlike' "></text>-->
<!--              <image :src="item.is_like ? $util.img('public/static/youpin/seed-icon.png') : $util.img('public/static/youpin/seed-no.png')" class="main-list-item-info-like-img"></image>-->
              <uni-icons :type="item.is_like ? 'heart-filled':'heart'" size="18" :color="item.is_like ? 'var(--custom-brand-color)' :''"/>
              <text>{{item.like_num ||　'点赞'}}</text>
            </view>
          </view>
        </view>
      </view>
      <view class="main-empty" v-if="isFinish && list.length<1">
        <image :src="$util.img('public/static/youpin/empty_data.png')" class="main-empty-img"></image>
        <text class="main-empty-text">主人，您还没分享过种草好物哦~</text>
      </view>
    </scroll-view>
<!--    <view class="footer-box" @click="findFun">+我也要发</view>-->
    <loading-cover ref="loadingCover"></loading-cover>
  </view>
</template>

<script>
import apiurls from "../../../common/js/apiurls";
import system from "../../../common/js/system";
import golbalConfig from "../../../common/mixins/golbalConfig";
import UniIcons from "../../../components/uni-icons/uni-icons.vue";

export default {
  name: "seeding_home_page",
  components: {UniIcons},
  mixins:[golbalConfig],
  data(){
    return{
      statusBarHeight: 0,
      navHeight: 0,
      other_mid:"",
      userInfo:{},
      type:"like",  //pub-发布；like-喜欢/点赞
      page_size:10,
      page:1,
      page_count:1,
      isFinish:false,
      list:[],
      member_id:null,
      isFirstLoad:true,
      from: '',
      statusText: ['已拒绝', '', '待审核'],
      refresh: 0,
      is_dark: false,
      triggered: false
    }
  },
  async onLoad(data){
    //导航高度
    // #ifdef MP-WEIXIN
    this.navHeight = uni.getSystemInfoSync().statusBarHeight + 46 - uni.getSystemInfoSync()['statusBarHeight'];
    this.statusBarHeight = uni.getSystemInfoSync()['statusBarHeight']
    // #endif
    // #ifdef H5
    this.navHeight = uni.getSystemInfoSync().statusBarHeight + 46 - uni.getSystemInfoSync()['statusBarHeight'];
    // #endif
    if(data && data.other_mid){
      this.other_mid=data.other_mid;
    }
    if(data && data.from){
      this.from = data.from
    }
  },
  async onShow(){
    this.member_id=uni.getStorageSync('member_id');
    if(this.refresh) {
      await this.changeTab('like')
      this.refresh = 0
    }
    if (this.$refs.loadingCover) this.$refs.loadingCover.hide();
  },
  async onReady(){
    await system.wait_staticLogin_success();
    await this.getData();
    await this.changeTab('like');
    if (this.$refs.loadingCover) this.$refs.loadingCover.hide();
  },
  methods:{
    navigateBack() {
      uni.navigateBack({
        delta: 1
      })
    },
    async getData(){
      let res = await this.$api.sendRequest({
        url: apiurls.personInfoUrl,
        async: false,
        data: {
          other_mid:this.other_mid
        }
      })
      if (res.code == 0) {
        this.userInfo=res.data;
        await this.getDominantColor(this.$util.img(res.data.headimg));
      }
    },
    async getDiscover(){
      if(this.page>this.page_count){
        return;
      }
      if(!this.isFirstLoad){
        uni.showLoading({
          title: '加载中',
          mask:true
        });
      }else{
        this.isFirstLoad=false;
      }

      let res = await this.$api.sendRequest({
        url: apiurls.discoverListUrl,
        async: false,
        data: {
          other_mid:this.other_mid,
          page:this.page,
          type:this.type,
          page_size:this.page_size
        }
      })
      if (res.code == 0) {
        if(res.data && Array.isArray(res.data.list)){
          this.page_count=res.data.page_count;
          res.data.list = res.data.list.map(item=>{
            if(item.share_resource){
              item.share_resource = item.share_resource.split(',').filter(img=>img)
            }
            if(item.video_info && Object.keys(item.video_info).length > 0){
              let video_metadata = item.video_info
              if(video_metadata.videoWidth >= video_metadata.videoHeight){
                item.is_vertical= false
              }else{
                item.is_vertical= true
              }
              let minute = parseInt(video_metadata.duration/60).toString();
              if(minute.length<2){
                minute = '0'+minute
              }
              let second = parseInt(video_metadata.duration%60).toString();
              if (second.length<2){
                second = '0'+second
              }
              item.play_time = `${minute}:${second}`
            }
            return item;
          })
          this.list=this.list.concat(res.data.list);
          this.page+=1;
        }
        if(this.page>=this.page_count){
          this.isFinish=true;
        }
      }
      uni.hideLoading();
    },
    resetData(){
      this.page=1;
      this.page_count=1;
      this.list=[];
      this.isFinish=false;
    },
    async changeTab(type){
      this.resetData();
      this.type=type;
      await this.getDiscover();
    },
    async scrolltolower(){
      await this.getDiscover();
    },
    async onRefresh(){
      setTimeout(async ()=>{
        this.resetData();
        await this.getDiscover();
        this.triggered = false
      },500)
    },
    onPulling(){
      this.triggered = true
    },
    async removeItem(item){
      let res = await this.$api.sendRequest({
        url: apiurls.delLaunchUrl,
        async: false,
        data: {
          id:item.id
        }
      })
      if (res.code == 0) {
        this.$util.showToast({
          title:'删除成功',
          mask:true
        })
        this.list=this.list.filter((tmp)=>{
          return tmp.id!=item.id
        })
      }else{
        this.$util.showToast({
          title:res.message,
          mask:true
        })
      }
    },
    longtap(item){
      if(this.member_id!=item.member_id){
        return;
      }
      if(this.type!='pub'){
        return;
      }
      uni.showModal({
        title: '你的作品将被永久删除，无法找回。确认删除？',
        content:item.title,
        confirmText:'确认删除',
        success:  async (res) =>{
          if (res.confirm) {
            await this.removeItem(item);
          } else if (res.cancel) {
          }
        }
      })
    },
    toSeeding(item){
      if(item.status == 0 || item.status == 2) {
        if(item.status == 2){
          // uni.showModal({
          //   title:'你的作品正在审核中，请稍作等待。',
          //   content:item.title,
          //   showCancel: false,
          //   confirmText: '知道了'
          // })
          this.$util.redirectTo('/promotionpages/seeding/seeding-add/seeding-add',{id:item.id});
          return false
        }else{
          uni.showModal({
            title: '你的作品内容违反平台规则，已被审核拒绝，确认删除？',
            content:item.title,
            confirmText:'确认删除',
            success:  async (res) =>{
              if (res.confirm) {
                await this.removeItem(item);
              } else if (res.cancel) {
              }
            }
          })
          return false
        }
      }
      if(item.content_type == 2) {
        this.$util.diyRedirectTo({
          wap_url: item.content_link
        })
      }else{
        this.$util.redirectTo('/promotionpages/seeding/seeding_detail/seeding_detail',{id:item.id});
      }
    },
    findFun(){
      if(!this.userInfo.can_pub){
        this.$util.showToast({
          title:'您还没有下单记录，无法发布动态内容'
        })
        return false
      }
      this.$util.redirectTo(`/promotionpages/seeding/seeding-add/seeding-add`);
    },
    /**
     *分享参数组装
     */
    getSharePageParams() {
      return this.$util.unifySharePageParams('/promotionpages/seeding/seeding_home_page/seeding_home_page', this.shareTitle || '先迈商城',
          '',{}, this.$util.img(this.shareImg) || this.$util.img('public/static/youpin/home_share.jpg'))
    },
    redirectToLink(url) {
      if (!uni.getStorageSync('token')) {
        // this.$refs.login.open(url);
        this.$util.toShowLoginPopup(this,null,'/promotionpages/seeding/seeding_home_page/seeding_home_page');
      } else {
        this.$util.redirectTo(url);
      }
    },
    changeAvatar(){
      if(!this.other_mid){
        this.redirectToLink('/otherpages/member/update_headimg_nickname/update_headimg_nickname')
      }
    },
    extractRGBValues(rgbString) {
      // 使用正则表达式匹配rgb()中的数字
      const result = rgbString.match(/\d+/g);

      // 将结果转换为数字数组
      return result ? result.map(Number) : [];
    },
    async getDominantColor(imagePath){
      let res = await this.$api.sendRequest({
        url:apiurls.getDominantColorUrl,
        data:{
          image_url:imagePath,
        },
        async: false,
      })
      if(res.code == 0){
        let color_list = this.extractRGBValues(res.data.dominantColor)
        // g越小，颜色越深，可以按照你自己的需要定一个阀值。 我的是 g < 100
        let g = color_list[0]*0.299 + color_list[1]*0.587 + color_list[2]*0.114;
        this.is_dark = g > 50;
      }
    },
    headimgError(dataList){
      dataList && (dataList['headimg'] = this.$util.getDefaultImage().default_headimg);
      this.$forceUpdate();
    },
    seedHome(event,id) {
      if (!uni.getStorageSync('token')) {
        this.$util.toShowLoginPopup(this,null,`/promotionpages/seeding/seeding-list/seeding-list`);
        return false
      }
      let is_self = false;  //是否是登录人的主页
      if(id) {
        if(this.userInfo.member_id && this.userInfo.member_id == id){
          is_self = true
        }
      }else{
        is_self = true;
      }
      if(!is_self){
        this.$util.redirectTo(`/promotionpages/seeding/seeding_home_page/seeding_home_page`,{other_mid:id});
      }else{
        this.$util.redirectTo(`/promotionpages/seeding/seeding_home_page/seeding_home_page`);
      }
    },
    async likeFun(id) {
      if (!uni.getStorageSync('token')) {
        this.$util.toShowLoginPopup(this,null,`/promotionpages/seeding/seeding-list/seeding-list`);
        return false
      }
      let res = await this.$api.sendRequest({
        url: this.$apiUrl.usershareexperienceLike,
        async: false,
        data: {
          id: id
        },
      });
      if(res.code == 0) {
        this.list.map(v => {
          if(id == v.id) {
            v.is_like = v.is_like ? 0:1
            v.like_num = res.data.like_num
          }
        })
      }else{
        this.$util.showToast({
          title: res.message
        })
      }
    },
  },
  /**
   * 自定义分享内容
   * @param {Object} res
   */
  onShareAppMessage(res) {
    let { title, link, imageUrl, query } = this.getSharePageParams()
    return this.$buriedPoint.pageShare(link , imageUrl, title);
  },
}
</script>

<style scoped lang="scss">
/* 标题栏 */
.custom {
  background: transparent;
  display: flex;
  align-items: center;
  justify-content: center;
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  z-index: 1;
  .iconfont {
    font-size: $ns-font-size-base + 12;
    color: white;
    font-weight: bold;
    position: absolute;
    left: 20rpx;
  }

  .custom-navbar {
    display: flex;
    // border-radius: 30rpx;
    // background: #FFF4F4;
    width: 360rpx;
    align-items: center;

    .navbar-item {
      height: 60rpx;
      line-height: 60rpx;
      width: 100%;
      text-align: center;
      color: white;
      font-size: $ns-font-size-base + 2;
      // &.active{
      // 	background:$base-color;
      // 	color: #FFFFFF;
      // 	border-radius: 30rpx;
      // }
    }
  }
}

.main{
  &-head{
    width: 750rpx;
    height: 360rpx;
    box-sizing: border-box;
    position: relative;
    overflow-y: hidden;
    &-bg{
      width: 100%;
      height: 100%;
      position: absolute;
      left: 0;
      top: 0;
      object-fit: cover;
      filter: blur(5px);
    }
    &-clo{
      width: 100%;
      height: 100%;
      position: absolute;
      left: 0;
      top: 0;
      background: rgba(0, 0, 0, 0.1);
    }
    &-info{
       width: 690rpx;
      box-sizing: border-box;
      display: flex;
      justify-content: space-between;
      align-items: center;
      position: absolute;
      left: 40rpx;
      bottom: 40rpx;
      &-row{
        display: flex;
        align-items: center;
      }
      &-pub{
        width: 180rpx;
        height: 64rpx;
        border-radius: 100rpx;
        background: var(--custom-brand-color);
        display: flex;
        justify-content: center;
        align-items: center;
        font-size: 32rpx;
        font-weight: 400;
        line-height: 32rpx;
        color: rgba(255, 255, 255, 1);
      }
      &-image{
        width: 80rpx;
        height: 80rpx;
        background: rgba(220, 220, 220, 0.39);
        border-radius: 50%;
        border: 2rpx solid rgba(255, 255, 255, 1);
      }
      &-right{
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        margin-left: 20rpx;
        &-name{
          font-size: 32rpx;
          font-weight: 700;
          line-height: 37.5rpx;
          color: rgba(56, 56, 56, 1);
        }
        &-content{
          display: flex;
          align-items: center;
          margin-top: 12rpx;
          &-one{
            font-size: 26rpx;
            font-weight: 400;
            line-height: 30.48rpx;
            color: rgba(128, 128, 128, 1);
            margin-right: 20rpx;
            &-num{
              margin-left: 10rpx;
              color: rgba(56, 56, 56, 1);
            }
          }
        }
      }
    }
  }
  &-tab{
    width: 100%;
    height: 104rpx;
    margin: 0 auto;
    border-bottom: 1px solid #EEEEEE;
    box-sizing: border-box;
    display: flex;
    align-items: center;
    &-text{
      font-size: 32rpx;
      font-weight: 400;
      line-height: 44rpx;
      color: rgba(56, 56, 56, 1);
      margin-left: 106rpx;
      &:not(:first-child){
        margin-left: 126rpx;
      }
      &-active{
        font-weight: 700;
        position: relative;
        &:after{
          content: " ";
          width: 20rpx;
          height: 6rpx;
          background: var(--custom-brand-color);
          border-radius: 20rpx;
          display: block;
          position: absolute;
          left: 50%;
          bottom: -12rpx;
          transform: translateX(-50%);
        }
      }
    }
  }
  &-scroll{
    height: calc(100vh - 464rpx);
  }
  &-list{
    padding: 0 24rpx;
    box-sizing: border-box;
    column-count:2;
    padding-top: 20rpx;
    padding-bottom: 20rpx;
    &-item{
      -moz-page-break-inside:avoid;
      -webkit-column-break-inside:avoid;
      break-inside:avoid;
      width: 346rpx;
      border-radius: 20rpx;
      background: rgba(255, 255, 255, 1);
      box-shadow: 0 4rpx 12rpx  rgba(0, 0, 0, 0.05);
      box-sizing: border-box;
      margin-top: 20rpx;
      padding-bottom: 28rpx;
      &:first-child{
        margin-top: 0;
      }
      &-image{
        width: 100%;
        background: rgba(220, 220, 220, 0.39);
        border-radius: 20rpx 20rpx 0 0;
        overflow-y: hidden;
        position: relative;
        &-img{
          width: 100%;
          height: auto;
          display: block;
        }
        &-status{
          display: flex;
          align-items: center;
          justify-content: center;
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          background: rgba(0, 0, 0, 0.45);
          height: 100%;
          color: #fff;
          font-size: 32rpx;
        }
        &-footer{
          position: absolute;
          left: 14rpx;
          bottom: 12rpx;
          height: 32rpx;
          border-radius: 40rpx;
          background: rgba(0, 0, 0, 0.5);
          min-width: 78rpx;
          padding: 0 12rpx;
          box-sizing: border-box;
          display: flex;
          align-items: center;
          &-play{
            font-size: 28rpx;
            color: #fff;
          }
          &-num{
            font-size: 24rpx;
            font-weight: 400;
            line-height: 28.12rpx;
            color: rgba(255, 255, 255, 1);
            margin-left: 10rpx;
          }
        }
      }
      &-play{
        width: 44rpx;
        height: 44rpx;
        position: absolute;
        top: 18rpx;
        right: 18rpx;
      }
      &-text{
        font-size: 28rpx;
        font-weight: 400;
        line-height: 40rpx;
        color: rgba(56, 56, 56, 1);
        margin-top: 8rpx;
        padding: 0 14rpx;
        box-sizing: border-box;
        word-break: break-all;
        overflow: hidden;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
      }
      &-info{
        display: flex;
        align-items: center;
        //justify-content: space-between;
        margin-top: 20rpx;
        padding: 0 14rpx;
        box-sizing: border-box;
        position: relative;
        &-head{
          width: 44rpx;
          height: 44rpx;
          background: rgba(220, 220, 220, 0.39);
          border-radius: 50%;
        }
        &-name{
          font-size: 24rpx;
          font-weight: 400;
          line-height: 28rpx;
          color: rgba(128, 128, 128, 1);
          width: 144rpx;
          margin-left: 8rpx;
        }
        &-like{
          font-size: 26rpx;
          font-weight: 400;
          line-height: 36rpx;
          color: rgba(128, 128, 128, 1);
          display: flex;
          align-items: center;
          position: absolute;
          right: 14rpx;
          top: 50%;
          transform: translateY(-50%);
          &-img{
            width: 28rpx;
            height: 28rpx;
          }
        }
      }
    }
  }
  &-empty{
    display: flex;
    flex-direction: column;
    align-items: center;
    padding-top: 206rpx;
    box-sizing: border-box;
    &-img{
      width: 404rpx;
      height: 283rpx;
    }
    &-text{
      font-size: 32rpx;
      font-weight: 400;
      line-height: 44rpx;
      color: #999999;
      margin-top: 44rpx;
    }
  }
}
.footer-box{
  position: fixed;
  bottom: 100rpx;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  align-items: center;
  justify-content: center;
  width: 360rpx;
  height: 88rpx;
  background-color: var(--custom-brand-color);
  border-radius: 44rpx;
  color: #FFFFFF;
  font-size: 32rpx;
}
</style>
