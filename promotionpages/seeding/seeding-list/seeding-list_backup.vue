<template>
  <view class="content">
      <!-- #ifdef H5 -->
      <uni-nav-bar left-icon="back" :border="false" @clickLeft="appGoBack" style="z-index: 1" :fixed="true" v-if="isOnXianMaiApp">
          <template>
              <view class="page-title">{{title}}</view>
          </template>
      </uni-nav-bar>
      <!-- #endif -->
    <view class="skeletons" v-if="isStartPage">
      <view class="skeletons-one">
        <view class="skeletons-one-img"></view>
        <view class="skeletons-one-info">
          <view class="skeletons-one-info-one"></view>
          <view class="skeletons-one-info-two"></view>
        </view>
      </view>
      <view class="skeletons-one skeletons-two">
        <view class="skeletons-one-img skeletons-two-img"></view>
        <view class="skeletons-one-info skeletons-two-info">
          <view class="skeletons-one-info-one skeletons-two-info-one"></view>
          <view class="skeletons-one-info-two skeletons-two-info-two"></view>
        </view>
      </view>
      <view class="skeletons-one">
        <view class="skeletons-one-img"></view>
        <view class="skeletons-one-info">
          <view class="skeletons-one-info-one"></view>
          <view class="skeletons-one-info-two"></view>
        </view>
      </view>
      <view class="skeletons-one skeletons-two">
        <view class="skeletons-one-img skeletons-two-img"></view>
        <view class="skeletons-one-info skeletons-two-info">
          <view class="skeletons-one-info-one skeletons-two-info-one"></view>
          <view class="skeletons-one-info-two skeletons-two-info-two"></view>
        </view>
      </view>
      <view class="skeletons-one">
        <view class="skeletons-one-img"></view>
        <view class="skeletons-one-info">
          <view class="skeletons-one-info-one"></view>
          <view class="skeletons-one-info-two"></view>
        </view>
      </view>
      <view class="skeletons-one skeletons-two">
        <view class="skeletons-one-img skeletons-two-img"></view>
        <view class="skeletons-one-info skeletons-two-info">
          <view class="skeletons-one-info-one skeletons-two-info-one"></view>
          <view class="skeletons-one-info-two skeletons-two-info-two"></view>
        </view>
      </view>
    </view>
    <block v-if="!isStartPage">
      <view class="list" v-if="list.length">
        <uni-waterfall ref="fallsFlow" :list="list" imageSrcKey="image" @wapper-lick="toSeeding" v-if="list.length">
          <!--  #ifdef  MP-WEIXIN -->
          <!-- 微信小程序自定义内容 -->
          <view v-for="(item, index) of list" :key="index" slot="slot{{index}}">
            <view class="cnt">
              <view class="cnt">
                <view class="text">{{item.title}}</view>
                <view class="info">
                  <image :src="item.headimg" class="head"></image>
                  <text class="name">{{item.nickname}}</text>
                  <view class="link" @click.stop="likeFun(item.id)">
                    <image class="" :src="item.is_like ? $util.img('public/static/youpin/seed-icon.png'):$util.img('public/static/youpin/seed-no.png')" mode="" />
                    <text :style="{color: item.is_like ? '#FF3333':''}">{{item.like_num}}</text>
                  </view>
                </view>
                <image class="video-play" :src="$util.img('public/static/youpin/video-play-icon.png')" mode="" v-if="item.content_type == 4" />
              </view>
            </view>
          </view>
          <!--  #endif -->

          <!-- #ifndef  MP-WEIXIN -->
          <!-- app、h5 自定义内容 -->
          <template v-slot:default="item">
            <view class="cnt">
              <view class="text">{{item.title}}</view>
              <view class="info">
                <image :src="item.headimg" class="head"></image>
                <text class="name">{{item.nickname}}</text>
                <view class="link" @click.stop="likeFun(item.id)">
                  <image class="" :src="item.is_like ? $util.img('public/static/youpin/seed-icon.png'):$util.img('public/static/youpin/seed-no.png')" mode="" />
                  <text :style="{color: item.is_like ? '#FF3333':''}">{{item.like_num}}</text>
                </view>
              </view>
              <image class="video-play" :src="$util.img('public/static/youpin/video-play-icon.png')" mode="" v-if="item.content_type == 4" />
            </view>
          </template>
          <!-- #endif -->
        </uni-waterfall>
      </view>
      <view class="main-empty" v-else>
        <image :src="$util.img('public/static/youpin/empty_data.png')" class="main-empty-img"></image>
        <text class="main-empty-text">暂无买家秀</text>
      </view>
    </block>



    <ns-loading v-if="showLoading"></ns-loading>
<!--    <loading-cover ref="loadingCover"></loading-cover>-->

    <!-- 授权登录弹窗 -->
    <yd-auth-popup ref="ydauth"></yd-auth-popup>
    <ns-login ref="login"></ns-login>
    <uni-coupon-pop ref="couponPop"></uni-coupon-pop>

    <view class="footer" v-if="!goods_id && token">
      <view class="left" @click="seedHome">
        <image :src="memberInfo.headimg" mode="" />
        <view class="info">
          <view>{{memberInfo.nickname}}</view>
          <view>
            {{userInfo.experience_num ? userInfo.experience_num+'个发现':'快来说说你的新发现吧~'}}
            <text class="iconfont iconright"></text>
          </view>
        </view>
      </view>
<!--      <view class="right" @click="findFun">+发现</view>-->
    </view>

<!--    <view class="footer-box" v-if="goods_id && token" @click="findFun">+我也要发</view>-->

    <uni-popup ref="selectPopup" type="bottom" class="select-popup">
      <div class="select-box">
        <view @click="addFun(3)">
          <image :src="$util.img('public/static/youpin/photo-icon.png')" mode="" />
          <view>发图片</view>
        </view>
        <view @click="addFun(4)">
          <image :src="$util.img('public/static/youpin/video-icon.png')" mode="" />
          <view>发视频</view>
        </view>
      </div>
      <image class="close" :src="$util.img('public/static/youpin/seed-close.png')" mode="" @click="closeFun()" />
    </uni-popup>
  </view>
</template>

<script>
import scrollLoading from '@/common/mixins/scroll-loading.js'
import system from "../../../common/js/system";
import appInlineH5 from "../../../common/mixins/appInlineH5";
// #ifdef H5
import {isOnXianMaiApp} from "../../../common/js/h5/appOP";
// #endif
export default {
  mixins: [scrollLoading,appInlineH5],
  data() {
    return {
      list: [],
      goods_id: '',
      memberInfo: {},
      userInfo: {},
      token: null,
      isStartPage:true, //是否启动页面
      show_publish:null,
        // #ifdef H5
        isOnXianMaiApp:isOnXianMaiApp,
        // #endif
        title:''
    }
  },
  async onLoad({goods_id,show_publish}) {
    uni.setNavigationBarTitle({
      title: goods_id ? '买家秀':'发现'
    });
    this.title=goods_id ? '买家秀':'发现'
    if(goods_id) this.goods_id = goods_id
    // if (this.$refs.loadingCover) this.$refs.loadingCover.show();
    await system.wait_staticLogin_success();
    this.isStartPage=true;
    this.list = []
    this.page = 1
    this.token=uni.getStorageSync('token')
    await this.getInit()
    await this.getData();
    this.isStartPage=false;
    this.show_publish = show_publish
    // if(this.show_publish == 1){
    //   this.findFun()
    // }
  },
  onShow() {
    if (uni.getStorageSync('token')) {
			this.getMemberInfo();
    }
    if (uni.getStorageSync('is_register')) {
      this.$util.toShowCouponPopup(this)
      uni.removeStorageSync('is_register');
    }
    if(!this.goods_id) {
      uni.removeStorageSync('selectGood');
    }
    uni.removeStorageSync('selectGoodType');
  },
  methods: {
    // 获取会员基础信息
		async getMemberInfo() {
			let res = await this.$api.sendRequest({
				url: '/api/member/info',
				async: false
			});
			if (res.code >= 0 && res.data) {
				this.token = uni.getStorageSync('token');
				this.memberInfo = res.data;
			}
		},
    async getData(){
      let res = await this.$api.sendRequest({
        url: this.$apiUrl.personInfoUrl,
        async: false
      })
      if (res.code == 0) {
        this.userInfo=res.data;
      }
    },
    // 获取数据
    async getInit() {
      let res = await this.$api.sendRequest({
        url: this.$apiUrl.usershareexperienceListUrl,
        async:false,
        data: {
          page: this.page,
          page_size: this.page_size,
          goods_id: this.goods_id
        },
      });
      let data = res.data.list
      if(data.length) {
        this.page ++;
        this.list = this.list.concat(data); //追加新数据
        if(data.length < this.page_size) {
          this.scrollLoading = true
        }else{
          this.scrollLoading = false
        }
        this.showLoading = false
      }else{
        this.scrollLoading = true
        this.showLoading = false
      }
      if (this.$refs.loadingCover) this.$refs.loadingCover.hide();
    },
    findFun() {
      this.$refs.selectPopup.open()
    },
    closeFun() {
      this.$refs.selectPopup.close()
    },
    addFun(type) {
      this.$util.redirectTo(`/promotionpages/seeding/seeding-add/seeding-add?type=${type}&goods_id=${this.goods_id}`);
      this.$refs.selectPopup.close()
    },
    seedHome() {
      this.$util.redirectTo(`/promotionpages/seeding/seeding_home_page/seeding_home_page`);
    },
    toSeeding(item){
      if(item.content_type == 2) {
        this.$util.diyRedirectTo({
          wap_url: item.content_link
        })
      }else{
        this.$util.redirectTo('/promotionpages/seeding/seeding_detail/seeding_detail',{id:item.id});
      }
    },
    async likeFun(id) {
      if (!uni.getStorageSync('token')) {
        this.$util.toShowLoginPopup(this,null,`/promotionpages/seeding/seeding-list/seeding-list`);
        return false
      }
      let res = await this.$api.sendRequest({
        url: this.$apiUrl.usershareexperienceLike,
        async: false,
        data: {
          id: id
        },
      });
      if(res.code == 0) {
        this.list.map(v => {
          if(id == v.id) {
            v.is_like = v.is_like ? 0:1
            v.like_num = res.data.like_num
          }
        })
      }else{
        this.$util.showToast({
          title: res.message
        })
      }
    },
    /**
     *分享参数组装
     */
    getSharePageParams() {
      return this.$util.unifySharePageParams('/otherpages/shop/home/<USER>', this.shareTitle || '先迈商城',
          '',{}, this.$util.img(this.shareImg) || this.$util.img('public/static/youpin/home_share.jpg'))
    }
  },
  /**
   * 自定义分享内容
   * @param {Object} res
   */
  onShareAppMessage(res) {
    let { title, link, imageUrl, query } = this.getSharePageParams()
    return this.$buriedPoint.pageShare(link , imageUrl, title);
  },
  async onPullDownRefresh() {
    this.isStartPage=true;
    this.list = []
    this.page = 1
    // if (this.$refs.loadingCover) this.$refs.loadingCover.show();
    await this.getInit()
    await this.getData();
    this.isStartPage=false;
    uni.stopPullDownRefresh();
  },
};
</script>

<style lang="scss" scoped>
.content{
  padding-bottom: 120rpx;
  .skeletons{
    width: 100vw;
    columns: 2; // 默认列数
    column-gap: 30rpx; // 列间距
    padding: 0 30rpx;
    box-sizing: border-box;
    padding-top: 40rpx;
    &-one{
      width: 320rpx;
      break-inside: avoid;
      margin-bottom: 40rpx;
      &-img{
        width: 320rpx;
        height: 380rpx;
        border-radius: 20rpx;
        background-color: #F2F2F2;
        //margin-top: 20rpx;
      }
      &-info{
        width: 320rpx;
        &-one{
          width: 100%;
          height: 20rpx;
          border-radius: 20rpx;
          margin-top: 20rpx;
          background-color: #F2F2F2;
        }
        &-two{
          width: 60%;
          height: 20rpx;
          border-radius: 20rpx;
          margin-top: 20rpx;
          background-color: #F2F2F2;
        }
      }
    }
    &-two{
      &-img{
        background-color: #EEEEEE;
        height: 450rpx;
      }
      &-info{
        &-one{
          background-color: #EEEEEE;
        }
        &-two{
          background-color: #EEEEEE;
        }
      }
    }
  }
  .list{
    margin: 24rpx;
  }
  .cnt{
    .text{
      font-size: 28rpx;
			font-weight: 600;
			line-height: 38rpx;
			color: #333333;
			margin-top: 8rpx;
			padding: 0 14rpx;
			box-sizing: border-box;
			word-break: break-all;
			text-overflow: ellipsis;
			display: -webkit-box;
			-webkit-box-orient: vertical;
			-webkit-line-clamp: 2;
			overflow: hidden;
    }
    .info{
      position: relative;
      display: flex;
			align-items: center;
			// justify-content: space-between;
			margin: 20rpx 0;
			padding: 0 14rpx;
			box-sizing: border-box;
      .head{
        width: 44rpx;
				height: 44rpx;
				background: rgba(220, 220, 220, 0.39);
				border-radius: 50%;
      }
      .name{
        font-size: 24rpx;
				font-weight: 400;
				line-height: 28rpx;
				color: #666666;
				width: 144rpx;
				word-break: break-all;
				text-overflow: ellipsis;
				display: -webkit-box;
				-webkit-box-orient: vertical;
				-webkit-line-clamp: 2;
				overflow: hidden;
				margin-left: 8rpx;
      }
      .link{
        display: flex;
				align-items: center;
        position: absolute;
        right: 14rpx;
				font-size: 26rpx;
				font-weight: 400;
				line-height: 36rpx;
				color: #999999;
        image{
          width: 28rpx;
					height: 28rpx;
					margin-right: 2rpx;
        }
        text{}
      }
    }
    .video-play{
      width: 44rpx;
      height: 44rpx;
      position: absolute;
      top: 18rpx;
      right: 18rpx;
    }
  }
  .footer-box{
    position: fixed;
    bottom: 20rpx;
    left: 50%;
    transform: translateX(-50%);
    display: flex;
    align-items: center;
    justify-content: center;
    width: 360rpx;
    height: 88rpx;
    background-color: #FF3333;
    border-radius: 44rpx;
    color: #FFFFFF;
    font-size: 32rpx;
  }
  .footer{
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    height: 98rpx;
    background-color: #FFFFFF;
    // border-radius: 44rpx;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 40rpx 0 34rpx;
    .left{
      display: flex;
      align-items: center;
      image{
        width: 80rpx;
        height: 80rpx;
        border-radius: 50%;
      }
      .info{
        margin-left: 16rpx;
        &>view:first-child{
          font-size: 28rpx;
          color: #333333;
          line-height: 42rpx;
          margin-top: 8rpx;
        }
        &>view:last-child{
          font-size: 22rpx;
          color: #666666;
          line-height: 36rpx;
          text{
            font-size: 18rpx;
          }
        }
      }
    }
    .right{
      display: flex;
      align-items: center;
      justify-content: center;
      width: 240rpx;
      height: 88rpx;
      background-color: #FF3333;
      border-radius: 44rpx;
      color: #FFFFFF;
      font-size: 32rpx;
    }
  }
  /deep/ .uni-popup__wrapper{
    border-radius: 20rpx 20rpx 0 0;
  }
  .select-box{
    position: relative;
    height: 580rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    &>view:first-child{
      flex: 1;
      display: flex;
      flex-direction: column;
      margin-left: 110rpx;
      image{
        width: 200rpx;
        height: 200rpx;
      }
      view{
        width: 200rpx;
        text-align: center;
      }
    }
    &>view:last-child{
      flex: 1;
      display: flex;
      flex-direction: column;
      image{
        width: 200rpx;
        height: 200rpx;
      }
      view{
        width: 200rpx;
        text-align: center;
      }
    }
  }
  .close{
    position: absolute;
    width: 32rpx;
    height: 32rpx;
    right: 36rpx;
    top: 28rpx;
  }
  .main-empty{
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #fff;
    display: flex;
    flex-direction: column;
    align-items: center;
    padding-top: 206rpx;
    box-sizing: border-box;
    &-img{
      width: 404rpx;
      height: 283rpx;
    }
    &-text{
      font-size: 32rpx;
      font-weight: 400;
      line-height: 44rpx;
      color: #999999;
      margin-top: 44rpx;
    }
  }
}
.page-title{
  width: 360rpx;
  overflow:hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  -o-text-overflow:ellipsis;
  text-align: center;
}
</style>
