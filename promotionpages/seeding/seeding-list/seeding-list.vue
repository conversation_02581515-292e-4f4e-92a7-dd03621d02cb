<template>
  <view class="content" :class="{'content-has': openBottomNav}" :style="[themeColorVar]">
	<!-- #ifdef MP-WEIXIN -->
	<uni-nav-bar :left-icon="openBottomNav ? '' : 'back'" :border="false" :status-bar="true" @clickLeft="$util.goBack()" style="z-index: 1" :fixed="true">
	  <template>
	    <view class="page-title">{{title}}</view>
	  </template>
	</uni-nav-bar>
	<!-- #endif -->

      <!-- #ifdef H5 -->
      <uni-nav-bar left-icon="back" :border="false" @clickLeft="appGoBack" style="z-index: 1" :fixed="true" v-if="isOnXianMaiApp">
          <template>
              <view class="page-title">{{title}}</view>
          </template>
      </uni-nav-bar>
      <!-- #endif -->
    <view class="header" :style="{top : $util.getPlatform()=='h5'&& isOnXianMaiApp ? '88rpx':  $util.getPlatform() == 'weapp' ? navBarHeight+'px' : '0'}">
      <view class="user-info">
        <view class="user-info-left" @click="seedHome">
          <image :src="userInfo.headimg" mode="" class="user-info-left-avatar" @error="headimgError(userInfo)"/>
          <view class="user-info-left-base">
            <view class="user-info-left-base-name">{{userInfo.nickname || '未登录'}}</view>
            <view class="user-info-left-base-data">
              <text class="user-info-left-base-data-one">发现 {{userInfo.experience_num || 0}}</text>
              <text class="user-info-left-base-data-one">喜欢 {{userInfo.total_like_num || 0}}</text>
<!--              <text class="user-info-left-base-data-one">收藏 67</text>-->
            </view>
          </view>
        </view>
        <view class="user-info-right">
          <text class="user-info-right-op" @click="findFun">发布</text>
        </view>
      </view>
    </view>
    <view class="content-info" :style="{marginTop : $util.getPlatform()=='h5'&& isOnXianMaiApp ? '210rpx': '122rpx'}">
      <view class="list">
        <view class="list-one" v-for="(item,index) in list" :key="index">
          <view class="list-one-user" @click="seedHome($event,`${item.member_id}|${item.member_id}`)">
            <image :src="item.headimg" class="list-one-user-avatar"></image>
            <view class="list-one-user-base">
              <view class="list-one-user-base-name">{{item.nickname}}</view>
              <view class="list-one-user-base-time">{{item.create_time}}</view>
            </view>
          </view>
          <view @click="toSeeding(item)">
            <view class="list-one-desc" v-if="item.content || item.title">{{item.content || item.title}}</view>
            <view class="list-one-img" v-if="item.content_type == 2 && item.image"
                  :class="{'list-one-img-one': true}">
              <view class="list-one-img-box">
                <image :src="$util.img(item.image)" mode="widthFix" class="image"></image>
              </view>
            </view>
            <view class="list-one-img" v-if="item.content_type == 3 && item.share_resource.length > 0"
                  :class="{'list-one-img-one': item.share_resource.length == 1,
                 'list-one-img-two': item.share_resource.length == 2,
                 'list-one-img-three': item.share_resource.length >= 3}">
              <view v-for="(img,j) in item.share_resource.slice(0,3)" :key="j" class="list-one-img-box">
                <image :src="$util.img(img)" :mode="item.share_resource.length > 1 ? 'aspectFill' : 'widthFix'" class="image"></image>
                <text class="list-one-img-box-tip" v-if="j==2">+{{item.share_resource.length - 3}}</text>
              </view>
            </view>
            <view class="list-one-video" v-if="item.content_type == 4 && item.share_resource.length > 0">
              <view class="list-one-video-info">
                <image :src="$util.img(item.image)" class="list-one-video-info-one" :class="{'list-one-video-info-one-horizontal':!item.is_vertical}" mode="widthFix"></image>
                <text class="list-one-video-info-tip">{{item.play_time}}</text>
                <image class="list-one-video-info-play" :src="$util.img('public/static/youpin/play.png')"></image>
              </view>
            </view>
          </view>
          <view class="list-one-op">
<!--            <view class="list-one-op-one"><uni-icons type="star" size="18"/><text class="list-one-op-one-text">收藏</text></view>-->
            <view class="list-one-op-one" @click="likeFun(item.id)"><uni-icons :type="item.is_like ? 'heart-filled':'heart'" size="18" :color="item.is_like ? 'var(--custom-brand-color)' :''"/><text class="list-one-op-one-text">{{item.like_num || '喜欢'}}</text></view>
            <!-- #ifdef MP-WEIXIN -->
				    <button class="list-one-op-one" open-type="share" data-share-type="detail" :data-share-index="index"><uni-icons type="redo" size="18"/><text class="list-one-op-one-text">分享</text></button>
            <!-- #endif -->
            <!-- #ifdef H5 -->
            <button class="list-one-op-one" @click="openSharePopup"><uni-icons type="redo" size="18"/><text class="list-one-op-one-text">分享</text></button>
            <!-- #endif -->
          </view>
        </view>
      </view>
    </view>

    <uni-popup ref="selectPopup" type="bottom" class="select-popup">
      <div class="select-box">
        <view @click="addFun(3)">
          <image :src="$util.img('public/static/youpin/photo-icon.png')" mode="" />
          <view>发图片</view>
        </view>
        <view @click="addFun(4)">
          <image :src="$util.img('public/static/youpin/video-icon.png')" mode="" />
          <view>发视频</view>
        </view>
      </div>
      <image class="close" :src="$util.img('public/static/youpin/seed-close.png')" mode="" @click="closeFun()" />
    </uni-popup>
    <diy-bottom-nav type="shop" :site-id="shop_id" v-if="openBottomNav"></diy-bottom-nav>
    <ns-loading v-if="showLoading"></ns-loading>
    <loading-cover ref="loadingCover"></loading-cover>
    <yd-auth-popup ref="ydauth"></yd-auth-popup>
    <ns-login ref="login"></ns-login>
    <uni-coupon-pop ref="couponPop"></uni-coupon-pop>
    <diy-share-navigate-h5 ref="shareNavigateH5"></diy-share-navigate-h5>
    <!-- 返回顶部 -->
    <to-top v-if="showTop" @toTop="scrollToTopNative()"></to-top>
  </view>
</template>

<script>
import scrollLoading from '@/common/mixins/scroll-loading.js'
import system from "../../../common/js/system";
import appInlineH5 from "../../../common/mixins/appInlineH5";
import golbalConfig from "../../../common/mixins/golbalConfig";
import UniIcons from "../../../components/uni-icons/uni-icons.vue";
import diyShareNavigateH5 from '@/components/diy-share-navigate-h5/diy-share-navigate-h5'
import toTop from '@/components/toTop/toTop.vue'
import scroll from '@/common/mixins/scroll-view.js';
// #ifdef H5
import {isOnXianMaiApp} from "../../../common/js/h5/appOP";
// #endif
export default {
  components: {UniIcons,diyShareNavigateH5,toTop},
  mixins: [scrollLoading,appInlineH5,golbalConfig,scroll],
  data() {
    return {
      list: [],
      goods_id: '',
      memberInfo: {},
      userInfo: {
        headimg: this.$util.img("/upload/default/default_img/head.png"),
        nickname: "请登录",
      },
      token: null,
      isStartPage:true, //是否启动页面
      show_publish:null,
        // #ifdef H5
        isOnXianMaiApp:isOnXianMaiApp,
        // #endif
        title:'',
      shop_id: null,
      openBottomNav: true,
      navBarHeight: 44,
    }
  },
  async onLoad({goods_id,show_publish,is_not_show_nav}) {
    uni.setNavigationBarTitle({
      title: goods_id ? '买家秀':'发现'
    });
    this.title=goods_id ? '买家秀':'发现'
    if(goods_id) this.goods_id = goods_id
    if(is_not_show_nav) this.openBottomNav = !is_not_show_nav;
    if (this.$refs.loadingCover) this.$refs.loadingCover.show();
	// #ifdef MP-WEIXIN
		this.navBarHeight += uni.getSystemInfoSync().statusBarHeight;
	// #endif
    await system.wait_staticLogin_success();
    let shop_id = uni.getStorageSync('shop_id');
    this.shop_id = shop_id;
    this.isStartPage=true;
    this.list = []
    this.page = 1
    this.token=uni.getStorageSync('token')
    await this.getData();
    await this.getInit()
    this.isStartPage=false;
    this.show_publish = show_publish
    if(this.show_publish == 1){
      this.findFun()
    }
  },
  onShow() {
    if (uni.getStorageSync('token')) {
			this.getMemberInfo();
    }
    if (uni.getStorageSync('is_register')) {
      this.$util.toShowCouponPopup(this)
      uni.removeStorageSync('is_register');
    }
    if(!this.goods_id) {
      uni.removeStorageSync('selectGood');
    }
    uni.removeStorageSync('selectGoodType');
  },
  methods: {
    // 获取会员基础信息
		async getMemberInfo() {
			let res = await this.$api.sendRequest({
				url: '/api/member/info',
				async: false
			});
			if (res.code >= 0 && res.data) {
				this.token = uni.getStorageSync('token');
				this.memberInfo = res.data;
			}
		},
    async getData(){
      let res = await this.$api.sendRequest({
        url: this.$apiUrl.personInfoUrl,
        async: false
      })
      if (res.code == 0) {
        this.userInfo=res.data;
        // #ifdef H5
        this.setWechatShare();
        // #endif
      }
    },
    // 获取数据
    async getInit() {
      let res = await this.$api.sendRequest({
        url: this.$apiUrl.usershareexperienceListUrl,
        async:false,
        data: {
          page: this.page,
          page_size: this.page_size,
          goods_id: this.goods_id
        },
      });
      let data = res.data.list.map(item=>{
        if(item.share_resource){
          item.share_resource = item.share_resource.split(',').filter(img=>img)
        }
        if(item.video_info && Object.keys(item.video_info).length > 0){
          let video_metadata = item.video_info
          if(video_metadata.videoWidth >= video_metadata.videoHeight){
            item.is_vertical= false
          }else{
            item.is_vertical= true
          }
          let minute = parseInt(video_metadata.duration/60).toString();
          if(minute.length<2){
            minute = '0'+minute
          }
          let second = parseInt(video_metadata.duration%60).toString();
          if (second.length<2){
            second = '0'+second
          }
          item.play_time = `${minute}:${second}`
        }
        return item;
      })
      if(data.length) {
        this.page ++;
        this.list = this.list.concat(data); //追加新数据
        if(data.length < this.page_size) {
          this.scrollLoading = true
        }else{
          this.scrollLoading = false
        }
        this.showLoading = false
      }else{
        this.scrollLoading = true
        this.showLoading = false
      }
      if (this.$refs.loadingCover) this.$refs.loadingCover.hide();
    },
    findFun() {
      if (!uni.getStorageSync('token')) {
        this.$util.toShowLoginPopup(this,null,`/promotionpages/seeding/seeding-list/seeding-list`);
        return false
      }
      if(!this.userInfo.can_pub){
        this.$util.showToast({
          title:'您还没有下单记录，无法发布动态内容'
        })
        return false
      }
      // this.$refs.selectPopup.open()
      this.$util.redirectTo(`/promotionpages/seeding/seeding-add/seeding-add`);
    },
    closeFun() {
      this.$refs.selectPopup.close()
    },
    addFun(type) {
      if (!uni.getStorageSync('token')) {
        this.$util.toShowLoginPopup(this,null,`/promotionpages/seeding/seeding-list/seeding-list`);
        return false
      }
      this.$util.redirectTo(`/promotionpages/seeding/seeding-add/seeding-add?type=${type}&goods_id=${this.goods_id}`);
      this.$refs.selectPopup.close()
    },
    seedHome(event,id) {
      if (!uni.getStorageSync('token')) {
        this.$util.toShowLoginPopup(this,null,`/promotionpages/seeding/seeding-list/seeding-list`);
        return false
      }
      let is_self = false;  //是否是登录人的主页
      if(id) {
        if(this.userInfo.member_id && this.userInfo.member_id == id){
          is_self = true
        }
      }else{
        is_self = true;
      }
      if(!is_self){
        this.$util.redirectTo(`/promotionpages/seeding/seeding_home_page/seeding_home_page`,{other_mid:id});
      }else{
        this.$util.redirectTo(`/promotionpages/seeding/seeding_home_page/seeding_home_page`);
      }
    },
    toSeeding(item){
      if(item.content_type == 2) {
        this.$util.diyRedirectTo({
          wap_url: item.content_link
        })
      }else{
        this.$util.redirectTo('/promotionpages/seeding/seeding_detail/seeding_detail',{id:item.id});
      }
    },
    async likeFun(id) {
      if (!uni.getStorageSync('token')) {
        this.$util.toShowLoginPopup(this,null,`/promotionpages/seeding/seeding-list/seeding-list`);
        return false
      }
      let res = await this.$api.sendRequest({
        url: this.$apiUrl.usershareexperienceLike,
        async: false,
        data: {
          id: id
        },
      });
      if(res.code == 0) {
        this.list.map(v => {
          if(id == v.id) {
            v.is_like = v.is_like ? 0:1
            v.like_num = res.data.like_num
          }
        })
      }else{
        this.$util.showToast({
          title: res.message
        })
      }
    },
    /**
     *分享参数组装
     */
    getSharePageParams() {
      return this.$util.unifySharePageParams('/promotionpages/seeding/seeding-list/seeding-list', this.shareTitle || '先迈商城',
          '',{}, this.$util.img(this.shareImg) || this.$util.img('public/static/youpin/home_share.jpg'))
    },
    // 打开分享弹出层
    openSharePopup() {
      // #ifdef H5
      let share_data=this.getSharePageParams();
      if(this.$refs.shareNavigateH5) this.$refs.shareNavigateH5.open(share_data);
      // #endif

    },
    /**
     * 设置微信公众号分享
     */
    setWechatShare() {
      // 微信公众号分享
      // #ifdef H5
      let share_data=this.$util.deepClone(this.getSharePageParams());
      let link=window.location.origin+this.$router.options.base+share_data.link.slice(1);
      share_data.link=link;
      this.$util.publicShare(share_data);
      // #endif
    },
    // 分享触发
    getTransmit(id) {
      this.$api.sendRequest({
        url: apiurls.usershareexperienceTransmit,
        data: {
          id
        }
      });
    },
    headimgError(dataList){
      dataList && (dataList['headimg'] = this.$util.getDefaultImage().default_headimg);
      this.$forceUpdate();
    }
  },
  /**
   * 自定义分享内容
   * @param {Object} res
   */
  onShareAppMessage(res) {
    let shareType = res.target && res.target.dataset && res.target.dataset.shareType
    let index = res.target && res.target.dataset && res.target.dataset.shareIndex
    if(shareType && shareType == 'detail'){
      let link = `/mini-h5/promotionpages/seeding/seeding_detail/seeding_detail?id=${this.list[index].id}`
      let title = this.list[index].title
      let imageUrl = this.list[index].image
      this.getTransmit(this.list[index].id)
      return this.$buriedPoint.pageShare(link , imageUrl, title);
    }
    let { title, link, imageUrl, query } = this.getSharePageParams()
    return this.$buriedPoint.pageShare(link , imageUrl, title);
  },
  async onPullDownRefresh() {
    this.isStartPage=true;
    this.list = []
    this.page = 1
    if (this.$refs.loadingCover) this.$refs.loadingCover.show();
    await this.getData();
    await this.getInit()
    this.isStartPage=false;
    uni.stopPullDownRefresh();
  },
};
</script>

<style lang="scss" scoped>
.content{
  &-has{
    padding-bottom: 80rpx;
    box-sizing: border-box;
  }
  &-info{
    box-sizing: border-box;
  }
  .header{
    width: 100%;
    position: fixed;
    left: 0;
    top: 0;
    padding: 20rpx 20rpx;
    box-sizing: border-box;
    background-color: white;
    border-bottom: 1px solid rgba(166, 166, 166, 0.2);
    z-index: 1;
    .user-info{
      display: flex;
      justify-content: space-between;
      align-items: center;
      &-left{
        display: flex;
        align-items: center;
        &-avatar{
          width: 80rpx;
          height: 80rpx;
          border-radius: 50%;
        }
        &-base{
          margin-left: 20rpx;
          &-name{
            font-size: 32rpx;
            line-height: 36rpx;
            font-weight: 400;
            color: rgba(56, 56, 56, 1);
          }
          &-data{
            line-height: 24rpx;
            margin-top: 12rpx;
            &-one{
              font-size: 24rpx;
              font-weight: 400;
              color: rgba(166, 166, 166, 1);
              &:not(:first-child){
                margin-left: 20rpx;
              }
            }
          }
        }
      }
      &-right{
        &-op{
          width: 180rpx;
          height: 64rpx;
          line-height: 64rpx;
          border-radius: 100rpx;
          background: var(--custom-brand-color);
          font-size: 32rpx;
          font-weight: 400;
          color: rgba(255, 255, 255, 1);
          display: flex;
          justify-content: center;
          align-items: center;
        }
      }
    }
  }
  .list{
    &-one{
      background-color: white;
      padding: 22rpx 20rpx;
      box-sizing: border-box;
      &:not(:first-child){
        margin-top: 20rpx;
      }
      &-user{
        display: flex;
        align-items: center;
        &-avatar{
          width: 80rpx;
          height: 80rpx;
          border-radius: 50%;
        }
        &-base{
          margin-left: 20rpx;
          &-name{
            font-size: 32rpx;
            font-weight: 700;
            line-height: 37.5rpx;
            color: rgba(56, 56, 56, 1);
          }
          &-time{
            margin-top: 4rpx;
            font-size: 26rpx;
            font-weight: 400;
            line-height: 30.48rpx;
            color: rgba(166, 166, 166, 1);
          }
        }
      }
      &-desc{
        margin-top: 30rpx;
        font-size: 32rpx;
        font-weight: 400;
        line-height: 40rpx;
        color: rgba(56, 56, 56, 1);
        word-break: break-all;
        overflow: hidden;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
      }
      &-img{
        margin-top: 20rpx;
        &-box{
          position: relative;
          &-tip{
            width: 64rpx;
            height: 44rpx;
            border-radius: 20rpx 0 20rpx 0;
            background: rgba(0, 0, 0, 0.5);
            display: flex;
            justify-content: center;
            align-items: center;
            font-size: 28rpx;
            font-weight: 400;
            line-height: 40rpx;
            color: rgba(255, 255, 255, 1);
            position: absolute;
            right: 0;
            bottom: 0;
          }
        }
        .image{
          border-radius: 20rpx;
        }
        &-one{
          .image{
            width: 480rpx;
            height: auto;
            display: block;
          }
        }
        &-two{
          display: flex;
          justify-content: space-between;
          align-items: center;
          .image{
            width: 344rpx;
            height: 344rpx;
            display: block;
          }
        }
        &-three{
          display: flex;
          justify-content: space-between;
          align-items: center;
          .image{
            width: 220rpx;
            height: 220rpx;
            display: block;
          }
        }
      }
      &-video{
        margin-top: 20rpx;
        display: flex;
        &-info{
          position: relative;
          display: inline-block;
          &-tip{
            width: 104rpx;
            height: 44rpx;
            border-radius: 20rpx 0 20rpx 0;
            background: rgba(0, 0, 0, 0.5);
            display: flex;
            justify-content: center;
            align-items: center;
            font-size: 28rpx;
            font-weight: 400;
            line-height: 40rpx;
            color: rgba(255, 255, 255, 1);
            position: absolute;
            right: 0;
            bottom: 0;
          }
          &-play{
            width: 60rpx;
            height: 60rpx;
            position: absolute;
            left: 50%;
            top: 50%;
            transform: translate(-50%, -50%);
          }
          &-one{
            width: 480rpx;
            //height: 640rpx;
            border-radius: 20rpx;
            display: block;
            &-horizontal{
              width: 710rpx;
              //height: 396rpx;
            }
          }
          &-two{
            width: 710rpx;
            height: 396rpx;
            border-radius: 20rpx;
            display: block;
          }
        }
      }
      &-op{
        display: flex;
        justify-content: space-around;
        align-items: center;
        box-sizing: border-box;
        margin-top: 48rpx;
        &-one{
          font-size: 28rpx;
          font-weight: 400;
          line-height: 40rpx;
          color: rgba(56, 56, 56, 1);
          display: flex;
          justify-content: center;
          align-items: center;
          background: transparent;
          min-width: 200rpx;
          padding: 0;
          margin: 0;
          &-text{
            margin-left: 10rpx;
          }
        }
      }
    }
  }
}
/deep/ .uni-popup__wrapper{
  border-radius: 20rpx 20rpx 0 0;
}
.select-box{
  position: relative;
  height: 580rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  &>view:first-child{
    flex: 1;
    display: flex;
    flex-direction: column;
    margin-left: 110rpx;
    image{
      width: 200rpx;
      height: 200rpx;
    }
    view{
      width: 200rpx;
      text-align: center;
    }
  }
  &>view:last-child{
    flex: 1;
    display: flex;
    flex-direction: column;
    image{
      width: 200rpx;
      height: 200rpx;
    }
    view{
      width: 200rpx;
      text-align: center;
    }
  }
}
.close{
  position: absolute;
  width: 32rpx;
  height: 32rpx;
  right: 36rpx;
  top: 28rpx;
}
.page-title{
  width: 360rpx;
  overflow:hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  -o-text-overflow:ellipsis;
  text-align: center;
}
</style>
