<template>
  <view class="add" :style="[themeColorVar]">
    <view class="add-warp">
      <!--    <view class="title"><input placeholder="标题(选填)" v-model="title" maxlength="24" placeholder-class="input-place"></input></view>-->
      <view class="content">
        <textarea placeholder="快快分享你发现的趣事吧~" v-model="content" auto-height maxlength="-1" placeholder-class="textarea-place"></textarea>
      </view>
      <view class="default-box" v-if="img.length < 1 && !videoFile">
        <view class="default-box-add" @click="initAdd(3)">
          <text class="default-box-add-icon iconfont iconzhaopian"></text>
          <text class="default-box-add-text">添加图片</text>
        </view>
        <view class="default-box-add" @click="initAdd(4)" v-if="is_show_add_video">
          <text class="default-box-add-icon iconfont iconshipin"></text>
          <text class="default-box-add-text">添加视频</text>
        </view>
      </view>
      <template v-else>
        <view class="img-info" v-if="type == 3">
          <view v-for="(item, index) in img" :key="index">
            <image class="phone-img" :src="item" mode="aspectFill"/>
            <!-- <img :src="$util.img(item)" alt="" @click="preViewImage(PREVIEWIMAGELIST,index)"/> -->
            <view class="close" @click="colseFun(item)">x</view>
          </view>
          <view class="add-info" @click="seedingFun" v-if="img.length < imgLimit">
            <!--        <image class="add-icon" :src="$util.img('public/static/youpin/add-icon.png')" mode="" />-->
            <image class="add-icon" :src="$util.img('public/static/youpin/add-icon.png')" mode="" />
            <text class="add-info-text">({{img.length}}/{{imgLimit}})</text>
          </view>
        </view>
        <view class="video-info-box" v-else-if="type == 4">
          <view>
            <view class="video-img">
              <image class="img-video" :class="{'img-video-vertical' : video_is_vertical}" :src="videoImage" alt="" v-if="videoImage" mode="aspectFill" @click="changeVideoCover" >
                <text class="img-video-tip" @click="changeVideoCover" v-if="videoImage">点击修改封面</text>
              </image>
              <view class="add-info" @click="seedingFun" v-if="videoImage == ''">
                <image class="add-icon" :src="$util.img('public/static/youpin/add-icon.png')" mode="" />
                <text class="add-info-text">视频</text>
              </view>
              <view class="close" @click="colseVideoFun" v-if="videoImage">x</view>
              <text class="iconfont iconarrow- video-img-play" v-if="videoImage"></text>
            </view>
            <!--          <view class="video-info">-->
            <!--            <view>*<view>最多支持100S</view></view>-->
            <!--            <view v-if="videoDuration">已选择时长{{videoDuration}}S</view>-->
            <!--          </view>-->
          </view>
        </view>
      </template>
    </view>
    <view class="goods-list">
      <view class="goods-list-title">
        <image :src="$util.img('public/static/youpin/goods-add.png')" class="goods-list-title-icon"></image>
        关联商品<text class="goods-list-title-number">(<text class="goods-list-title-number-select">{{allGoodsList.filter(item=>item.is_selected).length}}</text>/{{allGoodsList.length}})</text></view>
      <template v-if="allGoodsList.length > 0">
        <template v-for="(item, index) in allGoodsList">
          <view class="goods-list-one" @click="changeSelected(index)">
            <image :src="$util.img(item.goods_image)" class="goods-list-one-img" @error="errorFun(allGoodsList,index)"></image>
            <view class="goods-list-one-info">
              <view class="goods-list-one-info-title">{{item.goods_name}}</view>
              <view class="goods-list-one-info-price"><text class="goods-list-one-info-price-symbol">￥</text>{{item.retail_price}}</view>
            </view>
            <view class="goods-list-one-select">
              <uni-icons type="checkbox-filled" size="28" color="var(--custom-brand-color)"  v-if="item.is_selected"></uni-icons>
              <uni-icons type="circle" size="28" color="rgba(196, 196, 196, 1)" v-else></uni-icons>
            </view>
          </view>
        </template>
      </template>
      <template v-else>
        <view class="goods-list-empty">
          <image :src="$util.img('public/static/youpin/goods/not_orders.png')" class="goods-list-empty-img"></image>
          <view class="goods-list-empty-title">暂无可关联的商品订单</view>
        </view>
      </template>
    </view>
    <view class="footer">
<!--      <image :src="$util.img('public/static/youpin/add-good.png')" @click="addGood" v-if="!goods_id"></image>-->
<!--      <view class="text"  v-if="goodInfo.length == 0 && !goods_id" @click="addGood">添加关联商品</view>-->
<!--      <view class="good-box" v-else>-->
<!--        <image :src="item.goods_image" class="img" v-for="(item, index) in goodInfo" :key="index"></image>-->
<!--      </view>-->
      <view class="cancel" @click="cancelFun">取消</view>
      <view class="btn" @click="comfirmFun">确认发布</view>
    </view>

    <uni-popup ref="goodsSelectPopupRef" type="bottom" class="goods-select-pop-father">
      <view class="goods-select-pop">
        <view class="goods-select-pop-header">
          <text class="goods-select-pop-header-title">可关联的订单</text>
        </view>
        <view class="goods-select-pop-list">
          <view class="goods-select-pop-list-one" v-for="(item,index) in allGoodsList" :key="index">
            <view class="goods-select-pop-list-one-left">
              <image :src="$util.img(item.goods_image)" class="goods-select-pop-list-one-left-img" @error="errorFun(allGoodsList,index)"></image>
              <view class="goods-select-pop-list-one-left-info">
                <view class="goods-select-pop-list-one-left-info-name">{{item.goods_name}}</view>
                <view class="goods-select-pop-list-one-left-info-price"><text class="goods-select-pop-list-one-left-info-price-symbol">￥</text>{{item.retail_price}}</view>
              </view>
            </view>
            <view class="goods-select-pop-list-one-right">
              <uni-icons type="checkbox-filled" size="28" color="var(--custom-brand-color)"  v-if="item.is_selected" @click="changeSelected(index)"></uni-icons>
              <uni-icons type="circle" size="28" color="rgba(196, 196, 196, 1)" v-else @click="changeSelected(index)"></uni-icons>
            </view>
          </view>
        </view>
        <view class="goods-select-pop-nav">
          <view class="goods-select-pop-nav-tip">
            <text class="goods-select-pop-nav-tip-text">未完成订单或已发布关联订单商品不支持关联</text>
<!--            <uni-icons type="clear" size="18" color="var(&#45;&#45;custom-brand-color)"></uni-icons>-->
          </view>
          <view class="goods-select-pop-nav-op">
            <text class="goods-select-pop-nav-op-cancel" @click="$refs.goodsSelectPopupRef.close()">取消</text>
            <text class="goods-select-pop-nav-op-confirm" @click="$refs.goodsSelectPopupRef.close()">确认关联({{allGoodsList.filter(item=>item.is_selected).length}})</text>
          </view>
        </view>
      </view>
    </uni-popup>
    <loading-cover ref="loadingCover"></loading-cover>
    <video-cover-image ref="videoCoverImageRef" @confirm="videoCoverConfirm"></video-cover-image>
  </view>
</template>

<script>
  import golbalConfig from "../../../common/mixins/golbalConfig";
  import system from "../../../common/js/system";
  import UniPopup from "../../../components/uni-popup/uni-popup-sku.vue";
  import UniIcons from "../../../components/uni-icons/uni-icons.vue";
  import videoCoverImage from "../../components/video-cover-image/video-cover-image.vue"

  export default {
    components: {UniIcons, UniPopup,videoCoverImage},
    mixins:[golbalConfig],
    data() {
      return {
        title: '',
        content: '',
        type: '',
        img: [],
        videoImage: '',
        videoFile: '',
        videoDuration: '',
        height:'',
        goodInfo: [],
        allGoodsList:[],
        goods_id: '',
        id: '',
        imgLimit:9,
        video_is_vertical: false,
        is_show_add_video: true,
      }
    },
    async onLoad({ type, goods_id, id }) {
      let osName = uni.getSystemInfoSync().osName;
      if(this.$util.getPlatform() == 'weapp' && (osName=='windows' || osName == 'macos')){
        this.is_show_add_video = false
      }
      await system.wait_staticLogin_success();
      // uni.setNavigationBarTitle({
      //   title: type == 3 ? '发图片':'发视频'
      // });
      this.type = type
      this.goods_id = goods_id
      await this.getGoodsList();
      if(id) {
        this.id = id || null
        await this.getData()
      }else{
        // // #ifdef MP-WEIXIN
        // this.seedingFun()
        // // #endif
      }
      if (this.$refs.loadingCover) this.$refs.loadingCover.hide();
    },
    onShow() {
      // if(uni.getStorageSync('selectGood')) this.goodInfo = JSON.parse(uni.getStorageSync('selectGood'))
    },
    methods: {
      async getData(){
        try{
          let res = await this.$api.sendRequest({
            url: this.$apiUrl.usershareexperienceContent,
            data: {
              id:this.id,
            },
            async:false
          })
          let data = res.data
          this.content = data.content
          this.title = data.title
          this.type = data.content_type
          if(data.content_type == 3){
            this.img = data.share_resource
          }
          if(data.content_type == 4){
            this.videoImage = data.image
            this.videoFile = data.share_resource
          }
          if(data.goods.length) {
            data.goods= data.goods.map(item=>{
              item.is_selected = true;
              return item;
            })
            this.allGoodsList = data.goods.concat(this.allGoodsList)
          }
        }catch (e) {

        }
      },
      async getGoodsList(){
        try{
          let res = await this.$api.sendRequest({
            url: this.$apiUrl.usePublishGoodsUrl,
            data:{},
            async:false
          })
          if(res.code == 0){
            this.allGoodsList = res.data.map(item=>{
              item.is_selected = false;
              return item;
            })
          }
        }catch (e) {

        }
      },
      initAdd(type){
        this.type = type
        this.seedingFun()
      },
      seedingFun() {
        let that = this
        switch(parseInt(this.type)) {
          case 3:
            this.$util.upload(
              this.imgLimit-that.img.length, {
                path: 'userShareImg'
              },
              res => {
                for (var i = 0; i < res.length; i++) {
                  that.img.push(res[i]);
                }
              },
              ['album']
            );
          break;
          case 4:
            this.$util.uploadVideo({path: 'userShareVideo'}, res => {
              this.videoImage = res.cover
              this.videoFile = res.path
              this.videoDuration = res.duration
              if(res.video_metadata && res.video_metadata.width && res.video_metadata.height){
                if(res.video_metadata.width > res.video_metadata.height){
                  this.video_is_vertical = true
                }
              }
              this.$refs.videoCoverImageRef.show(this.videoFile,this.videoDuration*1000)
            })
          break;
        }
      },
      // 删除图片
      colseFun(url) {
        this.img = this.img.filter((i) => i != url);
        if(this.img.length <= 0){
          this.type = null
        }
      },
      // 删除视频
      colseVideoFun() {
        this.type = null
        this.videoImage = ''
        this.videoFile = ''
      },
      changeVideoCover(){
        this.$refs.videoCoverImageRef.show(this.videoFile,this.videoDuration*1000)
      },
      videoCoverConfirm(video_cover){
        this.videoImage = video_cover
      },
      cancelFun(){
        uni.navigateBack();
      },
      // 发布
      comfirmFun() {
        // if(this.title == '') {
        //   this.$util.showToast({
        //     title: '标题不能为空'
        //   })
        //   return false
        // }
        if(this.content == '') {
          this.$util.showToast({
            title: '分享内容不能为空'
          })
          return false
        }
        if(this.img.length == 0 && this.type == 3) {
          this.$util.showToast({
            title: '请上传图片'
          })
          return false
        }
        if(this.videoImage == '' && this.type == 4) {
          this.$util.showToast({
            title: '请上传视频'
          })
          return false
        }
        let goods_id = this.allGoodsList.filter(item=>item.is_selected).map(item=>{
          return {order_no:item.order_no,goods_id:item.goods_id}
        })
        if(!goods_id.length){
          this.$util.showToast({
            title: '请关联商品'
          })
          return false
        }
        uni.showLoading({
          title: '发布中...'
        })
        this.$api.sendRequest({
					url: this.id ? this.$apiUrl.usershareexperienceEditLaunch:this.$apiUrl.usershareexperienceLaunch,
					data: {
						title: this.title,
            content: this.content,
            share_resource: this.type == 3 ? this.img.join(','):this.videoFile,
            content_type: this.type,
            order_goods_ids: JSON.stringify(goods_id),
            id: this.id
					},
					success:(res)=>{
            uni.hideLoading()
						if(res.code == 0){
              uni.removeStorageSync('selectGood');
                this.$util.showToast({
                  title: this.id ? '已更新':'已上传'
                })
                setTimeout(() => {
                  if(this.id){
                    uni.navigateBack({
                      delta: 1
                    });
                  }else{
                    this.$util.redirectTo(`/promotionpages/seeding/seeding_home_page/seeding_home_page?from=1`, {}, 'redirectTo');
                  }
                }, 1500)
            }else{
              this.$util.showToast({
                title: res.message
              })
              if(res.code == -10020) {
                setTimeout(() => {
                  let pages = getCurrentPages()
                  let prevPage = pages[ pages.length - 2 ];  //上一页页面实例
                  console.log(prevPage);
                  prevPage.$vm.refresh = 1;

                  uni.navigateBack({
                    delta: 1
                  });
                }, 1500)
              }
            }
          }
        })
      },
      // 添加关联商品
      addGood() {
        // if(this.goods_id) return false
        // this.$util.redirectTo('/promotionpages/seeding/seeding-select-good/seeding-select-good');
        this.$refs.goodsSelectPopupRef.open()
      },
      // 编辑视频
      editVideo() {
        // #ifdef MP-WEIXIN
        // this.$util.editVideo({path: 'userShareVideo', files: this.videoFile}, res => {
        //   this.videoImage = res.cover
        //   this.videoFile = res.path
        //   this.videoDuration = res.duration
        // })
        // #endif
      },
      /**
       *分享参数组装
       */
      getSharePageParams() {
        return this.$util.unifySharePageParams('/otherpages/shop/home/<USER>', this.shareTitle || '先迈商城',
            '',{}, this.$util.img(this.shareImg) || this.$util.img('public/static/youpin/home_share.jpg'))
      },
      errorFun(dataList,index) {
        dataList[index] && (dataList[index].goods_image = this.$util.getDefaultImage().default_goods_img);
        this.$forceUpdate();
      },
      changeSelected(index){
        this.allGoodsList = this.allGoodsList.map((item,j)=>{
          if(index == j){
            item.is_selected = !item.is_selected
          }
          return item;
        })
      }
    },
    /**
     * 自定义分享内容
     * @param {Object} res
     */
    onShareAppMessage(res) {
      let { title, link, imageUrl, query } = this.getSharePageParams()
      return this.$buriedPoint.pageShare(link , imageUrl, title);
    },
  }
</script>

<style lang="scss">
/* #ifdef MP-WEIXIN */
.video-cover-image-father /deep/ .uni-popup__wrapper.uni-custom.center .uni-popup__wrapper-box{
  max-width: 100%;
  max-height: 100%;
  overflow: unset;
}
/* #endif */
</style>
<style lang="scss" scoped>
page{
  background: rgba(250, 250, 250, 1);
}
.add{
  background: rgba(250, 250, 250, 1);
  min-height: 100vh;
  padding: 0 20rpx calc(200rpx + env(safe-area-inset-bottom));
  padding-top: 20rpx;
  box-sizing: border-box;
  &-warp{
    border-radius: 20rpx;
    background: rgba(255, 255, 255, 1);
    padding: 20rpx;
    box-sizing: border-box;
  }
  .title{
    padding: 30rpx 0;
    border-bottom: 1px solid #EEEEEE;
  }
  .content{
    textarea{
      width: initial;
      min-height: 200rpx;
    }
  }
  .img-info, .video-info-box{
    display: flex;
    flex-wrap: wrap;
    margin-top: 10rpx;
    &>view{
      position: relative;
      margin-top: 20rpx;
      margin-right: 12rpx;
    }
    .add-info{
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      width: 208rpx;
      height: 208rpx;
      border-radius: 20rpx;
      background: rgba(247, 247, 247, 1);
      box-sizing: border-box;
      &-text{
        font-size: 26rpx;
        font-weight: 400;
        line-height: 30.48rpx;
        color: rgba(166, 166, 166, 1);
        margin-top: 16rpx;
      }
    }
    .phone-img{
      border-radius: 20rpx;
      width: 208rpx;
      height: 208rpx;
      display: block;
    }
    .close {
      position: absolute;
      right: 0;
      top: 0;
      width: 44rpx;
      height: 44rpx;
      border-radius: 0 20rpx 0 10rpx;
      background: rgba(0, 0, 0, 0.3);
      font-size: 28rpx;
      color: #fff;
      display: flex;
      justify-content: center;
      align-items: center;
    }
    .add-icon{
      width: 60rpx;
      height: 60rpx;
    }
  }
  .video-info-box{
    .video-img{
      position: relative;
      &-play{
        font-size: 50rpx;
        color: rgb(255,255,255);
        border-radius: 50%;
        position: absolute;
        left: 50%;
        top: 50%;
        transform: translate(-50%, -50%);
      }
    }
    &>view{
      display: flex;
    }
    .img-video{
      border-radius: 20rpx;
      width: 300rpx;
      height: 400rpx;
      position: relative;
      &-vertical{
        width: 400rpx;
        height: 300rpx;
      }
      &-tip{
        width: 100%;
        text-align: center;
        font-size: 28rpx;
        color: white;
        position: absolute;
        left: 50%;
        top: 58%;
        transform: translatex(-50%);
      }
    }
    .video-info{
      margin-left: 30rpx;
      margin-top: 25rpx;
      &>view:first-child{
        display: flex;
        align-items: center;
        color: var(--custom-brand-color);
        font-size: 28rpx;
        line-height: 38rpx;
        view{
          color: #333;
          margin-left: 10rpx;
        }
      }
      &>view:last-child{
        margin-left: 20rpx;
        color: #666666;
        font-size: 24rpx;
      }
    }
  }
  .default-box{
    display: flex;
    align-items: center;
    &-add{
      width: 208rpx;
      height: 208rpx;
      border-radius: 20rpx;
      background: rgba(247, 247, 247, 1);
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      margin-right: 24rpx;
      &-icon{
        font-size: 60rpx;
        color: rgba(196, 196, 196, 1);
      }
      &-text{
        font-size: 26rpx;
        font-weight: 400;
        line-height: 30.48rpx;
        color: rgba(56, 56, 56, 1);
      }
    }
  }
  .goods-list{
    margin-top: 56rpx;
    padding: 20rpx 20rpx 20rpx 20rpx;
    border-radius: 20rpx;
    background: rgba(255, 255, 255, 1);
    box-sizing: border-box;
    &-title{
      font-size: 32rpx;
      font-weight: 700;
      line-height: 46.34rpx;
      color: rgba(56, 56, 56, 1);
      display: flex;
      align-items: center;
      &-icon{
        width: 32rpx;
        height: 32rpx;
        margin-right: 14rpx;
      }
      &-number{
        font-size: 28rpx;
        font-weight: 400;
        line-height: 46.34rpx;
        color: rgba(166, 166, 166, 1);
        margin-left: 10rpx;
        &-select{
          font-weight: 700;
          color: rgba(56, 56, 56, 1);
        }
      }
    }
    &-one{
      display: flex;
      align-items: center;
      width: 100%;
      height: 144rpx;
      border-radius: 20px;
      background: rgba(247, 247, 247, 1);
      padding: 12rpx;
      box-sizing: border-box;
      margin-top: 20rpx;
      position: relative;
      &-img{
        width: 120rpx;
        height: 120rpx;
        border-radius: 20rpx;
      }
      &-info{
        margin-left: 20rpx;
        width: 410rpx;
        &-title{
          font-size: 30rpx;
          font-weight: 400;
          line-height: 43.44rpx;
          color: rgba(34, 34, 34, 1);
          text-overflow: ellipsis;
          overflow: hidden;
          word-break: break-all;
          white-space: nowrap;
          width: 100%;
        }
        &-price{
          font-size: 32rpx;
          font-weight: 400;
          line-height: 37.5rpx;
          color: rgba(56, 56, 56, 1);
          margin-top: 14rpx;
          &-symbol{
            font-size: 24rpx;
            font-weight: 400;
          }
        }
      }
      &-select{
        margin-left: 32rpx;
      }
    }
    &-empty{
      display: flex;
      flex-direction: column;
      align-items: center;
      margin-top: 100rpx;
      &-img{
        width: 105rpx;
        height: 120rpx;
      }
      &-title{
        margin-top: 40rpx;
        font-size: 32rpx;
        font-weight: 400;
        line-height: 37.5rpx;
        color: rgba(128, 128, 128, 1);
        text-align: center;
      }
    }
  }
  .footer{
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    height: calc(146rpx + env(safe-area-inset-bottom));
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 32rpx;
    box-sizing: border-box;
    background-color: #fff;
    border-top: 2rpx solid rgba(229, 229, 229, 1);
    .text{
      font-size: 28rpx;
      color: #999999;
      margin-left: 10rpx;
    }
    .cancel{
      width: 320rpx;
      height: 80rpx;
      border-radius: 40rpx;
      background: var(--custom-brand-color-10);
      font-size: 32rpx;
      font-weight: 400;
      line-height: 40rpx;
      color: var(--custom-brand-color);
      display: flex;
      justify-content: center;
      align-items: center;
    }
    .btn{
      width: 320rpx;
      height: 80rpx;
      border-radius: 40rpx;
      background: var(--custom-brand-color);
      font-size: 32rpx;
      font-weight: 400;
      line-height: 40rpx;
      color: rgba(255, 255, 255, 1);
      display: flex;
      justify-content: center;
      align-items: center;
    }
    image{
      width: 68rpx;
      height: 68rpx;
    }
    .good-box{
      display: flex;
    }
    .img{
      width: 80rpx;
      height: 80rpx;
      border-radius: 8rpx;
      background-color: #999;
      margin-left: 12rpx;
    }
  }
}
/deep/ .input-place{
  font-size: 32rpx;
  color: #999999;
}
/deep/ .textarea-place{
  font-size: 32rpx;
  font-weight: 400;
  line-height: 46.34rpx;
  color: rgba(166, 166, 166, 1);
}

.goods-select-pop-father /deep/ .uni-popup__wrapper{
  border-radius: 40rpx 40rpx 0 0;
}

.goods-select-pop{
  position: relative;
  max-height: 80vh;
  &-header{
    height: 100rpx;
    padding-top: 24rpx;
    box-sizing: border-box;
    position: sticky;
    left: 0;
    top: 0;
    background: white;
    z-index: 1;
    &-title{
      font-size: 32rpx;
      font-weight: 700;
      line-height: 46.34rpx;
      color: rgba(56, 56, 56, 1);
      padding-left: 20rpx;
      box-sizing: border-box;
    }
  }
  &-list{
    padding: 0 20rpx;
    padding-bottom: calc(186rpx + 20rpx);
    box-sizing: border-box;
    &-one{
      padding: 12rpx;
      box-sizing: border-box;
      height: 144rpx;
      border-radius: 20rpx;
      background: rgba(247, 247, 247, 1);
      display: flex;
      justify-content: space-between;
      align-items: center;
      &:not(:first-child){
        margin-top: 20rpx;
      }
      &-left{
        display: flex;
        align-items: center;
        &-img{
          width: 120rpx;
          height: 120rpx;
          border-radius: 20rpx;
        }
        &-info{
          width: 410rpx;
          margin-left: 20rpx;
          display: flex;
          flex-direction: column;
          justify-content: center;
          &-name{
            text-overflow: ellipsis;
            overflow: hidden;
            word-break: break-all;
            white-space: nowrap;
            font-size: 30rpx;
            font-weight: 400;
            line-height: 43.44rpx;
            color: rgba(34, 34, 34, 1);
          }
          &-price{
            font-size: 32rpx;
            font-weight: 400;
            line-height: 37.5rpx;
            color: rgba(56, 56, 56, 1);
            margin-top: 14rpx;
            &-symbol{
              font-size: 24rpx;
            }
          }
        }
      }
    }
  }
  &-nav{
    position: fixed;
    left: 0;
    bottom: 0;
    width: 100%;
    background: white;
    &-tip{
      height: 60rpx;
      background: var(--custom-brand-color-10);
      padding: 0 20rpx;
      box-sizing: border-box;
      display: flex;
      justify-content: space-between;
      align-items: center;
      &-text{
        font-size: 26rpx;
        font-weight: 400;
        line-height: 37.64rpx;
        color: var(--custom-brand-color);
      }
    }
    &-op{
      height: calc(126rpx + env(safe-area-inset-bottom));
      padding: 0 36rpx;
      padding-bottom: env(safe-area-inset-bottom);
      box-sizing: border-box;
      background: rgba(255, 255, 255, 0.98);
      border-top: 2rpx solid rgba(242, 242, 242, 1);
      display: flex;
      justify-content: space-between;
      align-items: center;
      &-cancel{
        width: 320rpx;
        height: 80rpx;
        border-radius: 40rpx;
        background: var(--custom-brand-color-10);
        font-size: 32rpx;
        font-weight: 400;
        line-height: 40rpx;
        color: var(--custom-brand-color);
        display: flex;
        justify-content: center;
        align-items: center;
      }
      &-confirm{
        width: 320rpx;
        height: 80rpx;
        border-radius: 40px;
        background: var(--custom-brand-color);
        font-size: 32rpx;
        font-weight: 400;
        line-height: 40rpx;
        color: rgba(255, 255, 255, 1);
        display: flex;
        justify-content: center;
        align-items: center;
      }
    }
  }
}
</style>
