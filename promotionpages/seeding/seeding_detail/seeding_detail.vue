<template>
  <view :style="[themeColorVar]">
    <view class="video-preview" v-if="type==0">
      <!-- #ifdef MP-WEIXIN -->
      <view class="custom" :style="{paddingTop:statusBarHeight+'px',height:navHeight+'px',backgroundColor:'transparent'}">
        <view class="iconfont iconback_light" style="color: white" @click="navigateBack"></view>
        <view class="custom-navbar">
          <view class="navbar-item"></view>
        </view>
      </view>
      <!-- #endif -->
        <!-- #ifdef H5 -->
        <view class="custom" :style="{paddingTop:statusBarHeight+20+'px',height:navHeight+'px',backgroundColor:'transparent'}" v-if="isOnXianMaiApp">
            <view class="iconfont iconback_light" style="color: white" @click="appGoBack"></view>
            <view class="custom-navbar">
                <view class="navbar-item"></view>
            </view>
        </view>
        <!-- #endif -->
      <template v-if="list.length">
        <video-list :list="list" @nextVideo="nextVideo" @previousVideo="previousVideo" @changeLike="changeLike" @toShare="openSharePopup" @toLogin="toLogin"></video-list>
      </template>
    </view>

    <!-- 种草--图片预览模式   -->
    <view class="image-preview" :style="{paddingTop:statusBarHeight+navHeight+'px'}" v-if="type==1">
      <!-- #ifdef MP-WEIXIN -->
      <view class="custom" :style="{paddingTop:statusBarHeight+'px',height:navHeight+'px'}">
        <view class="iconfont iconback_light" @click="navigateBack"></view>
        <view class="custom-navbar">
          <view class="navbar-item"></view>
        </view>
      </view>
      <!-- #endif -->
        <!-- #ifdef H5 -->
        <uni-nav-bar left-icon="back" :border="false" @clickLeft="appGoBack" v-if="isOnXianMaiApp">
        </uni-nav-bar>
        <!-- #endif -->
      <view class="image-preview-header" v-if="info.share_resource && info.share_resource.length>0">
        <swiper class="swiper" @change="changeSwiper" :style="{height:listHeight+'px'}">
          <swiper-item class="swiper-item" v-for="(item,index) in info.share_resource" :key="index">
            <image :src="item" alt="" class="swiper-image" mode="scaleToFill" ref="proImg" @load="imgLoad($event,index)"/>
          </swiper-item>
        </swiper>
        <text class="image-preview-header-index">{{swiperIndex+1}}/{{info.share_resource.length}}</text>
      </view>
      <view class="image-preview-article">
<!--        &lt;!&ndash; #ifdef MP-WEIXIN &ndash;&gt;-->
<!--        <button class="image-preview-article-share" @click="openSharePopup"><image :src="$util.img('public/static/youpin/whiteShare.png')" class="image-preview-article-share-img"></image><text class="image-preview-article-share-text">分享</text></button>-->
<!--        &lt;!&ndash; #endif &ndash;&gt;-->
<!--        &lt;!&ndash; #ifdef H5 &ndash;&gt;-->
<!--        <view class="image-preview-article-share" @click="openSharePopup"><image :src="$util.img('public/static/youpin/whiteShare.png')" class="image-preview-article-share-img"></image><text class="image-preview-article-share-text">分享</text></view>-->
<!--        &lt;!&ndash; #endif &ndash;&gt;-->
        <view class="image-preview-article-info">
          <view class="image-preview-article-info-left" @click="toSeedingHome(info.member_id)">
            <image :src="info.headimg" class="image-preview-article-info-image"></image>
            <text class="image-preview-article-info-name">{{info.nickname}}</text>
          </view>
<!--          <text class="image-preview-article-info-date">{{info.create_day}}</text>-->
        </view>
        <view class="image-preview-article-title" v-if="info.title != ''">{{ info.title}}</view>
        <view class="image-preview-article-desc">
          <!-- 内容 -->
          <mphtml v-if="info.content" :content="info.content" class="mphtml" ref="mphtml"/>
<!--          {{info.content}}-->
        </view>
      </view>
      <template v-if="info.goods && info.goods.length>0">
        <view class="image-preview-product">
          <view class="image-preview-product-header"><image :src="$util.img('public/static/youpin/goods-add.png')" class="image-preview-product-header-icon"></image>推荐商品</view>
          <view class="image-preview-product-item" v-for="(item,index) in info.goods" @click="$util.toProductDetail(item)">
            <image :src="item.goods_image" alt="" class="image-preview-product-item-img" @error="errorFun(info.goods,index)"/>
            <view class="image-preview-product-item-info">
              <view class="image-preview-product-item-info-text overtext-hidden-one">{{item.goods_name}}</view>
              <view class="image-preview-product-item-info-price"><text>￥</text>{{item.retail_price}}</view>
            </view>
            <text class="image-preview-product-item-buy">购买</text>
<!--            <image :src="$util.img('public/static/youpin/redCart.png')" alt="" class="image-preview-product-item-card" :class="{'image-preview-product-item-card-one':info.goods.length==1}"/>-->
          </view>
        </view>
      </template>
      <view class="image-preview-separate"></view>
<!--      <comment-preview :article-id="id" v-if="id" :like="{is_show:true,is_like:info.is_like,like_num:info.like_num}" @changeLike="changeLike" @toLogin="toLogin"></comment-preview>-->
    </view>

    <uni-popup ref="downloadSharePopup" type="center">
      <view class="share-list">
        <button class="share-list-one" open-type="share"><image :src="$util.img('public/static/youpin/member/icon-wechat.png')" class="share-list-one-icon"></image>分享给微信好友</button>
        <button class="share-list-one" @click="toDownload"><image :src="$util.img('public/static/youpin/download.png')" class="share-list-one-icon"></image>保存图片/视频到手机</button>
        <button class="share-list-one" @click="copywriting"><image :src="$util.img('public/static/youpin/copy.png')" class="share-list-one-icon"></image>复制标题与正文文案</button>
      </view>
    </uni-popup>
    <template v-if="Object.keys(info).length">
      <bottom-nav :dataInfo="info" @changeLike="changeLike" @toShare="openSharePopup" :theme="type==0 ? 'dark' : 'white'"></bottom-nav>
    </template>
    <!-- 返回顶部 -->
    <to-top v-if="showTop" @toTop="scrollToTopNative()"></to-top>
    <diy-share-navigate-h5 ref="shareNavigateH5"></diy-share-navigate-h5>
    <yd-auth-popup ref="ydauth"></yd-auth-popup>
    <ns-login ref="login"></ns-login>
    <loading-cover ref="loadingCover"></loading-cover>
  </view>
</template>

<script>

import videoList from "../../components/videoList/videoList";
import commentPreview from "../../components/commentPreview/commentPreview";
import system from "../../../common/js/system";
import apiurls from "../../../common/js/apiurls";
import mphtml from "../../../components/mp-html/mp-html";
import scroll from '@/common/mixins/scroll-view.js';
import toTop from "../../../components/toTop/toTop";
import diyShareNavigateH5 from "../../../components/diy-share-navigate-h5/diy-share-navigate-h5";
import appInlineH5 from "../../../common/mixins/appInlineH5";
import golbalConfig from "../../../common/mixins/golbalConfig";
import bottomNav from "../../components/bottom-nav/bottom-nav.vue";
import UniIcons from "../../../components/uni-icons/uni-icons.vue";
import UniPopup from "../../../components/uni-popup/uni-popup.vue";
// #ifdef H5
import {isOnXianMaiApp} from "../../../common/js/h5/appOP";
// #endif

export default {
  name: "seeding_detail",
  components: {
    UniPopup,
    UniIcons,
    videoList,
    commentPreview,
    mphtml,
    toTop,
    diyShareNavigateH5,
    bottomNav
  },
  mixins: [scroll,appInlineH5,golbalConfig],
  data() {
    return {
      oldId:null,
      id:null,
      type:1, // 0显示视频  1显示图片
      statusBarHeight: 0,
      navHeight: 0,
      listHeight:0,
      imgHeights:[],
      info:{},
      showCommnetPopup:false,
      swiperIndex:0,
      page_size:10,
      page:1,
      page_count:1,
      list:[],
      videIndex:0,
        // #ifdef H5
        isOnXianMaiApp:isOnXianMaiApp,
        // #endif
    }
  },
  async onLoad(data){
    await system.wait_staticLogin_success();
    this.id=data.id;
    this.oldId=data.id;
    //导航高度
    // #ifdef MP-WEIXIN
    this.navHeight = uni.getSystemInfoSync().statusBarHeight + 46 - uni.getSystemInfoSync()['statusBarHeight'];
    this.statusBarHeight = uni.getSystemInfoSync()['statusBarHeight']
    // #endif
    this.pageviews();
  },
  async onShow(){
    await system.wait_staticLogin_success();
    await this.getData();
    if(this.type==0){
      await this.getVideoList();
    }
    if (this.$refs.loadingCover) this.$refs.loadingCover.hide();
  },
  methods:{
    toLogin(){
      if(!uni.getStorageSync('token')){
        this.$util.toShowLoginPopup(this, null, `/promotionpages/seeding/seeding_detail/seeding_detail?id=${this.oldId}`);
      }
    },
    previousVideo(index){
      this.videIndex=index;
      this.id=this.list[index].id;
      this.info = this.list[index]
      this.pageviews();
    },
    async nextVideo(index){
      this.videIndex=index;
      this.id=this.list[index].id;
      this.info = this.list[index]
      this.pageviews();
      if(this.videIndex+1+4>=this.list.length){  // 加4 提早加载数据
        await this.getVideoList();
      }
    },
    navigateBack() {
      let pages_arr = getCurrentPages().reverse();
      if(pages_arr.length<2){
        this.$util.redirectTo('/promotionpages/seeding/seeding-list/seeding-list',{},'reLaunch');
      }else{
        uni.navigateBack();
      }
    },
    async getData(){
      let res=await this.$api.sendRequest({
        url: apiurls.usershareexperienceContent,
        async: false,
        data: {
          id: this.id,
        },
      });
      if (res.code == 0) {
        this.info=res.data;
        this.imgHeights=new Array(this.info.share_resource.length);
        if(this.info.content_type==3){
          this.type=1;
        }else if(this.info.content_type==4){
          this.type=0;
        }
      }
    },
    async getVideoList(){
      if(this.page>this.page_count){
        return;
      }
      let res = await this.$api.sendRequest({
        url: apiurls.usershareexperienceVideoListUrl,
        async: false,
        data: {
          id:this.id,
          page:this.page,
          page_size:this.page_size
        }
      })
      if (res.code == 0) {
        if(res.data && Array.isArray(res.data.list)){
          this.page_count=res.data.page_count;
          this.list=this.list.concat(res.data.list);
          this.page+=1;
        }
      }
    },
    changeSwiper(e){
      this.swiperIndex=e.detail.current;
      this.listHeight=this.imgHeights[this.swiperIndex];
    },
    imgLoad(e,index){
      this.systemInfo=wx.getSystemInfoSync();
      let { screenWidth } = this.systemInfo;
      let dp=screenWidth/e.detail.width;
      this.imgHeights[index]=e.detail.height*dp;
      if(index==this.swiperIndex){
        this.listHeight=this.imgHeights[index];
      }
    },
    async changeLike(is_like){
      if(!uni.getStorageSync('token')){
        // this.$util.showToast({
        //   title:'请先登录在点赞',
        //   mask:true
        // })
        this.toLogin();
        return;
      }
      let res=await this.$api.sendRequest({
        url: apiurls.usershareexperienceLike,
        async: false,
        data: {
          id: this.id,
        },
      });
      if (res.code == 0) {
        let like_num=res.data.like_num;
        this.info.like_num=like_num;
        if(this.info.is_like){
          this.info.is_like=false;
        }else{
          this.info.is_like=true;
        }
        if(this.type==1){

        }else{
          let tmp_list=this.list.filter((item)=>item.id==this.id);
          if(tmp_list.length>0){
            let tmp=tmp_list[0];
            tmp.like_num=like_num;
            if(tmp.is_like){
              tmp.is_like=false;
            }else{
              tmp.is_like=true;
            }
          }
        }
      }else{
        this.$util.showToast({
          title:res.message,
          mask:true
        })
      }
    },
    /**
     *分享参数组装(注意需要分享的那一刻再调此方法)
     */
    getSharePageParams(){
      let imgUrl='';
      let desc='';
      if(this.type==0){
        imgUrl=this.list[this.videIndex].image;
        desc=this.list[this.videIndex].title == '' ? this.list[this.videIndex].content:this.list[this.videIndex].title;
      }else{
        imgUrl=this.info.share_resource[0];
        desc=this.info.title == '' ? this.info.content:this.info.title;
      }

      let share_data=this.$util.unifySharePageParams('/promotionpages/seeding/seeding_detail/seeding_detail','先迈商城',
          desc,{id:this.id},imgUrl)
      return share_data;
    },
    /**
     * 设置微信公众号分享
     */
    setWechatShare() {
      // 微信公众号分享
      // #ifdef H5
      let share_data=this.$util.deepClone(this.getSharePageParams());
      let link=window.location.origin+this.$router.options.base+share_data.link.slice(1);
      share_data.link=link;
      this.$util.publicShare(share_data);
      // #endif
    },
    // 打开分享弹出层
    openSharePopup() {
      // #ifdef MP-WEIXIN
      this.$refs.downloadSharePopup.open()
      // #endif
      // #ifdef H5
      let share_data=this.getSharePageParams();
      if(this.$refs.shareNavigateH5) this.$refs.shareNavigateH5.open(share_data);
      // #endif

    },
    // 关闭分享弹出层
    closeSharePopup() {
      if(this.$refs.sharePopup) this.$refs.sharePopup.close();
    },
    toSeedingHome(id){
      this.$util.redirectTo('/promotionpages/seeding/seeding_home_page/seeding_home_page',{other_mid:id});
    },
    //浏览量
    pageviews(){
      this.$api.sendRequest({
        url: apiurls.usershareexperienceAddView,
        data: {
          id: this.id
        }
      });
    },
    // 分享触发
    getTransmit() {
      this.$api.sendRequest({
        url: apiurls.usershareexperienceTransmit,
        data: {
          id: this.id
        }
      });
    },
    downloadFile(file_url){
      return new Promise((resolve, reject)=>{
            uni.downloadFile({
              url: file_url,
              success: function (res) {
                let temp = res.tempFilePath
                resolve(temp)
              },
              fail:(err)=>{
                reject(err)
              }
          })
      })
    },
    saveFile(tempFilePath){
      return new Promise((resolve, reject)=>{
        uni.getSetting({
          success:(res) =>{
            let authSetting = res.authSetting
            if(authSetting.hasOwnProperty('scope.writePhotosAlbum')){
              if(authSetting['scope.writePhotosAlbum']){
                uni.saveImageToPhotosAlbum({
                  filePath: tempFilePath,
                  success(res) {
                    return resolve(res)
                  },
                  fail: function (err) {
                    reject(err)
                  }
                })
              }else{
                this.$util.showToast({
                  title: '未授权保存文件到相册的权限，需要授权才能保存',
                  success:(e)=>{
                    uni.openSetting({
                      success:()=>{
                        reject('')
                      },
                      fail:(err)=>{
                        reject(err)
                      }
                    })
                  },
                  fail:(err)=>{
                    reject(err)
                  }
                });
              }
            }else{
              uni.authorize({
                scope: 'scope.writePhotosAlbum',
                success:(res=>{
                  uni.saveImageToPhotosAlbum({
                    filePath: tempFilePath,
                    success(res) {
                      return resolve(res)
                    },
                    fail: function (err) {
                      reject(err)
                    }
                  })
                }),
                fail:(err)=>{
                  reject(err)
                }
              })
            }
          },
          fail:(err)=>{
            reject(err)
          }
        })
      })
    },
    async saveToPhotoAlbum(file_url_list){
      try {
        uni.showLoading({
          title: '保存中',
          mask: true
        })
        for (let i = 0; i < file_url_list.length; i++) {
          let tmp = await this.downloadFile(file_url_list[i])
          await this.saveFile(tmp)
        }
        uni.hideLoading()
        this.$util.showToast({
          title: '保存文件完成'
        });
      } catch (e) {
        uni.hideLoading()
        this.$util.showToast({
          title: e
        });
      }
    },
    async toDownload(){
      this.$refs.downloadSharePopup.close()
      if(this.type == 1){
          if(this.info.share_resource && this.info.share_resource.length>0){
            await this.saveToPhotoAlbum(this.info.share_resource)
          }else {
            this.$util.showToast({
              title: '没图片可以保存的'
            });
          }
      }else if(this.type == 0){
        let tmp_list=this.list.filter((item)=>item.id==this.id);
        if(tmp_list.length > 0){
          await this.saveToPhotoAlbum([tmp_list[0].share_resource])
        }
      }
    },
    copywriting(){
      this.$refs.downloadSharePopup.close()
      let text = ''
      if(this.type == 1){
        text = this.$refs.mphtml.getText(this.$refs.mphtml.nodes)
        if(this.info.title){
          text = this.info.title+'\r\n'+text
        }
      }else if (this.type == 0){
        let tmp_list=this.list.filter((item)=>item.id==this.id);
        if(tmp_list.length > 0){
          text = tmp_list[0].content
          if(tmp_list[0].title){
            text = tmp_list[0].title+'\r\n'+text
          }
        }
      }
      this.$util.copy(text)
    },
    errorFun(dataList,index) {
      dataList[index] && (dataList[index].goods_image = this.$util.getDefaultImage().default_goods_img);
      this.$forceUpdate();
    },
  },
  /**
   * 自定义分享内容
   * @param {Object} res
   */
  onShareAppMessage(res) {
    this.getTransmit()
    let { title, link, imageUrl, desc } = this.getSharePageParams()
    return this.$buriedPoint.pageShare(link , imageUrl, desc);
  },
}
</script>
<style lang="scss">
page{
  background-color: white;
}
/* #ifdef MP-WEIXIN */
.goods-list-pop-father /deep/ .uni-popup__mask{
  bottom: unset;
  height: calc(100vh - 126rpx - env(safe-area-inset-bottom));
}
.goods-list-pop-father /deep/ .uni-popup__wrapper.uni-bottom{
  border-radius: 40rpx 40rpx 0 0;
  bottom: calc(126rpx + env(safe-area-inset-bottom));
}
.goods-list-pop-father-dark /deep/ .uni-popup__wrapper.uni-bottom{
  background: transparent;
}
.goods-list-pop-father /deep/ .uni-popup__wrapper-box{
  border-radius: 40rpx 40rpx 0 0!important;
}
.goods-list-pop-father-dark /deep/ .uni-popup__wrapper-box{
  background-color: rgba(0,0,0,1)!important;
}
.goods-list-pop-father /deep/ .bottom{
  padding-bottom: 0!important;
  background: transparent!important;
}
/* #endif */
/* #ifdef MP-WEIXIN */
.video-desc-father /deep/ .uni-popup__wrapper-box{
  border-radius: 0!important;
}
.video-desc-father /deep/ .bottom{
  padding-bottom: 0!important;
}
/* #endif */
</style>

<style scoped lang="scss">
/* 标题栏 */
.custom {
  background: #fff;
  display: flex;
  align-items: center;
  justify-content: center;
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  z-index: 1;
  .iconfont {
    font-size: $ns-font-size-base + 12;
    color: #333;
    font-weight: bold;
    position: absolute;
    left: 20rpx;
  }

  .custom-navbar {
    display: flex;
    // border-radius: 30rpx;
    // background: #FFF4F4;
    width: 360rpx;
    align-items: center;

    .navbar-item {
      height: 60rpx;
      line-height: 60rpx;
      width: 100%;
      text-align: center;
      color: #333333;
      font-size: $ns-font-size-base + 2;
      // &.active{
      // 	background:$base-color;
      // 	color: #FFFFFF;
      // 	border-radius: 30rpx;
      // }
    }
  }
}
.video-preview{
  box-sizing: border-box;
}
.image-preview{
  box-sizing: border-box;
  padding-bottom: calc(126rpx + env(safe-area-inset-bottom));
  &-header{
    position: relative;
    .swiper{
      //height: 488rpx;
      background: rgba(220, 220, 220, 0.39);
      &-item{
        display: flex;
        justify-content: center;
        align-items: center;
      }
      &-image{
        width: 100%;
        height: 100%;
      }
    }
    &-index{
      width: 88rpx;
      height: 40rpx;
      border-radius: 40rpx;
      background: rgba(0, 0, 0, 0.5);
      font-size: 32rpx;
      font-weight: 400;
      line-height: 37.5rpx;
      color: rgba(255, 255, 255, 1);
      display: flex;
      justify-content: center;
      align-items: center;
      box-sizing: border-box;
      position: absolute;
      right: 14rpx;
      bottom: 14rpx;
    }
  }
  &-article{
    padding: 16rpx 24rpx 20rpx 24rpx;
    box-sizing: border-box;
    position: relative;
    &-share{
      margin: 0;
      padding: 0;
      width: 120rpx;
      height: 48rpx;
      background: linear-gradient(90deg, var(--custom-brand-color-80) 0%, var(--custom-brand-color) 100%, #FF3333 100%);
      border-radius: 24rpx 0px 0px 24rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      position: absolute;
      top: 16rpx;
      right: 0;
      &-img{
        width: 32rpx;
        height: 30rpx;
        margin-right: 8rpx;
      }
      &-text{
        font-size: 24rpx;
        font-weight: 400;
        line-height: 42rpx;
        color: #FFFFFF;
        margin-top: 4rpx;
      }
    }
    &-title{
      font-size: 32rpx;
      font-weight: 600;
      line-height: 38rpx;
      color: #333333;
      margin-top: 32rpx;
    }
    &-info{
      display: flex;
      align-items: center;
      &-left{
        display: flex;
        align-items: center;
      }
      &-image{
        width: 64rpx;
        height: 64rpx;
        background: rgba(220, 220, 220, 0.39);
        border-radius: 50%;
        border: 2rpx solid rgba(255, 255, 255, 1);
        margin-right: 8rpx;
      }
      &-name{
        font-size: 32rpx;
        font-weight: 700;
        line-height: 37.5rpx;
        color: rgba(56, 56, 56, 1);
        margin-right: 20rpx;
      }
      &-date{
        font-size: 24rpx;
        font-weight: 400;
        line-height: 42rpx;
        color: #999999;
      }
    }
    &-desc{
      font-size: 30rpx;
      font-weight: 400;
      line-height: 40rpx;
      color: #333333;
      margin-top: 12rpx;
    }
  }
  &-product{
    box-sizing: border-box;
    margin-bottom: 40rpx;
    width: 100%;
    padding: 0 20rpx;
    &-header{
      font-size: 32rpx;
      font-weight: 700;
      line-height: 46.34rpx;
      color: rgba(56, 56, 56, 1);
      display: flex;
      align-items: center;
      &-icon{
        width: 32rpx;
        height: 32rpx;
        margin-right: 14rpx;
      }
    }
    &-item{
      width: 100%;
      display: flex;
      align-items: center;
      height: 144rpx;
      border-radius: 20rpx;
      background: rgba(247, 247, 247, 1);
      position: relative;
      margin-top: 20rpx;
      &-img{
        width: 120rpx;
        height: 120rpx;
        border-radius: 20rpx;
        display: block;
      }
      &-info{
        height: 100%;
        width: 380rpx;
        margin-left: 20rpx;
        display: flex;
        flex-direction: column;
        justify-content: center;
        &-text{
          font-size: 30rpx;
          font-weight: 400;
          line-height: 43.44rpx;
          color: rgba(34, 34, 34, 1);
        }
        &-price{
          font-size: 32rpx;
          font-weight: 700;
          line-height: 37.5rpx;
          color: var(--custom-brand-color);
          margin-top: 14rpx;
          text{
            font-size: 24rpx;
          }
        }
      }
      &-buy{
        width: 180rpx;
        height: 64rpx;
        border-radius: 100px;
        background: var(--custom-brand-color);
        display: flex;
        justify-content: center;
        align-items: center;
        font-size: 32rpx;
        font-weight: 400;
        line-height: 32rpx;
        color: rgba(255, 255, 255, 1);
      }
      &-card{
        width: 48rpx;
        height: 48rpx;
        &-one{
          position: absolute;
          right: 24rpx;
          top: 50%;
          transform: translateY(-50%);
        }
      }
    }
  }
  &-separate{
    width: 702rpx;
    height: 1px;
    background-color: rgba(238,238,238,0.8);
    margin: 0 auto;
  }
}
.comment-popup{
  width: 100vw;
  height: 100vh;
  background: rgba(0, 0, 0, 0.39);
  position: fixed;
  left: 0;
  top: 0;
  &-content{
    width: 100%;
    height: 70vh;
    position: absolute;
    left: 0;
    bottom: 0;
    overflow-y: auto;
  }
}
.page-title{
  width: 360rpx;
  overflow:hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  -o-text-overflow:ellipsis;
  text-align: center;
}
.mphtml /deep/ img{
  display: flex;
}
/deep/.mescroll-totop{
  bottom: 150rpx !important;
}
.share-list{
  width: 450rpx;
  &-one{
    margin: 0;
    border-radius: 0;
    border-bottom: 1px solid #eee;
    background-color: white;
    height: 90rpx;
    display: flex;
    align-items: center;
    padding-left: 50rpx;
    &-icon{
      width: 45rpx;
      height: 45rpx;
      margin-right: 20rpx;
    }
  }
}
</style>
