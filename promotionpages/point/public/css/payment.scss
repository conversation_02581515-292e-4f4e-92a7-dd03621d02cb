@mixin wrap {
	margin: $ns-margin;
	padding: $ns-padding;
	border-radius: $ns-border-radius;
	background: #fff;
	position: relative;
}

.align-right{
	text-align: right;
}

.inline{
	display: inline!important;
}

.order-container {
	height: 100%;
	padding-bottom: 130rpx;
	padding-bottom: calc(130rpx + constant(safe-area-inset-bottom));
	padding-bottom: calc(130rpx + env(safe-area-inset-bottom));
}

.address-wrap {
	@include wrap;
	min-height: 100rpx;
	
	.icon{
		width: 80rpx;
		height: 80rpx;
		display: flex;
		justify-content:center;
		align-items:center;
		border-radius: 50%;
		position: absolute;
		top: 50%;
		transform: translateY(-50%);
		margin-right: 20rpx;
		
		.iconfont{
			line-height: 1;
			color: #fff;
			font-size: 36rpx;
		}
	}
	
	.address-info {
		padding-left: 100rpx;
		padding-right: 40rpx;
		
		.info {
			display: flex;
			
			text{
				flex: 1;
				
				&:last-of-type{
					text-align: right;
					color: #999;
				}
			}
		}
		
		.detail {
			line-height: 1.3;
		}
	}
	
	.address-empty{
		line-height: 100rpx;
	}
	
	.cell-more {
		position: absolute;
		top: 50%;
		transform: translateY(-50%);
		right: 10rpx;
		
		.iconfont{
			color: #999
		}
	}
}

.mobile-wrap{
	@include wrap;
	
	.form-group {
		.form-item{
			display: flex;
			line-height: 50rpx;
			
			.text{
				display: inline-block;
				line-height: 50rpx;
				padding-right: 10rpx;
			}
			
			.placeholder{
				line-height: 50rpx;
			}
			
			.input{
				flex: 1;
				height: 50rpx;
				line-height: 50rpx;
			}
		}
	}
}

.order-cell{
	display: flex;
	margin: 20rpx 0;
	align-items: center;
	background: #fff;
	line-height: 40rpx;
	
	.tit {
		text-align: left;
	}
	
	.box {
		flex: 1;
		padding: 0 20rpx;
		line-height: inherit;
		min-height: 40rpx;
		display: flex;
		justify-content: space-between;
		align-items: center;
		
		.textarea{
			min-height: 40rpx;
			line-height: 1.5;
			padding: 1rpx 0;
			box-sizing: border-box;
		}
	}
	
	.iconfont {
		color: #bbb;
		font-size: 28rpx;
	}
	
	.order-pay{
		padding: 0;
		
		text{
			display: inline-block;
			margin-left: 6rpx;
		}
	}
}

.site-wrap {
	@include wrap;
	
	.site-header{
		display: flex;
		align-items:center;
		
		.icondianpu{
			display: inline-block;
			line-height: 1;
			margin-right: 12rpx;
			font-size: 30rpx;
		}
	}
	
	.site-body {
		.goods-wrap {
			margin-bottom: 20rpx;
			display: flex;
			position: relative;
			
			&:last-of-type{
				margin-bottom: 0;
			}
			
			.goods-img {
				width: 180rpx;
				height: 180rpx;
				padding: 20rpx 0 0 0;
				margin-right: 20rpx;
					
				image {
					width: 100%;
					height: 100%;
					border-radius: $ns-border-radius;
				}
			}
					
			.goods-info {
				flex: 1;
				position: relative;
				padding: 20rpx 0 0 0;
				max-width: calc(100% - 200rpx);
					
				.goods-name {
					display: -webkit-box;
					-webkit-box-orient: vertical;
					-webkit-line-clamp: 2;
					overflow: hidden;
					line-height: 1.5;
					font-size: 28rpx;
				}
					
				.goods-sub-section {
					width: 100%;
					line-height: 1.3;
					display: flex;
					
					.goods-price {
						font-weight: 700;
						font-size: 15px;
					}
					
					.unit {
						font-weight: normal;
						font-size: 24rpx;
						margin-right: 2rpx;
					}
					
					view{
						flex: 1;
						line-height: 1.3;
						&:last-of-type{
							text-align: right;
							
							.iconfont{
								line-height: 1;
								font-size: 26rpx;
							}
						}
					}
				}
			}
		}
	}
}

.order-checkout{
	@include wrap;
}

.order-money{
	@include wrap;
	
	.order-cell {
		.box {
			padding: 0;
			
			.operator{
				font-size: 24rpx;
				margin-right: 6rpx;
			}
		}
	}
}

.order-buyer-message{
	@include wrap;

	.textarea{
		width: 100%;
	}
}

.order-submit{
	position: fixed;
	z-index: 5;
	left: 0;
	bottom: 0;
	width: 100vw;
	height: 120rpx;
	background: #fff;
	box-shadow: 0 0px 10px rgba(0,0,0,.1);
	text-align: right;
	display: flex;
	padding-bottom: 0;  
	padding-bottom: constant(safe-area-inset-bottom);  
	padding-bottom: env(safe-area-inset-bottom);  
	
	.order-settlement-info{
		flex: 1;
		height: 120rpx;
		line-height: 120rpx;
		
		.money{
			font-size: 36rpx;
		}
	}
	
	.submit-btn {
		height: 80rpx;
		margin: 20rpx;
		display: flex;
		justify-content: center;
		align-items: center;
		button{
			line-height: 2.6;
		}
	}
}

.popup {
	width: 100vw;
	background: #fff;
	border-top-left-radius: 24rpx;
	border-top-right-radius: 24rpx;
	
	.popup-header{
		height: 90rpx;
		display: flex;
		align-items:center;
		padding: 0 30rpx;
		
		& > view {
			flex: 1;
			line-height: 1;
		}
		
		.tit {
			font-size: 32rpx;
			font-weight: 600;
		}
		
		.vice-tit{
			margin-right: 20rpx;
		}
	}
	
	.popup-body{
		height: calc(100% - 210rpx);
		
		&.safe-area{
			height: calc(100% - 278rpx);
		}
	}
}