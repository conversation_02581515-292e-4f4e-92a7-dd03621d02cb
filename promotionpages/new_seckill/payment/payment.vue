<template>
	<view class="order-container" :class="themeStyle" :style="[themeColorVar]">
		<!-- #ifdef MP-WEIXIN -->
		<view class='nav bg-white' :style="{ height: navHeight + 'px' }">
			<view class='nav-title'>
				<image  :src="$util.img('public/static/youpin/order/back.png')" mode='aspectFit' class='back' @click='openPopup()'></image>
				<text>确认订单</text>
			</view>
		</view>
		<!-- #endif -->
    <!-- #ifdef H5 -->
    <view class='nav bg-white' :style="{ height: navHeight + 'px' }" v-if="isOnXianMaiApp">
      <view class='nav-title'>
        <image  :src="$util.img('public/static/youpin/order/back.png')" mode='aspectFit' class='back' @click='openPopup()'></image>
        <text>确认订单</text>
      </view>
    </view>
    <!-- #endif -->
		<view class="wrapper" :style="{ marginTop: navHeight + 'px'}">
			<!-- 收货地址 -->
			<view class="address-wrap">
				<view class="icon">
					<image :src="$util.img('public/static/youpin/order/icon-no-pay-address.png')" mode=""></image>
				</view>
				<!-- <view class="address-info" @click="selectAddress"> -->
				<view class="address-info" @click="getChooseAddress">
					<block v-if="orderPaymentData.member_address">
						<view class="info">
							<text>{{ orderPaymentData.member_address.name }}</text>
							<text>{{ orderPaymentData.member_address.mobile }}</text>
						</view>
						<view class="detail">
							<text>{{ orderPaymentData.member_address.full_address }} {{ orderPaymentData.member_address.address }}</text>
						</view>
					</block>
					<block v-else>
						<view class="address-empty">
							<text>选择收货地址</text>
						</view>
					</block>
					<view class="cell-more"><view class="iconfont iconright"></view></view>
				</view>
			</view>

			<!-- 店铺 -->
			<view class="site-wrap" v-if="orderPaymentData.goodsInfo" >
				<view class="site-header" style="visibility: hidden;">
					<view class="iconfont icondianpu"></view>
					<text class="site-name">{{ orderPaymentData.goodsInfo.site_name }}</text>
				</view>
				<view class="site-body">
					<view class="goods-wrap">
						<navigator hover-class="none" class="goods-img" :url="'/pages/goods/detail/detail?sku_id=' + orderPaymentData.goodsInfo.sku_id">
							<image :src="$util.img(orderPaymentData.goodsInfo.sku_image)" @error="imageError(orderPaymentData)" mode="aspectFill"></image>
						</navigator>
						<view class="goods-info">
							<navigator hover-class="none" :url="'/pages/goods/detail/detail?sku_id=' + orderPaymentData.goodsInfo.sku_id" class="goods-name">{{ orderPaymentData.goodsInfo.goods_name }}</navigator>
							<view class="goods-sub-section">
								<view>
									<text>{{ orderPaymentData.goodsInfo.spec_name }}</text>
								</view>
								<view>
									<text>x {{orderPaymentData.seckillBuy.num}}</text>
								</view>
							</view>
							<view class="goods-price">
								<text>
									<text class="unit">￥</text>
									<text class="price">{{ orderPaymentData.seckillBuy.seckill_price }}</text>
								</text>
							</view>
						</view>
					</view>
				</view>
				<!-- 运费金额 -->
				<view class="order-money">
					<view class="order-cell">
						<text class="tit">运费</text>
						<view class="box align-right">
              <text class="" v-if="parseFloat(orderPaymentData.delivery_money)>0">
                <text class="ns-font-size-sm">{{ $lang('common.currencySymbol') }}</text>
                <text>{{ orderPaymentData.delivery_money | moneyFormat }}</text>
              </text>
              <text v-else>包邮</text>
						</view>
					</view>
				</view>
				<view class="site-footer">
					<view class="order-cell">
						<text class="tit">订单备注</text>
						<view class="box">
							<input
								type="text"
								value=""
								placeholder="请填对本次交易的说明"
								class="ns-font-size-base"
								placeholder-style="{color:#CCCCCC}"
								v-model="buyer_message"
								maxlength='50'
							/>
						</view>
					</view>
					<view class="order-cell">
						<view class="box align-right order-pay">
							共 {{orderPaymentData.seckillBuy.num}} 件商品
							<text>
微信支付								小计：￥<text class="pay-money"> {{ orderPaymentData.seckillBuy.goods_money  }}</text>
							</text>
						</view>
					</view>
				</view>
			</view>

			<view class="site-wrap payment-methods">
				<view class="item" :class="{'disable':method.disable}"  v-for="method in otherPaymentMethods" @click="changePayment(method)">
					<image class="icon" :src="method.icon"></image>
					<view class="title">
						<text class="text">{{method.name}}</text>
						<text class="desc">{{method.desc}}</text>
					</view>
<!--					<image class="checkbox" v-show="paymentMethod==method.key" :src="$util.img('public/static/youpin/maidou/get.png')"></image>-->
<!--					<image class="checkbox" v-show="paymentMethod!=method.key" :src="$util.img('public/static/youpin/maidou/noneget.png')"></image>-->
          <uni-icons class="checkbox" type="checkbox-filled" :color="paymentMethod==method.key ? 'var(--custom-brand-color)' : '#ccc'" size="18"></uni-icons>
				</view>
			</view>

			<view class="order-submit bottom-safe-area">
				<view class="order-settlement-info">
					<text>实付金额：</text>
					<text class="text-color">
						{{ $lang('common.currencySymbol') }}
						<text class="money">{{ orderPaymentData.seckillBuy.goods_money  }}</text>
					</text>
				</view>
				<view class="submit-btn"><button type="primary" size="mini" @click="showGoWeixin">{{orderCreateData.is_balance==1?'余额支付':'微信支付'}}</button></view>
			</view>
		</view>


		<!-- 未支付返回上一页提示弹窗 -->
		<uni-popup ref="popup" class="my-popup-dialog">
		    <view class="popup-dialog">
				<view class="popup-dialog-header">提示</view>
				<view class="popup-dialog-body">是否放弃本次付款？</view>
				<view class="popup-dialog-footer">
					<view class="button white" @click="toBack()">放弃</view>
					<button class="button red" @click="closePopup()">继续付款</button>
				</view>
			</view>
		</uni-popup>
		<!-- 取消支付弹窗 -->
		<uni-popup ref="popupToList" class="my-popup-dialog" :mask-click="false">
		    <view class="popup-dialog">
				<view class="popup-dialog-header">提示</view>
				<view class="popup-dialog-body">订单超过2小时未支付将被取消，请尽快完成支付哦！~</view>
				<view class="popup-dialog-footer">
					<button class="button red" @click="toWaitPayList()">我知道了</button>
				</view>
			</view>
		</uni-popup>

		<!-- 支付密码弹窗 -->
		<uni-popup ref="payPassword" :custom="true">
			<view class="pay-password">
				<block v-if="orderPaymentData.member_account.is_pay_password == 0">
					<view class="title">为了您的账户安全,请先设置您的支付密码</view>
					<view class="tips">可到"个人中心-设置-支付密码设置"中设置</view>
					<view class="btn ns-bg-color ns-border-color" @click="setPayPassword">立即设置</view>
					<view class="btn white ns-border-color ns-text-color" @click="noSet">暂不设置</view>
				</block>
				<block v-else>
					<view class="popup-title">
						<image class="cha_close" :src="$util.img('public/static/youpin/maidou/cha.png')" mode="" @click="close_pay()"></image>
						<view class="title">请输入支付密码</view>
					</view>
					<view class="money-box">
						<view class="total-fee">总金额￥{{orderPaymentData.seckillBuy.goods_money | moneyFormat}}</view>
						<view class="balance">(当前余额￥{{orderPaymentData.member_account.balance_money}})</view>
					</view>

					<view class="password-wrap">
						<myp-one :maxlength="6" :is-pwd="true" @input="input" ref="input" :auto-focus="isFocus" type="box"></myp-one>
						<view class=" ns-text-color ns-font-size-sm forget-password error-tips" v-if="errMsg">{{errMsg}}</view>
						<view class="align-right"><text class="ns-text-color ns-font-size-sm forget-password" @click="setPayPassword">忘记密码</text></view>
					</view>
				</block>
			</view>
		</uni-popup>

    <!-- 在app中需要提示用户跳转微信小程序支付 -->
    <uni-popup ref="popupGoWeixin" class="my-popup-dialog">
      <view class="popup-dialog">
        <view class="popup-dialog-header">提示</view>
        <view class="popup-dialog-body">需要跳转到微信APP，使用先迈小程序支付订单金额</view>
        <view class="popup-dialog-footer">
          <button class="button white" @click="$refs.popupGoWeixin.close()">取消</button>
          <view class="button red" @click="comfirmGoWeixin">去支付</view>
        </view>
      </view>
    </uni-popup>

		<loading-cover ref="loadingCover"></loading-cover>
	</view>
</template>

<script>
import payment from '../public/js/payment.js';
import uniPopup from '@/components/uni-popup/uni-popup.vue';
import golbalConfig from "../../../common/mixins/golbalConfig";
// #ifdef H5
import {isOnXianMaiApp} from "../../../common/js/h5/appOP";
// #endif
import UniIcons from "../../../components/uni-icons/uni-icons.vue";

export default {
	components: {
    UniIcons,
		uniPopup,
	},
	data() {
		return {
			time:null,
			navHeight: 0,
      choiceWechatAdderError:false,  //直接选择微信地址失败
      ischoiceWechatAdder:false,  //是否直接选择微信地址
      postWechatAdder:false,  //微信地址添加到后台请求完成
      // #ifdef H5
      isOnXianMaiApp:isOnXianMaiApp,
      // #endif
		};
	},
	computed: {
		themeStyle() {
			return 'theme-' + this.$store.state.themeStyle;
		},
		otherPaymentMethods(){
			return [{
				icon: this.$util.img('public/static/youpin/paySuccess-icon.png'),
				name: '微信支付',
				desc: '',
				key: 'WECHAT',
				disable: false
			},{
				icon: this.$util.img('public/static/youpin/balance.png'),
				name: '余额支付',
				desc: `(当前余额￥${this.orderPaymentData.member_account.balance_money}）`,
				key: 'BALANCE',
				disable: Number(this.orderPaymentData.member_account.balance_money) < Number(this.orderPaymentData.seckillBuy.goods_money)
			}]
		}
	},
	onLoad() {
		uni.getSystemInfo({
		  success: res => {
			//导航高度
			let navHeight = res.statusBarHeight + 46;
			this.navHeight = navHeight;
        // #ifdef H5
        if(!this.isOnXianMaiApp){
          this.navHeight=0;
        }
        // #endif
		  },
		  fail(err) {
			console.log(err);
		  }
		})
	},
	onShow() {},
	mixins: [payment,golbalConfig],
	methods: {
		toShopDetail(e){
			this.$util.redirectTo('/otherpages/shop/index/index', { site_id: e});
		},
		openPopup(){
			// 未支付返回上一页提示弹窗
			this.$refs.popup.open()
		},
		closePopup(){
			this.$refs.popup.close();
		},
		openCouponInstructionsPopup(){
			// 未支付返回上一页提示弹窗
			this.$refs.couponInstructions.open()
		},
		closeCouponInstructionsPopup(){
			this.$refs.couponInstructions.close();
		},
		/**
		 * 关闭弹出层
		 * @param {Object} ref
		 */
		closePopupCoupon(ref) {
			if (this.tempData) {
				Object.assign(this.orderCreateData, this.tempData);
				Object.assign(this.orderPaymentData, this.tempData);
				this.tempData = null;
				this.$forceUpdate();
			}
			this.$refs[ref].close();
		},

		close_pay(){
			this.$refs.payPassword.close();
			this.errMsg = ''
		},

		toBack() {
			this.closePopup()
			uni.navigateBack()
		},
		toWaitPayList() {
			this.$refs.popupToList.close();
      this.$util.redirectTo("/pages/order/list/list?status=waitpay", {}, "redirectTo")
			uni.removeStorage({
				key: 'orderCreateData',
				success: () => {}
			});
		},
		/**
		 * 一键获取地址
		 */
		getChooseAddress() {
			var that = this;
			if(!this.orderPaymentData.member_address) {
        // #ifdef MP-WEIXIN
        this.ischoiceWechatAdder=true;
				uni.chooseAddress({
					success: res => {
						console.log('success.res')
						console.log(res)
						if (res.errMsg == 'chooseAddress:ok') {
							this.saveAddress({
								name: res.userName, // 收货人姓名,
								mobile: res.telNumber, // 手机号
								province: res.provinceName, // 省
								city: res.cityName, // 市
								district: res.countyName, // 县
								address: res.detailInfo, // 详细地址
								full_address: res.provinceName + ' ' + res.cityName + ' ' + res.countyName
							});
						} else {
							this.$util.showToast({
								title: res.errMsg
							});
						}
					},
					fail: res => {
						console.log('fail.res')
						console.log(res)
						this.$util.showToast({
							title: '获取微信地址失败',
							success: () => {
								setTimeout(() => {
									that.selectAddress()
								}, 1500)
							}
						});
            this.postWechatAdder=true;
					}
				});
        // #endif
        // #ifdef H5
        this.selectAddress()
        // #endif
			} else {
				this.selectAddress()
			}
		},
		/**
		 * 保存微信地址
		 * @param {Object} params
		 */
		saveAddress(params) {
			this.$api.sendRequest({
				url: '/api/memberaddress/addthreeparties',
				data: params,
				success: res => {
					if (res.code >= 0) {
					} else {
						this.$util.showToast({
							title: res.message
						});
            this.choiceWechatAdderError=true;
					}
				},
        complete: ()=>{
          this.postWechatAdder=true;
        }
			});
		},
		imageError() {
			this.orderPaymentData.goodsInfo.sku_image = this.$util.getDefaultImage().default_goods_img;
			this.$forceUpdate();
		},
    /**
     *分享参数组装
     */
    getSharePageParams() {
      return this.$util.unifySharePageParams('/otherpages/shop/home/<USER>', this.shareTitle || '先迈商城',
          '',{}, this.$util.img(this.shareImg) || this.$util.img('public/static/youpin/home_share.jpg'))
    }
	},
  /**
   * 自定义分享内容
   * @param {Object} res
   */
  onShareAppMessage(res) {
    let { title, link, imageUrl, query } = this.getSharePageParams()
    return this.$buriedPoint.pageShare(link , imageUrl, title);
  },
};
</script>

<style lang="scss" scoped>
	/deep/ .uni-navbar--border {
		border-bottom-width: 0;
	}
</style>
<style lang="scss" scoped>
	/deep/ .my-popup-dialog .uni-popup__wrapper-box {
		max-width: 540rpx;
		width: 540rpx;
		border-radius: 20rpx;
	}
	/deep/ .coupon-instructions .uni-popup__wrapper-box {
		max-width: 620rpx;
		width: 620rpx;
		border-radius: 20rpx;
	}
	.popup-dialog {
		overflow: hidden;
		background: #FFFFFF;
		box-sizing: border-box;
		.popup-dialog-header {
			height: 106rpx;
			line-height: 106rpx;
			text-align: center;
			font-size: 36rpx;
			color: #333333;
			font-weight: bold;
		}
		.popup-dialog-body {
			color: #656565;
			text-align: center;
			padding: 0 30rpx;
		}
		.popup-dialog-footer {
			margin: 0 32rpx;
			height: 140rpx;
			display: flex;
			align-items: center;
			justify-content: space-around;
			.button {
				width: 220rpx;
				height: 68rpx;
				line-height: 68rpx;
				text-align: center;
				border-radius: 34rpx;
				box-sizing: border-box;
				margin: 0;
				&.white {
					color: var(--custom-brand-color);
					background: #FFFFFF;
					border: 1rpx solid var(--custom-brand-color);
				}
				&.red {
					color: #FFFFFF;
					background: var(--custom-brand-color);

				}
			}
		}
	}
</style>


<style lang="scss">
@import '../public/css/payment.scss';
</style>
