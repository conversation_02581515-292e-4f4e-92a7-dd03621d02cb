import htmlParser from '@/common/js/html-parser.js';
import apiurls from "@/common/js/apiurls.js";
import system from "@/common/js/system.js";

import { Weixin } from '@/common/js/wx-jssdk.js';
import {scenePare,query_to_scene} from '../../../../common/js/scene_handle'

export default {
	data() {
		return {
			skuId: 0,
			// 商品详情
			goodsSkuDetail: {
				goods_id: 0
			},
			address_info:{},  //配送地址
			delivery_money:0, //邮费
			district_id:null, //配送的区id
			// 店铺详情
			shopInfo: {
				logo: '',
				shop_baozh: 0,
				shop_qtian: 0,
				shop_zhping: 0,
				shop_erxiaoshi: 0,
				shop_tuihuo: 0,
				shop_shiyong: 0,
				shop_shiti: 0,
				shop_xiaoxie: 0
			},
			cartCount: 0, // 购物车商品数量
			whetherCollection: 0,
			// 媒体,图片,视频
			// 解决每个商品SKU图片数量不同时，无法切换到第一个，导致轮播图显示不出来
			swiperInterval: 1,
			swiperAutoplay: false,
			swiperCurrent: 1,
			switchMedia: 'img',
			couponList: [], //优惠券列表
			couponBtnSwitch: false, //获取优惠券防止重复提交
			//评价数量
			token: "",
			poster: "-1", //海报
			posterMsg: "", //海报错误信息
			posterHeight: 0,
			manjianList: {}, //满减活动列表
			//评价
			goodsEvaluate: {
				member_headimg: '',
				member_name: '',
				content: '',
				images: [],
				create_time: 0,
				sku_name: ''
			},
			//组合套餐
			bundling: [{
				bundling_goods: {
					bl_name: '',
					sku_image: ''
				}
			}],
			memberId: 0,
			detailTab: 'productDetail',
			afterSale:null,
			service: null,
			//是否开启预览，0：不开启，1：开启
			preview: 0,
			isDistributor:false,
			time:0,
			goodSeckillDetail:{},

			sharePopupOptions:[],
			isShowCanvas:false,
			canvasOptions:{
				width:'560',
				height:'830',
				borderRadius:'20rpx'
			},
			is_shopper:0, //是否是店主
			sharePopupOptions_share:[],
			canvasOptions_share: {
				width: '420',
				height: '336',
				borderRadius: '10',
				scale:0.5,
			},
			buyerList: [],
			addressIndex: 0,
			addressList:[]
		}
	},
	computed: {
		share_shop_id(){
			return this.$store.state.share_shop_id;
		},
		is_audit_mode(){
			return this.$store.state.audit_mode;
		}
	},
	onLoad(data) {
		// 小程序扫码进入
		if (data.scene) {
			scenePare(false,data);
		}
		this.skuId = data.sku_id || 0;
		this.preview = data.preview || 0;
		if (data.source_member) uni.setStorageSync('source_member', data.source_member);
		uni.removeStorageSync('selectGood');
		uni.removeStorageSync('selectGoodType');
	},
	async onShow() {
		// 刷新多语言
		this.$langConfig.refresh();
		await system.wait_staticLogin_success();
		//登录后查询
		this.token = uni.getStorageSync('token');
		this.is_shopper=uni.getStorageSync('is_shopper');
		if(this.token){
			await this.getAddressList();
		}
		//同步获取商品详情
		await this.getGoodsSkuDetail();
		if (this.token != '' && this.preview == 0) {
			// this.getCartCount();
			// this.getWhetherCollection();
			this.getMemberId();
		}
		// 开启预览，禁止任何操作和跳转
		if (this.preview == 0) {
			//修改商品信息
			this.modifyGoodsInfo();
			//商品评论
			// this.getGoodsEvaluate();
			//组合套餐
			if(this.addonIsExit.bundling){
				// this.getBundling();
			}
		}
		await this.getDeliveryMoney()
	},
	onHide() {
		this.couponBtnSwitch = false;
		this.$buriedPoint.browseGoods({sku_id:this.skuId})
	},
	onUnload(data){
		this.$buriedPoint.browseGoods({sku_id:this.skuId})
		//当通过商品分销分享过来的，当用户离开此页面则清除分销客ID
		this.$store.dispatch('writeShareMemberId',null); //清除分享过来的分销客memberIP
	},
	/**
	 * 自定义分享内容
	 * @param {Object} res
	 */
	onShareAppMessage(res) {
		system.goodsShare(this.goodsSkuDetail.goods_id)
		let share_data=this.getSharePageParams();
		let shareImgPath = this.shareImgPath || share_data.imageUrl
		return this.$buriedPoint.pageShare( share_data.link, shareImgPath, this.goodsSkuDetail.goods_name,true,{goods_id:this.goodsSkuDetail.goods_id});
	},
	/**
	 * 自定义分享朋友圈-暂时只支持安卓
	 * @param {Object} res
	 */
	onShareTimeline(res) {
		system.goodsShare(this.goodsSkuDetail.goods_id)

		let share_data=this.getSharePageParams();
		return {
			title: share_data.desc,
			imageUrl: share_data.imageUrl,
			query: share_data.query,
			success: res => {},
			fail: res => {}
		};
	},
	methods: {
		async buyerFun() {
			try{
				let res = await this.$api.sendRequest({
					url: apiurls.usershareexperienceListUrl,
					async: false,
					data: {
						page_size:100,
						page: 1,
						goods_id: this.goodsSkuDetail.goods_id
					},
				});
				this.buyerList = res.data.list
				// this.buyerList = [{}, {}]
			}catch{}
		},
		buyersDetailFun(item) {
			if(item.content_type == 2) {
        this.$util.diyRedirectTo({
          wap_url: item.content_link
        })
      }else{
				this.$util.redirectTo(`/promotionpages/seeding/seeding_detail/seeding_detail?id=${item.id}`)
			}
		},
		buyersMoreFun(show_publish) {
			let arr = []
			arr.push({
				goods_id: this.goodsSkuDetail.goods_id,
				goods_image: this.$util.img(this.goodsSkuDetail.sku_image)
			})
			uni.setStorageSync('selectGood', JSON.stringify(arr))
			uni.setStorageSync('selectGoodType', 1)
			if(show_publish){
				this.$util.redirectTo(`/promotionpages/seeding/seeding-add/seeding-add?goods_id=${this.goodsSkuDetail.goods_id}`);
			}else{
				this.$util.redirectTo(`/promotionpages/seeding/seeding-list/seeding-list?goods_id=${this.goodsSkuDetail.goods_id}`);
			}
		},
		getShareImg(value){
			this.shareImgPath = value
		},
		//获取分享信息
		getShareInfo() {
			return new Promise((resolve, reject) => {
				system.checkToken().then(res => {
					// console.log('this.share_shop_id',this.share_shop_id)
					// 判断用户或者分销客进入分享商品详情是不是自己绑定的店铺，不是则提示不能购买弹窗
					resolve();
				})
			})

		},
		//h5播放视频
		openVideo(){
			this.$refs.videoPopup.open();
		},

		//秒杀倒计时结束
		onFinish(){
			if(this.goodSeckillDetail.seckill_status == 0){
				this.getGoodsSkuDetail(this.skuId);
			}
			if(this.goodSeckillDetail.seckill_status == 1){
				let path = `/pages/goods/detail/detail?sku_id=${this.skuId}`;
				this.$util.redirectTo(path, {}, 'redirectTo');
			}
		},
		// 获取商品详情
		async getGoodsSkuDetail(skuId) {
			await this.getShareInfo();
			this.skuId = skuId || this.skuId;
			let datas = {
				sku_id: this.skuId
			}
			if(this.district_id){
				datas.district_id = this.district_id;
			}
			let res = await this.$api.sendRequest({
				url: this.$apiUrl.getSeckillGoodsDetail,
				async: false,
				data: datas
			});
			if(res.code==-10030){
				return;
			}
			let data = res.data;
			if (data.goods_sku_detail != null) {
				this.goodsSkuDetail = data.goods_sku_detail;
				this.goodsSkuDetail.preview = this.preview;
				this.shopInfo = data.shop_info;

				if (this.skuId == 0) this.skuId = this.goodsSkuDetail.sku_id;

				//媒体
				if (this.goodsSkuDetail.video_url) this.switchMedia = "video";

				this.goodsSkuDetail.sku_images = this.goodsSkuDetail.sku_images.split(",");

				this.goodsSkuDetail.unit = this.goodsSkuDetail.unit || "件";

				this.goodsSkuDetail.show_price = this.goodsSkuDetail.retail_price;

				if(this.addressList.length<1){
					if(data.address_info) this.address_info = data.address_info
					if(data.address_info && data.address_info.district_id){
						this.district_id = data.address_info.district_id
					}
				}

				// 当前商品SKU规格
				if (this.goodsSkuDetail.sku_spec_format) this.goodsSkuDetail.sku_spec_format = JSON.parse(this.goodsSkuDetail.sku_spec_format);

				// 商品属性
				if (this.goodsSkuDetail.goods_attr_format) {
					let goods_attr_format = JSON.parse(this.goodsSkuDetail.goods_attr_format);
					this.goodsSkuDetail.goods_attr_format = JSON.parse(this.goodsSkuDetail.goods_attr_format);
					this.goodsSkuDetail.goods_attr_format = this.$util.unique(this.goodsSkuDetail.goods_attr_format, "attr_id");
					for (var i = 0; i < this.goodsSkuDetail.goods_attr_format.length; i++) {
						for (var j = 0; j < goods_attr_format.length; j++) {
							if (this.goodsSkuDetail.goods_attr_format[i].attr_id == goods_attr_format[j].attr_id && this.goodsSkuDetail.goods_attr_format[
									i].attr_value_id != goods_attr_format[j].attr_value_id) {
								this.goodsSkuDetail.goods_attr_format[i].attr_value_name += "、" + goods_attr_format[j].attr_value_name;
							}
						}
					}
				}

				// 商品SKU格式
				if (this.goodsSkuDetail.goods_spec_format) this.goodsSkuDetail.goods_spec_format = JSON.parse(this.goodsSkuDetail.goods_spec_format);

				uni.setNavigationBarTitle({
					title: this.goodsSkuDetail.goods_name
				});

				// 商品详情
				// if (this.goodsSkuDetail.goods_content) this.goodsSkuDetail.goods_content = htmlParser(this.goodsSkuDetail.goods_content);

				// 秒杀倒计时
				if(data.seckill_info){
					// 秒杀只能限购一件
					this.goodsSkuDetail.buy_num = data.seckill_info.buy_limit
					this.goodsSkuDetail.seckill_id = data.seckill_info.seckill_id
					this.$refs.countdown.reset()
					//倒计时时间
					this.time = data.seckill_info.left_time * 1000
					this.goodSeckillDetail = data.seckill_info
				}
				this.getService();
				if (this.$refs.loadingCover) this.$refs.loadingCover.hide();
				// #ifdef MP-WEIXIN
				this.newCommQrcode()
				// #endif
				// #ifdef H5
				this.setWechatShare();
				// #endif

				//获取买家秀
				await this.buyerFun();
			} else {
				this.$util.redirectTo('/pages/index/index/index', {}, 'reLaunch');
			}
		},
		/**
		 * 计算运费
		 * @returns {Promise<void>}
		 */
		async getDeliveryMoney(){
			let nums = 1
			// 多规格商品
			if(this.goodsSkuDetail.sku_spec_format){
				nums = this.$refs.goodsSku.number
			}
			let res = await this.$api.sendRequest({
				url: this.$apiUrl.deliveryMoneyUrl,
				async: false,
				data: {
					district_id: this.district_id,
					sku_id: this.skuId,
					nums
				}
			});
			if(res.code == 0){
				this.delivery_money = res.data.delivery_money
			}
		},
		/**
		 *分享参数组装(注意需要分享的那一刻再调此方法)
		 */
		getSharePageParams(){
			let share_member_id = uni.getStorageSync('member_id');
			let params = {}
			params.sku_id = this.skuId
			// 是分销客才带上分销客ID
			if (this.isDistributor) params.share_member_id = share_member_id
			// if (this.memberId) params.source_member = this.memberId
			let share_data=this.$util.unifySharePageParams('/promotionpages/new_seckill/detail/detail','先迈商城',
				`￥${this.goodsSkuDetail.price} 秒杀${this.goodsSkuDetail.goods_name}`,params,this.$util.img('public/static/youpin/seckill_share.jpg'))
			return share_data;
		},
		newCommQrcode(){
			let share_data=this.getSharePageParams();
			let url=share_data.link;
			let queryDict=this.$util.GetRequestQuery(url);
			let scene=query_to_scene(queryDict);
			this.$api.sendRequest({
				url: '/api/Website/newCommQrcode',
				data: {
					path:share_data.path.slice(1),
					scene
				},
				success:(res)=>{
					if(res.code==0){
						this.drawCanvas(res.data.qrcodeUrl)
						this.drawCanvas_share()
						setTimeout(()=>{
							this.$refs.sharePopup_share.open();
						},1000)
					}else{
						this.$util.showToast({
							title:res.message
						})
					}
				}
			});

		},
		drawCanvas(qrcodeUrl){
			this.sharePopupOptions = [
				{
					background:'#fff',
					x: 0,
					y: 0,
					width: 560,
					height: 830,
					type: 'image',
				},
				{
					path:this.$util.img(this.goodsSkuDetail.sku_image),
					x: 0,
					y: 0,
					width: 560,
					height: 560,
					type: 'image',
				},
				{
					text: '￥',
					size: 32,
					color:'#FF1010',
					x: 20,
					y: 620,
					type: 'text',
				},
				{
					text: this.goodsSkuDetail.retail_price,
					size: 52,
					color:'#FF1010',
					fontWeight:'bold',
					x: 60,
					y: 620,
					type: 'text',
				},
				{
					text: '￥'+this.goodsSkuDetail.market_price,
					size: 26,
					color:'#999999',
					x: 30,
					y: 660,
					type: 'text',
					textBaseline:true
				},
				{
					text: this.goodsSkuDetail.goods_name,
					size: 26,
					color:'#333333',
					x: 30,
					y: 700,
					width:310,
					lineNum:3,
					lineHeight:34,
					type: 'text',
				},
				{
					path:qrcodeUrl,
					x: 354,
					y: 580,
					width: 180,
					height: 180,
					type: 'image',
				},
				{
					text: '请使用微信扫码',
					size: 20,
					color:'#999999',
					x: 376,
					y: 786,
					type: 'text',
				},
			]
			this.isShowCanvas = true
		},
		drawCanvas_share() {
			this.sharePopupOptions_share = [{
					background: "#fff",
					x: 0,
					y: 0,
					width: 420,
					height: 336,
					type: 'image',
				},
				{
					path: this.$util.img('https://www.xianmai88.com/static/youpin/share_bg.png'),
					x: 0,
					y: 0,
					width: 420,
					height: 336,
					type: 'image',
				},
				{
					path: this.$util.img(this.goodsSkuDetail.sku_image)+'?image_process=resize,s_200',
					x: 13,
					y: 90,
					width: 231,
					height: 231,
					type: 'image',
				},
				{
					text: '￥',
					size: 32,
					color: '#FF1010',
					x: 260,
					y: 160,
					type: 'text',
				},
				{
					text: this.goodsSkuDetail.retail_price,
					size: 38,
					color: '#FF1010',
					fontWeight: 'bold',
					x: 290,
					y: 160,
					type: 'text',
				},
				{
					text: '￥' + this.goodsSkuDetail.market_price,
					size: 26,
					color: '#999999',
					x: 260,
					y: 200,
					type: 'text',
					textBaseline:true
				},
			]
		},

		/**
		 * 刷新商品详情数据
		 * @param {Object} goodsSkuDetail
		 */
		refreshGoodsSkuDetail(goodsSkuDetail) {
			Object.assign(this.goodsSkuDetail, goodsSkuDetail);

			// 解决轮播图数量不一致时，切换到第一个
			if (this.swiperCurrent > this.goodsSkuDetail.sku_images.length) {
				this.swiperAutoplay = true;
				this.swiperCurrent = 1;
				setTimeout(() => {
					this.swiperAutoplay = false;
				}, 40);
			}
			this.getDeliveryMoney()
		},
		goHome() {
			if (this.preview) return; // 开启预览，禁止任何操作和跳转
			this.$util.redirectTo('/otherpages/shop/home/<USER>', {}, 'reLaunch');
		},
		checkShoppingStatus(){
			let is_shopping_status = uni.getStorageSync('is_shopping_status')
			if(is_shopping_status == 0){
				this.$refs.popupBan.open()
				return true
			}
		},

		redirectDetailsUrl(){
			let share_member_id = this.$store.state.share_member_id;
			let shop_id=uni.getStorageSync('shop_id');
			let path = `/promotionpages/new_seckill/detail/detail?sku_id=${this.skuId}&shop_id=${shop_id}`;
			// 只要之前有传值，普通用户重新登录后还是得重新传一遍
			if (share_member_id) path += '&share_member_id=' + share_member_id;
			if (this.memberId) path += '&source_member=' + this.memberId;
			return path
		},

		//获取购物车数量
		getCartCount() {
			if (this.preview) return; // 开启预览，禁止任何操作和跳转
			this.$store.dispatch('getCartNumber').then((e)=>{
				this.cartCount = e;
			})
		},
		// 立即购买
		async buyNow() {
			if (!this.token && this.preview == 0) {
				let path = await this.redirectDetailsUrl()
				this.$util.toShowLoginPopup(this,null,path);
				return;
			}
			//检查会员是否禁止购物
			if(await this.checkShoppingStatus()) return false

			//当是秒杀中时走秒杀选项，其他时间全部走普通下单
			if(this.goodSeckillDetail.seckill_status == 1){
				// buy_nums 是已购买数量
				if(this.goodSeckillDetail.buy_limit <= this.goodSeckillDetail.buy_nums ){
					this.$util.showToast({
						title: "您秒杀的商品数已超活动上限，无法参与本次秒杀"
					})
					return false;
				}
				//已选择规格直接执行操作
				if(this.goodsSkuDetail.sku_spec_format){
					this.$refs.goodsSku.show("seckill", () => {
					});
				}else{
					this.$refs.goodsSku.type = 'seckill';
					this.$refs.goodsSku.callback = ()=>{
					}
					this.$refs.goodsSku.confirm()
				}
			}else{
				if(this.goodsSkuDetail.sku_spec_format){
					this.$refs.goodsSku.show("buy_now", () => {
						this.getCartCount();
					});
				}else{
					this.$refs.goodsSku.type = 'buy_now';
					this.$refs.goodsSku.callback = ()=>{
						this.getCartCount();
					}
					this.$refs.goodsSku.confirm()
				}
			}

		},
		swiperChange(e) {
			this.swiperCurrent = e.detail.current + 1;
		},
		chooseSkuspecFormat(){
			if (!this.token && this.preview == 0) {
				this.$util.toShowLoginPopup(this,null,'/promotionpages/new_seckill/detail/detail?sku_id=' + this.skuId);
				return;
			}

			if(this.goodSeckillDetail.seckill_status == 1){
				this.$refs.goodsSku.isShowNumChoose = true;
				this.$refs.goodsSku.show("seckill", () => {
				});
			}else{
				this.$refs.goodsSku.show("choose_spec", () => {
					this.getCartCount();
				});
			}


		},
		//-------------------------------------服务-------------------------------------
		openMerchantsServicePopup() {
			this.$refs.merchantsServicePopup.open();
		},
		closeMerchantsServicePopup() {
			this.$refs.merchantsServicePopup.close();
		},
		//-------------------------------------属性-------------------------------------
		openAttributePopup() {
			this.$refs.attributePopup.open();
		},
		closeAttributePopup() {
			this.$refs.attributePopup.close();
		},
		//-------------------------------------属性-------------------------------------
		//-------------------------------------评价-------------------------------------
		//商品评论列表
		getGoodsEvaluate() {
			this.$api.sendRequest({
				url: "/api/goodsevaluate/firstinfo",
				data: {
					goods_id: this.goodsSkuDetail.goods_id
				},
				success: res => {
					let data = res.data;
					if (data) {
						this.goodsEvaluate = data;
						if (this.goodsEvaluate.images) this.goodsEvaluate.images = this.goodsEvaluate.images.split(",");
						if (this.goodsEvaluate.is_anonymous == 1) this.goodsEvaluate.member_name = this.goodsEvaluate.member_name.replace(
							this.goodsEvaluate.member_name.substring(1, this.goodsEvaluate.member_name.length - 1), '***')
					}
				}
			});
		},
		// 预览评价图片
		previewEvaluate(index, field) {
			var paths = [];
			for (let i = 0; i < this.goodsEvaluate[field].length; i++) {
				paths.push(this.$util.img(this.goodsEvaluate[field][i]));
			}
			uni.previewImage({
				current: index,
				urls: paths,
			});
		},
		//-------------------------------------关注-------------------------------------
		//获取用户是否关注
		getWhetherCollection() {
			this.$api.sendRequest({
				url: "/api/goodscollect/iscollect",
				data: {
					goods_id: this.goodsSkuDetail.goods_id
				},
				success: res => {
					this.whetherCollection = res.data;
				}
			});
		},
		editCollection() {
			if (this.preview) return; // 开启预览，禁止任何操作和跳转

			if (this.token != "") {

				//未关注添加关注
				if (this.whetherCollection == 0) {
					this.$api.sendRequest({
						url: "/api/goodscollect/add",
						data: {
							sku_id: this.skuId,
							site_id: this.goodsSkuDetail.site_id,
							goods_id: this.goodsSkuDetail.goods_id,
							category_id: this.goodsSkuDetail.category_id,
							sku_name: this.goodsSkuDetail.sku_name,
							sku_price: this.goodsSkuDetail.discount_price,
							sku_image: this.goodsSkuDetail.sku_image
						},
						success: res => {
							var data = res.data;
							if (data > 0) {
								this.whetherCollection = 1;
								this.$util.showToast({
									title: '收藏成功',
								});
							}
						}
					});
				} else {
					//已关注取消关注
					this.$api.sendRequest({
						url: "/api/goodscollect/delete",
						data: {
							goods_id: this.goodsSkuDetail.goods_id
						},
						success: res => {
							var data = res.data;
							if (data > 0) {
								this.whetherCollection = 0;
								this.$util.showToast({
									title: '已取消收藏',
								});
							}
						}
					});
				}
			} else {
				this.$util.toShowLoginPopup(this,null,'/promotionpages/new_seckill/detail/detail?sku_id=' + this.skuId);
			}
		},
		//更新商品信息
		modifyGoodsInfo() {
			if (this.preview) return; // 开启预览，禁止任何操作和跳转
			//更新商品点击量
			this.$api.sendRequest({
				url: "/api/goods/modifyclicks",
				data: {
					sku_id: this.skuId,
					site_id: this.goodsSkuDetail.site_id,
				},
				success: res => {}
			});

			let data = {
				goods_id: this.goodsSkuDetail.goods_id,
				sku_id: this.skuId,
				category_id: this.goodsSkuDetail.category_id,
				category_id_1: this.goodsSkuDetail.category_id_1,
				category_id_2: this.goodsSkuDetail.category_id_2,
				category_id_3: this.goodsSkuDetail.category_id_3,
				site_id: this.goodsSkuDetail.site_id,
			}
			if(this.$store.state.buried_shop_id){
				data.share_shop_id = this.$store.state.buried_shop_id
			}

			//添加足迹
			this.$api.sendRequest({
				url: "/api/goodsbrowse/add",
				data: data,
				success: res => {}
			});
		},
		//-------------------------------------组合套餐-------------------------------------

		//获取当前商品关联的组合套餐
		getBundling() {
			this.$api.sendRequest({
				url: "/bundling/api/bundling/lists",
				data: {
					sku_id: this.skuId
				},
				success: res => {
					if (res.data && res.data.length) {
						let bundling = res.data;
						bundling.forEach((v)=>{
							v.bundling_goods=v.bundling_goods.filter((j)=>{ return j.sku_id != this.goodsSkuDetail.sku_id;})
						})
						this.bundling=bundling;
					}
				}
			});
		},
		// 打开组合套餐弹出层
		openBundlingPopup() {
			this.$refs.bundlingPopup.open();
		},
		// 关闭组合套餐弹出层
		closeBundlingPopup() {
			this.$refs.bundlingPopup.close();
		},
		//-------------------------------------分享-------------------------------------
		// 打开分享弹出层
		openSharePopup() {
			// #ifdef MP-WEIXIN
			if(this.$refs.sharePopup) this.$refs.sharePopup.open();
			// #endif
			// #ifdef H5
			let share_data=this.getSharePageParams();
			if(this.$refs.shareNavigateH5) this.$refs.shareNavigateH5.open(share_data);
			// #endif
		},
		// 关闭分享弹出层
		closeSharePopup() {
			this.$refs.sharePopup.close();
		},
		onShareClick(){
			this.$refs.share_box.click();
		},
		//-------------------------------------海报-------------------------------------
		// 打开海报弹出层
		openPosterPopup() {
			this.getGoodsPoster();
			this.$refs.sharePopup.close();
			this.$refs.posterPopup.open();
			if (this.poster != '-1') {
				setTimeout(() => {
					let view = uni.createSelectorQuery().in(this).select(".poster-layer .image-wrap");
					view.fields({
						size: true,
					}, data => {
						let posterWhith = data.width;
						let ratio = parseFloat((740 / posterWhith).toFixed(2));
						if (this.token != '') {
							this.posterHeight = parseInt(1120 / ratio);
						} else {
							this.posterHeight = parseInt(1100 / ratio);
						}
					}).exec();
				}, 100);
			}
		},
		// 关闭海报弹出层
		closePosterPopup() {
			this.$refs.posterPopup.close();
		},
		//生成海报
		getGoodsPoster() {
			//活动海报信息
			let qrcode_param = {
				sku_id: this.skuId
			};
			if (this.memberId) qrcode_param.source_member = this.memberId;
			this.$api.sendRequest({
				url: "/api/goods/poster",
				data: {
					page: '/pages/goods/detail/detail',
					qrcode_param: JSON.stringify(qrcode_param),
					// #ifdef APP-PLUS
					app_type: 'weapp',
					app_type_name: '微信小程序',
					// #endif
				},
				success: res => {
					if (res.code == 0) {
						this.poster = res.data.path;
					} else {
						this.posterMsg = res.message;
					}
				}
			});
		},
		// 预览图片
		previewMedia(index) {
			var paths = [];
			for (let i = 0; i < this.goodsSkuDetail.sku_images.length; i++) {
				paths.push(this.$util.img(this.goodsSkuDetail.sku_images[i]));
			}
			uni.previewImage({
				current: index,
				urls: paths,
			});
		},
		imageError() {
			this.goodsSkuDetail.sku_image = this.$util.getDefaultImage().default_goods_img;
			this.$forceUpdate();
		},
		swiperImageError(index) {
			this.goodsSkuDetail.sku_images[index] = this.$util.getDefaultImage().default_goods_img;
			this.$forceUpdate();
		},
		bundlingImageError(index, goods_index) {
			this.bundling[index].bundling_goods[goods_index].sku_image = this.$util.getDefaultImage().default_goods_img;
			this.$forceUpdate();
		},
		// #ifdef MP
		//小程序中保存海报
		saveGoodsPoster() {
			let url = this.$util.img(this.poster);
			// this.$util.showToast({
			// 	title: "saveGoodsPoster:"+ url
			// });
			uni.downloadFile({
				url: url,
				success: (res) => {
					if (res.statusCode === 200) {
						uni.saveImageToPhotosAlbum({
							filePath: res.tempFilePath,
							success: () => {
								this.$util.showToast({
									title: "保存成功"
								});
							},
							fail: () => {
								this.$util.showToast({
									title: "保存失败，请稍后重试"
								});
							}
						});
					}
				},

				fail: (e) => {
					// this.$util.showToast({
					// 	title: "fail:" + JSON.stringify(e)
					// });
				}
			});
		},
		// #endif
		getMemberId() {
			this.$api.sendRequest({
				url: "/api/member/id",
				success: res => {
					if (res.code >= 0) {
						this.memberId = res.data;
						this.setWechatShare();
					}
				}
			});
		},
		getAfterSale() {
			this.$api.sendRequest({
				url: '/api/goods/aftersale',
				success: res => {
					if (res.code == 0 && res.data) {
						let data = res.data.content;
						if (res.data.content) this.afterSale = htmlParser(res.data.content);
					}
				}
			});
		},
		getService() {
			this.$api.sendRequest({
				url: apiurls.goodsServiceDesc,
				data:{
					goods_id:this.goodsSkuDetail.goods_id
				},
				success: res => {
					if (res.code == 0 && res.data) {
						let data = res.data.content;
						if (res.data.directions) this.service = htmlParser(res.data.directions);
						if (res.data.protocol) this.afterSale = htmlParser(res.data.protocol);
					}
				}
			});
		},
		errorShopLogo() {
			this.shopInfo.avatar = this.$util.getDefaultImage().default_shop_img;
			this.$forceUpdate();
		},
		fenxiao() {
			this.$refs.fenxiaoPopup.show()
		},
		/**
		 * 设置微信公众号分享
		 */
		setWechatShare() {
			// 微信公众号分享
			// #ifdef H5
			let share_data=this.$util.deepClone(this.getSharePageParams());
			let link=window.location.origin+this.$router.options.base+share_data.link.slice(1);
			share_data.link=link;
			this.$util.publicShare(share_data);
			// #endif
		},

		toPoint(event){
			let id=event.target.dataset.id;
			let that=this;
			uni.createSelectorQuery().select('.goods-detail').boundingClientRect(data=>{//目标位置节点 类或者 id
				uni.createSelectorQuery().select("#"+id).boundingClientRect((res)=>{//最外层盒子节点类或者 id
					that.detailTab=id;
					uni.pageScrollTo({
						duration:500,//过渡时间
						scrollTop: Math.abs(data.top - res.top)   ,//到达距离顶部的top值
					})
				}).exec()
			}).exec();
		}
	}
}
