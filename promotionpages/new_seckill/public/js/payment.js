import AdaPay from 'common/js/adaPay.js'
import apiUrl from "../../../../common/js/apiurls";
export default {
	data() {
		return {
			isSub: false,
			suk_id: '',
			seckill_id:'',
			buyer_message:'',
			orderPaymentData: {
				member_account: {
					balance: 0,
					is_pay_password: 0
				},
				is_balance: 0,
				seckillBuy:{
					seckill_price:'',
					goods_money:''
				}
			},
			seckillOrderCreateData:{},

			isFocus:false,
			orderCreateData: {
				is_balance: 0,
				pay_password: '',
			},
			errMsg:'',
			paymentMethod:"WECHAT",
			push_data:{},
			isSubscribed:false,

		}
	},
	methods: {
		/**
		 * 选择收货地址
		 */
		selectAddress() {
			this.$util.redirectTo('/otherpages/member/address/address', {
				'back': '/promotionpages/new_seckill/payment/payment'
			});
		},
		async getAddressList(){
			let addressList=[];
			let res = await this.$api.sendRequest({
				url: '/api/memberaddress/page',
				async:false,
				data: {
					page: 1,
					page_size: 20
				}
			})
			if(res.code ==0) {
				addressList = res.data.list;
			}
			return addressList;
		},
		/**
		 * 获取订单初始化数据
		 */
		getOrderPaymentData() {
			this.seckillOrderCreateData = uni.getStorageSync('seckillOrderCreateData');
			if (!this.seckillOrderCreateData) {
				this.$util.showToast({
					title: '未获取到创建订单所需数据!！',
					success: () => {
						setTimeout(() => {
							console.log('!this.seckillOrderCreateData')
							console.log(this.seckillOrderCreateData)
							this.$util.redirectTo('/pages/index/index/index', {}, 'reLaunch');
						}, 1500)
					}
				});
				return;
			}
			var member_address = uni.getStorageSync('member_address')
			if(member_address){
				this.seckillOrderCreateData.member_address = member_address.id
			}
			this.$api.sendRequest({
				url: this.$apiUrl.getSeckillPayment,
				data: this.seckillOrderCreateData,
				success: res => {
					if (res.code >= 0) {
						this.orderPaymentData = res.data;
						var member_address = uni.getStorageSync('member_address');
						if(member_address) {
							//本地存储的地址id和服务器地址id一直时，使用服务器的,否则使用本地
							if(member_address.id==this.orderPaymentData.member_address.id){
								uni.setStorageSync('member_address',this.orderPaymentData.member_address)
							}else{
								this.orderPaymentData.member_address = member_address
							}
						} else {
							uni.setStorageSync('member_address',this.orderPaymentData.member_address)
						}

						this.$buriedPoint.submitOrderContent({ sku_ids:[res.data.goodsInfo.sku_id], num:[res.data.seckillBuy.num], pages:1 })

						if (this.$refs.loadingCover) this.$refs.loadingCover.hide();
					} else {
						this.$util.showToast({
							title: res.message,
							success: () => {
								setTimeout(() => {
									this.$util.redirectTo('/pages/index/index/index', {}, 'reLaunch');
								}, 1500)
							}
						});
					}
				},
				fail: res => {
					if (this.$refs.loadingCover) this.$refs.loadingCover.hide();
				}
			})
		},
		/**
		 * 订单创建
		 */
		async orderCreate() {
			if (this.verify()) {
				if (this.isSub) return;
				this.isSub = true;
				if(!this.isSubscribed){
					let scene_type='';
					if(this.paymentMethod=='WECHAT'){
						scene_type='order_pay_before'
					}else{
						scene_type='other_pay_before'
					}
					try{
						this.push_data=await this.$util.subscribeMessage({
							source:'order',
							source_id:'',
							scene_type,
						},true);
						this.isSubscribed=true;
					}catch (e) {

					}
				}
				uni.showLoading({
					mask: true,
					title: '加载中'
				});

				// 获取收货地址选择后存储的地址
				var member_address = uni.getStorageSync('member_address')
				var data = {
					sku_id: this.seckillOrderCreateData.sku_id,
					seckill_id: this.seckillOrderCreateData.seckill_id,
					member_address: JSON.stringify(member_address.id),
					buyer_message: JSON.stringify(this.buyer_message),
					num:this.seckillOrderCreateData.num
				}
				Object.assign(data,this.orderCreateData);

				this.$api.sendRequest({
					url: this.$apiUrl.getSeckillSubmitorder,
					data,
					success: async res => {

						if (res.code >= 0) {

							this.createBuriedPoint(res.data.out_trade_no,3)

							this.push_data.source_id=res.data.order_ids;
							await this.$util.subscribeMessageMethod(this.push_data);
							// 当支付金额为0元时，调用微信支付接口，直接支付成功跳转到周期购管理页
							if(res.data.is_free == 1){

								this.createBuriedPoint(res.data.out_trade_no,11)

								// 跳转支付成功页面
								this.$util.redirectTo('/pages/pay/result/result?order_ids='+res.data.order_ids, {
									code: res.data.out_trade_no
								}, 'redirectTo');
								uni.removeStorage({
									key: 'seckillOrderCreateData',
									success: () => {}
								});
							}else{
								this.orderPayPay(res.data.out_trade_no,res.data.order_ids)
							}

							uni.removeStorageSync('member_address')
						} else {
							this.isSub = false;
							uni.hideLoading();
							if(this.$refs.payPassword){
								this.isFocus=false;
								this.$refs.payPassword.close();
							}
							if (res.data.error_code == 10 || res.data.error_code == 12) {
								uni.showModal({
									title: '订单未创建',
									content: res.message,
									confirmText: '去设置',
									success: res => {
										if (res.confirm) {
											this.selectAddress();
										}
									}
								})
							} else {
								this.$util.showToast({
									title: res.message
								});
							}
						}
					},
					fail: res => {
						uni.hideLoading();
						this.isSub = false;
					}
				})
			}
		},
		/**
		 * 订单支付
		 */
		orderPayPay(out_trade_no,order_ids) {
			uni.showLoading({
				mask: true,
				title: '加载中'
			});
			var that = this;
				this.$api.sendRequest({
					url: '/api/pay/pay',
					data: {
						out_trade_no: out_trade_no,
						pay_type: 'adapay'
					},
					success: res => {
						uni.hideLoading();
						if (res.code >= 0) {
							this.$util.wechatPay(res.data.pay_type,res.data.pay_type=='adapay'? res.data.payment : res.data.pay_info, (res)=>{

								this.createBuriedPoint(out_trade_no,11)

								uni.hideLoading();
								// 跳转支付成功页面
								// #ifdef MP-WEIXIN
								this.$util.redirectTo('/pages/pay/result/result?order_ids='+order_ids, {
									code: out_trade_no
								}, 'redirectTo');
								// #endif
								// #ifdef H5
								this.$util.redirectTo('/pages/order/list/list?status=all', {}, 'redirectTo');
								// #endif
								uni.removeStorage({
									key: 'seckillOrderCreateData',
									success: () => {}
								});
							},(err)=>{

								this.createBuriedPoint(out_trade_no,9001)

								uni.hideLoading();
								this.$refs.popupToList.open()
							},(err)=>{
								setTimeout(() => {
									this.$util.redirectTo("/pages/order/list/list", {}, "redirectTo")
								}, 2000)
							})
						} else {
							console.log(res)
							// this.isSub = false;
							if(res.message) {
								this.$util.showToast({
									title: res.message
								});
							} else {
								uni.hideLoading();
							}
							if(this.$refs.popupToList) this.$refs.popupToList.open();
						}
					},
					fail: res => {
						this.$util.showToast({
							title: 'request:fail'
						});
					}
				})
		},
		// 订单埋点
		createBuriedPoint(out_trade_no,status){
			this.$buriedPoint.orderStatus({out_trade_no,status})
		},
		/**
		 * 订单验证
		 */
		verify() {
			if (this.orderPaymentData.is_virtual == 1) {
				if (!this.orderCreateData.member_address.mobile.length) {
					this.$util.showToast({
						title: '请输入您的手机号码'
					});
					return false;
				}
				var reg = /^[1](([3][0-9])|([4][5-9])|([5][0-3,5-9])|([6][5,6])|([7][0-8])|([8][0-9])|([9][1,8,9]))[0-9]{8}$/;
				if (!reg.test(this.orderCreateData.member_address.mobile)) {
					this.$util.showToast({
						title: '请输入正确的手机号码'
					});
					return false;
				}
			}
			if (this.orderPaymentData.is_virtual == 0) {
				if (!this.orderPaymentData.member_address) {
					this.$util.showToast({
						title: '请先选择您的收货地址'
					});
					return false;
				}
			}
			if(this.paymentMethod == "BALANCE"){
				this.orderCreateData.is_balance = 1
			}
			if (this.orderCreateData.is_balance == 1 && this.orderCreateData.pay_password == '') {

				setTimeout(() => {
					if(this.$refs.input) this.$refs.input.clear();
				}, 0)
				// this.$refs.payPassword.open();
				this.openPasswordPopup();
				return false;
			}
			return true;
		},
		async init(){
			// 刷新多语言
			this.$langConfig.refresh();
			// 判断登录
			if (!uni.getStorageSync('token')) {
				this.$util.redirectTo('/otherpages/shop/home/<USER>');
			} else {
				let member_address = uni.getStorageSync('member_address');
				if(member_address){
					let address_list = await this.getAddressList();
					// 判断本地存储的地址是否在服务器上有
					if (address_list.length){
						if(address_list.filter(item=>member_address.id==item.id).length<1){
							uni.removeStorageSync('member_address');
						}
					}else{
						uni.removeStorageSync('member_address');
					}
				}
				this.orderPaymentData = {
					member_account: {
						balance: 0,
						// is_pay_password: 0
					},
					is_balance: 0,
					seckillBuy:{
						seckill_price:'',
						goods_money:''
					}
				},
				this.orderCreateData= {
					is_balance: 0,
					pay_password: '',
				}
				if (!this.isSub) {
					this.getOrderPaymentData();
				}
			}
		},
		changePayment(e){
			if(e.key == 'BALANCE'){
				this.orderCreateData.is_balance = !e.disable ? this.orderCreateData.is_balance == 1 ? 0 : 1 : 0;
			}else{
				this.orderCreateData.is_balance = 0
			}
			if(!e.disable){
				this.paymentMethod = e.key
			}
			this.$forceUpdate()
		},
		openPasswordPopup(){
			this.$refs.payPassword.open();
			setTimeout(()=>{
				this.isFocus = true;
			},500)
		},
		async showGoWeixin(){
			if(!uni.getStorageSync('member_address')){
				this.$util.showToast({
					title:"请先选择地址！"
				})
				return false;
			}
			if(this.$util.getPlatform() == 'h5' && this.paymentMethod=='WECHAT' && this.isOnXianMaiApp){
				this.$refs.popupGoWeixin.open()
			}else{
				await this.orderCreate()
			}
		},
		async comfirmGoWeixin(){
			this.$refs.popupGoWeixin.close()
			await this.orderCreate()
		},
		/**
		 * 设置支付密码
		 */
		setPayPassword() {
			this.$refs.payPassword.close();
			this.$util.redirectTo('/otherpages/member/setting/setting_password', {
				back: '/promotionpages/pintuan/payment/payment',
				phonenum: this.orderPaymentData.member_account.mobile
			});
		},
		/**
		 * 暂不设置支付密码
		 */
		noSet() {
			this.orderCreateData.is_balance = 0;
			this.$refs.payPassword.close();
			this.orderCalculate();
			this.$forceUpdate();
		},
		/**
		 * 支付密码输入
		 */
		input(pay_password) {
			this.errMsg = ''
			if (pay_password.length == 6) {
				uni.showLoading({
					title: '加载中...',
					mask: true
				})
				this.$api.sendRequest({
					url: apiUrl.checkpaypasswordUrl,
					data: {
						pay_password
					},
					success: res => {
						if (res.code >= 0) {
							this.orderCreateData.pay_password = pay_password;
							this.orderCreate();
						} else {
							uni.hideLoading();
							this.errMsg = res.message
							// this.$util.showToast({
							// 	title: res.message
							// });
						}
					},
					fail: res => {
						uni.hideLoading();
					}
				})
			}
		},
	},
	onLoad(option) {
		if(option && option.seckill_id){
			this.seckill_id = option.seckill_id
		}
	},
	onShow() {
		if(this.ischoiceWechatAdder){
			let wechatAdderInterval=setInterval(()=>{
				if(this.postWechatAdder){
					console.log("this.choiceWechatAdderError",this.choiceWechatAdderError)
					if(this.choiceWechatAdderError){
						//微信收获地址提交到服务器报错时，需要跳转到地址管理页面
						if(this.choiceWechatAdderError){
							if (this.$refs.loadingCover) this.$refs.loadingCover.hide();
							this.$util.showToast({
								title: '获取微信地址失败，请手动添加地址',
								success: () => {
									setTimeout(() => {
										this.selectAddress()
									}, 1500)
								}
							});
						}
					}
					this.ischoiceWechatAdder=false;
					this.postWechatAdder=false;
					this.choiceWechatAdderError=false;
					clearInterval(wechatAdderInterval);
					this.init();
				}
			},100)
		}else{
			this.init()
		}
	},
	onHide() {
		if (this.$refs.loadingCover) this.$refs.loadingCover.show();
	},
	filters: {
		/**
		 * 金额格式化输出
		 * @param {Object} money
		 */
		moneyFormat(money) {
			return parseFloat(money).toFixed(2);
		},
	}
}
