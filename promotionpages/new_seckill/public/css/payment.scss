@mixin wrap {
	margin: $ns-margin;
	padding: $ns-padding;
	border-radius: $ns-border-radius;
	background: #fff;
	position: relative;
}
.uni-list-cell {
	display: flex;
	justify-content: space-between;
}
.align-right {
	text-align: right;
}

.inline {
	display: inline !important;
}

.order-container {
	padding-bottom: 120rpx;

	&.safe-area {
		padding-bottom: 188rpx;
	}
}

.address-wrap {
	background: #fff;
	position: relative;
	min-height: 100rpx;
	max-height: 140rpx;
	display: flex;
	align-items: center;
	.icon {
		width: 94rpx;
		height: 100rpx;
		display: flex;
		justify-content: center;
		align-items: center;
		image {
		  width: 48rpx;
		  height: 48rpx;
		}
	}

	.address-info {
		width: 656rpx;
		.info {
			display: flex;
			padding-top: 10rpx;
			text {
				overflow: hidden;
				display: -webkit-box;
				-webkit-line-clamp: 1;
				-webkit-box-orient: vertical;
				word-break: break-all;
				font-weight: bold;
				&:first-of-type {
					max-width: 380rpx;
					margin-right: 32rpx;
				}
			}
		}

		.detail {
			width: 508rpx;
			padding-bottom: 17rpx;
			line-height: 1.3;
			font-size: 26rpx;
		}
	}

	.address-empty {
		line-height: 100rpx;
		color: #999999;
		font-size: 26rpx;
	}

	.cell-more {
		position: absolute;
		top: 50%;
		transform: translateY(-50%);
		right: 20rpx;

		.iconfont {
			color: #999;
		}
	}
}

.mobile-wrap {
	@include wrap;

	.form-group {
		.form-item {
			display: flex;
			line-height: 50rpx;

			.text {
				display: inline-block;
				line-height: 50rpx;
				padding-right: 10rpx;
			}

			.placeholder {
				line-height: 50rpx;
			}

			.input {
				flex: 1;
				height: 50rpx;
				line-height: 50rpx;
			}
		}
	}
}

.order-cell {
	display: flex;
	margin: 48rpx 0;
	align-items: center;
	background: #fff;
	line-height: 40rpx;

	.tit {
		text-align: left;
	}

	.box {
		flex: 1;
		line-height: inherit;

		.textarea {
			height: 40rpx;
		}
	}
	.iconfont {
		color: #bbb;
		font-size: 28rpx;
	}

	.order-pay {
		padding: 0;
		font-size: 26rpx;
		color: #333;
		text {
			display: inline-block;margin-left: 20rpx;
		}
		.pay-money {
			font-size: 32rpx;
			font-weight: bold;
			margin-left: 2rpx;
		}
	}
}

.site-wrap {
	margin: 24rpx;
	padding: 0 24rpx;
	border-radius: 20rpx;
	background: #fff;
	position: relative;

	.site-header {
		display: flex;
		align-items: center;
		height: 88rpx;
		.icondianpu {
			display: inline-block;
			line-height: 1;
			margin-right: 17rpx;
			font-size: 28rpx;
			font-weight: bold;
		}
	}

	.site-body {
		.goods-wrap {
			margin-bottom: 20rpx;
			display: flex;
			position: relative;

			&:last-of-type {
				margin-bottom: 0;
			}

			.goods-img {
				width: 180rpx;
				height: 180rpx;
				margin-right: 20rpx;

				image {
					width: 100%;
					height: 100%;
					border-radius: 20rpx;
				}
			}

			.goods-info {
				flex: 1;
				position: relative;
				max-width: calc(100% - 200rpx);

				.goods-name {
					display: -webkit-box;
					-webkit-box-orient: vertical;
					-webkit-line-clamp: 2;
					overflow: hidden;
					line-height: 1.5;
					font-size: 28rpx;
					margin-bottom: 30rpx;
				}

				.goods-sub-section {
					width: 100%;
					line-height: 1.3;
					display: flex;
					justify-content: space-between;
					.unit {
						font-weight: normal;
						font-size: 24rpx;
						margin-right: 2rpx;
					}

					view {
						color: #999999;
						font-size: 24rpx;
						line-height: 1.3;
						&:last-of-type {
							text-align: right;
						}
					}
				}
				.goods-price {
					color: #333333;
					text-align: right;
					font-size: 24rpx;
					.price {
						font-size: 28rpx;
					}
				}
			}
		}
	}

	.site-footer {
		padding-bottom: 30rpx;
		.order-cell {
			.tit {
				width: 180rpx;
			}
			.store-promotion-box {
				overflow: hidden;
				text-overflow: ellipsis;
				white-space: nowrap;
			}
			.box {
				input {
					text-align: right;
					font-size: 26rpx;
				}
				&.text-overflow {
					max-width: calc(100% - 180rpx);
				}
			}
			&.my-coupon {
				padding: 26rpx 0;
				margin: 0;
				text-align: right;
				.box {
					&.text-overflow {
							max-width: unset;
					}
				}
			}


			&:last-of-type {
				margin-bottom: 0;
			}
		}
	}
}

.order-checkout {
	@include wrap;

	.order-cell {
		.iconyuan_checkbox,
		.iconyuan_checked {
			font-size: 38rpx;
		}
	}
}

.order-money {

	.order-cell {
		.box {
			padding: 0;

			.operator {
				font-size: 24rpx;
				margin-right: 6rpx;
			}
		}
	}
}

.order-submit {
	position: fixed;
	z-index: 5;
	left: 0;
	bottom: 0;
	width: 100vw;
	height: 98rpx;
	background: #fff;
	box-shadow: 0 0px 10px rgba(0, 0, 0, 0.1);
	display: flex;
	align-items: center;

	&.bottom-safe-area {
		padding-bottom: 0 !important;
		padding-bottom: constant(safe-area-inset-bottom) !important;
		padding-bottom: env(safe-area-inset-bottom) !important;
	}

	.order-settlement-info {
		flex: 1;
		height: 98rpx;
		line-height: 98rpx;
		padding-left: 25rpx;
		font-size: 28rpx;
		.money {
			font-size: 48rpx;
		}
	}

	.submit-btn {
		height: 80rpx;
		margin-right: 24rpx;
		display: flex;
		justify-content: center;
		align-items: center;
		button {
			padding: 0;
			width: 200rpx;
			background-color: var(--custom-brand-color)!important;
			height: 80rpx;
			line-height: 80rpx;
			font-size: 28rpx;
			border-radius: 40rpx;
		}
	}
}

.popup {
	width: 100vw;
	background: #fff;
	border-top-left-radius: 24rpx;
	border-top-right-radius: 24rpx;

	.popup-header {
		height: 90rpx;
		display: flex;
		align-items: center;
		padding: 0 30rpx;

		& > view {
			flex: 1;
			line-height: 1;
		}

		.tit {
			font-size: 32rpx;
			font-weight: 600;
		}

		.vice-tit {
			margin-right: 20rpx;
		}
	}

	.popup-body {
		height: calc(100% - 210rpx);
		height: calc(100% - 210rpx - constant(safe-area-inset-bottom));
		height: calc(100% - 210rpx - env(safe-area-inset-bottom));
	}

	.popup-footer {
		// height: 120rpx;
		padding-bottom: 0 !important;
		padding-bottom: constant(safe-area-inset-bottom) !important;
		padding-bottom: env(safe-area-inset-bottom) !important;

		.confirm-btn {
			height: 72rpx;
			line-height: 72rpx;
			color: #fff;
			text-align: center;
			margin: 20rpx 40rpx;
			border-radius: 40rpx;
		}
	}
}



.pay-password {
	width: 80vw;
	background: #fff;
	box-sizing: border-box;
	border-radius: 10rpx;
	overflow: hidden;
	padding: 60rpx 40rpx;
	transform: translateY(-200rpx);
	.popup-title{
		display: flex;
		align-items: center;
		.title {
			font-size: 28rpx;
			text-align: center;
			width: calc(100% - 40rpx);
			text-align: center;
		}
	}
	.error-tips{
		text-align: center;
		width: 100%;
	}
	.money-box{
		margin-top: 50rpx;
		.total-fee{
			text-align: center;
			font-size: 48rpx;
			font-weight: bold;
			color: #333333;
		}
		.balance{
			font-size: 24rpx;
			color: #999999;
			text-align: center;
		}
	}
	.cha_close{
		width: 30rpx;
		height: 30rpx;
	}


	.tips {
		font-size: 24rpx;
		color: #999;
		text-align: center;
	}

	.btn {
		width: 60%;
		margin: 0 auto;
		margin-top: 30rpx;
		height: 70rpx;
		line-height: 70rpx;
		border-radius: 70rpx;
		color: #fff;
		text-align: center;
		border: 1px solid #ffffff;

		&.white {
			margin-top: 20rpx;
			background-color: #fff !important;
		}
	}

	.password-wrap {
		padding-top: 20rpx;
		width: 90%;
		margin: 0 auto;

		.forget-password {
			margin-top: 20rpx;
			display: inline-block;
		}
	}
}
.text-color {
	color: var(--custom-brand-color);
	.money {
		font-weight: bold;
		font-size: 48rpx;
	}
}
.nav{
  width: 100%;
  overflow: hidden;
  position: fixed;
  top: 0;
  left: 0;
  z-index: 10;
  background-color: #ffffff;
}
.nav-title{
  width: 100%;
  height: 88rpx;
  line-height: 88rpx;
  text-align: center;
  position: absolute;
  bottom: 0;
  left: 0;
  z-index: 10;
}
.nav .back{
  width: 42rpx;
  height: 70rpx;
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  left: 0rpx;
  padding:0 24rpx;
}
.wrapper {
	padding-top: 24rpx;
}

.coupon-instructions-close {
	display: flex;
	justify-content: flex-end;
	align-items: center;
}
.coupon-instructions-btn {
	margin-right: 20rpx;
	color: #999999;
	font-size: 24rpx;
}
.coupon-close {
	color: #A0A1A7;
}
.coupon-default {
	display: flex;
	align-items: center;
	justify-content: center;
	height: 100%;
	view {
		color: #999999;
	}
}

.payment-methods{
	.item{
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding: 10rpx 0;
		&.disable{
			.title{
				text{
					color: #CCCCCC;
				}
			}
		}
		.icon{
			width: 48rpx;
			height: 48rpx;
		}
		.title{
			flex: 1;
			font-size: 26rpx;
			margin: 0 10rpx;
			.desc{
				color: var(--custom-brand-color);
				margin-left: 10rpx;
			}
		}
	}
}
