.go-top {
	position: fixed;
	right: 30rpx;
	bottom: 220rpx;
	z-index: 1;
	background: #fff;
	padding: 10rpx;
	border: 1px solid;
	border-radius: 20px;
	width: 57rpx;
	height: 270rpx;
	// height: 180rpx;
	text-align: center;
	font-size: $ns-font-size-sm;
	.goods-share,
	.collection {
		margin-bottom: 10rpx;
		font-size: $ns-font-size-sm;
	}
	.icontop {
		font-size: 40rpx;
	}
}

.goods-detail {
	height: 100%;
	padding-bottom: 100rpx;
	padding-bottom: calc(100rpx + constant(safe-area-inset-bottom));
	padding-bottom: calc(100rpx + env(safe-area-inset-bottom));
}

// 商品媒体信息
.goods-media {
	width: 100%;
	position: relative;
	overflow: hidden;

	&:after {
		padding-top: 100%;
		display: block;
		content: '';
	}
	.over{
		width: 200rpx;
		height: 200rpx;
		position: absolute;
		left: 50%;
		top: 50%;
		transform: translate(-50%,-50%);
	}

	.pop-video {
		line-height: 1;
	}
	.goods-img,
	.goods-video {
		position: absolute;
		width: 100%;
		height: 100%;
		left: 0;
		top: 0;
		transition-property: transform;
		transition-timing-function: cubic-bezier(0, 0, 0.25, 1);
		transition-duration: 350ms;
		transform: translate3d(0, 0, 0);
		.video-img {
			width: 100%;
			height: 100%;
			position: relative;
		}
		image {
			width: 100%;
			height: 100%;
		}
		.video-open {
			width: 100%;
			height: 100%;
			position: absolute;
			left: 0;
			top: 0;
			display: flex;
			justify-content: center;
			align-items: center;
			.iconfont {
				font-size: 90rpx;
				color: #fff;
			}
		}
	}

	.goods-img {
		transform: translateX(100%);
	}

	.goods-video {
		transform: translateX(-100%);
	}

	.goods-img.show,
	.goods-video.show {
		transform: translateX(0);
	}

	.goods-img .swiper {
		width: 100%;
		height: 100%;

		.item {
			width: 100%;
			height: 100%;
		}

		image {
			width: 100%;
			height: 100%;
		}
	}

	.goods-img .img-indicator-dots {
		position: absolute;
		z-index: 5;
		bottom: 40rpx;
		right: 40rpx;
		background: rgba(100, 100, 100, 0.6);
		color: #fff;
		font-size: 24rpx;
		line-height: 40rpx;
		border-radius: 20rpx;
		padding: 0 20rpx;
	}

	.goods-video video {
		width: 100%;
		height: 100%;
	}

	.goods-video .uni-video-cover {
		background: none;
	}

	.media-mode {
		position: absolute;
		width: 100%;
		z-index: 5;
		bottom: 40rpx;
		//#ifdef MP
		bottom: 80rpx;
		//#endif
		text-align: center;
		line-height: 50rpx;

		text {
			background: rgba(100, 100, 100, 0.6);
			color: #fff;
			font-size: 24rpx;
			line-height: 50rpx;
			border-radius: 50rpx;
			padding: 0 30rpx;
			display: inline-block;

			&:last-child {
				margin-left: 40rpx;
			}
		}
	}
}

.group-wrap {
	margin-bottom: 20rpx;
	position: relative;
	background-color: white;
	&-padding{
		padding: 0 20rpx;
		box-sizing: border-box;
	}
}

button.group-wrap-share{
	margin: 0;
	padding: 0;
	border: none;
	line-height: 1;

	width: 142rpx;
	height: 60rpx;
	background: var(--custom-brand-color);
	border-top-left-radius: 30rpx;
	border-bottom-left-radius: 30rpx;
	border-top-right-radius: 0;
	border-bottom-right-radius: 0;
	font-size: 26rpx;
	font-weight: 400;
	color: #fff;
	display: flex;
	justify-content: center;
	align-items: center;
	position: absolute;
	right: 0;
	top: 22rpx;
}
.group-wrap-share-vip-share{
	width: 44rpx;
	height: 44rpx;
	margin-right: 6rpx;
}

button.group-wrap-share text{
	margin-left: 14rpx;
}

.goods-module-wrap {
	background-color: #fff;
	padding: 16rpx 20rpx 20rpx 20rpx;
	margin-top: -16rpx;
	border-radius: 20rpx 20rpx 0 0;
	&.discount {
		padding-top: 20rpx;
	}

	.goods-module-wrap-box {
		display: flex;
		justify-content: space-between;
		align-items: center;
	}

	.price-symbol {
		font-size: 36rpx;
		position: relative;
		top: 4rpx;
	}

	.price {
		font-size: 48rpx;
		position: relative;
		top: 4rpx;
		margin-right: 10rpx;
	}
	.market-price-symbol {
		position: relative;
		top: 4rpx;
		text-decoration: line-through;
		color: $ns-text-color-gray;
	}
	.market-price {
		position: relative;
		top: 4rpx;
		margin-right: 20rpx;
		color: $ns-text-color-gray;
		text-decoration: line-through;
	}
	.sku-name {
		font-weight: bold;
		display: inline;
		font-size: $ns-font-size-lg;
		position: relative
	}
	.sku-name,
	.introduction {
		overflow: hidden;
		text-overflow: ellipsis;
		display: -webkit-box;
		-webkit-line-clamp: 2;
		-webkit-box-orient: vertical;
	}
	.introduction {
		margin-bottom: 20rpx;
		line-height: 1;
	}

	.adds-wrap {
		display: flex;

		text {
			flex: 1;
			font-size: $ns-font-size-sm;
			color: #999;
			text-align: center;

			&:first-of-type {
				text-align: left;
			}

			&:last-of-type {
				text-align: right;
			}
			&.adds-wrap-volume{
				font-size: 26rpx;
				font-weight: 500;
				color: #999999;
				text-align: left;
			}
		}
	}
}

.goods-cell {
	display: flex;
	align-items: center;
	background: #fff;
	height: 80rpx;
	line-height: 80rpx;
	justify-content: space-between;
	box-shadow: 0px 1rpx 3rpx 0px rgba(238, 238, 238, 0.8);
	position: relative;
	box-sizing: border-box;
	&:not(:first-child){
		border-top: 2rpx solid rgba(245, 245, 245, 1);
	}
	.tit {
		color: #999;
		font-size: $ns-font-size-base;
		margin-right: 10rpx;
	}

	.box {
		width: 90%;
		font-size: $ns-font-size-base;
		line-height: inherit;
		overflow: hidden;
		text-overflow: ellipsis;
		white-space: nowrap;
	}
	&-left{
		width: 70%!important;
	}
	&-right{
		display: flex;
		align-items: center;
	}

	.iconfont {
		color: #bbb;
		font-size: 28rpx;
	}
}

.goods-cell.service {
	.box {
		display: -webkit-box;
		-webkit-line-clamp: 1;
		-webkit-box-orient: vertical;
		-webkit-box-pack: center;
		overflow: hidden;
		word-break: break-all;
		text {
			&::after {
				content: ' · ';
				display: inline-block;
			}
			&:last-child::after {
				content: '';
			}
		}
	}
}

.shop-wrap {
	padding: 20rpx;
	background: #fff;

	.box {
		display: flex;
	}

	.shop-logo {
		width: 120rpx;
		height: 120rpx;
		border-radius: 10rpx;
		border: 1rpx solid $ns-border-color-gray;

		image {
			width: 100%;
			height: 100%;
		}
	}

	.shop-info {
		padding-left: 20rpx;
		flex: 1;
		.description {
			color: $ns-text-color-gray;
		}
	}

	.shop-score {
		margin-top: 20rpx;
		text {
			flex: 1;
			text-align: center;
			color: #999;
		}
	}

	.goods-action {
		margin-top: 20rpx;
		text-align: center;
		width: 100%;

		navigator {
			display: inline-block;
			line-height: 40rpx;
			padding: 2rpx 40rpx;
			border: 1px solid #ffffff;
			border-radius: 40rpx;

			&:last-of-type {
				margin-left: 30rpx;
			}
		}
	}
}

.goods-evaluate {
	padding: 20rpx;
	background: #fff;

	.tit {
		padding-bottom: 20rpx;
		display: flex;
		align-items: center;

		view {
			flex: 1;
			line-height: 40rpx;
			text-align: left;
		}
		navigator {
			text-align: right;

			.iconfont {
				font-size: 24rpx;
			}
		}
	}

	.evaluate-item {
		.evaluator {
			display: flex;
			align-items: center;

			.evaluator-face {
				width: 50rpx;
				height: 50rpx;
				border-radius: 50%;
				overflow: hidden;

				image {
					width: 100%;
					height: 100%;
				}
			}

			.evaluator-name {
				margin-left: 20rpx;
				color: #999;
			}
		}

		.cont {
			text-align: justify;
			display: -webkit-box;
			-webkit-line-clamp: 2;
			-webkit-box-orient: vertical;
			-webkit-box-pack: center;
			overflow: hidden;
			word-break: break-all;
		}

		.evaluate-img {
			display: inline-flex;

			.img-box {
				width: 100rpx;
				height: 100rpx;
				overflow: hidden;
				margin: 0 20rpx 20rpx 0;

				image {
					width: 100%;
					height: 100%;
				}
			}
		}

		.time {
			color: #999;

			text {
				margin-right: 20rpx;
			}
		}
	}
	.evaluate-item-empty {
		width: 100%;
		height: 130rpx;
		display: flex;
		justify-content: center;
		align-items: center;
		color: $ns-text-color-gray;
	}
}

.goods-action-button {
	width: 200rpx;
	margin-left: 24rpx;
}
.active-text{
	display: flex;
	flex-direction: column;
	align-items: center;
	margin-left: auto;

	&-tip{
		font-size: 24rpx;
		font-weight: 400;
		color: var(--custom-brand-color);
		line-height: 1;
	}
	&-price{
		font-size: 40rpx;
		font-weight: 700;
		color: var(--custom-brand-color);
		line-height: 1;
		&-symbol{
			font-size: $ns-font-size-sm;
			font-weight: 400;
		}
	}
}
.line {
	height: 1px;
}

// 优惠券
.goods-coupon {
	position: relative;
	.get-coupon {
		border: 1px solid #ffffff;
		border-radius: 20rpx;
		display: block;
		height: 42rpx;
		width: 84rpx;
		position: absolute;
		top: 50%;
		right: 20rpx;
		text-align: center;
		transform: translateY(-50%);
		font-size: $ns-font-size-sm;
	}
}

// 优惠券弹出层
.goods-coupon-popup-layer {
	background: #fff;
	height: 800rpx;
	.tax-title {
		text-align: center;
		font-size: $ns-font-size-lg;
		line-height: 120rpx;
		height: 120rpx;
		display: block;
		font-weight: bold;
		position: relative;

		text {
			position: absolute;
			float: right;
			right: 22px;
			font-size: 40rpx;
			font-weight: 500;
		}
	}
	.coupon-body {
		position: absolute;
		left: 0;
		right: 0;
		height: 65%;

		.body-item {
			width: 702rpx;
			height: 130rpx;
			margin: 0 auto;
			border: 1px solid #fff;
			margin-bottom: 28rpx;
			border-radius: 10rpx;
			display: flex;
			justify-content: flex-start;
			align-items: center;
			overflow: hidden;
			padding-right: 18rpx;
			border: 1px solid rgba(0, 0, 0, 0.1) !important;

			.item-price {
				width: 240rpx;
				height: 100%;
				border-right: 1px dashed rgba(0, 0, 0, 0.2);
				display: flex;
				flex-direction: column;
				align-items: center;
				justify-content: center;
				line-height: 1;
				color: #000;
				.price {
					font-size: $ns-font-size-base;
					line-height: 1;
					.price-num {
						font-size: 48rpx;
					}
				}
				.sub {
					font-size: 20rpx;
					color: #000;
					line-height: 1;
					margin-top: 14rpx;
				}
			}
			.item-info {
				flex: 1;
				height: 100%;
				display: flex;
				justify-content: space-between;
				align-items: center;
				.info-box {
					height: 100%;
					display: flex;
					flex-direction: column;
					padding-left: 23rpx;
					box-sizing: border-box;
					display: flex;
					flex-direction: column;
					justify-content: center;
					.sub {
						font-size: $ns-font-size-sm;
						color: #000;
						line-height: 1;
						&:nth-child(2) {
							color: #ababab;
							margin-top: 18rpx;
						}
					}
				}

				.item-btn {
					width: 90rpx;
					height: 48rpx;
					color: #fff;
					border-radius: 24rpx;
					float: right;
					display: flex;
					align-items: center;
					justify-content: center;
					font-size: $ns-font-size-sm;
					opacity: 0.7;
					line-height: 1;
				}
			}
		}
		.free_div {
			height: 40rpx;
		}
		.item {
			overflow: hidden;
			margin: 0 20rpx 20rpx;
			border-radius: 12rpx;
			display: flex;
			&:last-child {
				margin-top: 0;
			}
			.main {
				flex: 1;
				padding: 20rpx 0 20rpx 20rpx;
				.price {
					text-overflow: ellipsis;
					white-space: nowrap;
					overflow: hidden;
					.money {
						font-size: 48rpx;
						font-weight: 700;
					}
				}
				.sub {
					font-size: $ns-font-size-sm;
					overflow: hidden;
					text-overflow: ellipsis;
					display: -webkit-box;
					-webkit-line-clamp: 2;
					-webkit-box-orient: vertical;
				}
			}
			.tax-split {
				border: 1px dotted;
				position: relative;
				border-right: 0;
				&::before {
					content: '';
					position: absolute;
					width: 10px;
					height: 10px;
					background: #fff;
					border-radius: 50%;
					left: -5px;
					top: -5px;
					z-index: 1;
				}
				&::after {
					content: '';
					position: absolute;
					width: 10px;
					height: 10px;
					background: #fff;
					border-radius: 50%;
					left: -5px;
					bottom: -5px;
					z-index: 1;
				}
			}
			.tax-operator {
				flex: 0.4;
				text-align: center;
				height: 220rpx;
				line-height: 220rpx;
				font-size: $ns-font-size-base;
			}
		}
	}
	.button-box {
		width: 100%;
		position: absolute;
		bottom: 0;
		z-index: 1;
		margin-bottom: $ns-margin !important;
	}
}

// 商家服务
.goods-merchants-service-popup-layer {
	background: #fff;
	height: 660rpx;
	.tax-title {
		text-align: center;
		font-size: $ns-font-size-lg;
		line-height: 120rpx;
		height: 120rpx;
		display: block;
		font-weight: bold;
	}
	scroll-view {
		position: absolute;
		left: 0;
		right: 0;
		height: 65%;
		.item {
			padding: 20rpx 40rpx;
			position: relative;
			.iconfont {
				vertical-align: top;
				display: inline-block;
				margin-right: $ns-margin;
				font-size: $ns-font-size-lg;
			}
			.info-wrap {
				display: inline-block;
				vertical-align: middle;
				width: 90%;
				.title {
					display: block;
					font-size: $ns-font-size-base;
					font-weight: bold;
				}
				.describe {
					font-size: $ns-font-size-sm;
					color: $ns-text-color-gray;
					display: block;
					padding: 10rpx 0;
				}
			}
		}
	}
	.button-box {
		width: 100%;
		position: absolute;
		bottom: 0;
		z-index: 1;
		margin-bottom: $ns-margin !important;
	}
}

// 商品属性
.goods-attribute-popup-layer {
	background: #fff;
	height: 660rpx;

	.title {
		font-size: $ns-font-size-lg;
		line-height: 120rpx;
		height: 120rpx;
		display: block;
		font-weight: bold;
		padding-left: $ns-padding;
	}
	.goods-attribute-body {
		position: absolute;
		left: 0;
		right: 0;
		height: 60%;
		.item {
			padding: $ns-padding;
			border-bottom: 1px solid;
			.value {
				margin-left: 20rpx;
			}
			&:last-child {
				border-bottom: 0;
			}
		}
	}
	.button-box {
		position: absolute;
		bottom: 0;
		z-index: 1;
		margin-bottom: $ns-margin !important;
		width: 100%;
	}
}

// 满减
.manjian-popup-layer {
	background: #fff;
	height: 660rpx;

	.title {
		font-size: $ns-font-size-lg;
		line-height: 120rpx;
		height: 120rpx;
		display: block;
		font-weight: bold;
		padding-left: $ns-padding;
		text-align: center;
	}
	.manjian-body {
		position: absolute;
		left: 0;
		right: 0;
		height: 60%;
		.item {
			padding: $ns-padding 30rpx;
			display: flex;
			align-items: center;
			.manjian-icon {
				display: inline-block;
				font-size: 20rpx;
				color: #ffffff;
				border-radius: 50rpx;
				padding: 6rpx 10rpx;
				line-height: 1;
			}
			.value {
				margin-left: 20rpx;
			}
			&:last-child {
				border-bottom: 0;
			}
		}
	}

	.button-box {
		width: 100%;
		position: absolute;
		bottom: 0;
		z-index: 1;
		margin-bottom: $ns-margin !important;
	}
}

// 组合套餐
.combo-goods-wrap {
	display: flex;
	align-items: center;
	padding: 20rpx;
	background: #f7f7f7;
	.goods {
		width: 25%;
		display: inline-block;
		border: 1px solid;
		margin-right: 5%;
		position: relative;
		&:first-child {
			margin-right: 0;
		}

		image {
			max-width: 100%;
			display: block;
			position: relative;
			width: 100%;
			height: 160rpx;
		}
		text {
			height: 40rpx;
			line-height: 40rpx;
			background: rgba(89, 86, 86, 0.7);
			color: #fff;
			position: absolute;
			bottom: 0;
			width: 100%;
			text-align: center;
			font-size: $ns-font-size-sm;
		}
	}
	.iconfont {
		width: 100rpx;
		text-align: center;
		font-size: 50rpx;
		font-weight: bold;
	}
}

// 组合套餐
.bundling-popup-layer {
	background: #fff;
	height: 660rpx;

	.title {
		font-size: $ns-font-size-lg;
		line-height: 120rpx;
		height: 120rpx;
		display: block;
		font-weight: bold;
		padding-left: $ns-padding;
	}
	.bundling-body {
		position: absolute;
		left: 0;
		right: 0;
		height: 60%;

		scroll-view {
			width: 100%;
			white-space: nowrap;
			box-sizing: border-box;
			.item {
				padding: $ns-padding;
				border-bottom: 1px solid;
				.right {
					vertical-align: middle;
					text {
						vertical-align: middle;
					}
					.iconright {
						color: #bbb;
					}
				}
				.value {
					margin-bottom: 10rpx;
					display: flex;
					justify-content: space-between;
					align-items: center;
				}
				&:last-child {
					// border-bottom: 0;
				}
				.goods-wrap {
					// display: flex;
					// align-items: center;

					.iconfont {
						width: 100rpx;
						text-align: center;
						font-size: 50rpx;
						font-weight: bold;
						display: inline-block;
						vertical-align: middle;
					}
					.goods {
						width: 25%;
						display: inline-block;
						margin-right: 20rpx;
						position: relative;
						vertical-align: middle;
						&:first-child {
							margin-right: 0;
						}

						image {
							max-width: 100%;
							display: block;
							position: relative;
							width: 100%;
							height: 160rpx;
						}
						text {
							height: 40rpx;
							line-height: 40rpx;
							background: rgba(89, 86, 86, 0.7);
							color: #fff;
							position: absolute;
							bottom: 0;
							width: 100%;
							text-align: center;
							font-size: $ns-font-size-sm;
						}
					}
				}
			}
		}
	}
	.button-box {
		width: 100%;
		position: absolute;
		bottom: 0;
		z-index: 1;
		margin-bottom: $ns-margin !important;
	}
}

// 详情
.goods-detail-tab {
	width: 100%;
	//padding-top: $ns-padding;
	//background: #ffffff;
	.detail-tab {
		width: 100%;
		position: fixed;
		left: 0;
		top: 0;
		z-index: 2;
		background-color: #ffffff;
		.tab-item {
			height: 70rpx;
			color: #999999;
			line-height: 70rpx;
			box-sizing: border-box;
		}
		.tab-item.active {
			position: relative;
			color:#333333;
			font-weight: bold;
		}
		.tab-item.active::after {
			content: '';
			display: inline-block;
			width: 40rpx;
			height: 8rpx;
			position: absolute;
			left: 50%;
			bottom: 0;
			transform: translateX(-50%);
			border-radius: 8rpx;
		}
		.tab-item:not(:first-child) {
			margin-left: 100rpx;
		}
	}
	.detail-content {
		width: 100%;
	}
	.detail-content-item{
		background: #ffffff;
		box-shadow: 0px 2rpx 3rpx 0px rgba(238, 238, 238, 0.8);
		padding-top: 40rpx;
	}
	.goods-details-title{
		display: flex;
		justify-content: center;
		align-items: center;
	}
	.goods-details-title view{
		font-size: 28rpx;
		font-weight: 500;
		color: #333333;
		&:first-child{
			margin-right: 14rpx;
			width: 90rpx;
			height: 2rpx;
			background-color: #EEEEEE;
		}
		&:last-child{
			margin-left: 14rpx;
			width: 90rpx;
			height: 2rpx;
			background-color: #EEEEEE;
		}
	}
	.goods-details {
		padding: $ns-padding;
		margin-bottom: 20rpx;
		overflow: hidden;
		box-sizing: border-box;
	}
	.goods-details.active {
		min-height: 150rpx;
		width: 100%;
		display: flex;
		justify-content: center;
		align-items: center;
		color: $ns-text-color-gray;
	}
}

// 海报
// .uni-popup__wrapper-box
.poster-layer {
	.generate-poster {
		padding: 40rpx 0;
		.iconfont {
			font-size: 80rpx;
			color: #07c160;
			line-height: initial;
		}
		> view {
			text-align: center;
			&:last-child {
				margin-top: 20rpx;
			}
		}
	}
	.image-wrap {
		width: 70%;
		margin: 30px auto 20px auto;
		box-shadow: 0 0 16px rgba(100, 100, 100, 0.3);
		image {
			width: 100%;
			height: 100%;
			height: 750rpx;
		}
	}
	.msg {
		padding: 40rpx;
	}
	.save {
		text-align: center;
		height: 80rpx;
		line-height: 80rpx;
	}
	.close {
		position: absolute;
		top: 0;
		right: 20rpx;
		width: 40rpx;
		height: 80rpx;
		font-size: 50rpx;
	}
}

.share-popup,
.uni-popup__wrapper-box {
	.share-title {
		line-height: 60rpx;
		font-size: $ns-font-size-lg;
		padding: 15rpx 0;
		text-align: center;
	}

	.share-content {
		display: flex;
		display: -webkit-flex;
		-webkit-flex-wrap: wrap;
		-moz-flex-wrap: wrap;
		-ms-flex-wrap: wrap;
		-o-flex-wrap: wrap;
		flex-wrap: wrap;
		padding: 15rpx;

		.share-box {
			flex: 1;
			text-align: center;

			.share-btn {
				margin: 0;
				padding: 0;
				border: none;
				line-height: 1;
				height: auto;
				text {
					margin-top: 20rpx;
					font-size: 24rpx;
					display: block;
					color: $ns-text-color-black;
				}
			}

			.iconfont {
				font-size: 80rpx;
				line-height: initial;
			}
			.iconpengyouquan,
			.iconiconfenxianggeihaoyou {
				color: #07c160;
			}
		}
	}

	.share-footer {
		height: 90rpx;
		line-height: 90rpx;
		border-top: 2rpx #f5f5f5 solid;
		text-align: center;
		color: #666;
	}
}

.selected-sku-spec {
	.box {
		text {
			margin-right: 10rpx;
			white-space: nowrap;
			overflow: hidden;
			margin-right: 10px;
			text-overflow: ellipsis;
		}
	}
}

// 秒杀模块
.goods-seckill {
	position: relative;
	padding: 0 18rpx 18rpx 30rpx;
	box-sizing: border-box;
	background-color: var(--custom-brand-color);
	display: flex;
	align-items: center;
	justify-content: space-between;
	.price-info {
		position: relative;
		.seckill-price {
			white-space: nowrap;
			overflow: hidden;
			font-size: 56rpx;
			color: #fff;
			font-weight: 500;
			line-height: 1;
			.symbol {
				font-size: $ns-font-size-base;
				font-weight: 700;
			}
			.tip{
				font-size: $ns-font-size-sm;
				font-weight: 400;
				color: rgba(255, 255, 255, 1);
				margin-left: 10rpx;
			}
		}
		.original-price {
			font-size: $ns-font-size-base;
			white-space: nowrap;
			overflow: hidden;
			color: #fff;
			font-weight: 400;
			.price {
				text-decoration: line-through;
			}
		}
		.sale-num {
			margin-left: 20rpx;
		}
	}
	.countdown {
		//width: 230rpx;
		height: 132rpx;
		padding-top: 10rpx;
		box-sizing: border-box;
		text-align: center;
		position: relative;
		&-lightning{
			width: 132rpx;
			height: 132rpx;
			position: absolute;
			left: -88rpx;
			top: 0;
		}
		.txt {
			text-align: center;
			font-size: $ns-font-size-xm;
			font-weight: 700;
			color: #fff;
		}
		.clockrun {
			margin-top: 5rpx;
			text-align: center;
			font-size: $ns-font-size-sm;
			color: #fff;

			/deep/ .custom {
				display: flex;
				justify-content: center;
				align-items: center;
			}
			/deep/.day {
				font-size: 28rpx;
				font-weight: 700;
				color: #fff;
			}

			/deep/.day-symbol {
				font-size: 28rpx;
				font-weight: 700;
				color: #fff;
				margin: 0 6rpx;
			}

			/deep/.hour, /deep/.minute, /deep/.second {
				font-size: 28rpx;
				font-weight: 700;
				color: var(--custom-brand-color);
				width: 44rpx;
				height: 44rpx;
				border-radius: 10rpx;
				background: #fff;
				display: flex;
				justify-content: center;
				align-items: center;
			}

			/deep/.hour-symbol, /deep/.minute-symbol, /deep/.second-symbol {
				font-size: 28rpx;
				font-weight: 400;
				color: #fff;
				margin: 0 6rpx;
			}
		}
	}
}

// 提示成为分销客
.distribution-guest{
	position: absolute;
	top:-100rpx;
	left:25rpx
}

.popup-dialog {
	overflow: hidden;
	background: #FFFFFF;
	box-sizing: border-box;
	.popup-dialog-header {
		height: 106rpx;
		line-height: 106rpx;
		text-align: center;
		font-size: 36rpx;
		color: #333333;
		font-weight: bold;
	}
	.popup-dialog-body {
		color: #656565;
		text-align: center;
		padding: 0 30rpx;
	}
	.popup-dialog-footer {
		margin: 0 32rpx;
		height: 140rpx;
		display: flex;
		align-items: center;
		justify-content: space-around;
		.button {
			width: 220rpx;
			height: 68rpx;
			line-height: 68rpx;
			text-align: center;
			border-radius: 34rpx;
			box-sizing: border-box;
			margin: 0;
			&.white {
				color: var(--custom-brand-color);
				background: #FFFFFF;
				border: 1rpx solid var(--custom-brand-color);
			}
			&.red {
				color: #FFFFFF;
				background: var(--custom-brand-color);

			}
		}
	}
}


// #ifdef APP-PLUS
.diy-back-btn {
	width: 64rpx;
	height: 64rpx;
	line-height: 65rpx;
	text-align: center;
	border-radius: 50%;
	background-color: rgba(0, 0, 0, 0.4);
	font-size: 42rpx;
	position: fixed;
	z-index: 2;
	top: calc(var(--status-bar-height) + 10rpx);
	left: 36rpx;
	color: #fff;
}
// #endif

.goods_name{
	width:calc(100% - 140rpx)
}

.buyer-info{
	width: 100%;
	background-color: #fff;
	border-radius: 20rpx;
	margin-bottom: 20rpx;
	.buyer-info-title{
		display: flex;
		align-items: center;
		justify-content: space-between;
		padding: 24rpx 24rpx 16rpx;
		font-size: 28rpx;
		color: #333;
		&>view:first-child{
			font-weight: bold;
		}
		&>view:last-child{
			color: #666666;
			font-size: 24rpx;
		}
	}
	.buyers-box{
		display: flex;
    padding: 6px 0 12px;
    overflow-x: auto;
    z-index: 111111;
		width: 736rpx;
		.buyers-list{
			width: 400rpx;
			height: 160rpx;
			padding: 0 24rpx;
			display: flex;
			align-items: center;
			background-color: #f5f5f5;
			// box-shadow: 0px 0px 6px rgba(0, 0, 0, 0.08);
			border-radius: 8rpx;
			margin-left: 24rpx;
			.buyers-list-left{
				flex: 1;
				height: 100%;
				.buyers-list-left-top{
					display: flex;
					margin: 18rpx 0 10rpx;
					&>image{
						width: 48rpx;
						height: 48rpx;
						border-radius: 50%;
					}
					&>view{
						width: 200rpx;
						overflow: hidden;
						text-overflow: ellipsis;
						white-space: nowrap;
						font-size: 26rpx;
						color: #999;
						margin-left: 10rpx;
					}
				}
				.buyers-list-left-bottom{
					padding-right: 24rpx;
					font-size: 28rpx;
					color: #333;
					line-height: 32rpx;
					word-break: break-all;
					text-overflow: ellipsis;
					overflow: hidden;
					display: -webkit-box;
					-webkit-line-clamp: 2;
					-webkit-box-orient: vertical;
				}
			}
			.buyers-list-right{
				width: 120rpx;
				height: 120rpx;
				border-radius: 8rpx;
			}
		}
	}
	.buyers-not{
		width: 100%;
		padding: 6px 0 12px;
		box-sizing: border-box;
		display: flex;
		flex-direction: column;
		justify-content: center;
		align-content: center;
		&-tip{
			font-size: 24rpx;
			color: #666666;
			text-align: center;
		}
		&-op{
			display: inline-flex;
			align-items: center;
			justify-content: center;
			width: 180rpx;
			height: 44rpx;
			line-height: 44rpx;
			border: 1px solid var(--custom-brand-color);
			border-radius: 44rpx;
			color: var(--custom-brand-color);
			background-color: white;
			font-size: 24rpx;
			margin: 0 auto;
			margin-top: 16rpx;
		}
	}
}
.address-popup{
	/deep/.uni-popup__wrapper{
		border-radius: 40rpx 40rpx 0 0;
	}
	&-header{
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding: 36rpx 32rpx;
		box-sizing: border-box;
		&-close{
			width: 32rpx;
			height: 32rpx;
			font-size: 20rpx;
			background: rgba(229, 229, 229, 1);;
			border-radius: 50%;
			line-height: 34rpx;
			text-align: center;
			color: #fff
		}
	}
	&-content{
		min-height: 700rpx;
	}
	&-list{
		padding: 0 22rpx;
		box-sizing: border-box;
		max-height: 750rpx;
		overflow-y: auto;
		&-one{
			display: flex;
			align-items: center;
			padding: 36rpx 24rpx;
			box-sizing: border-box;
			background: rgba(250, 250, 250, 1);
			border-radius: 20rpx;
			margin-bottom: 20rpx;
			&-active{
				background: var(--custom-brand-color-10);
			}
			&-left{
				margin-right: 36rpx;
			}
			&-right{
				&-one{
					font-size: 32rpx;
					font-weight: 400;
					line-height: 40rpx;
					color: rgba(56, 56, 56, 1);
					&-name{
						font-weight: 700;
						margin-right: 6rpx;
					}
					&-tip{
						width: 65.86rpx;
						height: 32rpx;
						border-radius: 4rpx;
						background: rgba(255, 255, 255, 1);
						border: 2rpx solid var(--custom-brand-color);
						font-size: 24rpx;
						font-weight: 400;
						line-height: 32rpx;
						color: var(--custom-brand-color);
						display: inline-flex;
						justify-content: center;
						align-items: center;
						margin-left: 6rpx;
					}
				}
				&-two{
					font-size: 28rpx;
					font-weight: 400;
					line-height: 36rpx;
					color: rgba(128, 128, 128, 1);
				}
			}
		}
	}
	&-op{
		border-top: 2rpx solid rgba(245, 245, 245, 1);
		height: 166rpx;
		display: flex;
		justify-content: center;
		align-items: center;
		text{
			width: 672rpx;
			height: 80rpx;
			border-radius: 40rpx;
			background: var(--custom-brand-color);
			font-size: 32rpx;
			font-weight: 400;
			line-height: 40rpx;
			color: rgba(255, 255, 255, 1);
			display: flex;
			justify-content: center;
			align-items: center;
		}
	}
	&-empty{
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		height: 600rpx;
		&-img{
			width: 240rpx;
			height: 240rpx;
			margin-bottom: 52rpx;
		}
		&-tip{
			font-size: 32rpx;
			font-weight: 400;
			line-height: 37.26rpx;
			color: rgba(56, 56, 56, 1);
			margin-bottom: 52rpx;
		}
		&-op{
			width: 400rpx;
			height: 80rpx;
			border-radius: 40rpx;
			background: var(--custom-brand-color);
			font-size: 32rpx;
			font-weight: 400;
			line-height: 32rpx;
			color: rgba(255, 255, 255, 1);
			display: flex;
			justify-content: center;
			align-items: center;
		}
	}
}
/deep/ ._img{
	display: block;
}
.free-postage{
	width: 97.18rpx;
	height: 40rpx;
	line-height: 40rpx;
	border-radius: 20rpx;
	background: var(--custom-brand-color-10);
	font-size: 26rpx;
	font-weight: 400;
	color: var(--custom-brand-color);
	display: flex;
	justify-content: center;
	align-items: center;
}
