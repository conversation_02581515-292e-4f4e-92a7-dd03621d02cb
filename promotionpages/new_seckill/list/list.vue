<template>
	<view :class="themeStyle" :style="[themeColorVar]">
		<view v-show="show">
			<view class="tabbar" v-if="false">
				<image class="tabbar-image" :src="$util.img('/public/static/youpin/seckill-icon.png')" mode="widthFix">
				</image>
				<scroll-view scroll-x="true" class="scrollView">
					<view class="tabbar-list">
						<view class="item" :class="{active:seckill_id == row.seckill_id}"
							v-for="(row,index) in seckillGroup" :key="index" @click="changeTab(row)">
							<view class="time">{{row.start_time}}</view>
							<view class="status">{{row.api_status_text}}</view>
							<view class="date">{{row.start_date}}</view>
						</view>
					</view>
				</scroll-view>
			</view>

			<mescroll-uni ref="mescroll" @getData="getData" :size="10" :top="headerTop" v-if="isLoad" @scroll="scrollTouch">
				<block slot="list">
					<view class="last-time flex-start-center" v-if="selectItem.api_status!=-1">
						<text class="ns-margin-right">{{selectItem.api_status == 1 ? '距离结束时间' : '距离开始时间'}}</text>
						<view class="clockrun">
							<countdown-timer ref="countdown" :time="time" @finish="onFinish" autoStart>
							</countdown-timer>
						</view>
					</view>
					<view class="seckill-goods-new" v-if="dataList.length">
						<view class="goods-item" v-for="(item, key) in dataList" :key="key"
							@click="toGoodsDetail(item.sku_id,item.remain_stock)">
							<view class="goods-img">
								<image class="expose_goods_index" :data-expose_goods_sku="item.sku_id" :src="$util.img(item.goods_image)" @error="imageError(key)" mode='aspectFit'>
								</image>
								<image :src="$util.img('public/static/youpin/product-sell-out.png')" class="over"
									v-if="item.goods_stock == 0"></image>
							</view>
							<view class="goods-content">
								<text class="title">{{ item.goods_name }}</text>
								<view class="progress-box flex-start-center" v-if="selectItem.api_status == 1">
									<!--<text>库存量</text>-->
									<!--<view class="progress-line">-->
									<!--<ns-progress :progress="item.percentage"></ns-progress>-->
									<!--</view>-->
									<text>剩余 {{item.remain_stock}} 件</text>
								</view>
								<view class="goods-content-info flex-space-between">
									<view class="goods-content-bottom">
										<view class="strong ns-text-color"><text>￥</text>{{ item.seckill_price }}</view>
										<view class="del">￥{{ item.market_price }}</view>
									</view>
									<view class="goods-details-btn"
										v-if="selectItem.api_status == 0 || selectItem.api_status == -1">
										<text class="going">查看详情</text>
									</view>
									<view class="goods-details-btn" v-if="selectItem.api_status == 1">
										<text class="going" v-if="item.remain_stock != 0">去抢购</text>
										<text class="ungoing" v-else>已抢完</text>
									</view>
								</view>
							</view>
						</view>
					</view>

					<view v-if="!dataList.length">
						<ns-empty :fixed="false"></ns-empty>
					</view>
				</block>
			</mescroll-uni>
		</view>
		<view class="big-empty" v-show="!show">
			<ns-empty text="当前秒杀活动已全部结束"
				:emptyBtn="{ text: '去逛逛',url:'/otherpages/shop/home/<USER>', mode:'redirectTo' }"></ns-empty>
		</view>

		<loading-cover ref="loadingCover"></loading-cover>
	</view>
</template>
<script>
	// import nsProgress from '@/components/ns-progress/ns-progress.vue';
	import countdownTimer from '@/components/countdown-timer/countdown-timer.vue';
	import system from "@/common/js/system.js";
	import wx_expose_goods from '@/common/mixins/wx_expose_goods.js';
  import golbalConfig from "../../../common/mixins/golbalConfig";
  // #ifdef H5
  import {isOnXianMaiApp} from "../../../common/js/h5/appOP";
  // #endif
	export default {
		components: {
			// nsProgress,
			countdownTimer
		},
		mixins:[wx_expose_goods,golbalConfig],
		data() {
			return {
				mescroll: null,
				seckillInfo: {}, //时间信息
				dataList: [], //选中时间块的商品列表
				time: 999999, //倒计时，type=0 为活动距离结束时间，type=1 为活动距离开始时间
				selectItem: {
					api_status: -1
				},
				seckillGroup: [], // 秒杀活动时间表
				seckill_id: 0,
				isLoad: false,
        headerTop:0,
			};
		},
		onLoad(data) {
			// type=0 为活动距离结束时间，type=1 为活动距离开始时间
			// if(data && data.type){
			// 	this.type = data.type
			// }
			if (data && data.seckill_id) {
				this.seckill_id = data.seckill_id
			}
      // #ifdef H5
      if(isOnXianMaiApp){
        this.headerTop+=88;
      }
      // #endif
			this.getSeckillGroup()

		},
		async onShow() {
			// this.mescroll.resetUpScroll(false);
			// 刷新多语言
			// this.$langConfig.refresh();
			await system.wait_staticLogin_success();
			// 修改顶部标题
			this.getDiyInfo()

			// #ifdef H5
			let share_data = this.$util.deepClone(this.getSharePageParams())
			let link = window.location.origin + this.$router.options.base + share_data.link.slice(1)
			share_data.link = link
			share_data.desc = share_data.title
			share_data.title = '先迈商城'
			await this.$util.publicShare(share_data);
			// #endif
		},
		computed: {
			themeStyle() {
				return 'theme-' + this.$store.state.themeStyle
			},
			show() {
				return this.dataList.length > 0
			},
		},
		watch: {},
		filters: {
			fillWithZero(num) {
				var len = num.toString().length;
				while (len < 2) {
					num = "0" + num;
					len++;
				}
				return num;
			},
		},

		methods: {

			async getDiyInfo() {
				let res = await this.$api.sendRequest({
					url: '/api/diyview/info',
					async: false,
					data: {
						name: 'DIYVIEW_INDEX'
					}
				});

				if (res.data) {
					let data = JSON.parse(res.data.value).value
					data.forEach(v => {
						if (v.controller == 'Seckill' && v.name) {
							uni.setNavigationBarTitle({
								title: v.name || '秒杀活动'
							})
						}
					})
				}
			},
			getSeckillGroup() {
				this.isLoad = false
				this.$api.sendRequest({
					url: this.$apiUrl.seckillGroup,
					data: {},
					success: res => {
						this.isLoad = true
						if (res.code == 0 && res.data.length) {
							this.seckillGroup = res.data;
							if (this.seckill_id > 0) {
								this.selectItem = res.data.filter(e => e.seckill_id == this.seckill_id)[0]
								if (this.$refs.mescroll) this.$refs.mescroll.refresh();
								return
							}
							// 链接没带id的时候执行（默认按 秒杀中--即将开始--第一个）
							if (res.data.filter(e => e.api_status == 1)[0]) {
								this.seckill_id = res.data.filter(e => e.api_status == 1)[0].seckill_id;
							} else if (res.data.filter(e => e.api_status == 0)[0]) {
								this.seckill_id = res.data.filter(e => e.api_status == 0)[0].seckill_id;
							} else {
								if (res.data.length > 0) {
									this.seckill_id = res.data[0].seckill_id
								}
							}
							this.selectItem = res.data.filter(e => e.seckill_id == this.seckill_id)[0]
							if (this.$refs.mescroll) this.$refs.mescroll.refresh();
						}
					}
				})
			},
			getData(mescroll) {
				this.mescroll = mescroll;
				if (this.seckillGroup.length == 0) {
					this.dataList = []
					if (this.$refs.loadingCover) this.$refs.loadingCover.hide();
					return
				}
				if (mescroll.size == 1) {
					this.dataList = [];
				}

				this.$api.sendRequest({
					url: this.$apiUrl.getSeckillList,
					data: {
						page_size: mescroll.size,
						page: mescroll.num,
						seckill_id: this.seckill_id
					},
					success: res => {
						let newArr = []
						let msg = res.message;
						if (res.code == 0 && res.data) {
							if (res.data.list && res.data.list.length != 0) {
								res.data.list.forEach((item, index) => {
									item.percentage = ((item.sale_num / (item.sale_num + item
										.remain_stock)).toFixed(2)) * 100;
								})
							}
							newArr = res.data.list;
							this.seckillInfo = res.data.seckillInfo;
						} else {
							this.$util.showToast({
								title: msg
							})
						}
						mescroll.endSuccess(newArr.length);
						//设置列表数据
						if (mescroll.num == 1) this.dataList = []; //如果是第一页需手动制空列表
						this.dataList = this.dataList.concat(newArr); //追加新数据

						//当有数据的时候才进行倒计时,无数据时代表该秒杀活动全部结束
						if (this.dataList.length != 0) {
							if (res.data.seckillInfo) {
								//需取绝对值
								if (this.$refs.countdown) this.$refs.countdown.reset()
								this.time = Math.abs(res.data.seckillInfo.action_time) * 1000
								// this.time = 5 * 1000
							}
						}

						if (this.$refs.loadingCover) this.$refs.loadingCover.hide();
					},
					fail() {
						//联网失败的回调
						mescroll.endErr();
						// if (this.$refs.loadingCover) this.$refs.loadingCover.hide();
					}
				});
			},
			onFinish() {
				this.getSeckillGroup()
			},
			changeTab(row) {
				this.seckill_id = row.seckill_id;
				this.selectItem = row
				this.$refs.mescroll.refresh();
				this.againDealWith(true)
			},
			//跳转到详情页
			toGoodsDetail(sku_id, stock) {
				if (this.selectItem.api_status == 1) {
					if (stock == 0) return
					this.$util.redirectTo('/promotionpages/new_seckill/detail/detail', {
						sku_id: sku_id
					});
				} else {
					this.$util.redirectTo('/pages/goods/detail/detail', {
						sku_id: sku_id
					});
				}
			},
			imageError(index) {
				this.dataList[index].goods_image = this.$util.getDefaultImage().default_goods_img;
				this.$forceUpdate();
			},
			getSharePageParams() {
        let params ={}
        if(this.seckill_id){
          params.seckill_id = this.seckill_id
        }
        let share_data=this.$util.unifySharePageParams('/promotionpages/new_seckill/list/list','每日10点，优惠不停！',
            '',params,this.$util.img('public/static/youpin/seckill_share.jpg'))
        return share_data;
			}
		},
		// 分享
		onShareAppMessage(res) {
      let share_data = this.getSharePageParams();
      return this.$buriedPoint.pageShare(share_data.link, share_data.imageUrl,share_data.title);
		},
		// 分享到微信
		onShareTimeline(res) {
			let {
				title,
				imageUrl,
				query
			} = this.getSharePageParams()
			return {
				title,
				imageUrl,
				query,
				success: res => {},
				fail: res => {}
			};
		}
	};
</script>

<style lang="scss">
	.tabbar {
		position: fixed;
		background: #fff;
		width: 100%;
		/* #ifdef MP-WEIXIN */
		top: 0;
		/* #endif */
		/* #ifdef H5 */
		left: 88rpx;
		/* #endif */
		left: 0;
		height: 120rpx;
		display: flex;
		align-items: center;
		justify-content: space-between;
		box-sizing: border-box;
		padding: 0 36rpx;

		.tabbar-image {
			width: 84rpx;
		}

		.scrollView {
			width: calc(100% - 100rpx);

			.tabbar-list {
				display: flex;
				align-items: center;

				.item {
					width: 25%;
					text-align: center;

					view {
						line-height: 1;
					}

					.time {
						font-size: 34rpx;
						color: #333;
						font-weight: bold;
					}

					.status {
						color: #333;
						font-size: 24rpx;
						width: 110rpx;
						line-height: 32rpx;
						border-radius: 16rpx;
						margin: 14rpx auto 4rpx auto;
					}

					.date {
						font-size: 20rpx;
						color: #999;
					}

					&.active {
						.time {
							color: #F84346;
						}

						.status {
							background: #F84346;
							color: #fff;
						}
					}
				}
			}
		}

	}

	// 活动开始头部
	.seckill-header {
		background-color: #ffffff;
		padding-bottom: 10rpx;

		.seckill-title {
			padding: 10rpx 0 20rpx 0;
			font-size: $ns-font-size-base;

			text {
				position: relative;
				padding: 0 100rpx;
			}

			.cur {
				color: $base-color;
				font-weight: bold;
			}

			.cur::after {
				content: '';
				display: inline-block;
				width: 40rpx;
				height: 6rpx;
				background-color: $base-color;
				position: absolute;
				left: 50%;
				bottom: -10rpx;
				transform: translateX(-50%);
				border-radius: 8rpx;
			}
		}
	}

	.last-time {
		padding: 24rpx 24rpx 0 24rpx;
		font-size: $ns-font-size-base;

		/deep/ .custom {
			display: flex;
		}

		/deep/ .custom :nth-child(odd) {
			background-color: var(--custom-brand-color);
			width: 46rpx;
			height: 40rpx;
			line-height: 40rpx;
			color: white;
			border-radius: 6upx;
			font-size: $ns-font-size-base;
			text-align: center;
			overflow: hidden;
		}

		/deep/ .custom :nth-child(even) {
			padding: 0 8upx;
		}

	}

	// 秒杀列表
	.seckill-goods-new {
		width: 100%;
		display: flex;
		flex-direction: row;
		flex-wrap: wrap;

		.goods-item {
			display: flex;
			flex-wrap: wrap;
			justify-content: space-between;
			background-color: #fff;
			border-radius: $ns-border-radius;
			width: 100%;
			overflow: hidden;
			margin: 24rpx 24rpx 0 24rpx;
			padding: 24rpx;
		}

		.goods-img {
			position: relative;
			width: 180rpx;
			height: 180rpx;
			margin-right: 20rpx;

			image {
				width: 100%;
				height: 100%;
				border-radius: 20rpx;
			}

			.over {
				width: 100rpx;
				height: 100rpx;
				position: absolute;
				left: 50%;
				top: 50%;
				transform: translate(-50%, -50%);
			}
		}

		.goods-content {
			flex: 1;
			display: flex;
			flex-direction: column;
			justify-content: space-between;

			.title {
				overflow: hidden;
				display: inline-block;
				font-size: $ns-font-size-base;
				color: #333333;
				font-weight: bold;
				line-height: 1.3;
				text-overflow: ellipsis;
				display: -webkit-box;
				-webkit-line-clamp: 1;
				-webkit-box-orient: vertical;
				margin-bottom: 5rpx;
			}

			.subtitle {
				overflow: hidden;
				text-overflow: ellipsis;
				display: -webkit-box;
				-webkit-line-clamp: 1;
				-webkit-box-orient: vertical;
				font-size: $ns-font-size-sm;
				color: $base-color;

				.subtitle-tap {
					color: #ffffff;
					padding: 4rpx 10rpx;
					background-color: $base-color;
					border-radius: 4rpx;
					font-size: 22rpx;
					margin-right: 10rpx;
				}
			}

			.progress-box {
				padding-top: 40rpx;
				font-size: 22rpx;
				color: $ns-vice-color-black;

				text:first-child {
					padding-right: 15rpx;
				}

				text:last-child {
					padding-left: 15rpx;
				}

				.progress-line {
					width: 180rpx;
					position: relative;

					.sales-num {
						color: $ns-vice-color-black;
						position: absolute;
						width: 100%;
						text-align: center;
						font-size: 22rpx;
						left: 50%;
						top: -100%;
						transform: translate(-50%, -50%);
					}

				}

				/deep/ .progress-line .progress {
					background-color: #FFEFEF;
					height: 16rpx;
				}
			}

			.goods-content-info {
				width: 100%;
				padding-top: 10rpx;

				.goods-content-bottom {
					display: flex;
					justify-content: flex-start;
					flex-direction: row;
					align-items: center;

					.strong {
						font-size: $ns-font-size-lg;
						font-weight: bold;

						text {
							font-size: $ns-font-size-xm;
							font-weight: normal;
						}
					}

					.del {
						margin: 0 0 0 15rpx;
						font-size: 24rpx;
						color: #ccc;
						text-decoration: line-through;
					}
				}

				.goods-details-btn text {
					padding: 10rpx 20rpx;
					font-size: $ns-font-size-sm;
					border-radius: 50rpx;
				}

				.going {
					color: #ffffff;
					background-color: var(--custom-brand-color);
				}

				.ungoing {
					color: #ffffff;
					background-color: $ns-btn-color-gray;
				}
			}
		}
	}

	/deep/.mescroll-upwarp {
		padding: 0 !important;
		margin-bottom: 0;
		min-height: 0;
		line-height: 0;
	}

	/deep/.mescroll-uni {
		height: auto !important;
	}
</style>
