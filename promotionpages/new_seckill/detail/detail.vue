<template>
	<view :class="themeStyle" :style="[themeColorVar]">
    <!-- #ifdef H5 -->
    <uni-nav-bar left-icon="back" :border="false" @clickLeft="appGoBack" v-if="isOnXianMaiApp">
      <template>
        <view class="page-title">{{goodsSkuDetail.goods_name}}</view>
      </template>
    </uni-nav-bar>
    <!-- #endif -->
		<view class="goods-detail">

			<view class="goods-container">
				<!-- 商品媒体信息 -->
				<view class="goods-media">
					<!-- 商品图片 -->
					<view class="goods-img" :class="{ show: switchMedia == 'img' }">
						<swiper class="swiper" @change="swiperChange" :interval="swiperInterval" :autoplay="swiperAutoplay" :circular="true">
							<swiper-item v-for="(item, index) in goodsSkuDetail.sku_images" :key="index">
								<view class="item" @click="previewMedia(index)">
									<image :src="$util.img(item)" @error="swiperImageError(index)" mode="aspectFit" />
								</view>
							</swiper-item>
						</swiper>
						<view class="img-indicator-dots">
							<text>{{ swiperCurrent }}</text>
							<text v-if="goodsSkuDetail.sku_images">/{{ goodsSkuDetail.sku_images.length }}</text>
						</view>
					</view>
					<!-- 商品视频 -->
					<view class="goods-video" :class="{ show: switchMedia == 'video' }">
						<!-- #ifndef H5 -->
						<video :src="$util.img(goodsSkuDetail.video_url)" :poster="$util.img(goodsSkuDetail.sku_image)" objectFit="contain"></video>
						<!-- #endif -->
						<!-- #ifdef H5 -->
						<view class="video-img">
							<image :src="$util.img(goodsSkuDetail.sku_image)" mode=""></image>
							<view class="video-open"><view class="iconfont iconarrow-" @click="openVideo"></view></view>
						</view>
						<!-- #endif -->
					</view>

					<!-- 切换视频、图片 -->
					<view class="media-mode" v-if="goodsSkuDetail.video_url != ''">
						<text :class="{ 'ns-bg-color': switchMedia == 'video' }" @click="switchMedia = 'video'">{{ $lang('video') }}</text>
						<text :class="{ 'ns-bg-color': switchMedia == 'img' }" @click="switchMedia = 'img'">{{ $lang('image') }}</text>
					</view>
					<image v-if="goodsSkuDetail.stock==0" :src="$util.img('public/static/youpin/product-over.png')" class="over"></image>
				</view>
				<view @touchmove.prevent.stop class="videoPopup-box">
					<uni-popup ref="videoPopup" type="center">
						<view class="pop-video">
							<video :src="$util.img(goodsSkuDetail.video_url)" :poster="$util.img(goodsSkuDetail.sku_image)" objectFit="contain"></video>
						</view>
					</uni-popup>
				</view>

				<!-- 秒杀 -->
				<view class="goods-seckill">
					<view class="price-info">
						<view class="seckill-price">
							<text class="symbol">￥</text>
							<text>{{ goodsSkuDetail.price }}</text>
              <text class="tip">活动价</text>
						</view>
						<view class="original-price">
							<text class="price">￥ {{ goodsSkuDetail.market_price }}</text>
						</view>
					</view>

					<view class="countdown">
            <image :src="$util.img('public/static/youpin/lightning.png')" class="countdown-lightning"></image>
						<view class="txt">{{goodSeckillDetail.seckill_status == 0 ? '距活动开始还剩' : '距活动结束还剩'}} </view>
						<view class="clockrun">
							<countdown-timer ref="countdown" :show-day-symbol="true" :time="time" @finish="onFinish" autoStart></countdown-timer>
						</view>
					</view>
				</view>

				<view class="group-wrap">
					<!-- <view class="goods-module-wrap" :class="{ discount: preview == 0 && goodsSkuDetail.promotion_type == 1 }">
						<view class="goods-module-wrap-box">
							<view>
								<template v-if="goodsSkuDetail.promotion_type == 0">
									<text class="price-symbol ns-text-color">{{ $lang('common.currencySymbol') }}</text>
									<text class="price ns-text-color">{{ goodsSkuDetail.retail_price }}</text>
									<text class="market-price-symbol" v-if="goodsSkuDetail.market_price > 0">{{ $lang('common.currencySymbol') }}</text>
									<text class="market-price" v-if="goodsSkuDetail.market_price > 0">{{ goodsSkuDetail.market_price }}</text>
								</template>
							</view>
						</view>
					</view> -->

					<view class="goods-module-wrap">
						<view class="goods_name">
							<view class="sku-name-box">
							<text class="sku-name" @click="copytextShow = true" @longpress="longpress" >{{ goodsSkuDetail.goods_name }}</text>
							<view class="showCopybox" v-if="copytextShow">
								<view class="copytext"><text  @click="$util.copy(goodsSkuDetail.goods_name,copyCallback)" class="fuzhi">复制</text><text class="quxiao" @click="copytextShow = false">取消</text></view>
							</view>
							</view>
							<text class="introduction ns-text-color-gray" v-if="goodsSkuDetail.introduction">{{ goodsSkuDetail.introduction }}</text>
						</view>
						<view class="adds-wrap">
							<block v-if="Development">
								<text v-if="goodsSkuDetail.is_free_shipping">快递免邮</text>
								<text v-else>快递不免邮</text>
							</block>
							<!-- <text class="adds-wrap-volume">已销量 {{ goodsSkuDetail.sale_num }} {{ goodsSkuDetail.unit }}</text> -->
						</view>
					</view>
          <button class="group-wrap-share" :plain="true"  @click="openSharePopup">
<!--            <view class="iconfont iconfenxiang"></view>-->
            <image :src="$util.img('public/static/youpin/share-new.png')" class="group-wrap-share-vip-share"></image>
            <text>分享</text>
          </button>
				</view>

				<view class="buyer-info" v-if="!is_audit_mode && buyerList.length && false">
					<view class="buyer-info-title">
						<view>买家秀（{{buyerList.length}}）</view>
						<view @click="buyersMoreFun(false)" v-if="buyerList.length">更多<text class="iconfont icon iconright" style="font-size: 28rpx"></text></view>
					</view>
					<view class="buyers-box" v-if="buyerList.length">
						<view class="buyers-list" v-for="(item, index) in buyerList" :key="index" @click="buyersDetailFun(item)">
							<view class="buyers-list-left">
								<view class="buyers-list-left-top">
									<image :src="item.headimg"></image>
									<view>{{item.nickname}}</view>
								</view>
								<view class="buyers-list-left-bottom">{{item.title}}</view>
							</view>
							<image :src="item.image" class="buyers-list-right"></image>
						</view>
					</view>
          <view class="buyers-not" v-else>
            <view class="buyers-not-tip">快成为首席体验官吧~</view>
            <view class="buyers-not-op" @click="buyersMoreFun(true)">发布买家秀</view>
          </view>
				</view>

				<share-popup v-if="isShowCanvas" :canvasOptions="canvasOptions" ref="sharePopup" :sharePopupOptions="sharePopupOptions"></share-popup>

<!--				<ns-fenxiao-good-detail :skuId="skuId" ref="fenxiaoPopup"></ns-fenxiao-good-detail>-->

				<view class="group-wrap group-wrap-padding">
					<!-- 已选规格 -->
					<view class="goods-cell selected-sku-spec" v-if="goodsSkuDetail.sku_spec_format" @click="chooseSkuspecFormat">
						<view class="box">
							<text class="tit">已选择</text>
							<text v-for="(item, index) in goodsSkuDetail.sku_spec_format" :key="index">{{ item.spec_name }}/{{ item.spec_value_name }}</text>
						</view>
            <text class="iconfont iconright"></text>
					</view>
          <!--  配送地址选择      -->
          <view class="goods-cell" @click="showAddressPopup">
            <view class="box goods-cell-left">
              <text class="tit">配送</text>
              <text>至 {{address_info.full_address}}</text>
            </view>
            <view class="goods-cell-right">
              <view v-if="parseFloat(delivery_money)>0">运费:
                <text style="color:var(--custom-brand-color);">{{delivery_money}}元</text>
              </view>
              <view class="free-postage" v-else>包邮</view>
              <text class="iconfont iconright"></text>
            </view>
          </view>
					<!-- #ifdef MP-WEIXIN -->
					<view v-if="goodsSkuDetail.sku_images" class="goods-cell">
					  <guarantee-bar  pageType="goods_detail" :goodsName="goodsSkuDetail.goods_name" :goodsImg="goodsSkuDetail.sku_images && goodsSkuDetail.sku_images.length ? $util.img(goodsSkuDetail.sku_images[0]) : ''"
					                  align="between" spaceSize="12" :goodsPrice="`${goodsSkuDetail.price}元`"  :bannerStyle="{fontSize: 'mini',fontOpacity: 'gray',}" style="width: 100%"/>
					</view>
					<!-- #endif -->

					<!-- 商品属性 -->
					<view class="goods-cell" @click="openAttributePopup()" v-if="goodsSkuDetail.goods_attr_format && goodsSkuDetail.goods_attr_format.length > 0">
						<view class="box">
							<text class="tit">规格参数</text>
							<!-- 							<text>{{ goodsSkuDetail.goods_attr_format[0].attr_name }} {{ goodsSkuDetail.goods_attr_format[0].attr_value_name }}...</text> -->
						</view>
						<text class="iconfont iconright"></text>
					</view>

					<view @touchmove.prevent.stop>
						<uni-popup ref="attributePopup" type="bottom">
							<view class="goods-attribute-popup-layer">
								<text class="title">规格参数</text>
								<scroll-view scroll-y class="goods-attribute-body">
									<view class="item ns-border-color-gray" v-for="(item, index) in goodsSkuDetail.goods_attr_format" :key="index">
										<text class="ns-text-color-gray">{{ item.attr_name }}</text>
										<text class="value">{{ item.attr_value_name }}</text>
									</view>
								</scroll-view>
								<view class="button-box"><button type="primary" @click="closeAttributePopup()">确定</button></view>
							</view>
						</uni-popup>
					</view>
				</view>

				<!-- 组合套餐 -->
				<view v-if="preview == 0 && bundling.length && bundling[0].bl_name && addonIsExit.bundling">
					<view class="group-wrap" @click="openBundlingPopup()">
						<view class="goods-cell" @click="openBundlingPopup()">
							<view class="box">
								<text class="tit">组合套餐</text>
								<text>{{ bundling[0].bl_name }}</text>
							</view>
							<text class="iconfont iconright"></text>
						</view>

						<view class="combo-goods-wrap ns-text-color-gray">
							<navigator hover-class="none" class="goods ns-border-color-gray" :url="'/pages/goods/detail/detail?sku_id=' + skuId">
								<image :src="$util.img(goodsSkuDetail.sku_image)" @error="imageError()" />
								<text>¥{{ goodsSkuDetail.price }}</text>
							</navigator>
							<view class="iconfont iconadd1 ns-text-color-gray"></view>
							<block v-for="(item, index) in bundling[0].bundling_goods" :key="index">
								<template v-if="index < 3">
									<navigator hover-class="none" class="goods ns-border-color-gray" :url="'/pages/goods/detail/detail?sku_id=' + item.sku_id">
										<image :src="$util.img(item.sku_image)" @error="bundlingImageError(0, index)" />
										<text>¥{{ item.price }}</text>
									</navigator>
								</template>
							</block>
						</view>
					</view>

					<view @touchmove.prevent.stop>
						<uni-popup ref="bundlingPopup" type="bottom">
							<view class="bundling-popup-layer">
								<text class="title">组合套餐</text>
								<scroll-view scroll-y class="bundling-body">
									<block v-for="(item, index) in bundling" :key="index">
										<scroll-view scroll-x>
											<view class="item ns-border-color-gray">
												<navigator hover-class="none" class="value" :url="'/promotionpages/combo/detail/detail?bl_id=' + item.bl_id">
													<text>{{ item.bl_name }}：￥{{ item.bl_price }}</text>
													<view class="right">
														<text class="ns-text-color">查看</text>
														<text class="iconfont iconright"></text>
													</view>
												</navigator>
												<view class="goods-wrap">
													<navigator hover-class="none" class="goods" :url="'/pages/goods/detail/detail?sku_id=' + skuId">
														<image :src="$util.img(goodsSkuDetail.sku_image)" @error="imageError()" />
														<text>¥{{ goodsSkuDetail.price }}</text>
													</navigator>
													<view class="iconfont iconadd1 ns-text-color-gray"></view>
													<block v-for="(goods, goods_index) in item.bundling_goods" :key="goods_index">
														<template v-if="goods_index < 3">
															<navigator hover-class="none" class="goods" :url="'/pages/goods/detail/detail?sku_id=' + goods.sku_id">
																<image :src="$util.img(goods.sku_image)" @error="bundlingImageError(index, goods_index)" />
																<text>¥{{ goods.price }}</text>
															</navigator>
														</template>
													</block>
												</view>
											</view>
										</scroll-view>
									</block>
								</scroll-view>
								<view class="button-box"><button type="primary" @click="closeBundlingPopup()">确定</button></view>
							</view>
						</uni-popup>
					</view>
				</view>

				<!-- 店铺信息 -->
				<block v-if="Development">
					<view class="group-wrap" v-if="preview == 0">
						<view class="shop-wrap">
							<navigator hover-class="none" :url="'/otherpages/shop/index/index?site_id=' + shopInfo.site_id" class="box">
								<view class="shop-logo">
									<image
										v-if="shopInfo.avatar"
										:src="$util.img(shopInfo.avatar)"
										@error="shopInfo.avatar = $util.getDefaultImage().default_shop_img"
										mode="aspectFit"
									/>
									<image v-else :src="$util.getDefaultImage().default_shop_img" mode="aspectFit" />
								</view>
								<view class="shop-info">
									<text>{{ shopInfo.site_name }}</text>
									<view class="description" v-if="shopInfo.seo_description">{{ shopInfo.seo_description }}</view>
								</view>
							</navigator>
							<navigator hover-class="none" :url="'/otherpages/shop/index/index?site_id=' + shopInfo.site_id" class="box shop-score">
								<text>商品描述{{ shopInfo.shop_desccredit }}</text>
								<text>卖家服务{{ shopInfo.shop_servicecredit }}</text>
								<text>发货速度{{ shopInfo.shop_deliverycredit }}</text>
							</navigator>
							<view class="box">
								<view class="goods-action">
									<navigator hover-class="none" class="ns-text-color ns-border-color" :url="'/otherpages/shop/list/list?site_id=' + shopInfo.site_id">
										全部商品
									</navigator>
									<navigator hover-class="none" class="ns-text-color ns-border-color" :url="'/otherpages/shop/index/index?site_id=' + shopInfo.site_id">
										查看店铺
									</navigator>
								</view>
							</view>
						</view>
					</view>
				</block>

				<!-- 商品评价 -->
				<view class="group-wrap" v-if="preview == 0 && isShowEvaluate">
					<view class="goods-evaluate">
						<view class="tit">
							<view>
								<text>商品评价（{{ goodsSkuDetail.evaluate }}）</text>
							</view>
							<navigator class="ns-text-color" hover-class="none" :url="'/otherpages/goods/evaluate/evaluate?goods_id=' + goodsSkuDetail.goods_id">
								<text>查看更多</text>
								<text class="iconfont iconright"></text>
							</navigator>
						</view>
						<view class="evaluate-item" v-if="goodsEvaluate.content">
							<view class="evaluator">
								<view class="evaluator-face">
									<image
										v-if="goodsEvaluate.member_headimg"
										:src="$util.img(goodsEvaluate.member_headimg)"
										@error="goodsEvaluate.member_headimg = $util.getDefaultImage().default_headimg"
										mode="aspectFill"
									/>
									<image
										v-else
										:src="$util.getDefaultImage().default_headimgsss"
										@error="goodsEvaluate.member_headimg = $util.getDefaultImage().default_headimg"
										mode="aspectFill"
									/>
								</view>
								<text class="evaluator-name">{{ goodsEvaluate.member_name }}</text>
							</view>
							<view class="cont">{{ goodsEvaluate.content }}</view>
							<view class="evaluate-img" v-if="goodsEvaluate.images">
								<view class="img-box" v-for="(item, index) in goodsEvaluate.images" :key="index" @click="previewEvaluate(index, 'images')">
									<image :src="$util.img(item)" mode="aspectFit" />
								</view>
							</view>
							<view class="time">
								<text>{{ $util.timeStampTurnTime(goodsEvaluate.create_time) }}</text>
								<text>{{ goodsEvaluate.sku_name }}</text>
							</view>
						</view>
						<view class="evaluate-item-empty" v-else>该商品暂无评价哦</view>
					</view>
				</view>

				<!-- 详情 -->
				<view class="goods-detail-tab">
					<view class="detail-tab flex-center" v-if="isShowDetailTab">
						<view class="tab-item" :class="detailTab == 'productDetail' ? 'active ns-bg-before' : ''" @click="toPoint" data-id="productDetail">商品详情</view>
						<view class="tab-item" :class="detailTab == 'productSale' ? 'active ns-bg-before' : ''" @click="toPoint" data-id="productSale" v-if="afterSale">售后保障</view>
						<view class="tab-item" :class="detailTab == 'productServe' ? 'active ns-bg-before' : ''" @click="toPoint" data-id="productServe" v-if="service">服务说明</view>
					</view>
					<view class="detail-content">
						<view class="detail-content-item" id="productDetail">
              <view class="goods-details-title"><view></view><view>图文详情</view><view></view></view>
							<view class="goods-details" v-if="goodsSkuDetail.goods_content">
<!--                <rich-text :nodes="goodsSkuDetail.goods_content"></rich-text>-->
                <mphtml  :content="goodsSkuDetail.goods_content" :preview-img="true"/>
              </view>
							<view class="goods-details active" v-else>该商家暂无上传相关详情哦！</view>
						</view>
            <diy-goods-detail-more-goodies :sku_id="skuId"/>
						<view class="detail-content-item" id="productSale" v-if="afterSale">
              <view class="goods-details-title"><view></view><view>售后保障</view><view></view></view>
							<view class="goods-details"><rich-text :nodes="afterSale"></rich-text></view>
<!--							<view class="goods-details active" v-else>该商品暂无相关售后哦！</view>-->
						</view>
            <view class="detail-content-item" id="productServe" v-if="service">
              <view class="goods-details-title"><view></view><view>服务说明</view><view></view></view>
              <view class="goods-details"><rich-text :nodes="service"></rich-text></view>
<!--              <view class="goods-details active" v-else>该商品暂无相关服务说明哦！</view>-->
            </view>
					</view>
				</view>


				<!-- SKU选择 -->
				<ns-goods-sku ref="goodsSku" @refresh="refreshGoodsSkuDetail" :isShowNumChoose="true" :limitBuy="goodsSkuDetail.buy_num < 2" :goods-detail="goodsSkuDetail"></ns-goods-sku>

				<!-- 海报 -->
				<!-- <view @touchmove.prevent.stop>
					<uni-popup ref="posterPopup" type="bottom" class="poster-layer">
						<template v-if="poster != '-1'">
							<view :style="{ height: posterHeight > 0 ? posterHeight + 80 + 'px' : '' }">
								<view class="image-wrap"><image :src="$util.img(poster)" :style="{ height: posterHeight > 0 ? posterHeight + 'px' : '' }" /></view> -->
								<!-- #ifdef MP || APP-PLUS  -->
								<!-- <view class="save" @click="saveGoodsPoster()">保存图片</view> -->
								<!-- #endif -->
								<!-- #ifdef H5 -->
								<!-- <view class="save">长按保存图片</view> -->
								<!-- #endif -->
							<!-- </view>
							<view class="close iconfont iconclose" @click="closePosterPopup()"></view>
						</template>
						<view v-else class="msg">{{ posterMsg }}</view>
					</uni-popup>
				</view> -->

				<!-- 分享弹窗 -->
				<!-- <view @touchmove.prevent.stop>
					<uni-popup ref="sharePopup" type="bottom" class="share-popup">
						<view>
							<view class="share-title">分享</view>
							<view class="share-content"> -->
								<!-- #ifdef MP -->
								<!-- <view class="share-box">
									<button class="share-btn" :plain="true" open-type="share">
										<view class="iconfont iconiconfenxianggeihaoyou"></view>
										<text>分享给好友</text>
									</button>
								</view> -->
								<!-- #endif -->
								<!-- <view class="share-box" @click="openPosterPopup">
									<button class="share-btn" :plain="true">
										<view class="iconfont iconpengyouquan"></view>
										<text>生成分享海报</text>
									</button>
								</view>
							</view>
							<view class="share-footer" @click="closeSharePopup"><text>取消分享</text></view>
						</view>
					</uni-popup>
				</view> -->
				<ns-login ref="login"></ns-login>
    		<uni-coupon-pop ref="couponPop"></uni-coupon-pop>
			</view>
		</view>
		<loading-cover ref="loadingCover"></loading-cover>
		<!-- 商品底部导航 -->
		<ns-goods-action>
			<template v-if="goodsSkuDetail.goods_state == 1 && goodsSkuDetail.verify_state == 1">
				<ns-goods-action-icon text="首页" icon="iconshouye" :imgicon="$util.img('public/static/youpin/home-icon.png')" @click="goHome" />
        <!-- <ns-goods-action-icon :text="whetherCollection == 1 ? '已收藏' :'收藏'" :imgicon="whetherCollection == 1 ? $util.img('public/static/youpin/collect-has-icon.png') : $util.img('public/static/youpin/collect-icon.png')" @click="editCollection()"/> -->
		    <ns-goods-action-icon text="客服" icon="iconkefu" v-if="addonIsExit.servicer" @click="$util.getCustomerService()" />
        <view class="active-text">
          <text class="active-text-tip">活动价</text>
          <text class="active-text-price"><text class="active-text-price-symbol">￥</text>{{goodsSkuDetail.price}}</text>
        </view>
        <ns-goods-action-button
						class="goods-action-button"
						:class="goodsSkuDetail.is_virtual == 0 ? 'active2' : 'active4'"
						text="立即购买"
						disabledText="立即购买"
						:disabled = "goodsSkuDetail.stock ? false : true"
						background="var(--custom-brand-color)"
						@click="buyNow" />
			</template>
			<template v-else>
				<ns-goods-action-button class="goods-action-button active3" disabled-text="该商品已下架" :disabled="true" />
			</template>
		</ns-goods-action>
		<!-- 授权登录弹窗 -->
    <yd-auth-popup ref="ydauth"></yd-auth-popup>
    <ns-login ref="login"></ns-login>
    <uni-coupon-pop ref="couponPop"></uni-coupon-pop>
		<!-- 返回顶部按钮 -->
    <image :src="$util.img('public/static/youpin/to-top.png')" class="to-top" v-show="showTop" @click="scrollToTopNative"></image>
    <!-- 分享的页面不是用户绑定的id,提示无法购物弹窗 -->
    <uni-popup ref="popupBan" :maskClick="false">
      <view class="popup-dialog">
        <view class="popup-dialog-header">提示</view>
        <view class="popup-dialog-body">哎哟~系统好像出了点问题，暂时不能支付，请联系客服。</view>
        <view class="popup-dialog-footer">
          <button class="button red" @click="$refs.popupBan.close()">知道了</button>
        </view>
      </view>
    </uni-popup>

    <!--  配置地址选择弹窗  -->
    <uni-popup ref="addressPopup" :maskClick="false" type="bottom" class="address-popup">
      <view class="address-popup-content">
        <view class="address-popup-header">
          <text>选择地址</text>
          <text class="address-popup-header-close iconfont iconclose" @click="closeAddressPopup"></text>
        </view>
        <template v-if="addressList.length">
          <view class="address-popup-list">
            <view class="address-popup-list-one" :class="{'address-popup-list-one-active':addressIndex==index}"
                  v-for="(item,index) in addressList" :key="index" @click="changeAddress(index)">
              <view class="address-popup-list-one-left">
                <uni-icons type="checkbox-filled" :color="addressIndex==index ? 'var(--custom-brand-color)' : 'rgba(204,204,204,1)'" size="20"></uni-icons>
              </view>
              <view class="address-popup-list-one-right">
                <view class="address-popup-list-one-right-one"><text class="address-popup-list-one-right-one-name">{{item.name}}</text>{{item.mobile}}<text v-if="item.is_default" class="address-popup-list-one-right-one-tip">默认</text></view>
                <view class="address-popup-list-one-right-two">{{item.full_address}}{{item.address}}</view>
              </view>
            </view>
          </view>
          <view class="address-popup-op">
            <text @click="toAddAddress" v-if="token">添加新地址</text>
          </view>
        </template>
        <view class="address-popup-empty" v-else>
          <image :src="$util.img('public/static/youpin/goods/address_empty.png')" class="address-popup-empty-img"></image>
          <text class="address-popup-empty-tip">暂无收货地址</text>
          <text class="address-popup-empty-op" @click="toAddAddress" v-if="token">新增收货地址</text>
        </view>
      </view>
    </uni-popup>

    <!--  升级vip的按钮  -->
<!--    <yp-upgrade-vip-button :is-show="!is_shopper" :positions="{position: 'fixed',right: 0,bottom: '600rpx'}"></yp-upgrade-vip-button>-->
	<diy-share  :canvasOptions="canvasOptions_share" ref="sharePopup_share"
	 :sharePopupOptions="sharePopupOptions_share" @childByValue="getShareImg"></diy-share>
    <diy-share-navigate-h5 ref="shareNavigateH5"></diy-share-navigate-h5>
	</view>
</template>

<script>
import nsGoodsAction from '@/components/ns-goods-action/ns-goods-action.vue';
import nsGoodsActionIcon from '@/components/ns-goods-action-icon/ns-goods-action-icon.vue';
import nsGoodsActionButton from '@/components/ns-goods-action-button/ns-goods-action-button.vue';
import uniPopup from '@/components/uni-popup/uni-popup.vue';
import nsGoodsSku from '@/components/ns-goods-sku/ns-goods-sku.vue';
import nsGoodsRecommend from '@/components/ns-goods-recommend/ns-goods-recommend.vue';
import detail from '../public/js/detail.js';
import scroll from '@/common/mixins/scroll-view.js';
import globalConfig from 'common/mixins/golbalConfig.js'
import countdownTimer from '@/components/countdown-timer/countdown-timer.vue';
import sharePopup from '@/components/share-popup/share-popup.vue';
import diyShareNavigateH5 from '@/components/diy-share-navigate-h5/diy-share-navigate-h5'
import appInlineH5 from "../../../common/mixins/appInlineH5";
import uniNavBar from '@/components/uni-nav-bar/uni-nav-bar.vue';
import mphtml from "../../../components/mp-html/mp-html.vue";
// #ifdef H5
import {isOnXianMaiApp} from "../../../common/js/h5/appOP";
// #endif
import UniIcons from "../../../components/uni-icons/uni-icons.vue";
import diyGoodsDetailMoreGoodies
  from "../../../components/diy-goods-detail-more-goodies/diy-goods-detail-more-goodies.vue";

export default {
	components: {
    diyGoodsDetailMoreGoodies,
    UniIcons,
    mphtml,
		nsGoodsAction,
		nsGoodsActionIcon,
		nsGoodsActionButton,
		uniPopup,
		nsGoodsSku,
		nsGoodsRecommend,
		countdownTimer,
		sharePopup,
    diyShareNavigateH5,
    uniNavBar
	},
	data() {
		return {
      isShowEvaluate:false,
      isShowDetailTab:false,
      // #ifdef H5
      isOnXianMaiApp:isOnXianMaiApp,
      // #endif
	  copytextShow: false
    };
	},
	onShow() {
		if (uni.getStorageSync('is_register')) {
			this.$util.toShowCouponPopup(this)
			uni.removeStorageSync('is_register');
		}
	},
	computed: {
		//是否为需发布状态
		Development() {
      return false
		},
		themeStyle() {
			return 'theme-' + this.$store.state.themeStyle;
		},
		addonIsExit() {
			return this.$store.state.addonIsExit;
		},
	},
	mixins: [detail, scroll,globalConfig,appInlineH5],
	methods: {
			longpress() {
				this.copytextShow = true
			},
			copyCallback() {
				this.copytextShow = false
			},
    showAddressPopup(){
      this.$refs.addressPopup.open()
    },
    closeAddressPopup(){
      this.$refs.addressPopup.close()
    },
    async changeAddress(index){
      this.addressIndex = index
      this.address_info = this.addressList[index];
      this.district_id = this.addressList[index].district_id;
      uni.setStorageSync('member_address',this.addressList[index]);
      this.closeAddressPopup()
      await this.getDeliveryMoney();
    },
    toAddAddress(){
      this.$util.redirectTo('/otherpages/member/address/address')
    },
    async getAddressList(){
      let res = await this.$api.sendRequest({
        url: '/api/memberaddress/page',
        async:false,
        data: {
          page: 1,
          page_size: 20
        }
      })
      if(res.code ==0){
        this.addressList = res.data.list;
        // 用户是否有设置默认地址
        let has_default = this.addressList.filter(item=>item.is_default==1).length>0 ? true : false;
        if(has_default){
          this.addressList.map((item,index)=>{
            if(item.is_default == 1 && !this.district_id){
              this.addressIndex = index
              this.address_info = item;
              this.district_id = item.district_id;
            }
          })
        }else{
          if(this.addressList.length>0 && !this.district_id){
            this.addressIndex = 0;
            this.address_info = this.addressList[0];
            this.district_id = this.addressList[0].district_id;
          }
        }
      }
    }
		}
};
</script>

<style lang="scss">
@import '../public/css/detail.scss';
.ns-text-color {
  color: var(--custom-brand-color);
}
 .sku-name-box {
	  width: 100%;
	  position: relative;
  }
     .showCopybox {
	  position: absolute;
	  top: -66rpx;
	  left: 45%;
	.copytext {
	  text-align: center;
	  border-radius: 10rpx;
	  color: #fff;
	  font-size: 24rpx;
	  position: relative;
	  &::after {
		content: '';
		display: block;
		width: 20rpx;
		height: 20rpx;
		background: #1F2022;
		position: absolute;
		bottom: -16rpx;
		left: 30rpx;
		transform: rotate(45deg);
	  }
	}
	.fuzhi {
		border-right: 1px solid rgba(255,255,255,0.7);
		padding:16rpx 24rpx;
		background: #1F2022;
		border-radius: 5px 0 0 5px;
	}
	.quxiao {
		padding:16rpx 24rpx;
		background: #1F2022;
		border-radius: 0 5px 5px 0;
	}
  }
</style>
<style scoped>
/deep/ .uni-video-cover {
	background: none;
}

/deep/ .uni-video-cover-duration {
	display: none;
}

/deep/ .uni-video-cover-play-button {
	border-radius: 50%;
	border: 4rpx solid #fff;
	width: 120rpx;
	height: 120rpx;
	background-size: 30%;
}

.poster-layer >>> .uni-popup__wrapper-box {
	max-height: initial !important;
}

/deep/ .sku-layer .uni-popup__wrapper-box {
	overflow-y: initial !important;
}

/deep/ .uni-popup__wrapper.uni-custom .uni-popup__wrapper-box {
	max-height: unset !important;
}

/deep/ .goods-action-button.active1 {
	padding-left: 10px;
}

/deep/ .goods-action-button.active2 {
	padding-right: 10px;
}

/deep/ .goods-action-button.active3 {
	padding: 0 10px;
}

/deep/ .goods-action-button.active4 {
	padding: 0 10px;
}

/deep/ .goods-action-button.active1 .action-buttom-wrap {
  color: var(--custom-brand-color);
  border: 1px solid var(--custom-brand-color);
  border-radius: 40rpx;
  box-sizing: border-box;
  margin-right: 14rpx;
}

/deep/ .goods-action-button.active2 .action-buttom-wrap {
  border-radius: 40rpx;
  box-sizing: border-box;
}

/deep/ .goods-action-button.active3 .action-buttom-wrap {
	border-radius: 36px;
	margin: 20rpx 0;
}

/deep/ .goods-action-button.active4 .action-buttom-wrap {
	border-radius: 36px;
}

/* 底部分享按钮 */
.distributor-share-button{
	width:auto !important;
	height:auto !important;
	border:none;
	margin:0;
	line-height: auto;;
	padding:0;
}

.disabled-share-btn{
	background-color:transparent !important;
}

.to-top{
  width: 144rpx;
  height: 152rpx;
  position: fixed;
  right: 0;
  bottom: 200rpx;
}

/* 周期购说明 */
.cycle-buying{
	background: #ffffff;
	padding:20rpx;
	margin-bottom: 20rpx;
}
.cycle-buying .title{
	font-size: $ns-font-size-base;
	color: $ns-text-color-black;
	font-weight: bold;
}
.cycle-buying .content{
	font-size: $ns-font-size-base;
	color: $ns-text-color-black;
}
.page-title{
  width: 360rpx;
  overflow:hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  -o-text-overflow:ellipsis;
  text-align: center;
}
</style>
