<template>
	<view class="main" :style="[themeColorVar]">
    <!-- #ifdef H5 -->
    <uni-nav-bar left-icon="back" :border="false" :fixed="true" @clickLeft="appGoBack" v-if="isOnXianMaiApp">
      <template>
        <view class="page-title">{{topic_name}}</view>
      </template>
    </uni-nav-bar>
    <!-- #endif -->
    <template v-if="goods_display_type==1">
      <mescroll-uni ref="mescroll" @getData="getData" @scroll="scrollTouch" :size="10" :top="headerTop">
        <block slot="list">
          <view class="banner" v-if="banner.length>0" ><image v-for="(row,index) in banner" v-if="index==0" :src="row.image_url" mode="widthFix" v-on:click="toAd(row)"></image></view>
          <view class="container" v-bind:class="{'container-has': containerHas}" :style="{backgroundColor:bg_color}">
            <view class="goods_list" v-if="dataList.length">
              <view class="goods_item" v-for="(row,index) in dataList" :key="row.goods_id" @click="toProductDetail(row)">
                <view class="thumbImage">
                  <image :src="$util.img(row.goods_image)" @error="imageError(index)" mode="aspectFill" class="expose_goods_index" :data-expose_goods_sku="row.sku_id"></image>
                  <image v-if="row.goods_stock==0" :src="$util.img('public/static/youpin/product-sell-out.png')" class="over"></image>
                </view>
                <view class="goods_info">
                  <view class="goods_name">
                    <text class="tag-cross-border">跨境<image src="/static/imgs/flag.png" mode="aspectFit" class="tag-cross-border--flag"></image></text>
                    <template v-if="(row.tags && row.tags.length>0)">
                      <text v-for="(sub,u) in row.tags" v-bind:key="u" >{{sub.tag_name}}</text>
                    </template>
                    {{row.goods_name}}
                  </view>
                  <view class="bottom">
                    <view class="distributor" v-if="is_shopper"><text>分销商价</text> <text>￥{{row.vip_price}}</text></view>
                    <view class="sale_price"><text>￥</text>{{row.sale_price}}</view>
                    <view class="market_price">￥{{row.market_price}}</view>
                    <view class="buy_btn">立即抢购</view>
                  </view>
                </view>
              </view>
            </view>
            <view v-if="!loading && !dataList.length">
              <ns-empty :fixed="false"></ns-empty>
            </view>
          </view>
        </block>
      </mescroll-uni>
    </template>
    <template v-else>
      <view class="banner" v-if="banner.length>0" ><image v-for="(row,index) in banner" v-if="index==0" :src="row.image_url" mode="widthFix" v-on:click="toAd(row)"></image></view>
      <view class="waterfall" :style="{backgroundColor:bg_color}">
        <uv-waterfall ref="fallsFlow" v-model="dataList" columnGap="20rpx" :addTime="50" @changeList="changeList" @finish="waterfallFinish" v-if="dataList.length">
          <template v-slot:list1>
            <view class="waterfall-one" v-for="(row,index) in list1" @click="toProductDetail(row)">
            <view class="waterfall-one-img">
              <image :src="$util.img(row.goods_image)" @error="imageError(index,list1)" mode='widthFix' class="waterfall-one-img-image"/>
              <image v-if="row.goods_stock==0" :src="$util.img('public/static/youpin/product-sell-out.png')" class="waterfall-one-img-over"></image>
              <view class="waterfall-one-img-tags" v-if="row.tags && row.tags.length">
                <text class="waterfall-one-img-tags-one" v-for="(tag,i) in row.tags" :key="i">{{tag.tag_name}}</text>
              </view>
            </view>
            <view class="waterfall-one-name expose_goods_index" :data-expose_goods_sku="row.sku_id">
              <text class="tag-cross-border">跨境<image src="/static/imgs/flag.png" mode="aspectFit" class="tag-cross-border--flag"></image></text>
              {{row.goods_name}}
            </view>
            <view class="waterfall-one-price">
              <view class="waterfall-one-price-sale"><text class="waterfall-one-price-sale-symbol">￥</text>{{row.sale_price}}</view>
              <view class="waterfall-one-price-market">￥{{row.market_price}}</view>
            </view>
            <view class="waterfall-one-income" v-if="row.forecast_price_display">预计赚￥{{row.forecast_price}}</view>
          </view>
          </template>
          <template v-slot:list2>
            <view class="waterfall-one" v-for="(row,index) in list2" @click="toProductDetail(row)">
              <view class="waterfall-one-img">
                <image :src="$util.img(row.goods_image)" @error="imageError(index,list2)" mode='widthFix' class="waterfall-one-img-image"/>
                <image v-if="row.goods_stock==0" :src="$util.img('public/static/youpin/product-sell-out.png')" class="waterfall-one-img-over"></image>
                <view class="waterfall-one-img-tags" v-if="row.tags && row.tags.length">
                  <text class="waterfall-one-img-tags-one" v-for="(tag,i) in row.tags" :key="i">{{tag.tag_name}}</text>
                </view>
              </view>
              <view class="waterfall-one-name expose_goods_index" :data-expose_goods_sku="row.sku_id">
                <text class="tag-cross-border">跨境<image src="/static/imgs/flag.png" mode="aspectFit" class="tag-cross-border--flag"></image></text>
                {{row.goods_name}}
              </view>
              <view class="waterfall-one-price">
                <view class="waterfall-one-price-sale"><text class="waterfall-one-price-sale-symbol">￥</text>{{row.sale_price}}</view>
                <view class="waterfall-one-price-market">￥{{row.market_price}}</view>
              </view>
              <view class="waterfall-one-income" v-if="row.forecast_price_display">预计赚￥{{row.forecast_price}}</view>
            </view>
          </template>
        </uv-waterfall>
        <view v-if="!loading && !dataList.length">
          <ns-empty :fixed="false"></ns-empty>
        </view>
      </view>
    </template>
    <loading-cover ref="loadingCover"></loading-cover>
	</view>
</template>

<script>
	import system from "@/common/js/system.js";
  import wx_expose_goods from '@/common/mixins/wx_expose_goods.js'
  import golbalConfig from "../../../common/mixins/golbalConfig";
	// #ifdef H5
  import {isOnXianMaiApp} from "@/common/js/h5/appOP";
  // #endif
  import appInlineH5 from "../../../common/mixins/appInlineH5";
  import uniNavBar from '@/components/uni-nav-bar/uni-nav-bar.vue';
	export default {
		data() {
			return {
        goods_display_type: 2,  // 显示模板 1是普通  2是瀑布流
				dataList:[],
				banner:[],
        topic_id:null,
				loading:true,
        is_shopper:0,  //是否是店主
        bg_color:'',
        topic_adv:'',
        topic_name:'',
        topic_bg:'',
				headerTop:0,
        page:1,
        page_size: 10,
        page_count:1, //总页面数
        list1:[],
        list2: [],
        // #ifdef H5
        isOnXianMaiApp:isOnXianMaiApp,
        // #endif
			}
		},
    components:{
      uniNavBar
    },
    mixins: [wx_expose_goods,golbalConfig,appInlineH5],
		computed: {
      containerHas(){
			  return this.banner.length>0;
      }
		},
		async onLoad(options) {
		  this.topic_id = options.topic_id ? options.topic_id : null;
			// #ifdef H5
      if(isOnXianMaiApp){
        this.headerTop = 88;
      }
			// #endif
      await system.wait_staticLogin_success();
      await this.getInit()
		},
		async onShow() {
			this.$langConfig.refresh();
			if(this.topic_name){
        uni.setNavigationBarTitle({
          title:this.topic_name
        })
      }
      if (uni.getStorageSync('is_shopper')) {
        this.is_shopper = uni.getStorageSync('is_shopper');
      }
		},
		methods: {
      // 这点非常重要：e.name在这里返回是list1或list2，要手动将数据追加到相应列
      changeList(e){
        this[e.name].push(e.value);
      },
      waterfallFinish(){
        if (this.$refs.loadingCover) this.$refs.loadingCover.hide();
      },
      async getInit(){
        if(this.page>this.page_count){
          return
        }
        try{
          let res = await this.$api.sendRequest({
            url: this.$apiUrl.getTopicGoodsListUrl,
            async:false,
            data: {
              topic_id: this.topic_id,
              page:this.page,
              page_size:this.page_size
            }
          })
          if(res.code == 0){
            this.goods_display_type = res.data.goods_display_type
            this.topic_name=res.data.topic_name;
            this.bg_color=res.data.bg_color;
            this.topic_bg=res.data.topic_bg;
            if(res.data.topic_adv){
              this.banner=[
                {image_url:res.data.topic_adv}
              ]
            }
            uni.setNavigationBarTitle({
              title:this.topic_name
            })
            if(this.goods_display_type==2){
              res.data.list = res.data.list.map(item=>{
                item.goods_image = this.$util.imageCdnResize(item.goods_image);
                return item;
              })
              this.dataList = this.dataList.concat(res.data.list);
              this.page_count = res.data.page_count
              this.page += 1
            }
          }
        }catch (e) {

        }
      },
			getData(mescroll){
				this.mescroll = mescroll;
				if(mescroll.num == 1) this.dataList = [];
				this.loading = true;
				this.$api.sendRequest({
					url: this.$apiUrl.getTopicGoodsListUrl,
					data: {
						page_size: mescroll.size,
						page: mescroll.num,
						topic_id: this.topic_id,
					},
					success:(res)=>{
						this.loading = false
            if (this.$refs.loadingCover) this.$refs.loadingCover.hide();
						this.h5Share(res)
						if(res.code!=0){
							this.$util.showToast({
								title: res.message
							})
							this.loading = false
							mescroll.endErr();
							return
						}
            this.goods_display_type = res.data.goods_display_type
						this.topic_name=res.data.topic_name;
						this.bg_color=res.data.bg_color;
						this.topic_bg=res.data.topic_bg;
						if(res.data.topic_adv){
						  this.banner=[
							{image_url:res.data.topic_adv}
						  ]
						}
						uni.setNavigationBarTitle({
						  title:this.topic_name
						})
						mescroll.endSuccess(res.data.list.length);
            res.data.list = res.data.list.map(item=>{
              item.goods_image = this.$util.imageCdnResize(item.goods_image);
              return item;
            })
						this.dataList = this.dataList.concat(res.data.list);
					},
					fail:(err)=>{
						this.loading = false
						mescroll.endErr();
						this.$util.showToast({
							title: err.message
						})
					}
				})
			},
			changTab(type){
				this.sf = type;
				this.$refs.mescroll.refresh();
			},
      toAd(item){
        this.$buriedPoint.diyReportAdEvent(
            {diy_ad_location_type:'list',diy_material_path:item.image_url,diy_ad_type:'image',diy_target_page:item.banner_url,diy_ad_id:item.id,diy_action_type:'click'})
        if (item.banner_url) {
          this.$util.diyCompateRedirectTo({
            wap_url: item.banner_url
          })
        }
      },
			imageError(index,dataList) {
        if(dataList && dataList[index]){
          dataList[index].goods_image = this.$util.getDefaultImage().default_goods_img;
          this.$forceUpdate();
        }
			},
			getSharePageParams() {
        let share_data=this.$util.unifySharePageParams('/promotionpages/task/list/list',this.topic_name ? this.topic_name:'活动已结束',
            '',{topic_id:this.topic_id},this.$util.img('public/static/youpin/task_share.jpg'))
        return share_data;
			},
			async h5Share(info) {
				// #ifdef H5
					let share_data = this.$util.deepClone(this.getSharePageParams())
					let link = window.location.origin+this.$router.options.base+share_data.link.slice(1)
					share_data.link=link
					share_data.desc= share_data.title
					share_data.title='先迈商城'
					await this.$util.publicShare(share_data);
				// #endif
			},
      toProductDetail(item){
        this.$util.toProductDetail(item,(wap_url)=>{
          this.$buriedPoint.diyReportTopicPageInteractionEvent({diy_action_type:'click_goods',diy_template_name:this.goods_display_type==1 ? 'template_1' : 'template_2',diy_product_name:item.goods_name,diy_activity_title:this.topic_name,diy_product_link:wap_url})
        })
      }
		},
    async onReachBottom() {
      if(this.goods_display_type==2){
        await this.getInit()
        uni.stopPullDownRefresh();
      }
    },
		// 分享
		onShareAppMessage(res) {
			let { title, link, imageUrl, query } = this.getSharePageParams()
			return this.$buriedPoint.pageShare(link, imageUrl, title);
		},
		// 分享到微信
		onShareTimeline(res){
			let { title, imageUrl, query } = this.getSharePageParams()
			return {
				title,
				imageUrl,
				query,
				success: res => {},
				fail: res => {}
			};
		}
	}
</script>

<style lang="scss" scoped>
.main{
  min-height: 100vh;
}
.banner{
	width: 100%;
	height: auto;
	image{
		width: 100%;
		height: auto;
    display: block;
	}
}
.container{
	width: 100%;
	//height: calc(100vh - 0rpx);
  min-height: 100vh;
  background: transparent;
	border-radius: 20rpx 20rpx 0px 0px;
	overflow: hidden;
  padding-bottom: 20rpx;
  padding-top: 20rpx;
  box-sizing: border-box;
  &-has{
    //top: 240rpx;
    //height: calc(100vh - 240rpx);
    min-height: 950rpx;
    margin-top: -20rpx;
  }
	z-index: 1;
	.nav_tab{
		width: 100%;
		height: 100rpx;
		line-height: 100rpx;
		display: flex;
		align-items: center;
		.tab_item{
			width: 33%;
			text-align: center;
			color: #999;
			font-size: 28rpx;
			&.active{
				color:var(--custom-brand-color);
			}
		}
	}
	.goods_list{
		.goods_item{
			width:690rpx;
			box-sizing: border-box;
			background: #fff;
			margin:0 auto;
			margin-bottom: 20rpx;
			padding: 20rpx 24rpx;
			display: flex;
			align-items: center;
			justify-content: space-between;
			border-radius: 20rpx;
			&:first-child{
				margin-top: 20rpx;
			}
			&:last-child{
				margin-bottom: 100rpx;
			}
			.thumbImage{
				position: relative;
				width: 240rpx;
				height: 240rpx;
				border-radius: 8rpx;
				margin-right: 26rpx;
				overflow: hidden;
				image{
					width: 100%;
					height: 100%;
				}
				.over{
					width: 120rpx;
					height: 120rpx;
					position: absolute;
					left: 50%;
					top: 50%;
					transform: translate(-50%,-50%);
				}
			}

			.goods_info{
				width: calc(100% - 270rpx);
				height: 240rpx;
				position: relative;
				.goods_name{
					font-size: 26rpx;
					color: #333;
					display: -webkit-box;
					-webkit-box-orient: vertical;
					-webkit-line-clamp: 2;
					overflow: hidden;
          text {
            vertical-align: text-bottom;
            padding: 2rpx 10rpx;
            margin-right: 5rpx;
            box-sizing: border-box;
            display: inline-block;
            text-align: center;
            font-size: 20rpx;
            font-weight: 500;
            color: #FFFFFF;
            background: linear-gradient(55deg, var(--custom-brand-color-80), var(--custom-brand-color));
            border-radius: 4rpx;

          }
				}
				.bottom{
					position: absolute;
					bottom: 0;
					width: 100%;
					line-height: 1;
          .distributor{
            text:first-child{
              //width: 84px;
              height: 40rpx;
              background: linear-gradient(90deg, var(--custom-brand-color-70) 0%, var(--custom-brand-color) 100%);
              border-radius: 4rpx 0px 0px 4rpx;
              padding: 0 9rpx 0 4rpx;
              box-sizing: border-box;
              font-size: 24rpx;
              font-weight: 500;
              color: #FFFFFF;
              display: inline-block;
            }
            text:last-child{
              height: 40rpx;
              background: var(--custom-brand-color-10);
              border-radius: 0rpx 4rpx 4rpx 0rpx;
              padding: 0 8rpx;
              box-sizing: border-box;
              font-size: 24rpx;
              font-weight: bold;
              color: var(--custom-brand-color);
              display: inline-block;
            }
          }
					.sale_price{
						font-size:36rpx;
						color: var(--custom-brand-color);
						font-weight: bold;
						line-height: 1;
						text{
							font-size: 26rpx;
							font-weight: normal;
						}
					}
					.market_price{
						line-height: 1;
						text-decoration: line-through;
						color: #999;
						font-size: 24rpx;
					}
					.buy_btn{
						position: absolute;
						right: 0;
						bottom: 0;
            background: linear-gradient(-90deg, var(--custom-brand-color-70) 0%, var(--custom-brand-color) 100%);
						height: 56rpx;
						line-height: 56rpx;
						border-radius: 28rpx;
						width: 148rpx;
						text-align: center;
						font-size: 24rpx;
						color: #fff;
					}
				}
			}
		}
	}
}
/deep/.mescroll-upwarp{
	padding: 0 !important;
	margin-bottom: 0;
	min-height: 0;
	line-height: 0;
}
.waterfall{
  padding: 0 20rpx;
  padding-top: 20rpx;
  padding-bottom: 50rpx;
  box-sizing: border-box;
  min-height: calc(100vh - 280rpx);
  &-one{
    display: inline-block;
    vertical-align: top;
    width: 344rpx;
    box-sizing: border-box;
    background-color: white;
    border-radius: 20rpx;
    margin-bottom: 20rpx;
    &-img{
      position: relative;
      &-image{
        width: 100%;
        border-radius: 20rpx;
      }
      &-over{
        width: 120rpx;
        height: 120rpx;
        position: absolute;
        left: 50%;
        top: 50%;
        transform: translate(-50%,-50%);
      }
      &-tags{
        position: absolute;
        left: 18rpx;
        top: 18rpx;
        line-height: 0;
        &-one{
          display: inline-block;
          padding: 0 12rpx;
          box-sizing: border-box;
          border-radius: 40rpx;
          background: var(--custom-brand-color);
          font-size: 20rpx;
          font-weight: 400;
          line-height: 28rpx;
          color: rgba(255, 255, 255, 1);
          margin-right: 10rpx;
        }
      }
    }
    &-name{
      margin-top: 30rpx;
      font-size: 32rpx;
      font-weight: 400;
      line-height: 37.26rpx;
      color: rgba(56, 56, 56, 1);
      display: -webkit-box;
      -webkit-box-orient: vertical;
      -webkit-line-clamp: 2;
      overflow: hidden;
      padding: 0 18rpx;
      box-sizing: border-box;
    }
    &-price{
      display: flex;
      margin-top: 6rpx;
      padding: 0 18rpx;
      box-sizing: border-box;
      &-sale{
        display: flex;
        font-size: 36rpx;
        font-weight: 700;
        line-height: 36rpx;
        color: var(--custom-brand-color);
        &-symbol{
          font-size: 24rpx;
          align-self: center;
        }
      }
      &-market{
        font-size: 20rpx;
        font-weight: 400;
        line-height: 44rpx;
        text-decoration-line: line-through;
        color: rgba(166, 166, 166, 1);
        margin-left: 18rpx;
      }
    }
    &-income{
      display: inline-block;
      padding: 0 18rpx;
      box-sizing: border-box;
      border-radius: 40rpx;
      background: rgba(246, 93, 114, 0.1);
      font-size: 20rpx;
      font-weight: 400;
      line-height: 44rpx;
      color: var(--custom-brand-color);
    }
  }
}
.page-title{
  width: 360rpx;
  overflow:hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  -o-text-overflow:ellipsis;
  text-align: center;
}
</style>
