<template>
  <view :class="themeStyle">
    <!-- #ifdef H5 -->
    <uni-nav-bar left-icon="back" :border="false" @clickLeft="goBack" v-if="isOnXianMaiApp">
      <template>
        <view class="page-title">{{title}}</view>
      </template>
    </uni-nav-bar>
    <!-- #endif -->
    <!-- #ifdef MP-WEIXIN -->
    <uni-nav-bar left-icon="back" :border="false" @clickLeft="goBack" :statusBar="true" :fixed="true" :backgroundColor="'transparent'" color="#fff">
      <template>
        <view class="page-title">{{title}}</view>
      </template>
    </uni-nav-bar>
    <!-- #endif -->
    <view class="container" v-if="isShowPage">
      <image :src="image" mode="widthFix" class="header-image" @click="$refs.rulePopupRef.open()"></image>
      <view class="container-form">
        <template v-for="(item,index) in list">
          <view class="container-form-item" v-if="item.type=='text'">
            <view class="title">{{item.name}}<view class="required" v-if="item.is_require==1"><text>*</text>（必填）</view></view>
            <input type="text" class="uni-input container-form-item-input" :placeholder="item.describe" placeholder-class="container-form-item-input-placeholder" v-model="item.value"/>
          </view>
          <view class="container-form-item" v-if="item.type=='number'">
            <view class="title">{{item.name}}<view class="required" v-if="item.is_require==1"><text>*</text>（必填）</view></view>
            <input type="number" class="uni-input container-form-item-input" :placeholder="item.describe" placeholder-class="container-form-item-input-placeholder" v-model="item.value"/>
          </view>
          <view class="container-form-item" v-if="item.type=='radio'">
            <view class="title">{{item.name}}<view class="required" v-if="item.is_require==1"><text>*</text>（必填）</view></view>
            <radio-group @change="radioChange($event,index)">
                <view v-if="item.select_data.filter(opt=>!!opt.image).length>0" class="container-form-item-radio-img">
                  <view v-for="(option,j) in item.select_data" :key="j" class="container-form-item-radio-img-one">
                    <image :src="option.image" mode="aspectFit" class="container-form-item-radio-img-one-img" @click="onRadioChange(index,j)"></image>
                    <view class="container-form-item-radio-img-one-bottom">
                      <view class="container-form-item-radio-img-one-bottom-text" @click="onRadioChange(index,j)">{{option.value}}</view>
                      <radio :value="option.value" borderColor="rgba(196, 196, 196, 1)" color="rgba(246, 93, 114, 1)" :checked="option.is_select"/>
                    </view>
                  </view>
                </view>
                <view v-else class="container-form-item-radio-text">
                  <view v-for="(option,j) in item.select_data" :key="j" class="container-form-item-radio-text-one">
                    <radio :value="option.value" borderColor="rgba(196, 196, 196, 1)" color="rgba(246, 93, 114, 1)" :checked="option.is_select" class="container-form-item-radio-text-one-control">
                     <text class="container-form-item-radio-text-one-text">{{option.value}}</text>
                    </radio>
                  </view>
                </view>
            </radio-group>
          </view>
          <view class="container-form-item" v-if="item.type=='checkbox'">
            <view class="title">{{item.name}}<view class="required" v-if="item.is_require==1"><text>*</text>（必填）</view></view>
            <checkbox-group @change="checkboxChange($event,index)">
              <view v-if="item.select_data.filter(opt=>!!opt.image).length>0" class="container-form-item-radio-img">
                <view v-for="(option,j) in item.select_data" :key="j" class="container-form-item-radio-img-one">
                  <image :src="option.image" mode="aspectFit" class="container-form-item-radio-img-one-img" @click="onCheckboxChange(index,j)"></image>
                  <view class="container-form-item-radio-img-one-bottom">
                    <view class="container-form-item-radio-img-one-bottom-text" @click="onCheckboxChange(index,j)">{{option.value}}</view>
                    <checkbox :value="option.value" borderColor="rgba(196, 196, 196, 1)" activeBackgroundColor="rgba(246, 93, 114, 1)"
                              activeBorderColor="rgba(246, 93, 114, 1)"
                              :checked="option.is_select"/>
                  </view>
                </view>
              </view>
              <view v-else class="container-form-item-radio-text">
                <view v-for="(option,j) in item.select_data" :key="j" class="container-form-item-radio-text-one">
                  <checkbox :value="option.value" borderColor="rgba(196, 196, 196, 1)" activeBackgroundColor="rgba(246, 93, 114, 1)"
                            activeBorderColor="rgba(246, 93, 114, 1)"
                            :checked="option.is_select" class="container-form-item-radio-text-one-control">
                  <text class="container-form-item-radio-text-one-text">{{option.value}}</text>
                  </checkbox>
                </view>
              </view>
            </checkbox-group>
          </view>
          <view class="container-form-item" v-if="item.type=='textarea'">
            <view class="title">{{item.name}}<view class="required" v-if="item.is_require==1"><text>*</text>（必填）</view></view>
            <textarea type="text" class="uni-input container-form-item-textarea" :placeholder="item.describe" placeholder-class="container-form-item-textarea-placeholder" v-model="item.value"/>
          </view>
          <view class="container-form-item" v-if="item.type=='datetime'">
            <view class="title">{{item.name}}<view class="required" v-if="item.is_require==1"><text>*</text>（必填）</view></view>
            <picker mode="time" :value="item.value" @change="bindTimeChange($event,index)">
              <view type="number" class="uni-input container-form-item-datetime" :class="{'container-form-item-datetime-placeholder' :!item.value}"
                    >{{item.value ? item.value : item.describe}}</view>
            </picker>
          </view>
          <view class="container-form-item" v-if="item.type=='date'">
            <view class="title">{{item.name}}<view class="required" v-if="item.is_require==1"><text>*</text>（必填）</view></view>
            <picker mode="date" :value="item.value" @change="bindDateChange($event,index)">
              <view type="number" class="uni-input container-form-item-date" :class="{'container-form-item-date-placeholder' :!item.value}"
                   >{{item.value ? item.value : item.describe}}</view>
            </picker>
          </view>
        </template>
      </view>
      <view class="container-qrcode" :style="{'background-image':`url(${this.$util.img('public/static/youpin/questionnaire/qrcode_bg.png')})`}" v-if="bottom_image">
        <image :src="bottom_image" class="container-qrcode-img" show-menu-by-longpress></image>
        <view class="container-qrcode-text">长按识别二维码有惊喜</view>
      </view>
      <view class="container-footer">
        <view class="container-footer-left">
          <button class="container-footer-left-op" @click="backToHome">
            <image :src="$util.img('public/static/youpin/questionnaire/home.png')" class="container-footer-left-op-img"></image>
            <text class="container-footer-left-op-text">首页</text>
          </button>
          <!-- #ifdef MP-WEIXIN -->
          <button class="container-footer-left-op" open-type="share">
            <image :src="$util.img('public/static/youpin/questionnaire/share.png')" class="container-footer-left-op-img"></image>
            <text class="container-footer-left-op-text">分享</text>
          </button>
          <!-- #endif -->
          <!-- #ifdef H5 -->
          <button class="container-footer-left-op" @click="toShare">
            <image :src="$util.img('public/static/youpin/questionnaire/share.png')" class="container-footer-left-op-img"></image>
            <text class="container-footer-left-op-text">分享</text>
          </button>
          <!-- #endif -->
        </view>
        <view class="container-footer-right">
          <button type="warn" class="container-footer-right-op container-footer-right-records" @click="toRecords">查看记录</button>
          <button type="primary" class="container-footer-right-op container-footer-right-submit" @click="submitData">提交</button>
        </view>
      </view>
    </view>

    <uni-popup ref="exitConfirmationRef" :maskClick="false">
      <view class="exit-confirmation">
        <view class="exit-confirmation-title">确认退出？</view>
        <view class="exit-confirmation-desc">您参与的问卷活动暂未完成提交是否确认退出？</view>
        <view class="exit-confirmation-op">
          <text class="exit-confirmation-op-cancel" @click="confirmationCancel">取消</text>
          <text class="exit-confirmation-op-back" @click="toBack">退出</text>
        </view>
      </view>
    </uni-popup>

    <uni-popup ref="tipPopupRef" :maskClick="false">
      <view class="tip-popup">
        <view class="tip-popup-title">{{tip_type=='invalid' ? '活动已过期' : '不符合参与要求' }}</view>
        <view class="tip-popup-desc">{{tip_type=='invalid' ?  '该活动到期结束或分享链接已失效' :'您未符合本活动的参与条件，无法填写问卷。' }}</view>
        <image :src="$util.img('public/static/youpin/questionnaire/invalid.png')" class="tip-popup-img"/>
        <view class="tip-popup-back" @click="backToHome">返回首页</view>
      </view>
    </uni-popup>

    <uni-popup ref="resultPopupRef" :maskClick="false">
      <view class="result-popup">
        <view class="result-popup-title">提交成功</view>
        <view class="result-popup-desc">感谢您的参与</view>
        <image :src="bottom_image || $util.img('public/static/youpin/questionnaire/questionnaire.png')" class="result-popup-img" show-menu-by-longpress/>
        <view class="result-popup-tip">长按识别二维码</view>
        <view class="result-popup-op">
          <text class="result-popup-op-left" @click="backToHome">返回首页</text>
          <text class="result-popup-op-right" @click="toRecords">查看记录</text>
        </view>
      </view>
    </uni-popup>

    <uni-popup ref="rulePopupRef" type="bottom" :bottom-radius="true">
      <view class="rule">
        <view class="rule-header">
          <text class="rule-header-text">活动规则</text>
          <text class="iconfont iconroundclose" @click="$refs.rulePopupRef.close()"></text>
        </view>
        <mphtml :content="content" :preview-img="true"/>
      </view>
    </uni-popup>

    <loading-cover ref="loadingCover"></loading-cover>
    <!-- 授权登录弹窗 -->
    <yd-auth-popup ref="ydauth"></yd-auth-popup>
    <ns-login ref="login"></ns-login>
    <diy-share-navigate-h5 ref="shareNavigateH5"></diy-share-navigate-h5>
  </view>
</template>

<script>
import system from "@/common/js/system.js";
import uniPopup from '@/components/uni-popup/uni-popup.vue';
import appInlineH5 from "../../../common/mixins/appInlineH5";
import diyShareNavigateH5 from '@/components/diy-share-navigate-h5/diy-share-navigate-h5'
import uniNavBar from '@/components/uni-nav-bar/uni-nav-bar.vue';
import apiurls from "../../../common/js/apiurls";
import mphtml from "../../../components/mp-html/mp-html.vue"
// #ifdef H5
import {isOnXianMaiApp} from "../../../common/js/h5/appOP";
// #endif
export default {
  name: "qform",
  components:{
    uniPopup,
    diyShareNavigateH5,
    uniNavBar,
    mphtml
  },
  computed: {
    themeStyle() {
      return 'theme-' + this.$store.state.themeStyle
    },
  },
  mixins:[appInlineH5],
  data(){
    return{
      title:"",
      share_title: '',
      questionnaire_id: null,
      submit_id: null,
      isShowPage:false,
      content:"",
      list:[],
      image: '',
      bottom_image:'',
      tip_type: 'invalid', // invalid, ineligible
      // #ifdef H5
      isOnXianMaiApp:isOnXianMaiApp,
      // #endif
    }
  },
  async onLoad(option){
    await system.wait_staticLogin_success();
    this.questionnaire_id = option.questionnaire_id || null;
    this.submit_id = option.submit_id || null;
    await this.getData();
  },
  async onShow(){
    await system.wait_staticLogin_success();
    this.checkLogin();
  },
  onReady(){
    // #ifdef H5
    this.setWechatShare();
    // #endif
  },
  methods:{
    toBack(){
      // #ifdef H5
      this.appGoBack();
      // #endif
      // #ifdef MP-WEIXIN
      this.$util.goBack()
      // #endif
    },
    goBack(){
      if(this.checkInputHasContent()){
        this.$refs.exitConfirmationRef.open()
      }else{
        this.toBack()
      }
    },
    confirmationCancel(){
      this.$refs.exitConfirmationRef.close();
    },
    async getData(){
      try{
        let params = {
          questionnaire_id: this.questionnaire_id,
        }
        let url = apiurls.questionnaireActivityFormUrl;
        let recommend_member_id = uni.getStorageSync("recommend_member_id")
        if(recommend_member_id){
          params.share_member_id = recommend_member_id
        }
        if(this.submit_id){
          params.submit_id = this.submit_id;
          url = apiurls.questionnaireActivityEditDetailUrl;
        }
        let res = await this.$api.sendRequest({
          url,
          async: false,
          data: params
        })
        if (this.$refs.loadingCover) this.$refs.loadingCover.hide();
        if(res.code == -20002){

        }else if(res.code == -20003){
          this.tip_type = 'invalid';
          this.$refs.tipPopupRef.open()
        }else if(res.code == -20004){
          this.tip_type = 'ineligible';
          this.$refs.tipPopupRef.open()
        }else if (res.code == 0) {
          this.share_title = res.data.share_title;
          this.image = res.data.image;
          this.bottom_image = res.data.bottom_image;
          this.list = res.data.column_data
          this.content = res.data.content;
          uni.setNavigationBarTitle({
            title:res.data.name
          });
          this.isShowPage = true
        }else{
          this.$util.showToast({
            title:res.message
          })
        }
      }catch (e) {

      }
    },
    radioChange(event,index){
      let tmp = this.list[index];
      tmp.select_data = tmp.select_data.map(one=>{
        if(one.value == event.detail.value){
          one.is_select = true
        }else{
          one.is_select = false
        }
        return one;
      });
      this.list[index] = tmp;
    },
    checkboxChange(event,index){
      let tmp = this.list[index];
      tmp.select_data = tmp.select_data.map(one=>{
        if(event.detail.value.includes(one.value)){
          one.is_select = true
        }else{
          one.is_select = false
        }
        return one;
      });
      this.list[index] = tmp;
    },
    onCheckboxChange(index,j){
      let tmp = this.list[index];
      tmp.select_data = tmp.select_data.map((one,k)=>{
        if(j==k){
          one.is_select = !one.is_select;
        }
        return one;
      });
      this.list[index] = tmp;
    },
    onRadioChange(index,j){
      let tmp = this.list[index];
      tmp.select_data = tmp.select_data.map((one,k)=>{
        if(j==k){
          one.is_select = true;
        }else{
          one.is_select = false;
        }
        return one;
      });
      this.list[index] = tmp;
    },
    bindDateChange(e,index){
      let tmp = this.list[index];
      tmp.value = e.detail.value;
      this.$set(this.list, index, tmp);
    },
    bindTimeChange(e,index){
      let tmp = this.list[index];
      tmp.value = e.detail.value;
      this.$set(this.list, index, tmp);
    },
    // 检查表单是否有填写内容
    checkInputHasContent(){
      let tmp_list = this.list.filter(item=>{
        if(item.value instanceof Array){
          if(item.value.length>1){
            return true;
          }else{
            return false
          }
        }else{
          return !!item.value
        }
      })
      return tmp_list.length >0
    },
    // 验证表单
    verifyData(){
     let unfilled = this.list.filter(item=>{
        if(item.is_require == 1){
          if(['radio','checkbox'].includes(item.type)){
            return item.select_data.filter(one=>one.is_select) < 1
          }else{
            return !item.value
          }
        }else{
          return false;
        }
      })
      return unfilled.length < 1;
    },
    async submitData(){
      if(!this.checkLogin()){
        return false;
      }
      if(!this.verifyData()){
        this.$util.showToast({
           title: '有必填项未填写完成！'
        })
        return false;
      }

      uni.showLoading({
        title: '提交中...'
      });
      let url = apiurls.questionnaireActivitySubmitUrl;
      let params = {
        questionnaire_id : this.questionnaire_id,
        data:[]
      }
      let recommend_member_id = uni.getStorageSync("recommend_member_id")
      if(recommend_member_id){
        params.share_member_id = recommend_member_id
      }
      if(this.submit_id){
        params.submit_id = this.submit_id;
        url = apiurls.questionnaireActivityEditUrl;
      }
      this.list.map(item=>{
        let value =  '';
        if(['radio','checkbox'].includes(item.type)){
          value = item.select_data.filter(one=>one.is_select).map(one=>one.value).join('/')
        }else{
          value = item.value;
        }
        params.data.push({name:item.name,value})
        return item;
      })
      params.data = JSON.stringify(params.data);
      try{
        let res = await this.$api.sendRequest({
          url,
          async: false,
          data: params
        })
        uni.hideLoading();
        if(res.code == -20002){

        }else if(res.code!=0){
          this.$util.showToast({
            title: res.message
          })
          return false;
        }else{
          this.$refs.resultPopupRef.open();
        }
      }catch (e) {

      }

    },
    toRecords(){
      this.$refs.resultPopupRef.close();
      if(!this.checkLogin()){
        return false;
      }
      this.$util.redirectTo(`/promotionpages/questionnaire/records/records?questionnaire_id=${this.questionnaire_id}`)
    },
    notAuthentication(){
      this.$refs.tipPopupRef.close();
    },
    toAuthentication(){
      this.$util.redirectTo(`/otherpages/member/real_name_authentication/real_name_authentication`)
    },
    backToHome(){
      this.$util.redirectTo('/otherpages/shop/home/<USER>', {}, 'reLaunch');
    },
    checkLogin(){
      if(uni.getStorageSync('token')){
        return true;
      }else{
        this.toLogin();
        return false;
      }
    },
    toLogin(){
      let path = `/promotionpages/questionnaire/qform/qform?questionnaire_id=${this.questionnaire_id}`
      this.$util.toShowLoginPopup(this,null,path);
    },
    /**
     *分享参数组装(注意需要分享的那一刻再调此方法)
     */
    getSharePageParams(){
      let params = {
        questionnaire_id:this.questionnaire_id
      }
      return this.$util.unifySharePageParams(`/promotionpages/questionnaire/qform/qform`,this.share_title || '先迈商城',
          ``,params,this.image)
    },
    /**
     * 设置微信公众号分享
     */
    setWechatShare() {
      // 微信公众号分享
      // #ifdef H5
      let share_data=this.$util.deepClone(this.getSharePageParams());
      let link=window.location.origin+this.$router.options.base+share_data.link.slice(1);
      share_data.link=link;
      this.$util.publicShare(share_data);
      // #endif
    },
    toShare(){
      // #ifdef H5
      let share_data=this.getSharePageParams();
      if(this.$refs.shareNavigateH5) this.$refs.shareNavigateH5.open(share_data);
      // #endif
    }
  },
  /**
   * 自定义分享内容
   * @param {Object} res
   */
  onShareAppMessage(res) {
    let { title, link, imageUrl, query } = this.getSharePageParams()
    return this.$buriedPoint.pageShare(link , imageUrl, title);
  },
}
</script>
<style>
/deep/ .uni-navbar{
  position: fixed;
  z-index: 999;
}
</style>
<style lang="scss">
/deep/.uni-picker-action-confirm{
  color: rgba(246, 93, 114, 1);
}
.weui-btn_primary{background-color:yellow;}
</style>

<style scoped lang="scss">
/deep/ .uni-popup__wrapper-box{
  border-radius: 40rpx!important;
}
/deep/.uni-radio-input-checked{
  background-color: rgba(246, 93, 114, 1)!important;
}
/deep/.uni-checkbox-input.uni-checkbox-input-checked{
  color: #fff!important;
}
/deep/checkbox .wx-checkbox-input.wx-checkbox-input-checked{
  color: #fff;
  background-color: rgba(246, 93, 114, 1)!important;
  border-color: rgba(246, 93, 114, 1)!important;
}

.header-image{
  width: 750rpx;
  height: auto;
  min-height: 216rpx;
}
.container{
  min-height: 100vh;
  padding-bottom: calc(220rpx + env(safe-area-inset-bottom));//兼容 IOS>11.2
  box-sizing: border-box;
  background: rgba(250, 250, 250, 1);
  &-form{
    width: 712rpx;
    border-radius: 20rpx;
    margin: 0 auto;
    margin-top: -216rpx;
    position: relative;
    padding-top: 56rpx;
    box-sizing: border-box;
    &-item{
      padding: 40rpx 24rpx;
      background-color: #fff;
      border-radius: 20rpx;
      &:not(:first-child){
        box-sizing: border-box;
        margin-top: 18rpx;
      }
      .title{
        font-size: 32rpx;
        font-weight: 700;
        color: rgba(26, 26, 26, 1);
        display: flex;
        align-items: center;
        margin-bottom: 20rpx;
        .required{
          font-size: 28rpx;
          font-weight: 400;
          color: rgba(128, 128, 128, 1);
          margin-left: 10rpx;
          text{
            color: rgba(246, 93, 114, 1);
          }
        }
      }
      &-input{
        height: 96rpx;
        border-radius: 20rpx;
        background: rgba(250, 250, 250, 1);
        padding: 0 20rpx;
        box-sizing: border-box;
        &-placeholder{
          font-size: 32rpx;
          font-weight: 400;
          color: rgba(166, 166, 166, 1);
        }
      }
      &-datetime{
        height: 96rpx;
        border-radius: 20rpx;
        background: rgba(250, 250, 250, 1);
        padding: 0 20rpx;
        box-sizing: border-box;
        display: flex;
        align-items: center;
        &-placeholder{
          font-size: 32rpx;
          font-weight: 400;
          color: rgba(166, 166, 166, 1);
        }
      }
      &-date{
        height: 96rpx;
        border-radius: 20rpx;
        background: rgba(250, 250, 250, 1);
        padding: 0 20rpx;
        box-sizing: border-box;
        display: flex;
        align-items: center;
        &-placeholder{
          font-size: 32rpx;
          font-weight: 400;
          color: rgba(166, 166, 166, 1);
        }
      }
      &-radio {
        &-text{
          &-one{
            margin-top: 28rpx;
            font-size: 32rpx;
            font-weight: 400;
            color: rgba(56, 56, 56, 1);
            display: flex;
            align-items: center;
            &-control{
              width: 100%;
            }
            &-text{
              margin-left: 22rpx;
              white-space: nowrap;
              text-overflow: ellipsis;
              overflow: hidden;
              word-break: break-all;
              display: inline-block;
              vertical-align: bottom;
              width: 580rpx;
            }
          }
        }
        &-img{
          display: flex;
          flex-wrap: wrap;
          justify-content: space-between;
            &-one{
              width: 320rpx;
              height: 480rpx;
              background: rgba(250, 250, 250, 1);
              border-radius: 20rpx;
              margin-bottom: 24rpx;
              &-img{
                width: 320rpx;
                height: 320rpx;
                border-radius: 20rpx 20rpx 0px 0px;
              }
              &-bottom{
                display: flex;
                flex-direction: column;
                align-items: center;
                padding: 0 20rpx;
                &-text{
                  margin-bottom: 12rpx;
                  height: 64rpx;
                  font-size: 32rpx;
                  font-weight: 400;
                  color: rgba(56, 56, 56, 1);
                  line-height: 32rpx;
                  text-align: center;
                  width: 100%;
                  word-break: break-all;
                  overflow: hidden;
                  text-overflow: ellipsis;
                  display: -webkit-box;
                  -webkit-line-clamp: 2;
                  line-clamp: 2;
                  -webkit-box-orient: vertical;
                }
              }
            }
        }
      }
      &-textarea{
        width: 100%;
        border-radius: 20rpx;
        background: rgba(250, 250, 250, 1);
        padding: 35rpx 24rpx;
        box-sizing: border-box;
        &-placeholder{
          font-size: 30rpx;
          font-weight: 400;
          color: rgba(166, 166, 166, 1);
        }
      }
    }
  }
  &-qrcode{
    width: 712rpx;
    height: 293rpx;
    border-radius: 20rpx;
    background-repeat: no-repeat;
    background-size: 100% 100%;
    margin: 0 auto;
    margin-top: 20rpx;
    display: flex;
    align-items: center;
    padding-left: 32rpx;
    box-sizing: border-box;
    &-img{
      width: 241rpx;
      height: 241rpx;
    }
    &-text{
      width: 220rpx;
      font-weight: 400;
      color: rgba(56, 56, 56, 1);
      margin-left: 40rpx;
    }
  }
  &-footer{
    background-color: #FFF;
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100vw;
    padding: 20rpx;
    box-sizing: border-box;
    padding-bottom: calc(20rpx + env(safe-area-inset-bottom));//兼容 IOS>11.2
    position: fixed;
    left: 0;
    bottom: 0;
    z-index: 10;
    &-left{
      display: flex;
      align-items: center;
      &-op{
        display: flex;
        flex-direction: column;
        align-items: center;
        background-color: transparent;
        margin: 0;
        padding: 0;
        &:last-child{
          margin-left: 34rpx;
        }
        &-img{
          width: 44rpx;
          height: 44rpx;
        }
        &-text{
          font-size: 24rpx;
          font-weight: 400;
          color: rgba(0, 0, 0, 1);
          display: block;
        }
      }
    }
    &-right{
      display: flex;
      align-items: center;
      &-op{
        width: 260rpx;
        height: 72rpx;
        border-radius: 100rpx;
        display: flex;
        justify-content: center;
        align-items: center;
        margin: 0;
        padding: 0;
        &:last-child{
          margin-left: 12rpx;
        }
      }
      &-records{
        color: rgba(246, 93, 114, 1);
        background-color: rgba(246, 93, 114, 0.1);
        border-color: rgba(246, 93, 114, 0.1)!important;
      }
      &-submit{
        color: #fff;
        background-color: rgba(246, 93, 114, 1)!important;
      }
    }

  }
}
.exit-confirmation{
  width: 601rpx;
  height: 457rpx;
  border-radius: 40rpx;
  background: linear-gradient(0deg, rgba(255, 255, 255, 0) 0%, rgba(255, 235, 237, 1) 100%);
  padding: 40rpx;
  box-sizing: border-box;
  &-title{
    font-size: 40rpx;
    font-weight: 700;
    color: rgba(56, 56, 56, 1);
    text-align: center;
    margin-top: 60rpx;
  }
  &-desc{
    font-size: 28rpx;
    font-weight: 400;
    color: rgba(128, 128, 128, 1);
    width: 393rpx;
    text-align: center;
    margin: 0 auto;
    margin-top: 30rpx;
  }
  &-op{
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-top: 40rpx;
    &-cancel{
      width: 248rpx;
      height: 72rpx;
      border-radius: 200rpx;
      background: rgba(246, 93, 114, 0.1);
      font-size: 28rpx;
      color: rgba(246, 93, 114, 1);
      display: flex;
      justify-content: center;
      align-items: center;
    }
    &-back{
      width: 248rpx;
      height: 72rpx;
      border-radius: 200rpx;
      background: rgba(246, 93, 114, 1);
      font-size: 28rpx;
      font-weight: 400;
      color: rgba(255, 255, 255, 1);
      display: flex;
      justify-content: center;
      align-items: center;
    }
  }
}
.tip-popup{
  width: 601rpx;
  //height: 734rpx;
  border-radius: 40rpx;
  background: linear-gradient(0deg, rgba(255, 255, 255, 0) 0%, rgba(255, 235, 237, 1) 100%);
  padding: 40rpx;
  box-sizing: border-box;
  &-title{
    font-size: 40rpx;
    font-weight: 700;
    color: rgba(56, 56, 56, 1);
    margin-top: 46rpx;
    text-align: center;
  }
  &-desc{
    font-size: 28rpx;
    font-weight: 400;
    color: rgba(128, 128, 128, 1);
    text-align: center;
  }
  &-img{
    width: 301rpx;
    height: 249rpx;
    display: block;
    margin: 0 auto;
    margin-top: 68rpx;
  }
  &-op{
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 60rpx;
    &-left{
      width: 248rpx;
      height: 72rpx;
      border-radius: 200rpx;
      background: rgba(246, 93, 114, 0.1);
      font-size: 28rpx;
      font-weight: 400;
      color: rgba(246, 93, 114, 1);
      display: flex;
      justify-content: center;
      align-items: center;
    }
    &-right{
      width: 248rpx;
      height: 72rpx;
      border-radius: 200rpx;
      background: rgba(246, 93, 114, 1);
      font-size: 28rpx;
      font-weight: 400;
      color: rgba(255, 255, 255, 1);
      display: flex;
      justify-content: center;
      align-items: center;
    }
  }
  &-back{
    width: 521rpx;
    height: 72rpx;
    border-radius: 200rpx;
    background: rgba(246, 93, 114, 1);
    font-size: 28rpx;
    font-weight: 400;
    color: rgba(255, 255, 255, 1);
    margin: 0 auto;
    margin-top: 60rpx;
    display: flex;
    justify-content: center;
    align-items: center;
  }
}
.result-popup{
  width: 601rpx;
  //height: 734rpx;
  border-radius: 40rpx;
  background: linear-gradient(0deg, rgba(255, 255, 255, 0) 0%, rgba(255, 235, 237, 1) 100%);
  padding: 40rpx;
  box-sizing: border-box;
  &-title{
    font-size: 40rpx;
    font-weight: 700;
    color: rgba(56, 56, 56, 1);
    margin-top: 46rpx;
    text-align: center;
  }
  &-desc{
    font-size: 28rpx;
    font-weight: 400;
    color: rgba(128, 128, 128, 1);
    text-align: center;
  }
  &-img{
    width: 281rpx;
    height: 281rpx;
    display: block;
    margin: 0 auto;
    margin-top: 30rpx;
  }
  &-tip{
    font-size: 28rpx;
    font-weight: 400;
    color: rgba(128, 128, 128, 1);
    text-align: center;
  }
  &-op{
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 40rpx;
    &-left{
      width: 248rpx;
      height: 72rpx;
      border-radius: 200rpx;
      background: rgba(246, 93, 114, 0.1);
      font-size: 28rpx;
      font-weight: 400;
      color: rgba(246, 93, 114, 1);
      display: flex;
      justify-content: center;
      align-items: center;
    }
    &-right{
      width: 248rpx;
      height: 72rpx;
      border-radius: 200rpx;
      background: rgba(246, 93, 114, 1);
      font-size: 28rpx;
      font-weight: 400;
      color: rgba(255, 255, 255, 1);
      display: flex;
      justify-content: center;
      align-items: center;
    }
  }
}
.page-title{
  width: 360rpx;
  overflow:hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  -o-text-overflow:ellipsis;
  text-align: center;
  color: #fff;
}
.rule{
  width: 100vw;
  padding: 0 40rpx 40rpx 40rpx;
  box-sizing: border-box;
  &-header{
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 80rpx;
    background-color: white;
    box-sizing: border-box;
    &-text{
      font-size: 36rpx;
      font-weight: 700;
      color: rgba(56, 56, 56, 1);
    }
    .iconfont{
      font-size: 32rpx;
      color: rgba(194, 194, 194, 1);
    }
    position: sticky;
    top: 0;
    z-index: 1;
  }
}
</style>
