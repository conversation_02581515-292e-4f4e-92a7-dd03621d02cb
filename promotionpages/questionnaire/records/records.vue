<template>
  <view :class="themeStyle">
    <view class="container">
      <view class="container-header">
          <view class="container-header-left">
            <view class="container-header-left-one" :class="{'container-header-left-one-active':tabIndex==0}" @click="changeTab(0)">我的</view>
            <view class="container-header-left-one" :class="{'container-header-left-one-active':tabIndex==1}" @click="changeTab(1)">好友</view>
          </view>
          <view class="container-header-right" @click="toFilter">
            <image :src="is_show_filter ? $util.img('public/static/youpin/questionnaire/filter-active.png') : $util.img('public/static/youpin/questionnaire/filter.png')" class="container-header-right-icon"></image>
            <text class="container-header-right-text" :class="{'container-header-right-text-active':is_show_filter}">筛选</text>
          </view>
      </view>
      <view class="container-count">共有{{count}}份提交数据</view>
      <view class="container-list">
        <view class="container-list-one" v-for="(item,index) in list" :key="index">
          <view class="container-list-one-left">
            <view class="container-list-one-left-title">{{item.name}}</view>
            <view class="container-list-one-left-mobile">{{item.mobile}}</view>
            <view class="container-list-one-left-time">提交时间:<text>{{item.last_update_time}}</text></view>
          </view>
          <view class="container-list-one-right">
            <text class="container-list-one-right-op" @click="toDetail(item.submit_id,index)" v-if="tabIndex==0">查看</text>
          </view>
        </view>
      </view>
      <view class="container-footer">
        <!-- #ifdef MP-WEIXIN -->
        <button type="primary" class="container-footer-op" open-type="share">邀请好友参与</button>
        <!-- #endif -->
        <!-- #ifdef H5 -->
        <button type="primary" class="container-footer-op" @click="toShare">邀请好友参与</button>
        <!-- #endif -->
      </view>
    </view>

    <uni-popup ref="popupRef" type="bottom" :bottom-radius="true">
      <view class="question-popup">
        <view class="question-popup-title">
          <text class="question-popup-title-text">{{list.length && list[showIndex].mobile}} 问卷</text>
          <text class="iconfont iconroundclose" @click="toClose"></text>
        </view>
        <view class="question-popup-list">
          <view class="question-popup-list-one" v-for="(item,index) in answerList" :key="index">
            <view class="question-popup-list-one-name">{{item.name}}</view>
            <template v-if="['radio','checkbox'].includes(item.type)">
              <view class="question-popup-list-one-input question-popup-list-one-input-more" v-for="(option,j) in item.value" :key="j">{{option}}</view>
            </template>
            <template v-else-if="item.type=='textarea'">
              <view class="question-popup-list-one-textarea">{{item.value}}</view>
            </template>
            <template v-else>
              <view class="question-popup-list-one-input">{{item.value}}</view>
            </template>
          </view>
        </view>
        <view class="question-popup-footer" v-if="is_edit">
          <text class="question-popup-footer-op" @click="toEdit">修改</text>
        </view>
      </view>
    </uni-popup>

    <uni-popup ref="filterRef" type="top" class="question-popup-filter-popup" top="96rpx" @change="filterChange">
      <view class="question-popup-filter">
        <form class="question-popup-filter-form">
          <view class="question-popup-filter-form-row">
            <view class="question-popup-filter-form-row-title">活动名称:</view>
            <view class="question-popup-filter-form-row-select" @click="selectPopupOpen">
              <text class="question-popup-filter-form-row-select-text">{{name_list.length ? name_list[name_index[0]]['name'] : ''}}</text>
              <uni-icons type="forward" color="rgba(166, 166, 166, 1)"></uni-icons>
            </view>
          </view>
          <view class="question-popup-filter-form-row" v-if="tabIndex==1">
            <view class="question-popup-filter-form-row-title">提交人手机号后4位<text class="question-popup-filter-form-row-title-option">（选填）</text></view>
            <input class="question-popup-filter-form-row-input" v-model="mobile" placeholder="输入手机尾号4尾数" placeholder-class="question-popup-filter-form-row-input-placeholder"/>
          </view>
          <view class="question-popup-filter-form-row">
            <view class="question-popup-filter-form-row-title">记录提交日期<text class="question-popup-filter-form-row-title-option">（选填）</text></view>
            <view class="question-popup-filter-form-row-date" @click="showDateRange">
              <text :class="{'question-popup-filter-form-row-date-start':start_date}">{{start_date || '开始日期'}}</text>
              <text class="question-popup-filter-form-row-date-center">-</text>
              <text :class="{'question-popup-filter-form-row-date-end':end_date}">{{end_date || '结束'}}</text>
            </view>
          </view>
          <view class="question-popup-filter-form-op">
            <text class="question-popup-filter-form-op-left" @click="filterReset">重置</text>
            <text class="question-popup-filter-form-op-right" @click="filterSubmit">确认</text>
          </view>
        </form>
      </view>
    </uni-popup>

    <uni-popup ref="selectPopup" type="bottom" :bottom-radius="true">
      <view class="select-view">
        <view class="select-view-header">活动名称</view>
        <picker-view v-if="visible" :value="name_index" @change="bindChange" class="select-view-picker" :indicator-style="indicatorStyle">
          <picker-view-column>
            <view class="select-view-picker-item" v-for="(item,index) in name_list" :key="index">{{item['name']}}</view>
          </picker-view-column>
        </picker-view>
        <button class="select-view-cancel" @click="selectPopupClose">确认</button>
      </view>
    </uni-popup>

    <uni-popup type="bottom" ref="dateRangeRef">
      <view class="date-range">
        <view class="date-range-header">记录提交日期</view>
        <DateSelector
            :mode="selectDateType"
            @onChange="onDateSelectorChange"
            @onSubmit="onDateSelectorSubmit"
            @onCancel="onDateSelectorCancel"
            :defaultStartDate="start_date"
            :defaultEndDate="end_date"
            ref="dateSelectorRef"
        />
      </view>
    </uni-popup>

    <diy-share-navigate-h5 ref="shareNavigateH5"></diy-share-navigate-h5>
  </view>
</template>


<script>
import system from "@/common/js/system.js";
import uniPopup from '@/components/uni-popup/uni-popup.vue';
import apiurls from "../../../common/js/apiurls";
import diyShareNavigateH5 from '@/components/diy-share-navigate-h5/diy-share-navigate-h5'
import DateSelector from '../../components/dengrq-datetime-picker/dateSelector/index.vue'
export default {
  name: "records",
  components:{
    uniPopup,
    diyShareNavigateH5,
    DateSelector
  },
  computed: {
    themeStyle() {
      return 'theme-' + this.$store.state.themeStyle
    },
  },
  data() {
    return {
      questionnaire_id:null,
      search_questionnaire_id: null,  //用于搜索请求
      title: "提交记录",
      tabIndex: 0,
      list:[],
      page_size: 10,
      page: 1,
      count: 0,
      finished: false,
      showIndex: 0,
      answerList:[],
      share_title: '',
      image: '',
      is_edit: false,
      is_show_filter: false,
      name_list:[],
      name_index:[0],
      visible: false,
      indicatorStyle: `height: 92rpx;`,
      mobile: '',
      start_date:'',
      end_date:'',
      selectDateType: 1
    }
  },
  async onLoad(option){
    await system.wait_staticLogin_success();
    this.questionnaire_id = option.questionnaire_id || null;
    this.search_questionnaire_id = option.questionnaire_id || null;
    await this.getData();
    await this.getListData();
  },
  onReady(){
    // #ifdef H5
    this.setWechatShare();
    // #endif
  },
  async onReachBottom(){
    await this.getListData();
  },
  methods:{
    goBack(){
      this.$util.goBack()
    },
    async toDetail(submit_id,index){
      this.showIndex = index;
      uni.showLoading({
        title: '加载中...'
      })
      try{
        let res = await this.$api.sendRequest({
          url: apiurls.questionnaireActivityDetailUrl,
          async: false,
          data: {
            submit_id
          }
        })
        uni.hideLoading()
        if(res.code == 0){
          this.is_edit = res.data.is_edit;
          this.answerList = res.data.column_data.map(item=>{
            if(['radio','checkbox'].includes(item.type)){
              item.value = item.value.split('/');
            }
            return item;
          });
          this.$refs.popupRef.open();
        }else{
          this.$util.showToast({
            title: res.message
          })
        }
      }catch (e) {

      }
    },
    toClose(){
      this.$refs.popupRef.close();
    },
    async getData(){
      try{
        let params = {
          questionnaire_id: this.questionnaire_id,
        }
        let url = apiurls.questionnaireActivityFormUrl;
        let recommend_member_id = uni.getStorageSync("recommend_member_id")
        if(recommend_member_id){
          params.share_member_id = recommend_member_id
        }
        let res = await this.$api.sendRequest({
          url,
          async: false,
          data: params
        })
        if (res.code == 0) {
          this.share_title = res.data.share_title;
          this.image = res.data.image;
        }
      }catch (e) {

      }
    },
    async changeTab(index){
      this.tabIndex = index;
      this.page = 1;
      this.finished = false;
      this.list = [];
      this.filterReset();
      await this.getListData();
    },
    async getListData(){
      if(this.finished){
        return;
      }
      let params ={
        page: this.page,
        page_size: this.page_size,
        questionnaire_id: this.search_questionnaire_id
      }
      if(this.start_date){
        params.submit_start_date = this.start_date;
      }
      if(this.end_date){
        params.submit_end_date = this.end_date;
      }
      if(this.mobile){
        params.mobile = this.mobile
      }
      uni.showLoading({
        title: '加载中...'
      })
      try{
        let res = await this.$api.sendRequest({
          url:this.tabIndex ? apiurls.questionnaireActivityChildListsUrl : apiurls.questionnaireActivityListsUrl,
          async: false,
          data: params
        })
        uni.hideLoading()
        if (res.code == 0){
          this.name_list = res.data.join_activity_list.map((item,index)=>{
            if(item.questionnaire_id == this.search_questionnaire_id){
              this.name_index = [index]
            }
            return item;
          });
          this.list = this.list.concat(res.data.list);
          this.count = res.data.count;
          if(this.list.length < this.count){
            this.page = this.page + 1;
          }else{
            this.finished = true;
          }
        }else{
          this.$util.showToast({
            title: res.message
          })
        }
      }catch (e) {
        uni.hideLoading();
      }
    },
    toEdit(){
      this.$refs.popupRef.close();
      this.$util.redirectTo(`/promotionpages/questionnaire/qform/qform?questionnaire_id=${this.questionnaire_id}&submit_id=${this.list[this.showIndex].submit_id}`)
    },
    toFilter(){
      this.$refs.filterRef.open();
    },
    filterChange(ele){
      this.is_show_filter = ele.show;
    },
    showDateRange(){
      this.$refs.dateRangeRef.open(()=>{
        setTimeout(()=>{
          this.$refs.dateSelectorRef.onTapStartDate()
        },10)
      });
    },
    onDateSelectorChange({ startDate, endDate }) {
      // console.log('onDateSelectorChange', startDate, endDate);
    },
    onDateSelectorSubmit({ startDate, endDate }) {
      // console.log('onDateSelectorSubmit', startDate, endDate);
      this.start_date = startDate;
      this.end_date = endDate;
      this.$refs.dateRangeRef.close();
    },
    onDateSelectorCancel(){
      this.$refs.dateRangeRef.close();
    },
    filterReset(){
      this.mobile = '';
      this.start_date = '';
      this.end_date = '';
      this.search_questionnaire_id = this.questionnaire_id;
      this.name_list.map((item,index)=>{
        if(item.questionnaire_id == this.search_questionnaire_id){
          this.name_index = [index];
        }
        return item;
      });
    },
    async filterSubmit(){
      this.page = 1;
      this.finished = false;
      this.list = [];
      this.$refs.filterRef.close()
      await this.getListData();
    },
    bindChange(ele){
      this.name_index = ele.detail.value;
      this.search_questionnaire_id = this.name_list[this.name_index[0]]['questionnaire_id'];
    },
    selectPopupOpen(){
      this.visible = true;
      this.$refs.selectPopup.open()
    },
    selectPopupClose(){
      this.$refs.selectPopup.close()
      this.visible = false;
    },
    /**
     *分享参数组装(注意需要分享的那一刻再调此方法)
     */
    getSharePageParams(){
      let params = {
        questionnaire_id:this.questionnaire_id
      }
      return this.$util.unifySharePageParams(`/promotionpages/questionnaire/qform/qform`,this.share_title || '先迈商城',
          ``,params,this.image)
    },
    /**
     * 设置微信公众号分享
     */
    setWechatShare() {
      // 微信公众号分享
      // #ifdef H5
      let share_data=this.$util.deepClone(this.getSharePageParams());
      let link=window.location.origin+this.$router.options.base+share_data.link.slice(1);
      share_data.link=link;
      this.$util.publicShare(share_data);
      // #endif
    },
    toShare(){
      // #ifdef H5
      let share_data=this.getSharePageParams();
      if(this.$refs.shareNavigateH5) this.$refs.shareNavigateH5.open(share_data);
      // #endif
    }
  },
  /**
   * 自定义分享内容
   * @param {Object} res
   */
  onShareAppMessage(res) {
    let { title, link, imageUrl, query } = this.getSharePageParams()
    return this.$buriedPoint.pageShare(link , imageUrl, title);
  },
}
</script>

<style scoped lang="scss">
/deep/.uni-popup__wrapper.uni-custom.bottom .uni-popup__wrapper-box{
  max-height: 90vh;
}
.container{
  min-height: 100vh;
  background: rgba(250, 250, 250, 1);
  padding-bottom: calc(140rpx + env(safe-area-inset-bottom));//兼容 IOS>11.2
  box-sizing: border-box;
  &-header{
    height: 96rpx;
    width: 100vw;
    display: flex;
    justify-content: space-between;
    align-items: center;
    background-color: white;
    position: fixed;
    left: 0;
    top: 0;
    z-index: 10;
    padding-left: 20rpx;
    padding-right: 46rpx;
    box-sizing: border-box;
    &-left{
      display: flex;
      align-items: center;
      &-one{
        width: 200rpx;
        height: 64rpx;
        font-size: 28rpx;
        font-weight: 400;
        color: rgba(245, 93, 113, 1);
        display: flex;
        justify-content: center;
        align-items: center;
        position: relative;
        background: rgba(245,93,113,0.1);
        border-radius: 100rpx;
        &-active{
          color: #fff;
          background: rgba(246, 93, 114, 1);
        }
        &:last-child{
          margin-left: -40rpx;
        }
      }
    }
    &-right{
      display: flex;
      align-items: center;
      &-icon{
        width: 32rpx;
        height: 32rpx;
      }
      &-text{
        font-size: 32rpx;
        font-weight: 400;
        color: rgba(128, 128, 128, 1);
        margin-left: 10rpx;
        &-active{
          color: rgba(245, 93, 113, 1);
        }
      }
    }
  }
  &-count{
    font-size: 26rpx;
    font-weight: 400;
    color: rgba(166, 166, 166, 1);
    height: 76rpx;
    padding-left: 20rpx;
    box-sizing: border-box;
    position: fixed;
    left: 0;
    top: 96rpx;
    width: 100vw;
    display: flex;
    align-items: center;
    background: rgba(250, 250, 250, 1);
    z-index: 10;
  }
  &-list{
    padding: 0 20rpx;
    box-sizing: border-box;
    margin-top: 172rpx;
    position: relative;
    &-one{
      width: 100%;
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 30rpx 20rpx;
      box-sizing: border-box;
      border-radius: 20rpx;
      background: rgba(255, 255, 255, 1);
      margin-bottom: 20rpx;
      &-left{
        width: 520rpx;
        &-title{
          font-size: 32rpx;
          font-weight: 400;
          color: rgba(56, 56, 56, 1);
          text-overflow: ellipsis;
          overflow: hidden;
          word-break: break-all;
          white-space: nowrap;
        }
        &-mobile{
          font-size: 28rpx;
          font-weight: 400;
          color: rgba(56, 56, 56, 1);
        }
        &-time{
          font-size: 28rpx;
          font-weight: 400;
          color: rgba(166, 166, 166, 1);
          text{
            margin-left: 10rpx;
          }
        }
      }
      &-right{
        &-op{
          display: flex;
          justify-content: center;
          align-items: center;
          width: 124rpx;
          height: 64rpx;
          border-radius: 200rpx;
          background: rgba(246, 93, 114, 1);
          font-size: 28rpx;
          font-weight: 400;
          color: rgba(255, 255, 255, 1);
        }
      }
    }
  }
  &-footer{
    background-color: #FFF;
    display: flex;
    justify-content: center;
    align-items: center;
    width: 100vw;
    padding: 20rpx;
    box-sizing: border-box;
    padding-bottom: calc(20rpx + env(safe-area-inset-bottom));//兼容 IOS>11.2
    position: fixed;
    left: 0;
    bottom: 0;
    &-op{
      width: 672rpx;
      height: 72rpx;
      border-radius: 100rpx;
      display: flex;
      justify-content: center;
      align-items: center;
      margin: 0;
      padding: 0;
      color: #fff;
      background-color: rgba(246, 93, 114, 1)!important;
    }
  }
}
.question-popup{
  width: 100vw;
  padding: 0 40rpx 40rpx 40rpx;
  padding-bottom: calc(120rpx + env(safe-area-inset-bottom));//兼容 IOS>11.2
  box-sizing: border-box;
  &-title{
    padding-top: 40rpx;
    box-sizing: border-box;
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20rpx;
    width: 100%;
    background-color: white;
    &-text{
      font-size: 36rpx;
      font-weight: 700;
      color: rgba(56, 56, 56, 1);
    }
    .iconfont{
      font-size: 32rpx;
      color: rgba(194, 194, 194, 1);
    }
    position: sticky;
    top: 0;
  }
  &-list{
    &-one{
      margin-top: 48rpx;
      &-name{
        font-size: 32rpx;
        font-weight: 400;
        color: rgba(26, 26, 26, 1);
        margin-bottom: 12rpx;
      }
      &-input{
        width: 100%;
        height: 96rpx;
        line-height: 96rpx;
        border-radius: 20rpx;
        background: rgba(250, 250, 250, 1);
        font-size: 32rpx;
        font-weight: 400;
        color: rgba(128, 128, 128, 1);
        padding: 0 20rpx;
        white-space: nowrap;
        box-sizing: border-box;
        text-overflow: ellipsis;
        overflow: hidden;
        word-break: break-all;
        display: inline-block;
        &-more{
          &:not(:last-child){
            margin-bottom: 20rpx;
          }
        }
      }
      &-textarea{
        width: 100%;
        height: 344rpx;
        border-radius: 20rpx;
        padding: 35rpx 24rpx;
        box-sizing: border-box;
        background: rgba(250, 250, 250, 1);
        overflow-y: auto;
      }
    }
  }
  &-footer{
    position: fixed;
    bottom: 0;
    left: 0;
    width: 100%;
    padding-bottom: calc(20rpx + env(safe-area-inset-bottom));//兼容 IOS>11.2
    box-sizing: border-box;
    background-color: white;
    &-op{
      width: 672rpx;
      height: 72rpx;
      border-radius: 100px;
      background: rgba(246, 93, 114, 1);
      font-size: 28rpx;
      font-weight: 400;
      color: rgba(255, 255, 255, 1);
      display: flex;
      justify-content: center;
      align-items: center;
      margin: 0 auto;
    }
  }
}
.question-popup-filter{
  padding: 50rpx 0 0 0;
  box-sizing: border-box;
  &-popup{
    /deep/.uni-popup__wrapper.uni-custom .uni-popup__wrapper-box{
      border-radius: 0rpx;
    }
  }
  &-form{
    &-row{
      padding: 0 36rpx;
      box-sizing: border-box;
      &:not(:first-child){
        margin-top: 50rpx;
      }
      &-title{
        font-size: 30rpx;
        font-weight: 400;
        color: rgba(51, 51, 51, 1);
        &-option{
          color: rgba(166, 166, 166, 1);
        }
      }
      &-select{
        width: 100%;
        height: 80rpx;
        border-radius: 20rpx;
        background: rgba(255, 255, 255, 1);
        border: 2rpx solid rgba(229, 229, 229, 1);
        padding: 0 40rpx;
        box-sizing: border-box;
        font-size: 30rpx;
        font-weight: 400;
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-top: 30rpx;
        &-text{

        }
      }
      &-input{
        width: 100%;
        height: 80rpx;
        border-radius: 20rpx;
        background: rgba(255, 255, 255, 1);
        border: 2rpx solid rgba(229, 229, 229, 1);
        padding: 0 40rpx;
        box-sizing: border-box;
        font-size: 30rpx;
        font-weight: 400;
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-top: 30rpx;
        &-placeholder{
          color: rgba(166, 166, 166, 1);
        }
      }
      &-date{
        width: 100%;
        height: 80rpx;
        border-radius: 20rpx;
        background: rgba(255, 255, 255, 1);
        border: 2rpx solid rgba(229, 229, 229, 1);
        color: rgba(166, 166, 166, 1);;
        padding: 0 90rpx;
        box-sizing: border-box;
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-top: 30rpx;
        &-start{
          color: #000;
        }
        &-end{
          color: #000;
        }
      }
    }
    &-op{
      width: 100%;
      height: 96rpx;
      display: flex;
      align-items: center;
      margin-top: 64rpx;
      background: rgba(250, 250, 250, 1);
      &-left{
        width: 50%;
        display: flex;
        justify-content: center;
        align-items: center;
        border-right:2rpx solid rgba(229,229,229,1);
        height: 100%;
      }
      &-right{
        width: 50%;
        display: flex;
        justify-content: center;
        align-items: center;
        height: 100%;
      }
    }
  }
}
.select-view{
  padding-bottom: 40rpx;
  box-sizing: border-box;
  &-header{
    font-size: 32rpx;
    font-weight: 700;
    color: rgba(56, 56, 56, 1);
    text-align: center;
    height: 92rpx;
    line-height: 92rpx;
  }
  &-picker{
    width: 750rpx;
    height: 400rpx;
    &-item{
      text-align: center;
      font-size: 30rpx;
      font-weight: 400;
      display: flex;
      justify-content: center;
      align-items: center;
    }
  }
  &-cancel{

  }
}
.date-range{
  padding: 0 30rpx 0 30rpx;
  box-sizing: border-box;
  &-header{
    font-size: 28rpx;
    font-weight: 700;
    color: rgba(56, 56, 56, 1);
    text-align: left;
    height: 92rpx;
    line-height: 92rpx;
  }
}
</style>
