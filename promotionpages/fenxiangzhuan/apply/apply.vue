<template>
<view class="apply"  :class="themeStyle">
	<view class="goods">
		<!-- 商品信息 -->
		<view class="goodsInfo">
			<image :src="$util.img(goodsSkuDetail.sku_image)" mode="widthFix"></image>
			<view class="info">
				<view class="name">{{goodsSkuDetail.goods_name}}</view>
				<view class="spec-sku" v-if="goodsSkuDetail.sku_spec_format">
					<label v-for="(item,index) in goodsSkuDetail.sku_spec_format" :key="index">{{item.spec_value_name}}<text v-if="index<goodsSkuDetail.sku_spec_format.length-1">,</text></label>
				</view>
				<view class="spec-sku" v-else>
					<text>{{goodsSkuDetail.spec_name}}</text>
				</view>
				<view class="price"><text>￥</text>{{goodsSkuDetail.price}}</view>
			</view>
		</view>
		<!-- 规格展示 -->
		<!-- <view class="skuInfo">
			<view class="sku-list-wrap" v-for="(item, index) in goodsSkuDetail.goods_spec_format" :key="index">
				<text class="title ns-font-size-base">{{ item.spec_name }}</text>
				<view
					v-for="(item_value, index_value) in item.value"
					:key="index_value"
					:class="{
						selected: item_value['selected'] || skuId == item_value.sku_id,
						disabled: item_value['disabled'] || (!item_value['selected'] && disabled)
					}"
					class="items ns-border-color-gray ns-bg-color-gray ns-font-size-base"
					@click="change(item_value.sku_id, item_value.spec_id)"
				>
					<image v-if="item_value.image" :src="$util.img(item_value.image)" @error="valueImageError(index, index_value)" />
					<text>{{ item_value.spec_value_name }}</text>
				</view>
			</view>
		</view> -->
	</view>

	<view class="address-edit-content">
		<view class="edit-wrap">
			<view class="edit-item">
				<text class="tit">姓名</text>
				<input class="uni-input" type="text" placeholder-style="color:#CCCCCC" placeholder="请输入姓名" maxlength="30" name="name" value=""
				 v-model="formData.name" />
			</view>
			<view class="edit-item">
				<text class="tit">手机号码</text>
				<input class="uni-input" type="number" placeholder-style="color:#CCCCCC" placeholder="请输入手机号码" maxlength="11" value="" v-model="formData.mobile" />
			</view>
			<view class="edit-item">
				<text class="tit">所在地区</text>
				<pick-regions :default-regions="defaultRegions" @getRegions="handleGetRegions" class="picker">
					<text class="select-address" :class="{ empty: !formData.full_address }">{{ formData.full_address ? formData.full_address : '请选择所在地区' }}</text>
				</pick-regions>
			</view>
			<view class="edit-item">
				<text class="tit">详细地址</text>
				<input class="uni-input" type="text" placeholder-style="color:#CCCCCC" placeholder="请输入详细地址" maxlength="50" v-model="formData.address" />
			</view>
		</view>
	</view>

	<!-- 支付 -->
	<view class="payment">微信支付
		<label class="radio">
			<radio checked="true" color="#04BE02"  style="transform:scale(0.8)"/><text></text>
		</label>
	</view>
	<view class="filling"> </view>
	<view class="footer">
		<view class="apply-btn" @click="submit">提交申请</view>
	</view>

	<!-- 加载动画 -->
	<loading-cover ref="loadingCover"></loading-cover>
	<!-- 授权登录弹窗 -->
	<yd-auth-popup ref="ydauth"></yd-auth-popup>
  <ns-login ref="login"></ns-login>
	<uni-coupon-pop ref="couponPop"></uni-coupon-pop>
</view>
</template>

<script>
import apply from '../public/js/apply.js';
export default{
	name:'apply',
	mixins: [apply],
	data(){
		return{ }
	},
	computed: {
		themeStyle() {
			return 'theme-' + this.$store.state.themeStyle;
		},
	},
	onShow() {
		if (uni.getStorageSync('is_register')) {
			this.$util.toShowCouponPopup(this)
			uni.removeStorageSync('is_register');
		}
	},
	methods:{

	}
}
</script>

<style lang="scss" scoped>
.goods{
	background: #fff;
	margin-bottom: 20rpx;
}
.goodsInfo{
	display: flex;
	align-items: flex-start;
	padding: 30rpx 36rpx;
	image{
		width: 180rpx;
		height: 180rpx;
		margin-right: 20rpx;
		border-radius:20rpx;
	}
	.info{
		height: 180rpx;
		width: calc(100% - 200rpx);
		position: relative;
		.name{
			display: -webkit-box;
			-webkit-box-orient: vertical;
			-webkit-line-clamp: 2;
			overflow: hidden;
			font-size: $ns-font-size-xm;
			color: #333;
		}
		.price{
			font-size: $ns-font-size-lg + 4rpx;
			color:$base-color;
			position: absolute;
			bottom: 0;
			left: 0;
			font-weight: bold;
			text{
				font-size: $ns-font-size-xm;
				font-weight: normal;
			}
		}
	}
}

.skuInfo{
	padding: 0 30rpx;
	height: calc(100% - 330rpx);
	box-sizing: border-box;
	overflow: scroll;
	.wrap {
		height: calc(100% - 116rpx);
	}

	.sku-list-wrap {
		// border-bottom: 1px solid rgba(0, 0, 0, 0.1);
		padding-bottom: 20rpx;
	}

	.sku-list-wrap .title {
		font-weight: 400;
		padding: 26rpx 0;
		margin: 0;
		display: block;
	}

	.sku-list-wrap .items {
		position: relative;
		display: inline-block;
		border: 1px solid;
		padding:0 20rpx;
		border-radius: 27rpx;
		margin: 0 10rpx 10rpx 0;
		font-size: 26rpx;
		line-height: 54rpx;
	}

	.sku-list-wrap .items.disabled {
		border: 1px dashed;
	}
	.selected{
		background-color: #fee9e6 !important;
		color: $base-color !important;
		border-color: $base-color !important;
	}

	.sku-list-wrap .items image {
		height: 48rpx;
		width: 48rpx;
		border-radius: 4rpx;
		margin-right: 10rpx;
		display: inline-block;
		vertical-align: middle;
	}

	 .number-wrap .number-line {
		padding: 20rpx 0;
		line-height: 72rpx;
		border-bottom: 1px solid rgba(0, 0, 0, 0.1);
	}

	.number-wrap .title {
		font-weight: 400;
	}
}

.address-edit-content{
	margin-bottom: 20rpx;
	.edit-wrap {
		background: #fff;
		overflow: hidden;
		padding-top: 30rpx;
	}

	.edit-item {
		display: flex;
		align-items: center;
		margin-bottom: 36rpx;
		background-color: #fff;
		&.arddress-defalut {
			justify-content: space-between;
			switch {
				margin-right: 30rpx;
			}
		}
		.tit {
			width: 200rpx;
			box-sizing: border-box;
			font-size: 28rpx;
			padding-left: 32rpx;

		}
		.select-address {
			display: inline-block;
			font-size: $ns-font-size-base;

			&.empty {
				color: #CCCCCC;
				width: 477rpx;
			}
		}
		.picker{
			display: flex;
			align-items: center;
		}
		input,.picker {
			width: 520rpx;
			height: 60rpx;
			line-height: 60rpx;
			box-sizing: border-box;
			border:2rpx solid #ccc;
			border-radius: 8rpx;
			padding:0 10rpx
		}
	}
}

.payment{
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 0 36rpx;
	height: 100rpx;
	background: #fff;
}
.footer{
	position: fixed;
	width: 100%;
	height: 100rpx;
	left: 0;
	bottom: 0;
	background: #fff;
	.apply-btn{
		width: 600rpx;
		height: 80rpx;
		line-height: 80rpx;
		text-align: center;
		color: #fff;
		background: $base-color;
		border-radius: 40rpx;
		margin:10rpx auto;
	}
}
.filling{
	width: 100%;
	height: 120rpx;
}
.spec-sku{
	color:#999;
	font-size: $ns-font-size-sm;
}
</style>
