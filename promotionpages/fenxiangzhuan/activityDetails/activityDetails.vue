<!-- 分享赚活动参与详情 -->
<template>
	<view>
    <view class="activity">
      <view class="activity--product">
        <image :src="$util.img(info.sku_image)" class="activity--product--img"></image>
        <view class="activity--product--info">
          <view class="activity--product--info--title">{{info.goods_name}}</view>
          <view class="activity--product--info--price"><text>￥</text>{{info.price}}</view>
          <view class="activity--product--info--status" >{{info.status_str}}</view>
        </view>
      </view>
      <view class="activity--share">
        <image :src="$util.img($util.img('public/static/youpin/share-earn-icon.png'))" class="activity--share--qrcode" show-menu-by-longpress></image>
<!--        <view class="activity&#45;&#45;share&#45;&#45;separate"></view>-->
<!--        <view class="activity&#45;&#45;share&#45;&#45;tip">长按保存二维码，分享好友成功下单组团成功</view>-->
        <button class="activity--share--op" open-type="share">分享</button>
      </view>
      <view class="activity--records" v-if="joinList.length>0">
        <view class="activity--records--title">参与记录({{info.has_original_num}}/{{info.original_num}}）</view>
        <view class="activity--records--list">
          <view class="activity--records--list--one" v-for="(item,index) in joinList"v-bind:key="index">
            <view class="activity--records--list--one--left">
              <image :src="$util.img(item.headimg)"></image>
              <text>{{item.nickname}}</text>
            </view>
            <view class="activity--records--list--one--right">{{item.create_time}}</view>
          </view>
        </view>
      </view>
    </view>
    <view class="site-qrcode">
      <view>保存添加店主微信，了解更多活动信息</view>
      <image :src="$util.img(site_qrcode)" show-menu-by-longpress></image>
    </view>

    <loading-cover ref="loadingCover"></loading-cover>
	</view>
</template>

<script>
	import apiurls from "../../../common/js/apiurls";

  export default {
		data() {
			return {
			  options:{},
        activity_id:null,  //分享赚活动id
        apply_id:null,  //参与分享赚的参与id
        info:{},
        joinList:[],
        site_qrcode:""
			};
		},
    methods:{
		  //处理进入页面参数
		  getParams(options){
        // 小程序扫码进入
        let sceneDict={}; //扫二维进入小程序的参数
        if (options.scene) {
          var sceneParams = decodeURIComponent(options.scene);
          sceneParams = sceneParams.split('&');
          if (sceneParams.length) {
            sceneParams.forEach(item => {
              let oneItme=item.split('=');
              if(oneItme.length>1){
                sceneDict[oneItme[0]]=oneItme[1];
              }
            });
          }
        }
        let activity_id=sceneDict.hasOwnProperty('ai') ? sceneDict['ai'] : options.share_activity_id;
        let apply_id=sceneDict.hasOwnProperty("qi") ? sceneDict['qi'] : options.share_apply_id;
        this.activity_id=activity_id;
        this.apply_id=apply_id;
      },
      getData(){
        this.$api.sendRequest({
          url: apiurls.ShareBuyActivitySuccessDetail,
          data: {
            id:this.apply_id,
          },
          success:(res)=>{
            if(res.code!=0){
              uni.showToast({
                title: res.message,
                mask:true,
                icon:"none",
                duration: 3000
              });
            }else{
              console.log("data",res.data)
              this.info=res.data.activityInfo;
              this.joinList=res.data.joinList;
              this.site_qrcode=res.data.shopInfo.wechat_qrcode_img;
            }
            if (this.$refs.loadingCover) this.$refs.loadingCover.hide();
          },
          fail:()=>{

          }
        });

      },
    },
    onLoad(options){
      this.$langConfig.refresh();
      this.options=options;
      this.getParams(options);
    },
    onShow(){
      this.getData();
    },
    onShareAppMessage(){
      let shop_id=uni.getStorageSync('shop_id');
      let path=`/pages/goods/detail/detail?shop_id=${shop_id}&sku_id=${this.info.sku_id}&share_activity_id=${this.activity_id}&share_apply_id=${this.apply_id}`;
      let recommend_member_id=uni.getStorageSync('member_id');
      if(recommend_member_id){
        path+=`&recommend_member_id=${recommend_member_id}`;
      }
		  let obj={
		    title:`${this.info.goods_name} \n ￥${this.info.sku_price}`,
        path:path,
        imageUrl:this.$util.img(this.info.sku_image)
      }
		  return obj
    }
	}
</script>

<style lang="scss">
  .activity{
    background-color: white;
    padding-bottom: 30rpx;
    &--product{
      padding: 36rpx 48rpx;
      box-sizing: border-box;
      display: flex;
      justify-content: space-between;
      align-items: center;
      &--img{
        width: 240rpx;
        height: 240rpx;
        border-radius: 20rpx;
      }
      &--info{
        display: flex;
        flex-direction: column;
        align-items: flex-start;
        width: 390rpx;
        &--title{
          width: 100%;
          overflow: hidden;
          text-overflow: ellipsis;
          display: -webkit-box;
          -webkit-line-clamp: 2;
          -webkit-box-orient: vertical;
        }
        &--price{
          font-size: 36rpx;
          font-weight: bold;
          color: #F2280D;
          margin-top: 36rpx;
          text{
            font-size: 26rpx;
          }
        }
        &--status{
          font-size: 28rpx;
          font-weight: 500;
          color: #F2280D;
          align-self: flex-end;
        }
      }
    }
    &--share{
      display: flex;
      flex-direction: column;
      align-items: center;
      padding: 48rpx 0;
      box-sizing: border-box;
      &--qrcode{
        width: 582rpx;
        height: 807rpx;
      }
      &--code{
        font-size: 28rpx;
        font-weight: 500;
        color: #343434;
        margin-top: 36rpx;
      }
      &--separate{
        width: 460rpx;
        height: 1rpx;
        border: 1rpx solid #EEEEEE;
        margin-top: 30rpx;
      }
      &--tip{
        font-size: 24rpx;
        font-weight: 500;
        color: #9A9A9A;
        margin-top: 30rpx;
      }
      &--op{
        width: 600rpx;
        height: 80rpx;
        background: #F2280C;
        border-radius: 40rpx;
        font-size: 32rpx;
        font-weight: 500;
        color: #FFFFFF;
        margin-top: 48rpx;
      }
    }
    &--records{
      width: 702rpx;
      margin: 0 auto;
      background: #FEF6F4;
      border-radius: 20px;
      padding: 36rpx 30rpx;
      box-sizing: border-box;
      &--title{
        font-size: 30rpx;
        font-weight: 500;
        color: #F2280C;
        text-align: center;
      }
      &--list{
        &--one{
          display: flex;
          justify-content: space-between;
          align-items: center;
          &:not(:first-child){
            margin-top: 36rpx;
          }
          &--left{
            display: flex;
            align-items: center;
            image{
              width: 72rpx;
              height: 72rpx;
              border-radius: 50%;
            }
            text{
              font-size: 28rpx;
              font-weight: 500;
              color: #666666;
              margin-left: 21rpx;
            }
          }
          &--right{
            font-size: 26rpx;
            font-weight: 500;
            color: #666666;
          }
        }
      }
    }
  }
  .site-qrcode{
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 40rpx 0 24rpx 0;
    box-sizing: border-box;
    margin-top: 20rpx;
    background-color: white;
    view{
      font-size: 32rpx;
      font-weight: 500;
      color: #343434;
    }
    image{
      width: 702rpx;
      height: 702rpx;
      margin-top: 40rpx;
    }
  }
</style>
