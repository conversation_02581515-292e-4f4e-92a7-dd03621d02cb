import htmlParser from '@/common/js/html-parser.js';
import apiurls from "@/common/js/apiurls.js";
import system from "@/common/js/system.js";

import { Weixin } from '@/common/js/wx-jssdk.js';

export default {
	data() {
		return {
			skuId: 0,
			// 商品详情
			goodsSkuDetail: {
				goods_id: 0
			},
			// 店铺详情
			shopInfo: {
				logo: '',
				shop_baozh: 0,
				shop_qtian: 0,
				shop_zhping: 0,
				shop_erxiaoshi: 0,
				shop_tuihuo: 0,
				shop_shiyong: 0,
				shop_shiti: 0,
				shop_xiaoxie: 0
			},
			cartCount: 0, // 购物车商品数量
			whetherCollection: 0,
			// 媒体,图片,视频
			// 解决每个商品SKU图片数量不同时，无法切换到第一个，导致轮播图显示不出来
			swiperInterval: 1,
			swiperAutoplay: false,
			swiperCurrent: 1,
			switchMedia: 'img',
			couponList: [], //优惠券列表
			couponBtnSwitch: false, //获取优惠券防止重复提交
			//评价数量
			token: "",

			//评价
			goodsEvaluate: {
				member_headimg: '',
				member_name: '',
				content: '',
				images: [],
				create_time: 0,
				sku_name: ''
			},
			//组合套餐
			bundling: [{
				bundling_goods: {
					bl_name: '',
					sku_image: ''
				}
			}],
			memberId: 0,
			contactData: {
				title: '',
				path: '',
				img: ''
			},
			detailTab: 'productDetail',
			afterSale:null,
			service: null,
			//是否开启预览，0：不开启，1：开启
			preview: 0,
			isDistributor:false,
			shop_name_array:{
				share_shop_name: '',
				shop_name:''
			},

			path:'/promotionpages/fenxiangzhuan/goodsDetail/goodsDetail',
		}
	},
	onLoad(data) {
		this.share_shop_id = data.shop_id || 0;
		this.preview = data.preview || 0;
		this.activity_id = data.activity_id || 0;

	},
	async onShow() {

		// 刷新多语言
		this.$langConfig.refresh();
		this.shop_id = uni.getStorageSync('shop_id');
		//同步获取商品详情
		await this.getGoodsSkuDetail();

		//登录后查询
		this.token = uni.getStorageSync('token');

		if (this.token != '' && this.preview == 0) {
			this.getCartCount();
			this.getWhetherCollection();
		}

		// 开启预览，禁止任何操作和跳转
		if (this.preview == 0) {
			//修改商品信息
			this.modifyGoodsInfo();
		}


	},
	/**
	 * 自定义分享内容
	 * @param {Object} res
	 */
	onShareAppMessage(res) {
		system.goodsShare(this.goodsSkuDetail.goods_id)

		let path = `${this.path}?activity_id=${this.activity_id}&shop_id=${this.share_shop_id}`;
		let recommend_member_id=uni.getStorageSync('member_id');
		if(recommend_member_id){
			path+=`&recommend_member_id=${recommend_member_id}`;
		}
		console.log(path)
		return {
			title: this.goodsSkuDetail.goods_name,
			imageUrl: this.$util.img(this.goodsSkuDetail.sku_image),
			path: path,
			success: res => {},
			fail: res => {}
		};
	},

	methods: {
		//h5播放视频
		openVideo(){
			this.$refs.videoPopup.open();
		},

		checkToken(){
			system.checkToken().then(res=>{
				this.shop_id = uni.getStorageSync('shop_id')
			})
		},

		// 获取商品详情
		async getGoodsSkuDetail(skuId) {
			await this.checkToken()

			let datas = {
				activity_id:this.activity_id,
				shop_id:this.shop_id
			}

			let res = await this.$api.sendRequest({
				url: '/api/ShareBuyActivity/activityInfo',
				async: false,
				data: datas
			});
			let data = res.data;
			if (data.activityInfo != null) {
				this.goodsSkuDetail = data.activityInfo;
				this.goodsSkuDetail.preview = this.preview;
				this.shopInfo = data.shopInfo;

				if (this.skuId == 0) this.skuId = this.goodsSkuDetail.sku_id;

				//媒体
				if (this.goodsSkuDetail.video_url) this.switchMedia = "video";

				this.goodsSkuDetail.sku_images = this.goodsSkuDetail.sku_images?this.goodsSkuDetail.sku_images.split(","):'';

				this.goodsSkuDetail.unit = this.goodsSkuDetail.unit || "件";

				this.goodsSkuDetail.show_price = this.goodsSkuDetail.retail_price;

				// 当前商品SKU规格
				if (this.goodsSkuDetail.sku_spec_format) this.goodsSkuDetail.sku_spec_format = JSON.parse(this.goodsSkuDetail.sku_spec_format);

				// 商品属性
				if (this.goodsSkuDetail.goods_attr_format) {
					let goods_attr_format = JSON.parse(this.goodsSkuDetail.goods_attr_format);
					this.goodsSkuDetail.goods_attr_format = JSON.parse(this.goodsSkuDetail.goods_attr_format);
					this.goodsSkuDetail.goods_attr_format = this.$util.unique(this.goodsSkuDetail.goods_attr_format, "attr_id");
					for (var i = 0; i < this.goodsSkuDetail.goods_attr_format.length; i++) {
						for (var j = 0; j < goods_attr_format.length; j++) {
							if (this.goodsSkuDetail.goods_attr_format[i].attr_id == goods_attr_format[j].attr_id && this.goodsSkuDetail.goods_attr_format[
									i].attr_value_id != goods_attr_format[j].attr_value_id) {
								this.goodsSkuDetail.goods_attr_format[i].attr_value_name += "、" + goods_attr_format[j].attr_value_name;
							}
						}
					}
				}

				// 商品SKU格式
				if (this.goodsSkuDetail.goods_spec_format) this.goodsSkuDetail.goods_spec_format = JSON.parse(this.goodsSkuDetail.goods_spec_format);

				// 商品详情
				if (this.goodsSkuDetail.goods_content) this.goodsSkuDetail.goods_content = htmlParser(this.goodsSkuDetail.goods_content);

				this.getService();
				if (this.$refs.loadingCover) this.$refs.loadingCover.hide();

			} else {
				this.$util.showToast({
					title:res.data || res.message
				})
				setTimeout(()=>{
					this.$util.redirectTo('/pages/index/index/index', {}, 'reLaunch');
				},1000)
			}
		},
		/**
		 * 刷新商品详情数据
		 * @param {Object} goodsSkuDetail
		 */
		refreshGoodsSkuDetail(goodsSkuDetail) {
			Object.assign(this.goodsSkuDetail, goodsSkuDetail);

			// 解决轮播图数量不一致时，切换到第一个
			if (this.swiperCurrent > this.goodsSkuDetail.sku_images.length) {
				this.swiperAutoplay = true;
				this.swiperCurrent = 1;
				setTimeout(() => {
					this.swiperAutoplay = false;
				}, 40);
			}
		},
		goHome() {
			if (this.preview) return; // 开启预览，禁止任何操作和跳转
			this.$util.redirectTo('/otherpages/shop/home/<USER>', {}, 'reLaunch');
		},

		checkShoppingStatus(){
			let is_shopping_status = uni.getStorageSync('is_shopping_status')
			if(is_shopping_status == 0){
				this.$refs.popupBan.open()
				return true
			}
		},
		goCart() {
			if (this.preview) return; // 开启预览，禁止任何操作和跳转
			this.$util.redirectTo('/pages/goods/cart/cart', {}, 'reLaunch');
		},
		redirectDetailsUrl(){
			let shop_id=this.share_shop_id || uni.getStorageSync('shop_id');
			let path = `${this.path}?activity_id=${this.activity_id}&shop_id=${shop_id}`;
			return path
		},

		// 立即购买
		async buyNow() {
			if (!this.token && this.preview == 0) {
				let path = await this.redirectDetailsUrl()
				this.$util.toShowLoginPopup(this,null,path);
				return;
			}
			//检查会员是否禁止购物
			if(await this.checkShoppingStatus()) return false
			//已选择规格直接执行操作
			if(this.goodsSkuDetail.sku_spec_format){
				this.$refs.goodsSku.type = 'buy_now';
				this.$refs.goodsSku.callback = ()=>{
					this.navToApply()
				}
				this.$refs.goodsSku.confirm("buy_now")
				return
			}
			this.$refs.goodsSku.show("buy_now", () => {
				this.navToApply()
			});
		},
		navToApply(){
			let path = `/promotionpages/fenxiangzhuan/apply/apply?activity_id=${this.activity_id}&skuId=${this.goodsSkuDetail.sku_id}`
			this.$util.redirectTo(path, {}, '');
		},
		swiperChange(e) {
			this.swiperCurrent = e.detail.current + 1;
		},
		chooseSkuspecFormat(){
			let shop_id = this.share_shop_id || uni.getStorageSync('shop_id');
			if (!this.token && this.preview == 0) {
				this.$util.toShowLoginPopup(this,null,this.path+'&activity_id='+this.activity_id+'&shop_id='+shop_id);
				return;
			}
			this.$refs.goodsSku.show("choose_spec", () => {
			});
		},

		//-------------------------------------服务-------------------------------------

		openMerchantsServicePopup() {
			this.$refs.merchantsServicePopup.open();
		},
		closeMerchantsServicePopup() {
			this.$refs.merchantsServicePopup.close();
		},



		//-------------------------------------属性-------------------------------------

		openAttributePopup() {
			this.$refs.attributePopup.open();
		},
		closeAttributePopup() {
			this.$refs.attributePopup.close();
		},

		//-------------------------------------属性-------------------------------------



		//-------------------------------------关注-------------------------------------

		//获取用户是否关注
		getWhetherCollection() {
			this.$api.sendRequest({
				url: "/api/goodscollect/iscollect",
				data: {
					goods_id: this.goodsSkuDetail.goods_id
				},
				success: res => {
					this.whetherCollection = res.data;
				}
			});
		},

		editCollection() {
			if (this.preview) return; // 开启预览，禁止任何操作和跳转

			if (this.token != "") {

				//未关注添加关注
				if (this.whetherCollection == 0) {
					this.$api.sendRequest({
						url: "/api/goodscollect/add",
						data: {
							sku_id: this.skuId,
							site_id: this.goodsSkuDetail.site_id,
							goods_id: this.goodsSkuDetail.goods_id,
							category_id: this.goodsSkuDetail.category_id,
							sku_name: this.goodsSkuDetail.sku_name,
							sku_price: this.goodsSkuDetail.discount_price,
							sku_image: this.goodsSkuDetail.sku_image
						},
						success: res => {
							var data = res.data;
							if (data > 0) {
								this.whetherCollection = 1;
								this.$util.showToast({
									title: '收藏成功',
								});
							}
						}
					});
				} else {
					//已关注取消关注
					this.$api.sendRequest({
						url: "/api/goodscollect/delete",
						data: {
							goods_id: this.goodsSkuDetail.goods_id
						},
						success: res => {
							var data = res.data;
							if (data > 0) {
								this.whetherCollection = 0;
								this.$util.showToast({
									title: '已取消收藏',
								});
							}
						}
					});
				}
			} else {
				let shop_id = this.share_shop_id || uni.getStorageSync('shop_id')
				this.$util.toShowLoginPopup(this,null,this.path+'?shop_id=' + shop_id+'&activity_id='+this.activity_id);
			}
		},
		//获取购物车数量
		getCartCount() {
			if (this.preview) return; // 开启预览，禁止任何操作和跳转
			this.$store.dispatch('getCartNumber').then((e)=>{
				this.cartCount = e;
			})
		},
		//更新商品信息
		modifyGoodsInfo() {
			if (this.preview) return; // 开启预览，禁止任何操作和跳转
			//更新商品点击量
			this.$api.sendRequest({
				url: "/api/goods/modifyclicks",
				data: {
					sku_id: this.skuId,
					site_id: this.goodsSkuDetail.site_id,
				},
				success: res => {}
			});

			let data = {
				goods_id: this.goodsSkuDetail.goods_id,
				sku_id: this.skuId,
				category_id: this.goodsSkuDetail.category_id,
				category_id_1: this.goodsSkuDetail.category_id_1,
				category_id_2: this.goodsSkuDetail.category_id_2,
				category_id_3: this.goodsSkuDetail.category_id_3,
				site_id: this.goodsSkuDetail.site_id,
			}
			if(this.$store.state.buried_shop_id){
				data.share_shop_id = this.$store.state.buried_shop_id
			}

			//添加足迹
			this.$api.sendRequest({
				url: "/api/goodsbrowse/add",
				data: data,
				success: res => {}
			});
		},


		//-------------------------------------分享-------------------------------------
		// 打开分享弹出层
		openSharePopup() {
			this.$refs.sharePopup.open();
		},
		// 关闭分享弹出层
		closeSharePopup() {
			this.$refs.sharePopup.close();
		},
		onShareClick(){
			// var shareButton = document.getElementById('ShareBox')

			console.log(this.$refs.share_box)
			this.$refs.share_box.click();
			// console.log(shareButton)
		},
		// 预览图片
		previewMedia(index) {
			var paths = [];
			for (let i = 0; i < this.goodsSkuDetail.sku_images.length; i++) {
				paths.push(this.$util.img(this.goodsSkuDetail.sku_images[i]));
			}
			uni.previewImage({
				current: index,
				urls: paths,
			});
		},
		imageError() {
			this.goodsSkuDetail.sku_image = this.$util.getDefaultImage().default_goods_img;
			this.$forceUpdate();
		},
		swiperImageError(index) {
			this.goodsSkuDetail.sku_images[index] = this.$util.getDefaultImage().default_goods_img;
			this.$forceUpdate();
		},
		bundlingImageError(index, goods_index) {
			this.bundling[index].bundling_goods[goods_index].sku_image = this.$util.getDefaultImage().default_goods_img;
			this.$forceUpdate();
		},

		getService() {
			this.$api.sendRequest({
				url: apiurls.goodsServiceDesc,
				data:{
					goods_id:this.goodsSkuDetail.goods_id
				},
				success: res => {
					if (res.code == 0 && res.data) {
						let data = res.data.content;
						if (res.data.directions) this.service = htmlParser(res.data.directions);
						if (res.data.protocol) this.afterSale = htmlParser(res.data.protocol);
					}
				}
			});
		},
		errorShopLogo() {
			this.shopInfo.avatar = this.$util.getDefaultImage().default_shop_img;
			this.$forceUpdate();
		},
		fenxiao() {
			this.$refs.fenxiaoPopup.show()
		},
		toPoint(event){
			let id=event.target.dataset.id;
			let that=this;
			uni.createSelectorQuery().select('.goods-detail').boundingClientRect(data=>{//目标位置节点 类或者 id
				uni.createSelectorQuery().select("#"+id).boundingClientRect((res)=>{//最外层盒子节点类或者 id
					that.detailTab=id;
					uni.pageScrollTo({
						duration:500,//过渡时间
						scrollTop: Math.abs(data.top - res.top)   ,//到达距离顶部的top值
					})
				}).exec()
			}).exec();
		}
	}
}
