import apiurls from "@/common/js/apiurls.js";
import validate from 'common/js/validate.js';

export default {
	data(){
		return{
			skuId:0,
			goodsSkuDetail:{},
			formData: {
				activity_id:0,
				sku_id:0,
				name: '',
				mobile: '',
				province_id: 0,
				city_id: 0,
				district_id:0,
				community_id:0,
				address: '',
				full_address: '',
			},
			addressValue: '',
			flag: false, //防重复标识
			defaultRegions: [],
		}
	},
	onLoad(options){
		this.formData.activity_id = options.activity_id || 0;
		this.skuId = options.skuId || 0
	},
	async onShow(){
		this.$langConfig.refresh();
		//同步获取商品详情
		await this.getGoodsSkuDetail();

		this.token = uni.getStorageSync('token');
		if(!this.token){
			if (this.$refs.loadingCover) this.$refs.loadingCover.hide();
			let path = `/promotionpages/fenxiangzhuan/apply/apply?activity_id=${this.formData.activity_id}&skuId=${this.skuId}`
			this.$util.toShowLoginPopup(this,null,path);
		}
	},
	methods:{
		// 获取商品详情
		async getGoodsSkuDetail(skuId) {
			this.skuId = skuId || this.skuId;
			let datas = {
				sku_id: this.skuId,
				activity_id:this.formData.activity_id
			}

			this.$api.sendRequest({
				url: this.$apiUrl.shareBuyApplyActivityDetail,
				data: datas,
				success:(res)=>{
					if (this.$refs.loadingCover) this.$refs.loadingCover.hide();
					let data = res.data;
					if (data.activityInfo != null) {
						this.goodsSkuDetail = data.activityInfo;
						this.shopInfo = data.shopInfo;

						if (this.skuId == 0) this.skuId = this.goodsSkuDetail.sku_id;

						// 当前商品SKU规格
						if (this.goodsSkuDetail.sku_spec_format) this.goodsSkuDetail.sku_spec_format = JSON.parse(this.goodsSkuDetail.sku_spec_format);

						// 商品属性
						if (this.goodsSkuDetail.goods_attr_format) {
							let goods_attr_format = JSON.parse(this.goodsSkuDetail.goods_attr_format);
							this.goodsSkuDetail.goods_attr_format = JSON.parse(this.goodsSkuDetail.goods_attr_format);
							this.goodsSkuDetail.goods_attr_format = this.$util.unique(this.goodsSkuDetail.goods_attr_format, "attr_id");
							for (var i = 0; i < this.goodsSkuDetail.goods_attr_format.length; i++) {
								for (var j = 0; j < goods_attr_format.length; j++) {
									if (this.goodsSkuDetail.goods_attr_format[i].attr_id == goods_attr_format[j].attr_id && this.goodsSkuDetail.goods_attr_format[
											i].attr_value_id != goods_attr_format[j].attr_value_id) {
										this.goodsSkuDetail.goods_attr_format[i].attr_value_name += "、" + goods_attr_format[j].attr_value_name;
									}
								}
							}
						}
						// 商品SKU格式
						if (this.goodsSkuDetail.goods_spec_format) this.goodsSkuDetail.goods_spec_format = JSON.parse(this.goodsSkuDetail.goods_spec_format);
						if (this.$refs.loadingCover) this.$refs.loadingCover.hide();
						console.log(this.goodsSkuDetail)
					} else {
						this.$util.showToast({
							title:err.message
						})
					}
				},
				fail:(err)=>{
					if (this.$refs.loadingCover) this.$refs.loadingCover.hide();
					this.$util.showToast({
						title:err.message
					})
				}
			});
		},

		change(skuId, spec_id) {
			if (this.disabled) return;
			this.skuId = skuId;
			// 清空选择
			for (var i = 0; i < this.goodsSkuDetail.goods_spec_format.length; i++) {
				var sku = this.goodsSkuDetail.goods_spec_format[i];
				for (var j = 0; j < sku.value.length; j++) {
					// 排除当前点击的规格值
					if (spec_id == this.goodsSkuDetail.goods_spec_format[i].value[j].spec_id) {
						this.goodsSkuDetail.goods_spec_format[i].value[j].selected = false;
					}
				}
			}
			this.getGoodsSkuInfo();
		},
		// 获取普通商品详情
		getGoodsSkuInfo() {
			let params = {
				sku_id: this.skuId
			}
			let res = this.$api.sendRequest({
				url: '/api/goodssku/info',
				data: params,
				success: res => {
					let data = res.data;
					if (data != null) {
						Object.assign(this.goodsSkuDetail, data);
						this.dealData()
					} else {
						this.$util.redirectTo('/pages/index/index/index', {}, 'reLaunch');
					}
				},
				fail: res => {
					this.$util.redirectTo('/pages/index/index/index', {}, 'reLaunch');
				}
			});
		},
		dealData() {
			this.goodsSkuDetail.sku_images = this.goodsSkuDetail.sku_images.split(',');

			this.goodsSkuDetail.show_price = this.goodsSkuDetail.discount_price;

			// 当前商品SKU规格
			if (this.goodsSkuDetail.sku_spec_format) this.goodsSkuDetail.sku_spec_format = JSON.parse(this.goodsSkuDetail.sku_spec_format);

			// 商品SKU格式
			if (this.goodsSkuDetail.goods_spec_format) this.goodsSkuDetail.goods_spec_format = JSON.parse(this.goodsSkuDetail.goods_spec_format);
			this.$forceUpdate();
		},
		submit(){
			if(this.vertify()){
				uni.showLoading({
					title:'提交中'
				})
				let data = {
					province_id: this.addressValue[0],
					city_id: this.addressValue[1],
					district_id: this.addressValue[2],
					sku_id:this.goodsSkuDetail.sku_id
				};

				let params = this.formData
				Object.assign(params,data)
				this.$api.sendRequest({
					url:this.$apiUrl.shareBuyApplyUrl,
					data:params,
					success:(res)=>{
						if(res.code == 0){
							uni.hideLoading()
							this.$util.showToast({
								title:'提交成功'
							})

							let path = `/promotionpages/fenxiangzhuan/activityDetails/activityDetails`
							this.$util.redirectTo(path, {share_activity_id:this.formData.activity_id,share_apply_id:res.data.id}, 'redirectTo');

						}else{
							this.$util.showToast({
								title:res.message
							})
						}
					},
					fail:(err)=>{
						this.$util.showToast({
							title:err.message
						})
					}
				})
			}
		},
		// 获取选择的地区
		handleGetRegions(regions) {
			this.formData.full_address = '';
			this.formData.full_address += regions[0] != undefined ? regions[0].label : '';
			this.formData.full_address += regions[1] != undefined ? ' ' + regions[1].label : '';
			this.formData.full_address += regions[2] != undefined ? ' ' + regions[2].label : '';
			this.addressValue = [];
			this.addressValue[0] = regions[0] != undefined ? regions[0].value : '';
			this.addressValue[1] = regions[1] != undefined ? regions[1].value : '';
			this.addressValue[2] = regions[2] != undefined ? regions[2].value : '';
			console.log(this.addressValue)
		},

		vertify() {
			this.formData.name = this.formData.name.trim();
			this.formData.mobile = this.formData.mobile.trim();
			this.formData.address = this.formData.address.trim();
			var rule = [{
					name: 'name',
					checkType: 'required',
					errorMsg: '请输入姓名',

				},
				{
					name: 'mobile',
					checkType: 'required',
					errorMsg: '请输入手机号'
				},
				{
					name: 'mobile',
					checkType: 'phoneno',
					errorMsg: '请输入正确的手机号'
				},
				{
					name: 'full_address',
					checkType: 'required',
					errorMsg: '请选择省市区县'
				},
				{
					name: 'address',
					checkType: 'required',
					errorMsg: '详细地址不能为空'
				}
			];
			var checkRes = validate.check(this.formData, rule);
			var consigneeReg = /[^\u4e00-\u9fa5-^a-zA-Z]+$/g;
			if (checkRes) {
				var consigneeTest = consigneeReg.test(this.formData.name)
				  if(consigneeTest == true) {
					this.$util.showToast({
						title: '请输入姓名'
					});
					this.flag = false;
					return
				  }
				  return true;
			} else {
				this.$util.showToast({
					title: validate.error
				});
				this.flag = false;
				return false;
			}
		}
	}
}
