<template>
	<view :class="themeStyle">
		<view class="goods-detail">
			<view class="goods-container">
				<!-- 商品媒体信息 -->
				<view class="goods-media">
					<!-- 商品图片 -->
					<view class="goods-img" :class="{ show: switchMedia == 'img' }">
						<swiper class="swiper" @change="swiperChange" :interval="swiperInterval" :autoplay="swiperAutoplay">
							<swiper-item v-for="(item, index) in goodsSkuDetail.sku_images" :key="index">
								<view class="item" @click="previewMedia(index)">
									<image :src="$util.img(item)" @error="swiperImageError(index)" mode="aspectFit" />
								</view>
							</swiper-item>
						</swiper>
						<view class="img-indicator-dots">
							<text>{{ swiperCurrent }}</text>
							<text v-if="goodsSkuDetail.sku_images">/{{ goodsSkuDetail.sku_images.length }}</text>
						</view>
					</view>
					<!-- 商品视频 -->
					<view class="goods-video" :class="{ show: switchMedia == 'video' }">
						<!-- #ifndef H5 -->
						<video :src="$util.img(goodsSkuDetail.video_url)" :poster="$util.img(goodsSkuDetail.sku_image)"
						 objectFit="cover"></video>
						<!-- #endif -->
						<!-- #ifdef H5 -->
						<view class="video-img">
							<image :src="$util.img(goodsSkuDetail.sku_image)" mode=""></image>
							<view class="video-open">
								<view class="iconfont iconarrow-" @click="openVideo"></view>
							</view>
						</view>
						<!-- #endif -->
					</view>

					<!-- 切换视频、图片 -->
					<view class="media-mode" v-if="goodsSkuDetail.video_url != ''">
						<text :class="{ 'ns-bg-color': switchMedia == 'video' }" @click="switchMedia = 'video'">{{ $lang('video') }}</text>
						<text :class="{ 'ns-bg-color': switchMedia == 'img' }" @click="switchMedia = 'img'">{{ $lang('image') }}</text>
					</view>
					<image v-if="goodsSkuDetail.stock==0" :src="$util.img('public/static/youpin/product-sell-out.png')" class="over"></image>
				</view>
				<view @touchmove.prevent.stop class="videoPopup-box">
					<uni-popup ref="videoPopup" type="center">
						<view class="pop-video">
							<video :src="$util.img(goodsSkuDetail.video_url)" :poster="$util.img(goodsSkuDetail.sku_image)"
							 objectFit="cover"></video>
						</view>
					</uni-popup>
				</view>

				<view class="group-wrap">

					<view class="goods-module-wrap">
						<view>
							<text class="sku-name">{{ goodsSkuDetail.goods_name }}</text>
							<text class="introduction ns-text-color" v-if="goodsSkuDetail.introduction">{{ goodsSkuDetail.introduction }}</text>
						</view>
						<view class="adds-wrap">
							<block v-if="Development">
								<text v-if="goodsSkuDetail.is_free_shipping">快递免邮</text>
								<text v-else>快递不免邮</text>
							</block>
							<text class="adds-wrap-volume">已售 {{ goodsSkuDetail.sale_num }} {{ goodsSkuDetail.unit }}</text>
						</view>
					</view>
					<button class="group-wrap-share" :plain="true" open-type="share">
						<view class="iconfont iconfenxiang"></view>
						<text>分享</text>
					</button>
				</view>

				<ns-fenxiao-good-detail :skuId="skuId" ref="fenxiaoPopup"></ns-fenxiao-good-detail>

				<view class="group-wrap">
					<!-- 已选规格 -->
					<view class="goods-cell selected-sku-spec" v-if="goodsSkuDetail.sku_spec_format" @click="chooseSkuspecFormat">
						<view class="box">
							<text class="tit">已选择</text>
							<text v-for="(item, index) in goodsSkuDetail.sku_spec_format" :key="index">{{ item.spec_name }}/{{ item.spec_value_name }}</text>
						</view>
						<text class="iconfont iconright"></text>
					</view>

					<!-- 商品属性 -->
					<view class="goods-cell" @click="openAttributePopup()" v-if="goodsSkuDetail.goods_attr_format && goodsSkuDetail.goods_attr_format.length > 0">
						<view class="box">
							<text class="tit">规格参数</text>
							<!-- 							<text>{{ goodsSkuDetail.goods_attr_format[0].attr_name }} {{ goodsSkuDetail.goods_attr_format[0].attr_value_name }}...</text> -->
						</view>
						<text class="iconfont iconright"></text>
					</view>

					<view @touchmove.prevent.stop>
						<uni-popup ref="attributePopup" type="bottom">
							<view class="goods-attribute-popup-layer">
								<text class="title">规格参数</text>
								<scroll-view scroll-y class="goods-attribute-body">
									<view class="item ns-border-color-gray" v-for="(item, index) in goodsSkuDetail.goods_attr_format" :key="index">
										<text class="ns-text-color-gray">{{ item.attr_name }}</text>
										<text class="value">{{ item.attr_value_name }}</text>
									</view>
								</scroll-view>
								<view class="button-box"><button type="primary" @click="closeAttributePopup()">确定</button></view>
							</view>
						</uni-popup>
					</view>
				</view>

				<!-- 详情 -->
				<view class="goods-detail-tab">
					<view class="detail-tab flex-center" v-if="isShowDetailTab">
						<view class="tab-item" :class="detailTab == 'productDetail' ? 'active ns-bg-before' : ''" @click="toPoint"
						 data-id="productDetail">商品详情</view>
						<view class="tab-item" :class="detailTab == 'productSale' ? 'active ns-bg-before' : ''" @click="toPoint" data-id="productSale">售后保障</view>
						<view class="tab-item" :class="detailTab == 'productServe' ? 'active ns-bg-before' : ''" @click="toPoint" data-id="productServe">服务说明</view>
					</view>
					<view class="detail-content">
						<view class="detail-content-item" id="productDetail">
							<view class="goods-details-title">
								<view></view>
								<view>图文详情</view>
								<view></view>
							</view>
							<view class="goods-details" v-if="goodsSkuDetail.goods_content">
								<rich-text :nodes="goodsSkuDetail.goods_content"></rich-text>
							</view>
							<view class="goods-details active" v-else>该商家暂无上传相关详情哦！</view>
						</view>
						<view class="detail-content-item" id="productSale">
							<view class="goods-details-title">
								<view></view>
								<view>售后保障</view>
								<view></view>
							</view>
							<view class="goods-details" v-if="afterSale">
								<rich-text :nodes="afterSale"></rich-text>
							</view>
							<view class="goods-details active" v-else>该商品暂无相关售后哦！</view>
						</view>
						<view class="detail-content-item" id="productServe">
							<view class="goods-details-title">
								<view></view>
								<view>服务说明</view>
								<view></view>
							</view>
							<view class="goods-details" v-if="service">
								<rich-text :nodes="service"></rich-text>
							</view>
							<view class="goods-details active" v-else>该商品暂无相关服务说明哦！</view>
						</view>
					</view>
				</view>

				<!-- SKU选择 -->
				<ns-fenxiangzuan-goods-sku ref="goodsSku" @refresh="refreshGoodsSkuDetail" :goods-detail="goodsSkuDetail"></ns-fenxiangzuan-goods-sku>

			</view>
		</view>
		<loading-cover ref="loadingCover"></loading-cover>
		<!-- 商品底部导航 -->
		<ns-goods-action>
			<ns-goods-action-icon text="首页" icon="iconshouye" :imgicon="$util.img('public/static/youpin/home-icon.png')" @click="goHome" />
			<!-- <ns-goods-action-icon :text="whetherCollection == 1 ? '已收藏' :'收藏'" :imgicon="whetherCollection == 1 ? $util.img('public/static/youpin/collect-has-icon.png') : $util.img('public/static/youpin/collect-icon.png')"
			 @click="editCollection()" /> -->
			 <ns-goods-action-icon text="客服" icon="iconkefu" v-if="addonIsExit.servicer" @click="$util.getCustomerService()" />
			<ns-goods-action-icon text="购物车" icon="icongouwuche" :imgicon="$util.img('public/static/youpin/shop-icon.png')"
			 :corner-mark="cartCount > 0 ? cartCount + '' : ''" @click="goCart" />

			<ns-goods-action-button class="goods-action-button active4"
			 text="立即购买"  disabledText="立即购买" :disabled="goodsSkuDetail.goods_stock ? false : true" background="#FF1010" @click="buyNow" />

		</ns-goods-action>
		<!-- 授权登录弹窗 -->
		<yd-auth-popup ref="ydauth"></yd-auth-popup>
    <ns-login ref="login"></ns-login>
    <uni-coupon-pop ref="couponPop"></uni-coupon-pop>
		<!-- 返回顶部按钮 -->
		<image :src="$util.img('public/static/youpin/to-top.png')" class="to-top" v-show="showTop" @click="scrollToTopNative"></image>

	</view>
</template>

<script>
	import nsGoodsAction from '@/components/ns-goods-action/ns-goods-action.vue';
	import nsGoodsActionIcon from '@/components/ns-goods-action-icon/ns-goods-action-icon.vue';
	import nsGoodsActionButton from '@/components/ns-goods-action-button/ns-goods-action-button.vue';
	import uniPopup from '@/components/uni-popup/uni-popup.vue';
	import nsFenxiangzuanGoodsSku from '@/components/ns-goods-sku/ng-fenxiangzuan-goods-sku.vue';
	// import nsGoodsRecommend from '@/components/ns-goods-recommend/ns-goods-recommend.vue';
	import uniCountDown from '@/components/uni-count-down/uni-count-down.vue';
	import detail from '../public/js/goodsDetail.js';
	import scroll from '@/common/mixins/scroll-view.js';
	// import nsFenxiaoGoodDetail from '@/components/ns-fenxiao-goods-detail/ns-fenxiao-goods-detail.vue';
	import globalConfig from 'common/mixins/golbalConfig.js'

	export default {
		components: {
			nsGoodsAction,
			nsGoodsActionIcon,
			nsGoodsActionButton,
			uniPopup,
			nsFenxiangzuanGoodsSku,
			// nsGoodsRecommend,
			uniCountDown,
			// nsFenxiaoGoodDetail
		},
		data() {
			return {
				isShowEvaluate: false,
				isShowDetailTab: false,
			};
		},
		onShow() {
			if (uni.getStorageSync('is_register')) {
				this.$util.toShowCouponPopup(this)
				uni.removeStorageSync('is_register');
			}
		},
		computed: {
			themeStyle() {
				return 'theme-' + this.$store.state.themeStyle;
			},
			addonIsExit() {
				return this.$store.state.addonIsExit;
			},
		},
		mixins: [detail, scroll, globalConfig],
	};
</script>

<style lang="scss">
	@import '../public/css/goodsDetail.scss';

	.ns-text-color {
		color: #FF1010 !important;
	}
</style>
<style scoped>
	/deep/ .uni-video-cover {
		background: none;
	}

	/deep/ .uni-video-cover-duration {
		display: none;
	}

	/deep/ .uni-video-cover-play-button {
		border-radius: 50%;
		border: 4rpx solid #fff;
		width: 120rpx;
		height: 120rpx;
		background-size: 30%;
	}

	.poster-layer>>>.uni-popup__wrapper-box {
		max-height: initial !important;
	}

	/deep/ .sku-layer .uni-popup__wrapper-box {
		overflow-y: initial !important;
	}

	.goods-discount .countdown .clockrun>>>.uni-countdown__number {
		min-width: 32rpx;
		height: 32rpx;
		text-align: center;
		line-height: 32rpx;
		background: #000;
		/* // #690b08 */
		border-radius: 4px;
		display: inline-block;
		padding: 4rpx;
		margin: 0;
		border: none;
	}

	.goods-discount .countdown .clockrun>>>.uni-countdown__splitor {
		width: 10rpx;
		height: 32rpx;
		line-height: 36rpx;
		text-align: center;
		display: inline-block;
		color: #000;
	}

	.goods-discount .countdown .clockrun>>>.uni-countdown__splitor.day {
		width: initial;
	}

	/deep/ .uni-popup__wrapper.uni-custom .uni-popup__wrapper-box {
		max-height: unset !important;
	}

	/deep/ .goods-action-button.active1 {
		padding-left: 10px;
	}

	/deep/ .goods-action-button.active2 {
		padding-right: 10px;
	}

	/deep/ .goods-action-button.active3 {
		padding: 0 10px;
	}

	/deep/ .goods-action-button.active4 {
		padding: 0 10px;
	}

	/deep/ .goods-action-button.active1 .action-buttom-wrap {
		color: #F2270C;
		border: 1px solid #F2270C;
		border-radius: 40rpx;
		box-sizing: border-box;
		margin-right: 14rpx;
	}

	/deep/ .goods-action-button.active2 .action-buttom-wrap {
		border-radius: 40rpx;
		box-sizing: border-box;
	}

	/deep/ .goods-action-button.active3 .action-buttom-wrap {
		border-radius: 36px;
		margin: 20rpx 0;
	}

	/deep/ .goods-action-button.active4 .action-buttom-wrap {
		border-radius: 36px;
	}

	/* 底部分享按钮 */
	.distributor-share-button {
		width: auto !important;
		height: auto !important;
		border: none;
		margin: 0;
		line-height: auto;
		;
		padding: 0;
	}

	.disabled-share-btn {
		background-color: transparent !important;
	}

	.to-top {
		width: 144rpx;
		height: 152rpx;
		position: fixed;
		right: 0;
		bottom: 200rpx;
	}
</style>
