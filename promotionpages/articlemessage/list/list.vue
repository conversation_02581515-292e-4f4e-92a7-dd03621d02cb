<template>
  <view>
    <view class="list">
      <template v-if="list.length>0">
        <view class="list-date" v-for="(date,index) in list" :key="index" @click="toSee(date)">
<!--          <view class="list-date-title">{{date.create_time}}</view>-->
          <view class="list-date-item">
            <image :src="date.image" class="list-date-item-left" @error="dateImageError(list,index)" mode="aspectFill"></image>
            <view class="list-date-item-right">
              <view class="list-date-item-right-title">{{date.title}}</view>
              <template v-if="date.goods && date.goods.length>0">
                <view class="list-date-item-right-recommend">达人推荐：</view>
                <view class="list-date-item-right-goods">
                  <image :src="item.goods_image" mode='aspectFit' class="list-date-item-right-goods-one expose_goods_index" :data-expose_goods_sku="item.sku_id" v-for="(item,j) in date.goods" :key="j"
                         @error="goodsImageError(list,index,j)" @click.stop.prevent="toProductDetail(item)"></image>
                </view>
              </template>
              <view class="list-date-item-right-to">
                <button type="default" class="list-date-item-right-to-but">去看看 <image :src="$util.img('public/static/youpin/to_more.png')" class="list-date-item-right-to-but-more"></image></button>
              </view>
            </view>
          </view>
        </view>
      </template>
      <template v-else>
        <view class="empty-list-text" v-if="!refresh">没有更多内容</view>
      </template>

    </view>
    <!-- 返回顶部 -->
    <image :src="$util.img('public/static/youpin/to-top.png')" class="to-top" v-show="showTop" @click="scrollToTopNative"></image>
    <loading-cover ref="loadingCover"></loading-cover>
  </view>
</template>

<script>
import scroll from '@/common/mixins/scroll-view.js'
import wx_expose_goods from '@/common/mixins/wx_expose_goods.js'
import system from "../../../common/js/system";
import apiurls from "../../../common/js/apiurls";
export default {
  data(){
    return{
      list:[],
      page:1,  //页码
      page_size:10, //每页条数
      page_count:1,  //总页数
      refresh:false,
    }
  },
  mixins: [scroll,wx_expose_goods],
  onLoad(){
  },
  async onShow(){
    // 刷新多语言
    this.$langConfig.refresh();
    await system.wait_staticLogin_success();

    // #ifdef H5
    let share_data = this.$util.deepClone(this.getSharePageParams())
    let link = window.location.origin+this.$router.options.base+share_data.link.slice(1)
    share_data.link=link;
    share_data.desc=share_data.title;
    share_data.title='先迈商城';
    await this.$util.publicShare(share_data);
    // #endif

    await this.getData();
  },
  async onReachBottom(){
    await this.getData();
  },
  async onPullDownRefresh() {
    this.refresh=true;
    this.list=[];
    this.page=1;
    this.page_count=1;
    await this.getData();
    uni.stopPullDownRefresh();
    this.refresh=false;
  },
  methods:{
    getSharePageParams() {
      let share_data=this.$util.unifySharePageParams('/promotionpages/articlemessage/list/list','必买爆品，千万别错过！',
          '',{},this.$util.img('public/static/youpin/articlemessage.jpg'))
      return share_data;
    },
    async getData(){
      if(this.page>this.page_count){
        return;
      }
      uni.showLoading({
        mask: true,
        title: '加载中'
      });
      try{
        let res = await this.$api.sendRequest({
          url: apiurls.usershareexperienceListUrl,
          async: false,
          data: {
            page_size:this.page_size,
            page: this.page,
          },
        });
        uni.hideLoading();
        if (res.code != 0) {
          uni.showToast({
            title: res.message,
            mask: true,
            icon: "none",
            duration: 3000
          });
          return
        }
        this.page_count=res.data.page_count;
        this.page+=1;
        this.list=this.list.concat(res.data.list);
      }catch (e) {
        uni.hideLoading();
        this.$util.showToast({
          title:e.message
        })
      }finally {
        if (this.$refs.loadingCover) this.$refs.loadingCover.hide();
      }
    },
    toProductDetail(item) {
      let url = item.applet_url;
      if(url){
        this.$util.redirectTo(url);
      }
    },
    toSee(info){
      if(info.content_type == 2) {
        this.$util.diyRedirectTo({
          wap_url: info.content_link
        })
      }else{
        this.$util.redirectTo('/promotionpages/articlemessage/detail/detail',{id: info.id});
      }
    },
    dateImageError(data, index) {
      if (data instanceof Object && data[index] && data[index].image) {
        data[index].image = this.$util.getDefaultImage().default_goods_img;
      }
      this.$forceUpdate();
    },
    goodsImageError(data, index,j) {
      if (data instanceof Object && data[index] && data[index]['goods'] && data[index]['goods'][j] && data[index]['goods'][j].goods_image) {
        data[index]['goods'][j].goods_image = this.$util.getDefaultImage().default_goods_img;
      }
      this.$forceUpdate();
    },
  },
  // 分享
  onShareAppMessage(res) {
    let { title, link, imageUrl, query } = this.getSharePageParams()
    return this.$buriedPoint.pageShare(link , imageUrl, title);
  },
  // 分享到微信
  onShareTimeline(res){
    let { title, imageUrl, query } = this.getSharePageParams()
    return {
      title,
      imageUrl,
      query,
      success: res => {},
      fail: res => {}
    };
  }
}
</script>

<style scoped lang="scss">
.list{
  padding-bottom: 30rpx;
  box-sizing: border-box;
  &-date{
    padding-top: 40rpx;
    box-sizing: border-box;
    width: 702rpx;
    margin: 0 auto;
    &-title{
      margin-bottom: 18rpx;
      text-align: center;
      font-size: 24rpx;
      font-weight: 500;
      color: #999999;
    }
    &-item{
      display: flex;
      background: #FFFFFF;
      border-radius: 20rpx;
      &:not(:nth-child(2)){
        margin-top: 24rpx;
      }
      &-left{
        width: 300rpx;
        height: 400rpx;
        background: #999999;
        border-radius: 20rpx 0px 0px 20rpx;
        display: block;
        flex-shrink: 0;
      }
      &-right{
        width: 402rpx;
        padding-left: 24rpx;
        padding-top: 20rpx;
        padding-bottom: 14rpx;
        box-sizing: border-box;
        position: relative;
        &-title{
          text-overflow: ellipsis;
          display: -webkit-box;
          -webkit-box-orient: vertical;
          -webkit-line-clamp: 2;
          overflow: hidden;
          font-size: 30rpx;
          font-weight: 500;
          color: #333333;
          width: 354rpx;
          margin-right: 24rpx;
          min-height: 108rpx;
        }
        &-recommend{
          font-size: 24rpx;
          font-weight: 500;
          color: #999999;
          margin-top: 16rpx;
          margin-bottom: 10rpx;
        }
        &-goods{
          display: flex;
          align-items: center;
          width: 100%;
          overflow-x: auto;
          &-one{
            flex-shrink: 0;
            width: 120rpx;
            height: 120rpx;
            border-radius: 10rpx;
            &:not(:first-child){
              margin-left: 18px;
            }
          }
        }
        &-to{
          display: flex;
          justify-content: flex-end;
          //margin-top: 22rpx;
          padding-right: 24rpx;
          box-sizing: border-box;
          position: absolute;
          right: 0;
          bottom: 14rpx;
          width: 100%;
          &-but{
            width: 140rpx;
            height: 48rpx;
            line-height: 48rpx;
            background: #FF3333;
            border-radius: 48rpx;
            font-size: 26rpx;
            font-weight: 400;
            color: #FFFFFF;
            box-sizing: border-box;
            padding: 0;
            margin: 0;
            &-more{
              width: 20rpx;
              height: 20rpx;
              display: inline-block;
              margin-left: 4rpx;
            }
          }
        }
      }
    }
  }
}
.to-top{
  width: 144rpx;
  height: 152rpx;
  position: fixed;
  right: 0;
  bottom: 82rpx;
}
</style>
