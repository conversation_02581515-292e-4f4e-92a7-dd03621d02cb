<template>
	<view>
		<!-- 文本内容 -->
		<view class="article-detail">
			<!-- 头部信息 -->
			<view class="title">{{info.title}}</view>
			<view class="base-msg">
				<view v-if="info.show_view_num">浏览：{{info.show_view_num}}</view>
				<view>{{info.create_time}}</view>
			</view>

			<!-- 内容 -->
			<mphtml v-if="content" :content="content" class="mphtml"/>

		</view>

		<!-- 达人推荐 -->
		<view class="product-recommend">
			<view class="product-title" id="product-recommend">达人推荐</view>
			<view v-if="productShowStatus" class="product-list" v-for="(item,index) in productList" :key="index" @click="toProductDetail(item)">
				<view class="product-info">
					<image class="img expose_goods_index" :data-expose_goods_sku="item.sku_id" :src="item.goods_image"
						@error="imageError(index)">
					</image>
					<view class="product-info-item">
						<view class="name overtext-hidden-one">{{item.goods_name}}</view>
						<view class="price"><text style="font-size: 24rpx;">￥</text>{{item.retail_price}}</view>
					</view>
				</view>
				<image class="butImg" v-if="item.showType !== 'seckill' && item.showType !== 'pintuan'"
					:src="$util.img('public/static/youpin/redCart.png')"
					@click.stop.prevent="changeSkuShow(index,item)" />
				<image class="butImg" v-if="item.showType == 'seckill'" :src="$util.img('public/static/youpin/seckill.png')"
					@click.stop.prevent="changeSkuShow(index,item)" />
				<image class="butImg" v-if="item.showType == 'pintuan'" :src="$util.img('public/static/youpin/pintuan.png')"
					@click.stop.prevent="changeSkuShow(index,item)" />
			</view>
			<view  v-if="!productShowStatus">
				<image class="emptyImg" :src="$util.img('public/static/youpin/emptyCart.jpg')" mode=""></image>
				<view class="emptyTips">推荐商品已下架</view>
			</view>
		</view>

		<view style="width: 100%;height: 200rpx;"></view>

		<!-- 底部按钮 -->
		<view class="article-but">
			<view class="article-but-box">
				<view class="but-item" v-for="(item,index) in butList" :key="index"
					@click="touchButton(item.type,index)">
					<!-- #ifdef MP-WEIXIN -->
					<template v-if="item.type == 'share'">
						<button class="shareBut" plain="true" type="default" open-type="share">
							<image class="touchImg" :src="item.img" mode="widthFix" />
							<view class="txt"> {{item.name}} </view>
						</button>
					</template>
					<!-- #endif -->
					<!-- #ifdef H5 -->
					<template v-if="item.type == 'share'">
						<image class="touchImg" :src="item.img" mode="widthFix" />
						<view v-if="item.type == 'recommend'" class="nums">{{productList.length}}</view>
						<view class="txt"> {{item.name}} </view>
					</template>
					<!-- #endif -->
					<template v-if="item.type !== 'share'">
						<image class="touchImg" :class="{'anim':item.type == 'like' && animShow}" :src="item.img"
							mode="widthFix" />
						<view v-if="item.type == 'recommend'" class="nums">{{productList.length}}</view>
						<view class="txt"> {{item.name}} </view>
					</template>
				</view>
			</view>
		</view>

		<!-- 规格弹窗 -->
		<ns-goods-sku ref="goodsSku" @refresh="refreshGoodsSkuDetail" :goods-detail="goodsSkuDetail"
			:entrance="'choose_spec'"></ns-goods-sku>

		<!-- h5分享提示 -->
		<diy-share-navigate-h5 ref="shareNavigateH5"></diy-share-navigate-h5>

		<!-- 返回顶部 -->
		<image :src="$util.img('public/static/youpin/to-top.png')" class="to-top" v-show="showTop"
			@click="scrollToTopNative"></image>

		<!-- 提示无法购物弹窗 -->
		<diy-wrong-dialog ref="diywrangDialog"></diy-wrong-dialog>

		<!-- 授权登录弹窗 -->
		<yd-auth-popup ref="ydauth"></yd-auth-popup>

		<ns-login ref="login"></ns-login>
		<uni-coupon-pop ref="couponPop"></uni-coupon-pop>
	</view>
</template>

<script>
	import scroll from "@/common/mixins/scroll-view.js";
	import system from "@/common/js/system.js";
	import wx_expose_goods from '@/common/mixins/wx_expose_goods.js';
	import apiurls from "../../../common/js/apiurls";
	import htmlParser from '@/common/js/html-parser.js';
	import mphtml from '../../../components/mp-html/mp-html';
	export default {
		mixins: [scroll, wx_expose_goods],
		components: {
			mphtml
		},
		data() {
			return {
				list:[],
				isShopOwner: 0, // 1是店主  0非店主
				token: '',
				shop_id: null,
				id: '',
				butList: [{
						img: this.$util.img('public/static/youpin/share.png'),
						name: '分享好友',
						type: 'share'
					}, {
						img: this.$util.img('public/static/youpin/noLike.png'),
						name: '点赞',
						type: 'like'
					},
					{
						img: this.$util.img('public/static/youpin/cart.png'),
						name: '达人推荐',
						type: 'recommend'
					}
				],
				productList: [],
				goodsSkuDetail: {}, // 规格参数
				animShow: false, // 点赞动画
				productShowStatus:true,
				info: {},
				content:'',
			}
		},
		onLoad(data) {

			this.id = data.id

		},
		onPullDownRefresh(){
			this.getArticleContent()
		},
		async onShow() {
			this.$langConfig.refresh();
			await system.wait_staticLogin_success();

			this.shop_id = uni.getStorageSync('shop_id')
			//登录后查询
			this.token = uni.getStorageSync('token');

			this.getArticleContent()
			if (uni.getStorageSync('is_register')) {
				this.$util.toShowCouponPopup(this)
				uni.removeStorageSync('is_register');
			}
		},
		methods: {
			// 获取详情
			getArticleContent() {
				this.$api.sendRequest({
					url: apiurls.usershareexperienceContent,
					data: {
						id: this.id,
					},
					success: (res) => {
						if (res.code == 0) {
							if(res.data.status == 1){

								this.isShopOwner = res.data.is_shop_owner
								this.info = res.data

								let imgUrl = res.data.content.match(/<img.*?(?:>|\/>)/gi)
								if(imgUrl && imgUrl.length > 0){
									imgUrl.forEach(it => {
										let imgurl = it.match(/src=[\'\"]?([^\'\"]*)[\'\"]?/i)[1]
										res.data.content = res.data.content.replace(imgurl,imgurl+ '?image_process=resize,s_600')
									})
								}
								if(res.data.content.indexOf('text-indent: 28px;') > 0){
									res.data.content = res.data.content.replace(/text-indent: 28px;/gi,'')
								}

								this.content = res.data.content

								if (res.data.is_like == 1) {
									this.animShow = true
									this.butList[1].img = this.$util.img('public/static/youpin/redLike.png')
									this.butList[1].name = '已点赞'
								}

								if (res.data.goods && res.data.goods.length > 0) {
									res.data.goods.forEach(it => {
										it.goods_detail.is_shop_owner = it.is_shop_owner
										// 处理规格
										if (it.goods_detail.goods_spec_format) {
											it.goods_detail.goods_spec_format = JSON.parse(it.goods_detail
												.goods_spec_format)
										}
										// 处理商品类型
										if (it.tags && it.tags.length > 0) {
											it.showType = it.tags[0].key
										} else {
											it.showType = 'normal'
										}
									})
								}

								this.productList = res.data.goods

								res.data.goods && res.data.goods.length <=0 && (this.productShowStatus = false)

								this.$api.sendRequest({
									url: apiurls.usershareexperienceAddView,
									data: {
										id: this.id
									}
								});
								// #ifdef H5
								// 公众号分享
									this.setWechatShare();
								// #endif
							}else {
								uni.showToast({
								    title: '该分享体验已过期，请查看其它分享',
								    duration: 2000,
									icon:'none'
								});
								setTimeout(()=>{
									this.$util.redirectTo('/promotionpages/articlemessage/list/list',{},'redirectTo');
								},1500)
							}
						}
					}
				});
			},
			// sku弹窗回调触发修改
			refreshGoodsSkuDetail(goodsSkuDetail) {
				Object.assign(this.goodsSkuDetail, goodsSkuDetail);
			},
			// sku弹窗控制
			changeSkuShow(index, data) {
				// 检查token
				if (!this.token) {
					this.$util.toShowLoginPopup(this, null, `/promotionpages/articlemessage/detail/detail?id=${this.id}`);
					return;
				}
				//检查 是否禁止购物 0为冻结
				if (uni.getStorageSync('is_shopping_status') == 0) {
					this.$refs.diywrangDialog.open()
					return;
				}

				let arr = ["newhand", "seckill", "pintuan", "maidou"]
				let target = this.productList[index]

				target.goods_detail.show_price = this.isShopOwner == 1 ? target.discount_price : target.retail_price;
				target.goods_detail.numControl = true
				this.goodsSkuDetail = target.goods_detail

				if (!arr.includes(target.showType)) {
					if (target.goods_detail.goods_spec_format && typeof target.goods_detail.goods_spec_format ==
						'object' && target.goods_detail.goods_spec_format.length > 0) {
						// 多规格弹窗
						this.$refs.goodsSku.show("choose_spec", () => {});
					} else {
						// 单规格加购
						this.joinCart(target)
					}
				} else {
					this.toProductDetail(target)
				}
			},
			// 加入购物车
			joinCart(data) {
				this.$refs.goodsSku.type = 'join_cart';
				this.$refs.goodsSku.confirm(null, false)
				this.$refs.goodsSku.callback = (res) => {
					if (!res) return;
					this.$buriedPoint.purchaseGoods({
						id: data.sku_id,
						action_type: 0,
						action_num: [1],
						is_goods_page: 1
					})

					uni.showModal({
						content: '该商品已加入购物车',
						cancelText: '再看看',
						cancelColor: '#999',
						confirmText: '去结算',
						confirmColor: '#000',
						success: res => {
							if (res.confirm) {
								this.$util.redirectTo('/pages/goods/cart/cart', {}, 'reLaunch');
							}
						},
					})
				}
			},
			toProductDetail(item) {
				let url = item.applet_url;
				if (url) {
					this.$util.redirectTo(url);
				}
			},
			/**
			 * 设置微信公众号分享
			 */
			setWechatShare() {
				// 微信公众号分享
				// #ifdef H5
				let share_data=this.$util.deepClone(this.getSharePageParams());
				let link=window.location.origin+this.$router.options.base+share_data.link.slice(1);
				share_data.link=link;
				this.$util.publicShare(share_data);
				// #endif
			},
			/**
			 *分享参数组装(注意需要分享的那一刻再调此方法)
			 */
			getSharePageParams(){
        let share_data=this.$util.unifySharePageParams('/promotionpages/articlemessage/detail/detail','先迈商城',
            this.info.title,{id:this.id},this.$util.img('public/static/youpin/articlemessage.jpg'))
        return share_data;
			},
			// 底部按钮触发
			touchButton(type, index) {
				if (type == 'share') {
					// #ifdef H5
					if (this.$refs.shareNavigateH5) this.$refs.shareNavigateH5.open(this.getSharePageParams());
					this.getTransmit()
					// #endif
				} else if (type == 'like') {
					// 检查token
					if (!this.token) {
						this.$util.toShowLoginPopup(this, null, `/promotionpages/articlemessage/detail/detail?id=${this.id}`);
						return;
					}
					this.$api.sendRequest({
						url: apiurls.usershareexperienceLike,
						data: {
							id: this.id
						},
						success: (res) => {
							if (res.code == 0) {
								this.animShow = !this.animShow
								this.butList[1].name = this.animShow ? '已点赞' : '点赞'
								this.butList[1].img = this.animShow ? this.$util.img(
									'public/static/youpin/redLike.png') : this.$util.img(
									'public/static/youpin/noLike.png')
							}
						}
					});

				} else if (type == 'recommend') {
					let that = this;
					uni.createSelectorQuery().select('#product-recommend').boundingClientRect(data => { //目标位置节点 类或者 id
						uni.createSelectorQuery().select(".article-detail").boundingClientRect((
							res) => { //最外层盒子节点类或者 id
							uni.pageScrollTo({
								duration: 500,
								scrollTop: Math.abs(data.top - res.top), //到达距离顶部的top值
							})
						}).exec()
					}).exec();
				}
			},
			// 分享触发
			getTransmit() {
				this.$api.sendRequest({
					url: apiurls.usershareexperienceTransmit,
					data: {
						id: this.id
					}
				});
			},
			imageError(index) {
				this.productList[index].goods_image = this.$util.getDefaultImage().default_goods_img;
				this.$forceUpdate();
			},
		},
		// 小程序分享
		onShareAppMessage() {
			this.getTransmit()

      let share_data=this.getSharePageParams();
      return this.$buriedPoint.pageShare( share_data.link, share_data.imageUrl, share_data.desc);

		},
		// 安卓分享到某圈
		onShareTimeline(res) {
			this.getTransmit()
      let share_data=this.getSharePageParams();
			return {
				title:share_data.desc,
				imageUrl: share_data.imageUrl,
				query:share_data.query,
				success: res => {},
				fail: res => {}
			};
		}
	}
</script>

<style lang="scss" scoped>
	.article-detail {
		background-color: white;
		box-sizing: border-box;
		padding: 24rpx;

		.title {
			font-size: 36rpx;
			color: #333333;
			line-height: normal;
			font-weight: bold;
		}

		.base-msg {
			width: 100%;
			display: flex;
			justify-content: space-between;
			align-items: center;
			margin: 20rpx 0rpx;
			font-size: 26rpx;

			view {
				color: #999999;
				line-height: normal;
			}
		}
	}


	.product-recommend {
		margin-top: 20rpx;
		background-color: white;
		box-sizing: border-box;
		padding: 20rpx 24rpx;

		.product-title {
			font-size: 28rpx;
			line-height: normal;
			margin-bottom: 20rpx;
			color: #333333;
			font-weight: bold;
		}

		.product-list {
			width: 100%;
			height: 160rpx;
			display: flex;
			justify-content: space-between;
			margin-bottom: 20rpx;
			border-radius: 10rpx;
			box-sizing: border-box;
			padding: 20rpx 30rpx 20rpx 20rpx;
			background-color: #F5F5F5;

			.product-info {
				display: flex;

				.img {
					width: 120rpx;
					height: 120rpx;
					border-radius: 4rpx;
					margin-right: 20rpx;
				}

				.product-info-item {
					.name {
						width: 410rpx;
						line-height: normal;
						margin-bottom: 36rpx;
					}

					.price {
						font-size: 32rpx;
						color: #FF3333;
					}
				}
			}

			.butImg {
				width: 48rpx;
				height: 48rpx;
				margin-top: 36rpx;
			}

		}
		.emptyImg {
			width: 100%;
		}
		.emptyTips {
			text-align: center;
		}
	}


	.article-but {
		width: 100%;
		background-color: white;
		border: 1rpx solid #EEEEEE;
		position: fixed;
		left: 0;
		bottom: 0;
		z-index: 1;
		padding-bottom: 0;
		padding-bottom: constant(safe-area-inset-bottom);
		padding-bottom: env(safe-area-inset-bottom);

		.article-but-box {
			width: 100%;
			height: 98rpx;
			box-sizing: border-box;
			padding: 0rpx 50rpx;
			display: flex;
			justify-content: space-around;
			text-align: center;

			.but-item {
				width: 130rpx;
				font-size: 20rpx;
				padding-top: 10rpx;
				position: relative;

				.shareBut {
					width: 130rpx;
					height: 85rpx;
					line-height: 42rpx;
					margin: 0;
					border: 0;
					padding: 0;
				}

				.touchImg {
					width: 48rpx;
					height: 48rpx;
				}

				.anim {
					animation: movescale 1s normal;
				}

				@keyframes movescale {
					0% {
						transform: scale(1.2);
					}

					50% {
						transform: scale(1.5);
					}

					100% {
						transform: scale(1);
					}
				}

				.nums {
					width: 20rpx;
					height: 20rpx;
					text-align: center;
					line-height: 20rpx;
					color: white;
					font-size: 16rpx;
					background-color: #FF3333;
					border-radius: 50%;
					position: absolute;
					top: 10rpx;
					right: 35rpx;
				}

				.txt {
					font-size: 20rpx;
					line-height: 0;
				}
			}
		}
	}

	.to-top {
		width: 144rpx;
		height: 152rpx;
		position: fixed;
		right: 0;
		bottom: 85rpx;
		padding-bottom: constant(safe-area-inset-bottom);
		padding-bottom: env(safe-area-inset-bottom);
	}
  .mphtml /deep/ img{
    display: flex;
  }
</style>
