<template>
	<view :class="themeStyle">
    <!-- #ifdef H5 -->
    <uni-nav-bar left-icon="back" :border="false" @clickLeft="appGoBack" v-if="isOnXianMaiApp">
      <template>
        <view class="page-title">{{goodsSkuDetail.goods_name}}</view>
      </template>
    </uni-nav-bar>
    <!-- #endif -->
	<!-- #ifdef MP-WEIXIN -->
	<uni-nav-bar left-icon="back" :border="false" @clickLeft="goBack" :statusBar="true" :backgroundColor="isShowDetailTab ? '#fff' :'transparent'" :fixed="true">
	  <template>
	    <view class="page-title" v-if="isShowDetailTab">{{goodsSkuDetail.goods_name}}</view>
	  </template>
	</uni-nav-bar>
	<!-- #endif -->
		<view class="goods-detail">

			<view class="goods-container">
				<!-- 商品媒体信息 -->
				<view class="goods-media">
					<!-- 商品图片 -->
					<view class="goods-img" :class="{ show: switchMedia == 'img' }">
						<swiper class="swiper" @change="swiperChange" :interval="swiperInterval" :autoplay="swiperAutoplay" :circular="true">
							<swiper-item v-for="(item, index) in goodsSkuDetail.sku_images" :key="index">
								<view class="item" @click="previewMedia(index)">
									<image :src="$util.img(item)" @error="swiperImageError(index)" mode="aspectFit" />
								</view>
							</swiper-item>
						</swiper>
						<view class="img-indicator-dots">
							<text>{{ swiperCurrent }}</text>
							<text v-if="goodsSkuDetail.sku_images">/{{ goodsSkuDetail.sku_images.length }}</text>
						</view>
					</view>
					<!-- 商品视频 -->
					<view class="goods-video" :class="{ show: switchMedia == 'video' }">
						<!-- #ifndef H5 -->
						<video :src="$util.img(goodsSkuDetail.video_url)" :poster="$util.img(goodsSkuDetail.sku_image)" objectFit="contain"></video>
						<!-- #endif -->
						<!-- #ifdef H5 -->
						<view class="video-img">
							<image :src="$util.img(goodsSkuDetail.sku_image)" mode=""></image>
							<view class="video-open">
								<view class="iconfont iconarrow-" @click="openVideo"></view>
							</view>
						</view>
						<!-- #endif -->
					</view>

					<!-- 切换视频、图片 -->
					<view class="media-mode" v-if="goodsSkuDetail.video_url != ''">
						<text :class="{ 'ns-bg-color': switchMedia == 'video' }" @click="switchMedia = 'video'">{{ $lang('video') }}</text>
						<text :class="{ 'ns-bg-color': switchMedia == 'img' }" @click="switchMedia = 'img'">{{ $lang('image') }}</text>
					</view>
					<!--					<image v-if="goodsSkuDetail.stock==0" :src="$util.img('public/static/youpin/product-sell-out.png')" class="over"></image>-->
				</view>
				<view @touchmove.prevent.stop class="videoPopup-box">
					<uni-popup ref="videoPopup" type="center">
						<view class="pop-video">
							<video :src="$util.img(goodsSkuDetail.video_url)" :poster="$util.img(goodsSkuDetail.sku_image)" objectFit="contain"></video>
						</view>
					</uni-popup>
				</view>

				<!-- 限时折扣 -->
				<view v-if="preview == 0 && goodsSkuDetail.promotion_type == 1 && goodsSkuDetail.discountTimeMachine && addonIsExit.discount"
				 class="goods-discount">
					<view class="price-info">
						<view class="discount-price">
							<text class="symbol">{{ $lang('common.currencySymbol') }}</text>
							<text>{{ goodsSkuDetail.discount_price }}</text>
						</view>
						<view class="original-price">
							<text class="price">{{ $lang('common.currencySymbol') }} {{ goodsSkuDetail.price }}</text>
							<text class="sale-num hide-sales">{{ goodsSkuDetail.sale_num }}{{ goodsSkuDetail.unit }}已售</text>
						</view>
					</view>

					<view class="countdown">
						<view class="txt">距结束仅剩</view>
						<view class="clockrun">
							<uni-count-down :day="goodsSkuDetail.discountTimeMachine.d" :hour="goodsSkuDetail.discountTimeMachine.h" :minute="goodsSkuDetail.discountTimeMachine.i"
							 :second="goodsSkuDetail.discountTimeMachine.s" color="#fff" splitorColor="#000" background-color="#000" />
						</view>
					</view>
				</view>

				<view class="group-wrap" style="margin-top: -20rpx;">
					<view class="pintuan-price">
						<view class="pintuan-price-left">
							<view class="pintuan-price-left-one">
								<view class="pintuan-price-left-one-tag">拼团价</view>
								<view class="pintuan-price-left-one-price"><text>￥</text>{{ pintuan_info.pintuan_price }}</view>
								<view class="pintuan-price-left-one-unprice"><text>￥</text>{{ goodsSkuDetail.market_price }}</view>
							</view>
						</view>
            <view class="pintuan-price-right">
              <text class="pintuan-price-right-number">{{pintuan_info.pintuan_num}}人团</text>
              <text class="pintuan-price-right-yet">已开{{pintuan_info.order_num}}团</text>
            </view>
					</view>
					<!--					<view class="goods-module-wrap" :class="{ discount: preview == 0 && goodsSkuDetail.promotion_type == 1 }">-->
					<!--						<view class="goods-module-wrap-box">-->
					<!--							<view>-->
					<!--								<template v-if="goodsSkuDetail.promotion_type == 0">-->
					<!--									<text class="goods_tag" v-for="tag in tags" :key="tag.id">{{tag.tag_name}}</text>-->
					<!--									<text class="price-symbol ns-text-color">{{ $lang('common.currencySymbol') }}</text>-->
					<!--									<text class="price ns-text-color">{{ goodsSkuDetail.retail_price }}</text>-->
					<!--									<text class="market-price-symbol" v-if="goodsSkuDetail.market_price > 0">{{ $lang('common.currencySymbol') }}</text>-->
					<!--									<text class="market-price" v-if="goodsSkuDetail.market_price > 0">{{ goodsSkuDetail.market_price }}</text>-->
					<!--								</template>-->
					<!--							</view>-->
					<!--						</view>-->
					<!--					</view>-->

					<view class="goods-module-wrap">
						<view>
							<view class="sku-name-box" style="overflow: none;">
							 <text class="sku-name-tuan" v-if="pintuan_info.promotion_type=='new'">{{pintuan_info.promotion_type_desc}}</text>
<!--							 <text class="sku-name-tuan">{{pintuan_info.pintuan_num}}人团</text>-->
							 	<!-- <view class="sku-name-box"> -->
							<text class="sku-name" style="display: inline; font-size: 30rpx; line-height: 44rpx; position: relative" @click="longpress" @longpress="longpress" >{{ goodsSkuDetail.goods_name }}</text>
							<view class="showCopybox" v-if="copytextShow">
							<view class="copytext"><text  @click="$util.copy(goodsSkuDetail.goods_name,copyCallback)" class="fuzhi">复制</text><text class="quxiao" @click="copytextShow = false">取消</text></view>
							</view>
							<!-- </view> -->
							</view>
							<text class="introduction ns-text-color" v-if="goodsSkuDetail.introduction">{{ goodsSkuDetail.introduction }}</text>
						</view>
						<view class="sku-subsidy hide-sales">
							<text class="sku-subsidy-one">拼团补贴</text>
							<text class="sku-subsidy-two">￥{{pintuan_info.not_win_money}}</text>
						</view>
            <button class="group-wrap-share" style="font-size: 24rpx;" :plain="true" @click="morePintuan('invite_politely')" v-if="pintuanList.length">
              <text>分享</text>
              <text style="margin-top: 10rpx;">有礼</text>
            </button>
						<button class="group-wrap-share" :plain="true" @click="openSharePopup" v-else>
<!--							<view class="iconfont iconfenxiang"></view>-->
                <uni-icons type="redo" color="#fff" class="group-wrap-share-icon"></uni-icons>
							<text>分享</text>
						</button>
					</view>
				</view>


				<share-popup v-if="isShowCanvas" :canvasOptions="canvasOptions" ref="sharePopup" :sharePopupOptions="sharePopupOptions"></share-popup>

				<!--  正在拼团列表  start    -->
				<view class="group-wrap" v-if="pintuanList.length>0">
					<view class="pintuan-list">
						<view class="pintuan-list-title">
							<view class="pintuan-list-title-left">
								<text class="pintuan-list-title-left-tip">{{pintuan_group_count}}人正在拼团</text>
							</view>
							<view class="pintuan-list-title-right">
								<text class="pintuan-list-title-right-more" v-on:click="morePintuan('view_all')">查看更多 <text class="iconfont icongengduo3" style="color: #ccc;"></text></text>
							</view>
						</view>
						<view class="pintuan-list-info">
							<block v-for="(item,index) in pintuanList" v-bind:key="item.group_id">
								<view class="pintuan-list-info-one" v-on:click="toPintuanDetail(item)" v-if="index<3">
									<view class="pintuan-list-info-one-left">
                    <view class="pintuan-list-info-one-left-img">
                      <image :src="$util.img(item.head_member_img)" @error="()=>{this.pintuanList[index].head_member_img=this.$util.getDefaultImage().default_goods_img}"></image>
                      <view class="pintuan-list-info-one-left-img-desc"><text class="pintuan-list-info-one-left-img-desc-text">团长</text></view>
                    </view>
										<text class="pintuan-list-info-one-left-name">{{item.head_nickname}}</text>
									</view>
									<view class="pintuan-list-info-one-right">
										<view class="pintuan-list-info-one-right-one">
											<view class="pintuan-list-info-one-right-one-count">还差<text>{{item.diff_num}}</text>人拼成</view>
											<view class="pintuan-list-info-one-right-one-time">剩余<countdown-timer ref="countdown" :time="item.distance_time*1000"
												 autoStart @finish="onFinish(item.group_id)"></countdown-timer>
											</view>
										</view>
										<button class="pintuan-list-info-one-right-two">去参团</button>
									</view>
								</view>
							</block>
						</view>
					</view>
				</view>
				<!--  正在拼团列表  end    -->

        <view class="group-wrap group-wrap-padding">
          <!-- 已选规格 ( 不属于新人专区或者属于新人专区并且是第一次下单 ) -->
          <view class="goods-cell selected-sku-spec" v-if="goodsSkuDetail.sku_spec_format" @click="chooseSkuspecFormat(goodsSkuDetail.maidou_tag)">
            <view class="box">
              <text class="tit">已选择</text>
              <text v-for="(item, index) in goodsSkuDetail.sku_spec_format" :key="index">{{ item.spec_name }}/{{ item.spec_value_name }}</text>
            </view>
            <text class="iconfont iconright"></text>
          </view>
          <!-- #ifdef MP-WEIXIN -->
          <view v-if="goodsSkuDetail.sku_images" class="goods-cell">
            <guarantee-bar  pageType="goods_detail" :goodsName="goodsSkuDetail.goods_name" :goodsImg="goodsSkuDetail.sku_images && goodsSkuDetail.sku_images.length ? $util.img(goodsSkuDetail.sku_images[0]) : ''"
                            align="between" spaceSize="12" :goodsPrice="`${goodsSkuDetail.retail_price}元`"  :bannerStyle="{fontSize: 'mini',fontOpacity: 'gray',}" style="width: 100%"/>
          </view>
          <!-- #endif -->

          <!-- 商品属性 -->
          <view class="goods-cell" @click="openAttributePopup()" v-if="goodsSkuDetail.goods_attr_format && goodsSkuDetail.goods_attr_format.length > 0">
            <view class="box">
              <text class="tit">规格参数</text>
              <!-- 							<text>{{ goodsSkuDetail.goods_attr_format[0].attr_name }} {{ goodsSkuDetail.goods_attr_format[0].attr_value_name }}...</text> -->
            </view>
            <text class="iconfont iconright"></text>
          </view>

          <view @touchmove.prevent.stop>
            <uni-popup ref="attributePopup" type="bottom">
              <view class="goods-attribute-popup-layer">
                <text class="title">规格参数</text>
                <scroll-view scroll-y class="goods-attribute-body">
                  <view class="item ns-border-color-gray" v-for="(item, index) in goodsSkuDetail.goods_attr_format" :key="index">
                    <text class="ns-text-color-gray">{{ item.attr_name }}</text>
                    <text class="value">{{ item.attr_value_name }}</text>
                  </view>
                </scroll-view>
                <view class="button-box"><button type="primary" @click="closeAttributePopup()">确定</button></view>
              </view>
            </uni-popup>
          </view>
        </view>

				<!--   拼团玩法 start   -->
				<view class="group-wrap">
					<view class="pintuan-method">
						<view class="pintuan-method-title">
							<view class="pintuan-method-title-left">
                <text class="pintuan-method-title-left-tip">拼团奖励</text>
                <text class="pintuan-method-title-left-desc">返现</text>
							</view>
							<view class="pintuan-method-title-right">
								<view class="pintuan-method-title-right-more" @click="showPintuanRule">了解规则
<!--									<image :src="$util.img('public/static/youpin/pintuan/question_mark.png')" />-->
                  <uni-icons type="help-filled" color="#ccc"></uni-icons>
								</view>
							</view>
						</view>
					</view>
          <view class="pintuan-space">
            <view class="pintuan-flow">
              <view class="pintuan-flow-one">
                <image :src="$util.img('public/static/youpin/pintuan/pintuan-flow-one.png')" alt="" />
                <view class="pintuan-flow-one-info">
                  <view>参与拼团</view>
                  <view><text class="pintuan-flow-one-info-red">{{pintuan_info.pintuan_num}}</text>人成团</view>
                </view>
              </view>
              <view class="pintuan-flow-one">
                <image :src="$util.img('public/static/youpin/pintuan/pintuan-flow-two.png')" alt="" />
                <view class="pintuan-flow-one-info">
                  <view><text class="pintuan-flow-one-info-red">{{pintuan_info.winning_num}}</text>人拼中发货</view>
                  <view :class="{'hide-sales':pintuan_info.pintuan_num-pintuan_info.winning_num<1}"><text class="pintuan-flow-one-info-red">{{pintuan_info.pintuan_num-pintuan_info.winning_num}}</text>人未中退款</view>
                </view>
              </view>
              <view class="pintuan-flow-one">
                <image :src="$util.img('public/static/youpin/pintuan/pintuan-flow-three.png')" alt="" />
                <view class="pintuan-flow-one-info pintuan-flow-one-info-other">
                  <!--								<view>邀请好友参团可得</view>-->
                  <!--								<view><text class="pintuan-flow-one-info-price"><text>￥</text>{{pintuan_info.not_win_money}}</text></view>-->
                  <view v-for="(item,index) in pintuan_info.play_list" :key="index">
                    {{item.reward_str}}
                    <text class="pintuan-flow-one-info-price"><text>￥</text>{{item.money}}</text>
                  </view>
                </view>
              </view>
            </view>
          </view>
				</view>
				<!--   拼团玩法 end   -->

        <view class="buyer-info" v-if="!is_audit_mode && buyerList.length && false">
          <view class="buyer-info-title">
            <view>买家秀（{{buyerList.length}}）</view>
            <view @click="buyersMoreFun(false)" v-if="buyerList.length">更多<text class="iconfont icon iconright" style="font-size: 28rpx"></text></view>
          </view>
          <view class="buyers-box" v-if="buyerList.length">
            <view class="buyers-list" v-for="(item, index) in buyerList" :key="index" @click="buyersDetailFun(item)">
              <view class="buyers-list-left">
                <view class="buyers-list-left-top">
                  <image :src="item.headimg"></image>
                  <view>{{item.nickname}}</view>
                </view>
                <view class="buyers-list-left-bottom">{{item.title}}</view>
              </view>
              <image :src="item.image" class="buyers-list-right"></image>
            </view>
          </view>
          <view class="buyers-not" v-else>
            <view class="buyers-not-tip">快成为首席体验官吧~</view>
            <view class="buyers-not-op" @click="buyersMoreFun(true)">发布买家秀</view>
          </view>
        </view>

				<!-- 营销活动 -->
				<view class="group-wrap" v-if="preview == 0 && couponList.length">
					<!-- 优惠券 -->
					<!-- <view class="goods-coupon goods-cell">
						<view class="box">
							<text class="tit">优惠券</text>
							<text class="ns-text-color">领取优惠劵</text>
						</view>
						<text class="get-coupon ns-border-color ns-border-color-gray ns-text-color" @click="openCouponPopup()">领取</text>
					</view>

					<view @touchmove.prevent.stop>
						<uni-popup ref="couponPopup" type="bottom">
							<view class="goods-coupon-popup-layer">
								<text class="tax-title ns-text-color-black" @click="closeCouponPopup()">
									优惠券
									<text class="iconfont iconclose"></text>
								</text>
								<scroll-view class="coupon-body" scroll-y>
									<view class="body-item ns-gradient-diy-goods-list" v-for="(item, index) in couponList" :key="index" :data-theme="themeStyle">
										<view class="item-price ns-gradient-detail-coupons-right-border" :data-theme="themeStyle">
											<text class="price" v-if="item.discount > 0">
												<text class="price-num">{{ item.discount }}折</text>
											</text>
											<text class="price" v-else>
												￥
												<text class="price-num">{{ item.money }}</text>
											</text>
											<view class="sub" v-if="item.at_least > 0">满{{ item.at_least }}元使用</view>
										</view>
										<view class="item-info">
											<view class="info-box">
												<text v-if="item.discount > 0" class="sub">{{ '满' + item.at_least + '享' + item.discount }}折优惠</text>
												<text v-else-if="item.money > 0" class="sub">{{ '满' + item.at_least + '减' + item.money }}</text>
												<text v-else class="sub">无门槛优惠券</text>
												<view class="sub" v-if="item.validity_type == 0">有效期至 {{ $util.timeStampTurnTime(item.end_time) }}</view>
												<view class="sub" v-else>领取之日起{{ item.fixed_term }}天内有效</view>
											</view>
											<view class="item-btn ns-bg-color" v-if="item.useState != 2" :data-theme="themeStyle" @click="receiveCoupon(item, index)">
												{{ item.useState != 0 ? '已领取' : '领取' }}
											</view>
										</view>
									</view>
									<view class="free_div"></view>
								</scroll-view>
								<view class="button-box"><button type="primary" @click="closeCouponPopup()">确定</button></view>
							</view>
						</uni-popup>
					</view> -->

					<!-- 满减 -->
					<view class="goods-cell" v-if="manjianList && manjianList.rule_json && addonIsExit.manjian" @click="openManjianPopup()">
						<view class="box">
							<text class="tit">满减</text>
							<text>{{ manjianList.manjian_name }}...</text>
						</view>
						<text class="iconfont iconright"></text>
					</view>
					<view @touchmove.prevent.stop v-if="addonIsExit.manjian">
						<uni-popup ref="manjianPopup" type="bottom">
							<view class="manjian-popup-layer">
								<text class="title">满减</text>
								<scroll-view scroll-y class="manjian-body">
									<view class="item ns-border-color-gray" v-for="(item, index) in manjianList.rule_json" :key="index">
										<text class="manjian-icon ns-bg-color">满减</text>
										<text class="value">{{ '满' + item.money + '减' + item.discount_money }}</text>
									</view>
								</scroll-view>
								<view class="button-box"><button type="primary" @click="closeManjianPopup()">确定</button></view>
							</view>
						</uni-popup>
					</view>

					<!-- 商家服务 -->
					<view class="goods-cell service" @click="openMerchantsServicePopup()" v-show="
							shopInfo.shop_baozh == 1 ||
								shopInfo.shop_qtian == 1 ||
								shopInfo.shop_zhping == 1 ||
								shopInfo.shop_erxiaoshi == 1 ||
								shopInfo.shop_tuihuo == 1 ||
								shopInfo.shop_shiyong == 1 ||
								shopInfo.shop_shiti == 1 ||
								shopInfo.shop_xiaoxie == 1
						">
						<view class="box">
							<text class="tit">{{ $lang('service') }}</text>
							<text v-if="shopInfo.shop_baozh == 1">保证服务</text>
							<text v-if="shopInfo.shop_qtian == 1">7天退换</text>
							<text v-if="shopInfo.shop_zhping == 1">正品保障</text>
							<text v-if="shopInfo.shop_erxiaoshi == 1">两小时发货</text>
							<text v-if="shopInfo.shop_tuihuo == 1">退货承诺</text>
							<text v-if="shopInfo.shop_shiyong == 1">试用中心</text>
							<text v-if="shopInfo.shop_shiti == 1">实体验证</text>
							<text v-if="shopInfo.shop_xiaoxie == 1">消协保证</text>
						</view>
						<text class="iconfont iconright"></text>
					</view>

					<view @touchmove.prevent.stop v-show="
							shopInfo.shop_baozh == 1 ||
								shopInfo.shop_qtian == 1 ||
								shopInfo.shop_zhping == 1 ||
								shopInfo.shop_erxiaoshi == 1 ||
								shopInfo.shop_tuihuo == 1 ||
								shopInfo.shop_shiyong == 1 ||
								shopInfo.shop_shiti == 1 ||
								shopInfo.shop_xiaoxie == 1
						">
						<uni-popup ref="merchantsServicePopup" type="bottom">
							<view class="goods-merchants-service-popup-layer">
								<text class="tax-title ns-text-color-black">{{ $lang('service') }}</text>
								<scroll-view scroll-y>
									<view class="item" v-if="shopInfo.shop_baozh == 1">
										<view class="iconfont icondui ns-text-color"></view>
										<view class="info-wrap">
											<text class="title">保证服务</text>
											<text class="describe">保证服务</text>
										</view>
									</view>
									<view class="item" v-if="shopInfo.shop_qtian == 1">
										<view class="iconfont icondui ns-text-color"></view>
										<view class="info-wrap">
											<text class="title">7天退换</text>
											<text class="describe">满足7天无理由退换货申请的前提下，包邮商品需要买家承担退货邮费，非包邮商品需要买家承担发货和退货邮费</text>
										</view>
									</view>
									<view class="item" v-if="shopInfo.shop_zhping == 1">
										<view class="iconfont icondui ns-text-color"></view>
										<view class="info-wrap">
											<text class="title">正品保障</text>
											<text class="describe">商品支持正品保障服务</text>
										</view>
									</view>
									<view class="item" v-if="shopInfo.shop_erxiaoshi == 1">
										<view class="iconfont icondui ns-text-color"></view>
										<view class="info-wrap">
											<text class="title">两小时发货</text>
											<text class="describe">付款后2小时内发货</text>
										</view>
									</view>
									<view class="item" v-if="shopInfo.shop_tuihuo == 1">
										<view class="iconfont icondui ns-text-color"></view>
										<view class="info-wrap">
											<text class="title">退货承诺</text>
											<text class="describe">退货承诺</text>
										</view>
									</view>
									<view class="item" v-if="shopInfo.shop_shiyong == 1">
										<view class="iconfont icondui ns-text-color"></view>
										<view class="info-wrap">
											<text class="title">试用中心</text>
											<text class="describe">试用中心</text>
										</view>
									</view>
									<view class="item" v-if="shopInfo.shop_shiti == 1">
										<view class="iconfont icondui ns-text-color"></view>
										<view class="info-wrap">
											<text class="title">实体验证</text>
											<text class="describe">实体验证</text>
										</view>
									</view>
									<view class="item" v-if="shopInfo.shop_xiaoxie == 1">
										<view class="iconfont icondui ns-text-color"></view>
										<view class="info-wrap">
											<text class="title">消协保证</text>
											<text class="describe">如有商品质量问题、描述不符或未收到货等，您有权申请退款或退货，来回邮费由卖家承担</text>
										</view>
									</view>
								</scroll-view>
								<view class="button-box"><button type="primary" @click="closeMerchantsServicePopup()">确定</button></view>
							</view>
						</uni-popup>
					</view>
				</view>

				<!-- 组合套餐 -->
				<view v-if="preview == 0 && bundling.length && bundling[0].bl_name && addonIsExit.bundling">
					<view class="group-wrap" @click="openBundlingPopup()">
						<view class="goods-cell" @click="openBundlingPopup()">
							<view class="box">
								<text class="tit">组合套餐</text>
								<text>{{ bundling[0].bl_name }}</text>
							</view>
							<text class="iconfont iconright"></text>
						</view>

						<view class="combo-goods-wrap ns-text-color-gray">
							<navigator hover-class="none" class="goods ns-border-color-gray" :url="'/pages/goods/detail/detail?sku_id=' + skuId">
								<image :src="$util.img(goodsSkuDetail.sku_image)" @error="imageError()" />
								<text>¥{{ goodsSkuDetail.price }}</text>
							</navigator>
							<view class="iconfont iconadd1 ns-text-color-gray"></view>
							<block v-for="(item, index) in bundling[0].bundling_goods" :key="index">
								<template v-if="index < 3">
									<navigator hover-class="none" class="goods ns-border-color-gray" :url="'/pages/goods/detail/detail?sku_id=' + item.sku_id">
										<image :src="$util.img(item.sku_image)" @error="bundlingImageError(0, index)" />
										<text>¥{{ item.price }}</text>
									</navigator>
								</template>
							</block>
						</view>
					</view>

					<view @touchmove.prevent.stop>
						<uni-popup ref="bundlingPopup" type="bottom">
							<view class="bundling-popup-layer">
								<text class="title">组合套餐</text>
								<scroll-view scroll-y class="bundling-body">
									<block v-for="(item, index) in bundling" :key="index">
										<scroll-view scroll-x>
											<view class="item ns-border-color-gray">
												<navigator hover-class="none" class="value" :url="'/promotionpages/combo/detail/detail?bl_id=' + item.bl_id">
													<text>{{ item.bl_name }}：￥{{ item.bl_price }}</text>
													<view class="right">
														<text class="ns-text-color">查看</text>
														<text class="iconfont iconright"></text>
													</view>
												</navigator>
												<view class="goods-wrap">
													<navigator hover-class="none" class="goods" :url="'/pages/goods/detail/detail?sku_id=' + skuId">
														<image :src="$util.img(goodsSkuDetail.sku_image)" @error="imageError()" />
														<text>¥{{ goodsSkuDetail.price }}</text>
													</navigator>
													<view class="iconfont iconadd1 ns-text-color-gray"></view>
													<block v-for="(goods, goods_index) in item.bundling_goods" :key="goods_index">
														<template v-if="goods_index < 3">
															<navigator hover-class="none" class="goods" :url="'/pages/goods/detail/detail?sku_id=' + goods.sku_id">
																<image :src="$util.img(goods.sku_image)" @error="bundlingImageError(index, goods_index)" />
																<text>¥{{ goods.price }}</text>
															</navigator>
														</template>
													</block>
												</view>
											</view>
										</scroll-view>
									</block>
								</scroll-view>
								<view class="button-box"><button type="primary" @click="closeBundlingPopup()">确定</button></view>
							</view>
						</uni-popup>
					</view>
				</view>

				<!-- 店铺信息 -->
				<block v-if="Development">
					<view class="group-wrap" v-if="preview == 0">
						<view class="shop-wrap">
							<navigator hover-class="none" :url="'/otherpages/shop/index/index?site_id=' + shopInfo.site_id" class="box">
								<view class="shop-logo">
									<image v-if="shopInfo.avatar" :src="$util.img(shopInfo.avatar)" @error="shopInfo.avatar = $util.getDefaultImage().default_shop_img"
									 mode="aspectFit" />
									<image v-else :src="$util.getDefaultImage().default_shop_img" mode="aspectFit" />
								</view>
								<view class="shop-info">
									<text>{{ shopInfo.site_name }}</text>
									<view class="description" v-if="shopInfo.seo_description">{{ shopInfo.seo_description }}</view>
								</view>
							</navigator>
							<navigator hover-class="none" :url="'/otherpages/shop/index/index?site_id=' + shopInfo.site_id" class="box shop-score">
								<text>商品描述{{ shopInfo.shop_desccredit }}</text>
								<text>卖家服务{{ shopInfo.shop_servicecredit }}</text>
								<text>发货速度{{ shopInfo.shop_deliverycredit }}</text>
							</navigator>
							<view class="box">
								<view class="goods-action">
									<navigator hover-class="none" class="ns-text-color ns-border-color" :url="'/otherpages/shop/list/list?site_id=' + shopInfo.site_id">
										全部商品
									</navigator>
									<navigator hover-class="none" class="ns-text-color ns-border-color" :url="'/otherpages/shop/index/index?site_id=' + shopInfo.site_id">
										查看店铺
									</navigator>
								</view>
							</view>
						</view>
					</view>
				</block>

				<!-- 商品评价 -->
				<view class="group-wrap" v-if="preview == 0 && isShowEvaluate">
					<view class="goods-evaluate">
						<view class="tit">
							<view>
								<text>商品评价（{{ goodsSkuDetail.evaluate }}）</text>
							</view>
							<navigator class="ns-text-color" hover-class="none" :url="'/otherpages/goods/evaluate/evaluate?goods_id=' + goodsSkuDetail.goods_id">
								<text>查看更多</text>
								<text class="iconfont iconright"></text>
							</navigator>
						</view>
						<view class="evaluate-item" v-if="goodsEvaluate.content">
							<view class="evaluator">
								<view class="evaluator-face">
									<image v-if="goodsEvaluate.member_headimg" :src="$util.img(goodsEvaluate.member_headimg)" @error="goodsEvaluate.member_headimg = $util.getDefaultImage().default_headimg"
									 mode="aspectFill" />
									<image v-else :src="$util.getDefaultImage().default_headimgsss" @error="goodsEvaluate.member_headimg = $util.getDefaultImage().default_headimg"
									 mode="aspectFill" />
								</view>
								<text class="evaluator-name">{{ goodsEvaluate.member_name }}</text>
							</view>
							<view class="cont">{{ goodsEvaluate.content }}</view>
							<view class="evaluate-img" v-if="goodsEvaluate.images">
								<view class="img-box" v-for="(item, index) in goodsEvaluate.images" :key="index" @click="previewEvaluate(index, 'images')">
									<image :src="$util.img(item)" mode="aspectFit" />
								</view>
							</view>
							<view class="time">
								<text>{{ $util.timeStampTurnTime(goodsEvaluate.create_time) }}</text>
								<text>{{ goodsEvaluate.sku_name }}</text>
							</view>
						</view>
						<view class="evaluate-item-empty" v-else>该商品暂无评价哦</view>
					</view>
				</view>
				<!-- 详情 -->
				<view class="goods-detail-tab">
					<view class="detail-tab flex-center" v-if="isShowDetailTab" :style="'top:' +statusBarHeight+'px;'">
						<view class="tab-item" :class="detailTab == 'productDetail' ? 'active ns-bg-before' : ''" @click="toPoint"
						 data-id="productDetail">商品详情</view>
						<view class="tab-item" :class="detailTab == 'productSale' ? 'active ns-bg-before' : ''" @click="toPoint" data-id="productSale" v-if="afterSale">售后保障</view>
						<view class="tab-item" :class="detailTab == 'productServe' ? 'active ns-bg-before' : ''" @click="toPoint" data-id="productServe" v-if="service">服务说明</view>
					</view>
					<view class="detail-content">
						<view class="detail-content-item" id="productDetail">
							<view class="goods-details-title">
								<view></view>
								<view>图文详情</view>
								<view></view>
							</view>
							<view class="goods-details" v-if="goodsSkuDetail.goods_content">
<!--								<rich-text :nodes="goodsSkuDetail.goods_content"></rich-text>-->
                <mphtml  :content="goodsSkuDetail.goods_content" :preview-img="true"/>
							</view>
							<view class="goods-details active" v-else>该商家暂无上传相关详情哦！</view>
						</view>
            <diy-goods-detail-more-goodies :sku_id="skuId"/>
						<view class="detail-content-item" id="productSale" v-if="afterSale">
							<view class="goods-details-title">
								<view></view>
								<view>售后保障</view>
								<view></view>
							</view>
							<view class="goods-details">
								<rich-text :nodes="afterSale"></rich-text>
							</view>
<!--							<view class="goods-details active" v-else>该商品暂无相关售后哦！</view>-->
						</view>
						<view class="detail-content-item" id="productServe" v-if="service">
							<view class="goods-details-title">
								<view></view>
								<view>服务说明</view>
								<view></view>
							</view>
							<view class="goods-details">
								<rich-text :nodes="service"></rich-text>
							</view>
<!--							<view class="goods-details active" v-else>该商品暂无相关服务说明哦！</view>-->
						</view>
					</view>
				</view>

				<!--		<view v-if="preview == 0"><nsGoodsRecommend ref="goodrecommend"></nsGoodsRecommend></view>-->

				<!-- SKU选择 -->
				<ns-goods-sku ref="goodsSku" @refresh="refreshGoodsSkuDetail" :goods-detail="goodsSkuDetail" :pintuan-info="pintuan_info"
				 :entrance="goodsSkuDetail.maidou_tag"></ns-goods-sku>

				<!-- 海报 -->
				<view @touchmove.prevent.stop>
					<uni-popup ref="posterPopup" type="bottom" class="poster-layer">
						<template v-if="poster != '-1'">
							<view :style="{ height: posterHeight > 0 ? posterHeight + 80 + 'px' : '' }">
								<view class="image-wrap">
									<image :src="$util.img(poster)" :style="{ height: posterHeight > 0 ? posterHeight + 'px' : '' }" />
								</view>
								<!-- #ifdef MP || APP-PLUS  -->
								<view class="save" @click="saveGoodsPoster()">保存图片</view>
								<!-- #endif -->
								<!-- #ifdef H5 -->
								<view class="save">长按保存图片</view>
								<!-- #endif -->
							</view>
							<view class="close iconfont iconclose" @click="closePosterPopup()"></view>
						</template>
						<view v-else class="msg">{{ posterMsg }}</view>
					</uni-popup>
				</view>

				<!-- 分享弹窗 -->
				<!-- <view @touchmove.prevent.stop>
					<uni-popup ref="sharePopup" type="bottom" class="share-popup">
						<view>
							<view class="share-title">分享</view>
							<view class="share-content"> -->
				<!-- #ifdef MP -->
				<!-- <view class="share-box">
									<button class="share-btn" :plain="true" open-type="share">
										<view class="iconfont iconiconfenxianggeihaoyou"></view>
										<text>分享给好友</text>
									</button>
								</view> -->
				<!-- #endif -->
				<!-- 	<view class="share-box" @click="openPosterPopup">
									<button class="share-btn" :plain="true">
										<view class="iconfont iconpengyouquan"></view>
										<text>生成分享海报</text>
									</button>
								</view>
							</view>
							<view class="share-footer" @click="closeSharePopup"><text>取消分享</text></view>
						</view>
					</uni-popup>
				</view> -->
				<ns-login ref="login"></ns-login>
			</view>
		</view>
		<loading-cover ref="loadingCover"></loading-cover>
		<!-- 商品底部导航 -->
		<ns-goods-action>
			<template v-if="goodsSkuDetail.goods_state == 1 && goodsSkuDetail.verify_state == 1">

				<ns-goods-action-icon text="首页" icon="iconshouye" :imgicon="$util.img('public/static/youpin/home-icon.png')" @click="goHome" />
				<!-- <ns-goods-action-icon :text="whetherCollection == 1 ? '已收藏' :'收藏'" :imgicon="whetherCollection == 1 ? $util.img('public/static/youpin/collect-has-icon.png') : $util.img('public/static/youpin/collect-icon.png')" @click="editCollection()"/> -->
				<ns-goods-action-icon text="客服" icon="iconkefu" v-if="addonIsExit.servicer" @click="$util.getCustomerService()" />
				<!--				<ns-goods-action-icon text="客服" icon="iconkefu" v-else open-type="contact" :send-data="contactData" />-->
				<ns-goods-action-icon text="购物车" icon="icongouwuche" :imgicon="$util.img('public/static/youpin/shop-icon.png')"
				 :corner-mark="cartCount > 0 ? cartCount + '' : ''" @click="goCart" />
				<template v-if="goodsSkuDetail.stock">
					<ns-goods-action-button class="goods-action-button" :text="'¥ ' + goodsSkuDetail.retail_price" text-price="单独购买"
					 :class="goodsSkuDetail.is_single_buy == 1 ? 'active1' : 'active1'" background="#FFF3F3" :disabledText="'¥ ' + goodsSkuDetail.retail_price"
					 :disabled="goodsSkuDetail.stock ? false : true" @click="buyNow" />
					<ns-goods-action-button class="goods-action-button" :class="goodsSkuDetail.is_single_buy == 1 ? 'active2' : 'active4'"
					 :text="'¥ ' + pintuan_info.pintuan_price" text-price="开团" :disabledText="'¥ ' + pintuan_info.pintuan_price"
					 :disabled="pintuan_info.stock ? false : true" @click="pintuan" />
				</template>
				<template v-else>
					<ns-goods-action-button class="goods-action-button active3" text="开团已满，请参团" v-if="pintuanList.length>0" @click="$refs.popupProceedPintuan.open()" />
					<ns-goods-action-button class="goods-action-button active4" disabledText="已售罄" :disabled='true' v-if="pintuanList.length == 0" />
				</template>
			</template>
			<template v-else>
				<ns-goods-action-button class="goods-action-button active3" disabled-text="该商品已下架" :disabled="true" />
			</template>
		</ns-goods-action>
		<!-- 授权登录弹窗 -->
		<yd-auth-popup ref="ydauth"></yd-auth-popup>
    <ns-login ref="login"></ns-login>
    <uni-coupon-pop ref="couponPop"></uni-coupon-pop>
		<!-- 返回顶部按钮 -->
		<image :src="$util.img('public/static/youpin/to-top.png')" class="to-top" v-show="showTop" @click="scrollToTopNative"></image>
		<!-- 分享的页面不是用户绑定的id,提示无法购物弹窗 -->
		<uni-popup ref="popupBan" :maskClick="false">
			<view class="popup-dialog">
				<view class="popup-dialog-header">提示</view>
				<view class="popup-dialog-body">哎哟~系统好像出了点问题，暂时不能支付，请联系客服。</view>
				<view class="popup-dialog-footer">
					<button class="button red" @click="$refs.popupBan.close()">知道了</button>
				</view>
			</view>
		</uni-popup>

		<!--   开团类型选择弹窗 start -->
		<uni-popup ref="popupOpenPintuan" type="bottom" class="choose-pintuan-type-parent" :custom="true">
			<view class="choose-pintuan-type">
				<view class="choose-pintuan-type-title">团购类型：</view>
				<view class="choose-pintuan-type-list">
					<text class="active">{{pintuan_info.pintuan_num}}人团</text>
				</view>
				<view class="choose-pintuan-type-op">
					<button v-on:click="closePinTuanPopup">确定</button>
				</view>
			</view>
		</uni-popup>
		<!--   开团类型选择弹窗 end -->

		<!--  正在进行中拼团的弹窗 start -->
		<uni-popup ref="popupProceedPintuan" type="bottom" class="proceed-pintuan-parent" :maskClick="true">
			<view @touchmove.stop.prevent="()=>{}">
				<view class="proceed-pintuan-title">
					<text class="proceed-pintuan-title-left">正在拼团</text>
          <view class="proceed-pintuan-title-right">
            <text class="proceed-pintuan-title-right-number">{{pintuan_info.pintuan_num}}人团</text>
            <text class="proceed-pintuan-title-right-yet">已开{{pintuan_info.order_num}}团</text>
          </view>
				</view>
				<scroll-view scroll-y="true" class="proceed-pintuan" :scroll-into-view="popUpPintuanInnerRule">
					<view class="proceed-pintuan-info">
						<view class="proceed-pintuan-info-one" v-for="(item,index) in (!isShowPintuanMore ? pintuanList : pintuanList.slice(0,3))" v-bind:key="index">
							<view class="proceed-pintuan-info-one-left">
                <view class="proceed-pintuan-info-one-left-img">
                  <image :src="$util.img(item.head_member_img)" @error="()=>{this.pintuanList[index].head_member_img=this.$util.getDefaultImage().default_goods_img}"></image>
                  <view class="proceed-pintuan-info-one-left-img-desc"><text>团长</text></view>
                </view>
								<view class="proceed-pintuan-info-one-left-info">
									<view class="proceed-pintuan-info-one-left-info-name">{{item.head_nickname}}</view>
								</view>
								<view class="proceed-pintuan-info-one-left-name">
									<view class="proceed-pintuan-info-one-left-name-text">还差<text>{{item.diff_num}}</text>人拼成</view>
									<view class="proceed-pintuan-info-one-left-name-time">剩余<countdown-timer ref="proceedCountdown" :time="item.distance_time*1000"
										 autoStart @finish="onFinish(item.group_id)"></countdown-timer>
									</view>
								</view>
							</view>
              <view class="proceed-pintuan-info-one-right">
                <button class="proceed-pintuan-info-one-right-join" v-on:click="toPintuanDetail(item)">参团</button>
                <!-- #ifdef MP-WEIXIN -->
                <button class="proceed-pintuan-info-one-right-share" open-type="share" :data-join="index">分享</button>
                <!-- #endif -->
                <!-- #ifdef H5 -->
                <button class="proceed-pintuan-info-one-right-share" @click="toShareJoin(index)">分享</button>
                <!-- #endif -->
              </view>
						</view>
						<view class="proceed-pintuan-info-loading" @click="showPintuanMore" v-if="isShowPintuanMore">
							<text>查看更多</text>
              <uni-icons type="arrowdown" color="rgba(166, 166, 166, 1)" size="12"></uni-icons>
						</view>
					</view>
          <!--   拼团玩法 start   -->
          <view class="group-wrap">
            <view class="pintuan-method" style="background-color: transparent">
              <view class="pintuan-method-title">
                <view class="pintuan-method-title-left">
                  <text class="pintuan-method-title-left-tip">拼团奖励</text>
                  <text class="pintuan-method-title-left-desc">返现</text>
                </view>
                <view class="pintuan-method-title-right">
                  <view class="pintuan-method-title-right-more" @click="scrollIntoPintunRule">了解规则
                    <uni-icons type="help-filled" color="#ccc"></uni-icons>
                  </view>
                </view>
              </view>
            </view>
            <view class="pintuan-space" style="background-color: transparent;">
              <view class="pintuan-flow">
                <view class="pintuan-flow-one">
                  <image :src="$util.img('public/static/youpin/pintuan/pintuan-flow-one.png')" alt="" />
                  <view class="pintuan-flow-one-info">
                    <view>参与拼团</view>
                    <view><text class="pintuan-flow-one-info-red">{{pintuan_info.pintuan_num}}</text>人成团</view>
                  </view>
                </view>
                <view class="pintuan-flow-one">
                  <image :src="$util.img('public/static/youpin/pintuan/pintuan-flow-two.png')" alt="" />
                  <view class="pintuan-flow-one-info">
                    <view><text class="pintuan-flow-one-info-red">{{pintuan_info.winning_num}}</text>人拼中发货</view>
                    <view :class="{'hide-sales':pintuan_info.pintuan_num-pintuan_info.winning_num<1}"><text class="pintuan-flow-one-info-red">{{pintuan_info.pintuan_num-pintuan_info.winning_num}}</text>人未中退款</view>
                  </view>
                </view>
                <view class="pintuan-flow-one">
                  <image :src="$util.img('public/static/youpin/pintuan/pintuan-flow-three.png')" alt="" />
                  <view class="pintuan-flow-one-info pintuan-flow-one-info-other">
                    <!--								<view>邀请好友参团可得</view>-->
                    <!--								<view><text class="pintuan-flow-one-info-price"><text>￥</text>{{pintuan_info.not_win_money}}</text></view>-->
                    <view v-for="(item,index) in pintuan_info.play_list" :key="index">
                      {{item.reward_str}}
                      <text class="pintuan-flow-one-info-price"><text>￥</text>{{item.money}}</text>
                    </view>
                  </view>
                </view>
              </view>
            </view>
          </view>
          <!--   拼团玩法 end   -->
          <view class="pintuan-rule pintuan-rule-inner">
            <view class="pintuan-rule-info" id="popUpPintuanInnerRule">
              <rich-text :nodes="UnchangeContent(pintuan_info.pintuan_rule)"></rich-text>
            </view>
            <!-- <button class="pintuan-rule-op" @click="()=>this.$refs.popupPintuanRule.close()">我知道了</button> -->
          </view>
				</scroll-view>
        <view class="proceed-pintuan-op">
          <ns-goods-action>
            <template v-if="goodsSkuDetail.goods_state == 1 && goodsSkuDetail.verify_state == 1">
              <template v-if="goodsSkuDetail.stock">
                <ns-goods-action-button class="goods-action-button" :class="goodsSkuDetail.is_single_buy == 1 ? 'active1' : 'active1'" background="#FFF3F3"
                                        :text="'¥ ' + pintuan_info.pintuan_price" text-price="自己开团" :disabledText="'¥ ' + pintuan_info.pintuan_price"
                                        :disabled="pintuan_info.stock ? false : true" @click="pintuan" />
                <!-- #ifdef MP-WEIXIN -->
                <button class="goods-action-button goods-action-button-share" :class="goodsSkuDetail.is_single_buy == 1 ? 'active2' : 'active4'" :disabled="pintuanList.length ? false : true"
                        open-type="share" :data-join="0"
                >
                  <text class="goods-action-button-share-one">{{pintuan_info.play_list&&pintuan_info.play_list.filter(item=>item.reward_type=='invite_award').length ? '邀请成团奖励¥ ' + pintuan_info.play_list.filter(item=>item.reward_type=='invite_award')[0].money : ''}}</text>
                  <text class="goods-action-button-share-two">分享邀请好友</text>
                </button>
                <!-- #endif -->
                <!-- #ifdef H5 -->
                <ns-goods-action-button class="goods-action-button" :class="goodsSkuDetail.is_single_buy == 1 ? 'active2' : 'active4'"
                                        :text="pintuan_info.play_list&&pintuan_info.play_list.filter(item=>item.reward_type=='invite_award').length ? '邀请成团奖励¥ ' + pintuan_info.play_list.filter(item=>item.reward_type=='invite_award')[0].money : ''"
                                        text-price="分享邀请好友"
                                        :disabledText="pintuan_info.play_list&&pintuan_info.play_list.filter(item=>item.reward_type=='invite_award').length ? '邀请成团奖励¥ ' + pintuan_info.play_list.filter(item=>item.reward_type=='invite_award')[0].money : ''"
                                        :disabled="pintuanList.length ? false : true" @click="toShareJoin(0)" />
                <!-- #endif -->

              </template>
              <template v-else>
                <ns-goods-action-button class="goods-action-button active3" text="开团已满，请参团" v-if="pintuanList.length>0" />
                <ns-goods-action-button class="goods-action-button active4" disabledText="已售罄" :disabled='true' v-if="pintuanList.length == 0" />
              </template>
            </template>
            <template v-else>
              <ns-goods-action-button class="goods-action-button active3" disabled-text="该商品已下架" :disabled="true" />
            </template>
          </ns-goods-action>
        </view>
			</view>
		</uni-popup>

		<!--  正在进行中拼团的弹窗 end -->

    <!--  在途拼团信息提醒 start  -->
    <swiper class="grouping-swiper" circular  :autoplay="true" :vertical="true" v-show="isShowDetailTab">
      <swiper-item v-for="(item,index) in pintuanList" :key="index">
        <view class="grouping-in-progress">
          <view class="grouping-in-progress-left">
            <image class="grouping-in-progress-left-head"
                   :src="item.head_member_img ? $util.img(item.head_member_img) : $util.getDefaultImage().default_headimg"
                   mode="aspectFill"
                   @error="item.head_member_img = $util.getDefaultImage().default_headimg"></image>
            <view class="grouping-in-progress-left-name">
              <text>{{item.head_nickname}}</text>
              <text>正在拼该商品</text>
            </view>
          </view>
          <view class="grouping-in-progress-right">
            <button class="grouping-in-progress-right-btn" @click="toPintuanDetail(item)">去参团</button>
          </view>
        </view>
      </swiper-item>
    </swiper>
    <!--  在途拼团信息提醒 end  -->

		<!--  拼团规则说明 start -->
		<uni-popup ref="popupPintuanRule" type="bottom" class="pintuan-rule-parent">
			<view class="pintuan-rule">
				<view class="sku-close iconfont iconclose" @click="()=>$refs.popupPintuanRule.close()"></view>
				<view class="pintuan-rule-title">拼团规则说明</view>
				<scroll-view :scroll-y="true" class="pintuan-rule-info">
					<rich-text :nodes="UnchangeContent(pintuan_info.pintuan_rule)"></rich-text>
				</scroll-view>
				<!-- <button class="pintuan-rule-op" @click="()=>this.$refs.popupPintuanRule.close()">我知道了</button> -->
			</view>
		</uni-popup>
		<!--  拼团规则说明 end -->
		<!-- 拼团未支付提示 -->
		<uni-popup ref="popupToList" class="my-popup-dialog">
			<view class="popup-box">
				<view class="popup-box-body">{{pintuan_info.group_limit}}</view>
				<view class="popup-box-footer">
					<button class="red-botton" @click="topituanOrderList">查看我的拼团</button>
				</view>
			</view>
		</uni-popup>
    <diy-floating-rolling-order positionType="pintuan_detail"
                                :top="$util.getPlatform()=='weapp' ? (isShowDetailTab ? '260rpx' : '104rpx') : $util.getPlatform()=='h5' ? isShowDetailTab ? '80rpx' : '20rpx' : ''"
                                :left="$util.getPlatform()=='weapp' ? '70rpx' : ''"></diy-floating-rolling-order>
    <!--  升级vip的按钮  -->
<!--    <yp-upgrade-vip-button :is-show="!goodsSkuDetail.is_shop_owner" :positions="{position: 'fixed',right: 0,bottom: '600rpx'}"></yp-upgrade-vip-button>-->
		<diy-share  :canvasOptions="canvasOptions_share" ref="sharePopup_share"
		 :sharePopupOptions="sharePopupOptions_share" @childByValue="getShareImg"></diy-share>
    <diy-share-navigate-h5 ref="shareNavigateH5"></diy-share-navigate-h5>
	</view>
</template>

<script>
	import nsGoodsAction from '@/components/ns-goods-action/ns-goods-action.vue';
	import nsGoodsActionIcon from '@/components/ns-goods-action-icon/ns-goods-action-icon.vue';
	import nsGoodsActionButton from '@/components/ns-goods-action-button/ns-goods-action-button.vue';
	import uniPopup from '@/components/uni-popup/uni-popup.vue';
	import nsGoodsSku from '@/components/ns-goods-sku/ns-goods-sku.vue';
	import nsGoodsRecommend from '@/components/ns-goods-recommend/ns-goods-recommend.vue';
	import uniCountDown from '@/components/uni-count-down/uni-count-down.vue';
	import countdownTimer from '@/components/countdown-timer/countdown-timer.vue';
	import detail from '../public/js/detail.js';
	import scroll from '@/common/mixins/scroll-view.js';
	import globalConfig from 'common/mixins/golbalConfig.js'
	import diyShare from '@/components/diy-share/diy-share.vue';
  import diyShareNavigateH5 from '@/components/diy-share-navigate-h5/diy-share-navigate-h5'
  import appInlineH5 from "../../../common/mixins/appInlineH5";
  import uniNavBar from '@/components/uni-nav-bar/uni-nav-bar.vue';
  import uniIcons from "@/components/uni-icons/uni-icons.vue";
  import diyFloatingRollingOrder from '@/components/diy-floating-rolling-order/diy-floating-rolling-order.vue';
  import mphtml from "../../../components/mp-html/mp-html.vue";
  // #ifdef H5
  import {isOnXianMaiApp} from "../../../common/js/h5/appOP";
  // #endif
  import diyGoodsDetailMoreGoodies
    from "../../../components/diy-goods-detail-more-goodies/diy-goods-detail-more-goodies.vue";
	export default {
		components: {
      diyGoodsDetailMoreGoodies,
      mphtml,
      uniIcons,
			nsGoodsAction,
			nsGoodsActionIcon,
			nsGoodsActionButton,
			uniPopup,
			nsGoodsSku,
			nsGoodsRecommend,
			uniCountDown,
			countdownTimer,
			diyShare,
      diyShareNavigateH5,
      uniNavBar,
      diyFloatingRollingOrder
		},
		data() {
			return {
				isShowEvaluate: false,
				isShowDetailTab: false,
        // #ifdef H5
        isOnXianMaiApp:isOnXianMaiApp,
        // #endif
		copytextShow: false
			};
		},
		onShow() {
			if (uni.getStorageSync('is_register')) {
				this.$util.toShowCouponPopup(this)
				uni.removeStorageSync('is_register');
			}
		},
		computed: {
			//是否为需发布状态
			Development() {
				// return this.$store.state.Development;
				return false
			},
			themeStyle() {
				return 'theme-' + this.$store.state.themeStyle;
			},
			addonIsExit() {
				return this.$store.state.addonIsExit;
			},
		},
		mixins: [detail, scroll, globalConfig,appInlineH5],
	    methods: {
			longpress() {
				this.copytextShow = true
			},
			copyCallback() {
				this.copytextShow = false
			}
		}
	};
</script>

<style lang="scss">
	@import '../public/css/detail.scss';
	.ns-text-color {
		color: #FF1010 !important;
	}
	.sku-name-box {
	  width: 100%;
	  position: relative;
  }
   .showCopybox {
	  position: absolute;
	  top: -66rpx;
	  left: 45%;
	.copytext {
	  text-align: center;
	  border-radius: 10rpx;
	  color: #fff;
	  font-size: 24rpx;
	  position: relative;
	  &::after {
		content: '';
		display: block;
		width: 20rpx;
		height: 20rpx;
		background: #1F2022;
		position: absolute;
		bottom: -16rpx;
		left: 30rpx;
		transform: rotate(45deg);
	  }
	}
	.fuzhi {
		border-right: 1px solid rgba(255,255,255,0.7);
		padding:16rpx 24rpx;
		background: #1F2022;
		border-radius: 5px 0 0 5px;
	}
	.quxiao {
		padding:16rpx 24rpx;
		background: #1F2022;
		border-radius: 0 5px 5px 0;
	}
  }
</style>
<style scoped>
	/deep/ .uni-video-cover {
		background: none;
	}

	/deep/ .uni-video-cover-duration {
		display: none;
	}

	/deep/ .uni-video-cover-play-button {
		border-radius: 50%;
		border: 4rpx solid #fff;
		width: 120rpx;
		height: 120rpx;
		background-size: 30%;
	}

	.poster-layer>>>.uni-popup__wrapper-box {
		max-height: initial !important;
	}

	/deep/ .sku-layer .uni-popup__wrapper-box {
		overflow-y: initial !important;
	}

	.goods-discount .countdown .clockrun>>>.uni-countdown__number {
		min-width: 32rpx;
		height: 32rpx;
		text-align: center;
		line-height: 32rpx;
		background: #000;
		/* // #690b08 */
		border-radius: 4px;
		display: inline-block;
		padding: 4rpx;
		margin: 0;
		border: none;
	}

	.goods-discount .countdown .clockrun>>>.uni-countdown__splitor {
		width: 10rpx;
		height: 32rpx;
		line-height: 36rpx;
		text-align: center;
		display: inline-block;
		color: #000;
	}

	.goods-discount .countdown .clockrun>>>.uni-countdown__splitor.day {
		width: initial;
	}

	/deep/ .uni-popup__wrapper.uni-custom .uni-popup__wrapper-box {
		max-height: unset !important;
	}

	/deep/ .goods-action-button.active1 {
		padding-left: 10px;
	}

	/deep/ .goods-action-button.active2 {
		padding-right: 10px;
	}

	/deep/ .goods-action-button.active3 {
		padding: 0 10px;
	}

	/deep/ .goods-action-button.active4 {
		padding: 0 10px;
	}

	/deep/ .goods-action-button.active1 .action-buttom-wrap {
		color: #F2270C;
		border: 1px solid #F2270C;
		border-radius: 40rpx;
		box-sizing: border-box;
		margin-right: 14rpx;
	}

	/deep/ .goods-action-button.active2 .action-buttom-wrap {
		border-radius: 40rpx;
		box-sizing: border-box;
	}

	/deep/ .goods-action-button.active3 .action-buttom-wrap {
		border-radius: 36px;
		margin: 20rpx 0;
	}

	/deep/ .goods-action-button.active4 .action-buttom-wrap {
		border-radius: 36px;
	}

	/* 底部分享按钮 */
	.distributor-share-button {
		width: auto !important;
		height: auto !important;
		border: none;
		margin: 0;
		line-height: auto;
		;
		padding: 0;
	}

	.disabled-share-btn {
		background-color: transparent !important;
	}

	.to-top {
		width: 144rpx;
		height: 152rpx;
		position: fixed;
		right: 0;
		bottom: 380rpx;
	}
</style>
<style lang="scss" scoped>
	.goods_tag {
		padding: 5rpx;
		background: linear-gradient(270deg, #FE5838 0%, #FB331D 100%);
		border-radius: 8rpx;
		font-size: 20rpx;
		color: #fff;
		text-align: center;
		line-height: 24rpx;
		margin-right: 10rpx;
	}

	.pintuan-price {
		height: 170rpx;
    padding-bottom: 58rpx;
		//background: linear-gradient(-84.104752310177deg, rgba(255, 79, 79, 1) 0%, rgba(255, 0, 0, 1) 100%);
    background: rgba(246, 93, 114, 0.95);
		//border-radius: 20rpx 20rpx 0 0;
		box-sizing: border-box;
		padding-right: 25rpx;
		display: flex;
		justify-content: space-between;
		align-items: center;

		&-left {
			width: 440rpx;
			height: 108rpx;
			//background: linear-gradient(90deg, #F84346 0%, #E94841 100%);
			//border-radius: 20rpx;
			padding-left: 28rpx;
			display: flex;
			justify-content: center;
			flex-direction: column;

			&-one {
				display: flex;
				align-items: center;

				&-tag {
					width: 80rpx;
					height: 28rpx;
					line-height: 28rpx;
					background: #FEEEEE;
					border-radius: 14rpx;
					font-size: 20rpx;
					font-weight: 500;
					color: #F84346;
					text-align: center;
					margin-right: 12rpx;
				}

				&-price {
					font-size: 58rpx;
					font-weight: bold;
					color: #FFFFFF;
					line-height: 1;

					text {
						font-size: 34rpx;
					}
				}

				&-unprice {
					font-size: 22rpx;
					font-weight: 500;
					text-decoration: line-through;
					color: #FEEEEE;
					align-self: flex-end;
					margin-left: 7rpx;
				}
			}
		}

    &-right{
      border-radius: 40px;
      background: rgba(255, 255, 255, 0.1);
      height: 32rpx;
      display: flex;
      align-items: center;
      padding-right: 20rpx;
      box-sizing: border-box;
      &-number{
        font-size: 24rpx;
        font-weight: 400;
        line-height: 32rpx;
        color: rgba(246, 93, 114, 1);
        padding: 0 20rpx;
        box-sizing: border-box;
        border-radius: 40rpx;
        background: rgba(255, 255, 255, 1);
      }
      &-yet{
        font-size: 24rpx;
        font-weight: 400;
        line-height: 32rpx;
        color: rgba(255, 255, 255, 1);
        margin-left: 16rpx;
      }
    }
	}

	.pintuan-list {
		background: #FFFFFF;
		//border-radius: 20rpx;

		&-title {
			box-sizing: border-box;
			height: 88rpx;
			padding: 0 24rpx;
			display: flex;
			justify-content: space-between;
			align-items: center;

			&-left {
				display: flex;
				align-items: center;

				&-fill {
					width: 6rpx;
					height: 32rpx;
					background: #F84346;
					border-radius: 3rpx;
					display: inline-block;
				}

				&-tip {
					font-size: 30rpx;
          font-weight: 400;
					color: #383838;
					margin-left: 14rpx;
				}
			}

			&-right {
				&-more {
					font-size: 26rpx;
					font-weight: 500;
					color: #383838;
				}
			}
		}

		&-info {
			padding: 0 24rpx;
			box-sizing: border-box;

			&-one {
				//height: 100rpx;
				box-sizing: border-box;
        padding-bottom: 32rpx;

				&:not(:first-child) {
					//border-top: 2rpx solid #eeeeee;
				}

				display: flex;
				justify-content: space-between;
				align-items: center;

				&-left {
					display: flex;
					align-items: center;
          &-img{
            width: 92rpx;
            height: 92rpx;
            position: relative;
            image {
              width: 92rpx;
              height: 92rpx;
              border-radius: 50%;
            }
            &-desc{
              position: absolute;
              left: 50%;
              bottom: -12rpx;
              transform: translateX(-50%);
              background-color: white;
              height: 32rpx;
              width: 56rpx;
              border-radius: 40rpx;
              display: flex;
              justify-content: center;
              align-items: center;
              &-text{
                width: 48rpx;
                height: 24rpx;
                border-radius: 40rpx;
                background: rgba(246, 93, 114, 1);
                font-size: 16rpx;
                font-weight: 400;
                line-height: 24rpx;
                color: rgba(255, 255, 255, 1);
                text-align: center;
              }
            }
          }

					&-name {
						font-size: 28rpx;
						font-weight: bold;
						color: #333333;
						margin-left: 15rpx;
						overflow: hidden;
						white-space: nowrap;
						text-overflow: ellipsis;
						max-width: 260rpx;
					}
				}

				&-right {
					display: flex;
					align-items: center;

					&-one {
						display: flex;
						justify-content: center;
						flex-direction: column;

						&-count {
							font-size: 24rpx;
              color: rgba(56, 56, 56, 1);
              font-weight: 400;

							text {
								color: rgba(246, 93, 114, 1);
                font-weight: 700;
							}
						}

						&-time {
							font-size: 24rpx;
							font-weight: 500;
							color: #666666;
							display: flex;
							align-items: center;
						}
					}

					&-two {
            width: 160rpx;
            height: 72rpx;
            border-radius: 40rpx;
            background: rgba(246, 93, 114, 1);
            font-size: 28rpx;
            font-weight: 400;
            line-height: 40rpx;
            color: rgba(255, 255, 255, 1);
            display: flex;
            justify-content: center;
            align-items: center;
						margin: 0;
						margin-left: 18rpx;
					}
				}
			}
		}
	}

	.pintuan-list-info-one-right-one-time /deep/.custom view {
		font-size: 24rpx;
		font-weight: 500;
		color: #666666;
	}

	.proceed-pintuan-info-one-left-name-time /deep/.custom view {
		font-size: 24rpx;
		font-weight: 500;
		color: #666666;
	}

	.pintuan-method {
		background: #FFFFFF;
		//border-radius: 20rpx;

		&-title {
			box-sizing: border-box;
			height: 88rpx;
      padding: 0 32rpx 0 28rpx;
			display: flex;
			justify-content: space-between;
			align-items: center;

			&-left {
				display: flex;
				align-items: center;

				&-tip {
          font-size: 30rpx;
          font-weight: 400;
          line-height: 44rpx;
          color: rgba(56, 56, 56, 1);
				}
        &-desc{
          width: 48rpx;
          height: 24rpx;
          border-radius: 40rpx;
          background: rgba(246, 93, 114, 1);
          border: 2rpx solid rgba(255, 255, 255, 1);
          font-size: 16rpx;
          font-weight: 400;
          color: rgba(255, 255, 255, 1);
          display: flex;
          justify-content: center;
          align-items: center;
        }
			}

			&-right {
				&-more {
					font-size: 26rpx;
					font-weight: 500;
          color: rgba(56, 56, 56, 1);
					display: flex;
					align-items: center;

					image {
						width: 30rpx;
						height: 30rpx;
						margin-left: 12rpx;
					}
				}
			}
		}

		&-info {
			display: flex;
			align-items: center;
			height: 110rpx;
			padding: 0 40rpx;
			padding-bottom: 10rpx;

			&-node {
				display: flex;
				align-items: center;
				position: relative;

				&-order {
					width: 28rpx;
					height: 28rpx;
					line-height: 28rpx;
					background: #CCCCCC;
					border-radius: 50%;
					font-size: 20rpx;
					font-weight: bold;
					color: #FFFFFF;
					text-align: center;
				}

				&-text {
					margin-left: 8rpx;
					font-size: 26rpx;
					font-weight: bold;
					color: #333333;
				}

				&-tip {
					position: absolute;
					left: 8rpx;
					bottom: -28rpx;
					font-size: 22rpx;
					font-weight: 500;
					color: #666666;
					width: 160rpx;
				}
			}

			&-span {
				margin: 0 50rpx;
				color: #cccccc;
			}
		}
	}

	.choose-pintuan-type {
		background: #FFFFFF;
		border-radius: 30rpx 30rpx 0rpx 0rpx;
		padding-top: 40rpx;
		box-sizing: border-box;

		&-title {
			font-size: 36rpx;
			font-weight: bold;
			color: #333333;
			padding-left: 33rpx;
			box-sizing: border-box;
		}

		&-list {
			display: flex;
			flex-wrap: wrap;
			padding-left: 33rpx;
			box-sizing: border-box;
			margin-top: 38rpx;
			min-height: 212rpx;

			text {
				width: 144rpx;
				height: 54rpx;
				line-height: 54rpx;
				color: #333333;
				background: #F2F2F2;
				border-radius: 27rpx;
				font-size: 26rpx;
				font-weight: 500;
				margin-right: 38rpx;
				text-align: center;
				box-sizing: border-box;

				&.active {
					border: 2rpx solid #EB655A;
					color: #F84346;
					background: #FCEDEB;
				}
			}
		}

		&-op {
			height: 98rpx;
			border-top: 2rpx solid #eeeeee;
			box-sizing: border-box;
			display: flex;
			justify-content: center;
			align-items: center;

			button {
				width: 654rpx;
				height: 80rpx;
				background: #F2270C;
				border-radius: 40rpx;
				font-size: 28rpx;
				font-weight: 500;
				color: #FFFFFF;
			}
		}
	}

	.choose-pintuan-type-parent /deep/.uni-popup__wrapper {
		border-radius: 30rpx 30rpx 0rpx 0rpx;
	}

	.proceed-pintuan-parent /deep/.uni-popup__wrapper {
		border-radius: 40rpx 40rpx 0rpx 0rpx;
	}
  .proceed-pintuan-parent /deep/.uni-popup__wrapper-box{
    background-color: transparent!important;
    padding-bottom: 100rpx;
  }

	.proceed-pintuan {
		width: 750rpx;
		//max-height: 980rpx;
		min-height: 500rpx;
		max-height: 980rpx;
    margin-top: -48rpx;
		background: #FFFFFF;
		border-radius: 40rpx;
		box-sizing: border-box;
		position: relative;
    /deep/ .uni-scroll-view{
      border-radius: 40rpx;
      box-sizing: border-box;
    }

		&-title {
			height: 148rpx;
			display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 32rpx 22rpx 72rpx 40rpx;
      box-sizing: border-box;
			width: 100%;
			z-index: 9999;
      border-radius: 40rpx 40rpx 0 0;
      background: rgba(246, 93, 114, 1);

			&-left {
        font-size: 32rpx;
        font-weight: 700;
        line-height: 44rpx;
        color: rgba(255, 255, 255, 1);
			}
      &-right{
        border-radius: 40px;
        background: rgba(255, 255, 255, 0.1);
        height: 32rpx;
        display: flex;
        align-items: center;
        padding-right: 20rpx;
        box-sizing: border-box;
        &-number{
          font-size: 24rpx;
          font-weight: 400;
          line-height: 32rpx;
          color: rgba(246, 93, 114, 1);
          padding: 0 20rpx;
          box-sizing: border-box;
          border-radius: 40rpx;
          background: rgba(255, 255, 255, 1);
        }
        &-yet{
          font-size: 24rpx;
          font-weight: 400;
          line-height: 32rpx;
          color: rgba(255, 255, 255, 1);
          margin-left: 16rpx;
        }
      }
		}

		&-info {
      padding: 0 30rpx;
      padding-bottom: 24rpx;
      box-sizing: border-box;
      border-bottom: 2rpx solid #eeeeee;
			&-one {
				//height: 110rpx;
				display: flex;
				justify-content: space-between;
				align-items: center;
				box-sizing: border-box;
				padding: 30rpx 0;


				&-left {
					display: flex;
					align-items: center;
          &-img{
            width: 92rpx;
            height: 92rpx;
            position: relative;
            image {
              width: 92rpx;
              height: 92rpx;
              border-radius: 50%;
            }
            &-desc{
              position: absolute;
              left: 50%;
              bottom: -12rpx;
              transform: translateX(-50%);
              background-color: white;
              height: 32rpx;
              width: 56rpx;
              border-radius: 40rpx;
              display: flex;
              justify-content: center;
              align-items: center;
              text{
                width: 48rpx;
                height: 24rpx;
                border-radius: 40rpx;
                background: rgba(246, 93, 114, 1);
                font-size: 16rpx;
                font-weight: 400;
                line-height: 24rpx;
                color: rgba(255, 255, 255, 1);
                text-align: center;
              }
            }
          }


					&-info {
						display: flex;
						flex-direction: column;
						justify-content: center;
            margin-left: 22rpx;

						&-name {
							font-size: 28rpx;
							font-weight: bold;
							color: #333333;
							overflow: hidden;
							white-space: nowrap;
							text-overflow: ellipsis;
							width: 104rpx;
						}
					}

					&-name {
						display: flex;
						flex-direction: column;
						justify-content: center;
						margin-left: 20rpx;

						&-text {
							font-size: 24rpx;
							font-weight: 500;
							color: #333333;
							line-height: 1;
							text-align: right;

							text {
								color: #F84346;
							}
						}

						&-time {
							font-size: 24rpx;
							font-weight: 500;
							color: #666666;
							display: flex;
							justify-content: flex-end;
							align-items: center;
							line-height: 1;
						}
					}
				}

				&-right {
          display: flex;
          justify-content: center;
          align-items: center;
          &-join{
            //width: 120rpx;
            height: 72rpx;
            border-radius: 40rpx;
            background: rgba(246, 93, 114, 0.1);
            border: 2rpx solid rgba(246, 93, 114, 1);
            font-size: 28rpx;
            font-weight: 400;
            line-height: 40rpx;
            color: rgba(246, 93, 114, 1);
            display: flex;
            justify-content: center;
            align-items: center;
            margin: 0;
          }
					&-share{
            width: 120rpx;
            height: 72rpx;
            border-radius: 40rpx;
            background: rgba(246, 93, 114, 1);
            font-size: 28rpx;
            font-weight: 400;
            color: #FFFFFF;
            display: flex;
            justify-content: center;
            align-items: center;
            margin: 0;
            margin-left: 14rpx;
          }
				}
			}

			&-loading {
				display: flex;
				justify-content: center;
        align-items: center;
				box-sizing: border-box;
        margin-top: 20rpx;

				text {
          font-size: 28rpx;
          font-weight: 400;
          line-height: 44rpx;
          color: rgba(166, 166, 166, 1);
				}
			}
		}
    &-op{
      display: flex;
      /deep/ .ns-goods-action.bottom-safe-area{
        padding-bottom: 20rpx;
        padding-bottom: calc(20rpx + constant(safe-area-inset-bottom));
        padding-bottom: calc(20rpx + env(safe-area-inset-bottom));
      }
      /deep/ .action-buttom-wrap.has-second{
        height: 80rpx!important;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        box-sizing: border-box;
      }
    }
	}

	@keyframes loading {
		50% {
			transform: rotate(180deg);
		}

		100% {
			transform: rotate(360deg);
		}
	}

	.proceed-pintuan-parent {
		//position: relative;
	}

	.proceed-pintuan-info-one-left-info-time /deep/.custom view {
		font-size: 22rpx;
		font-weight: 500;
		color: #999999;
	}

	.pintuan-rule {
		// width: 730rpx;
		height: 785rpx;
		background: #FFFFFF;
		border-radius: 20rpx 20rpx 0rpx 0rpx;
		box-sizing: border-box;
		padding: 50rpx 40rpx 30rpx 40rpx;
		// margin-left: 10rpx;
    &-inner{
      height: auto!important;
      .pintuan-rule-info{
        height: auto !important;
      }
    }
		&-title {
			font-size: 36rpx;
			font-weight: bold;
			color: #333333;
			text-align: center;
		}

		&-info {
			font-size: 28rpx;
			font-weight: 500;
			color: #666666;
			height: 580rpx;
			box-sizing: border-box;
		}

		&-op {
			width: 480rpx;
			height: 68rpx;
			background: #F2270C;
			border-radius: 34rpx;
			font-size: 28rpx;
			font-weight: 500;
			color: #FFFFFF;
			margin: 0 auto;
		}
	}
  .pintuan-space{
    background: #ffffff;
    display: flex;
    justify-content: center;
    padding-bottom: 20rpx;
    box-sizing: border-box;
  }
	.pintuan-flow {
		display: flex;
		justify-content: space-between;
		align-items: flex-start;
		box-sizing: border-box;
    width: 690rpx;
    border-radius:10rpx;
    background:rgba(250,250,250,1);
    padding: 40rpx;
    position: relative;

		&-one {
			display: flex;
			flex-direction: column;
			justify-content: center;
			align-items: center;
			position: relative;
      &:nth-child(2){
        width: 160rpx;
      }

			&:nth-child(1) {
				&:after {
					content: '';
					width: 100rpx;
					border-top: 2rpx dashed #CCCCCC;
					position: absolute;
					left: 106rpx;
					top: 44rpx;
				}
			}
      &:nth-child(2) {
        &:after {
          content: '';
          width: 130rpx;
          border-top: 2rpx dashed #CCCCCC;
          position: absolute;
          left: 140rpx;
          top: 44rpx;
        }
      }

			image {
				width: 88rpx;
				height: 88rpx;
				border-radius: 50%;
				margin-bottom: 20rpx;
			}

			&-info {
        &-other{
          display: flex;
          flex-direction: column;
          align-items: flex-start;
        }
				view {
					font-size: 24rpx;
					font-weight: 500;
					color: #333333;
					text-align: center;
				}

				&-red {
					color: #F84346;
				}

				&-price {
					//font-size: 30rpx;
					color: #F84346;
					font-weight: bold;

					text {
						font-size: 24rpx;
					}
				}
			}
		}
	}

	.popup-box {
		overflow: hidden;
		background: #FFFFFF;
		box-sizing: border-box;
	}

	.popup-box-body {
		color: #656565;
		text-align: center;
		padding: 30rpx 30rpx 0 30rpx;
		width: 416rpx;
	}

	.popup-box-footer {
		margin: 0 32rpx;
		height: 140rpx;
		display: -webkit-box;
		display: -webkit-flex;
		display: flex;
		-webkit-box-align: center;
		-webkit-align-items: center;
		align-items: center;
		-webkit-justify-content: space-around;
		justify-content: space-around;
	}

	.red-botton {
		width: 235rpx;
		height: 68rpx;
		line-height: 68rpx;
		text-align: center;
		border-radius: 34rpx;
		box-sizing: border-box;
		margin: 0;
		font-size: 28rpx;
		color: #FFFFFF;
		background: #F2270C;
	}

	.iconclose {
		position: absolute;
		top: 24rpx;
		right: 24rpx;
		width: 32rpx;
		height: 32rpx;
		font-size: 20rpx;
		background: #999;
		border-radius: 50%;
		line-height: 34rpx;
		text-align: center;
		color: #fff;
	}
	.pintuan-rule-parent /deep/.uni-popup__wrapper{
		border-radius: 30rpx 30rpx 0rpx 0rpx;
	}
	.pintuan-rule /deep/.uni-popup__wrapper {
		border-radius: 30rpx 30rpx 0rpx 0rpx;
	}
  .page-title{
    width: 360rpx;
    overflow:hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    -o-text-overflow:ellipsis;
    text-align: center;
  }
  /deep/ .uni-navbar{
    position: fixed;
    z-index: 999;
  }
  .grouping-swiper{
    position: fixed;
    left: 0;
    bottom: calc(100rpx + env(safe-area-inset-bottom));
    bottom: calc(100rpx + constant(safe-area-inset-bottom));
    width:100%;
    height: 90rpx;
  }
  .grouping-in-progress{
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
    background-color: rgba(253, 237, 136, 1);
    padding: 12rpx 24rpx;
    box-sizing: border-box;
    height: 90rpx;
    &-left{
      display: flex;
      align-items: center;
      &-head{
        width: 66rpx;
        height: 66rpx;
        border-radius: 50%;
      }
      &-name{
        color: #FF0000;
        font-size: 24rpx;
        margin-left: 16rpx;
        display: flex;
        align-items: center;
        text:first-child{
          overflow: hidden;
          white-space: nowrap;
          text-overflow: ellipsis;
          max-width: 240rpx;
        }
        text:last-child{
          margin-left: 16rpx;
        }
      }
    }
    &-right{
      &-btn{
        margin: 0;
        height: 54rpx;
        line-height: 54rpx;
        background: #F84346;
        border-radius: 27rpx;
        font-size: 28rpx;
        font-weight: 500;
        color: #FFFFFF;
      }
    }
  }
  .goods-action-button-share{
    font-size: 26rpx;
    color: white;
    background: rgba(246, 93, 114, 1);
    line-height: 34rpx;
    height: 80rpx;
    box-sizing: border-box;
    margin: 0;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    margin-right: 20rpx;
    &-one{

    }
    &-two{

    }
  }
</style>
