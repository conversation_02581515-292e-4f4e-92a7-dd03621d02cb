<template>
	<view>
		<mescroll-uni ref="mescroll" @getData="getListData">
			<block slot="list">
				<view class="invite-list" v-if="inviteList.length > 0">
					<view class="invite-item" v-for="(inviteItem, inviteIndex) in inviteList" :key="inviteIndex">
						<view class="user">
							<view class="head">
								<image class="head-img" :src="$util.img(inviteItem.headimg)" @error="imageError(inviteIndex)" mode="aspectFill"
									 :lazy-load="true"></image>
							</view>
							<view class="">
								<view class="name">{{ inviteItem.nickname }}</view>
								<view class="mobile">{{ inviteItem.mobile }}</view>
							</view>
						</view>
						<view class="date">{{ inviteItem.reg_time }}</view>
					</view>
				</view>
				<view v-else>
					<ns-empty :isIndex="!1" :text="text"></ns-empty>
					<view class="defalt-tip">您没有邀请新用户注册</view>
				</view>
			</block>
		</mescroll-uni>
		<loading-cover ref="loadingCover"></loading-cover>
	</view>
</template>

<script>
	import apiurls from "@/common/js/apiurls";

	export default {
		components: {
		},
		data() {
			return {
				inviteList: [],
				group_id: null
			};
		},
		mixins: [],
		onLoad(option) {
		},
		onShow() {
			// 刷新多语言
			this.$langConfig.refresh();

			if (uni.getStorageSync('token')) {
				if (this.$refs.mescroll) this.$refs.mescroll.refresh();
			} else {
				if(!uni.getStorageSync('token')){
				 //  this.$refs.ydauth.init(()=>{
					// this.$util.redirectTo('promotionpages/pintuan/order/list/list', {
					//   status: this.orderStatus
					// }, 'reLaunch');
				 //  });
				}
			}
		},
		methods: {
			// 获取订单数据
			getListData(mescroll) {
				this.$api.sendRequest({
					url: '/api/Member/recommend',
					data: {
						page: mescroll.num,
						page_size: mescroll.size,
					},
					success: res => {
						let newArr = []
						let msg = res.message;
						if (res.code == 0 && res.data) {
							newArr = res.data.list;
						} else {
						  if(!uni.getStorageSync('token') && res.code==-10009){

						  }else{
							this.$util.showToast({
							  title: msg
							})
						  }
						}
						mescroll.endSuccess(newArr.length);
						//设置列表数据
						if (mescroll.num == 1) this.inviteList = []; //如果是第一页需手动制空列表
						this.inviteList = this.inviteList.concat(newArr); //追加新数据
						if (this.$refs.loadingCover) this.$refs.loadingCover.hide();

					},
					fail: res => {
						mescroll.endErr();
						if (this.$refs.loadingCover) this.$refs.loadingCover.hide();
					}
				});
			},
			// 订单列表商品图片缺省
			imageError(inviteIndex) {
				this.inviteList[inviteIndex].sku_image = this.$util.getDefaultImage().default_goods_img;
				this.$forceUpdate();
			},
			navigateBack() {
				this.$util.redirectTo('/pages/member/index/index','','redirectTo');
			},
      /**
       *分享参数组装
       */
      getSharePageParams() {
        return this.$util.unifySharePageParams('/otherpages/shop/home/<USER>', this.shareTitle || '先迈商城',
            '',{}, this.$util.img(this.shareImg) || this.$util.img('public/static/youpin/home_share.jpg'))
      }
		},
		computed: {
			//vueX页面主题
			themeStyle() {
				return 'theme-' + this.$store.state.themeStyle
			}
		},
    /**
     * 自定义分享内容
     * @param {Object} res
     */
    onShareAppMessage(res) {
      let { title, link, imageUrl, query } = this.getSharePageParams()
      return this.$buriedPoint.pageShare(link , imageUrl, title);
    },
	};
</script>

<style lang="scss">
	.invite-list {
		padding-top: 20rpx;
		.invite-item {
			height: 140rpx;
			padding-left: 34rpx;
			padding-right: 29rpx;
			display: flex;
			align-items: center;
			justify-content: space-between;
			background-color: #FFFFFF;
			.user {
				display: flex;
				align-items: center;
				.head {
					position: relative;
					display: flex;
					.head-img {
						position: relative;
						width: 80rpx;
						height: 80rpx;
						margin-right: 20rpx;
						box-sizing: border-box;
						border: 2rpx solid #FF0000;
						border-radius: 50%;
					}
				}
				.name {
					color: #333333;
					font-size: 28rpx;
				}
				.mobile {
					color: #999999;
					font-size: 24rpx;
				}
				.win-status {
					margin-left: 36rpx;
					padding: 0 15rpx;
					height: 32rpx;
					line-height: 32rpx;
					font-size: 20rpx;
					color: #fff;
					background: linear-gradient(90deg, #FB331D 0%, #FE5838 100%);
					border-radius: 16rpx;

				}
			}
			.date {
				color: #999999;
				font-size: 24rpx;
			}
		}
	}
	.defalt-tip {
		position: fixed;
		width: 100%;
		text-align: center;
		left: 0;
		top: 40vh;
		color: #aaa;
	}
</style>
