<template>
	<view :class="themeStyle">
		<view class="main">
			<image v-if="bannerlist.banner.length>0" :src="$util.img(bannerlist.banner[0].image_url)" mode="" class="bgbanner" v-on:click="toAd(bannerlist.banner[0].banner_url)"></image>
			<image v-else :src="$util.img('public/static/youpin/pintuan/bombanner.png')" mode="" class="bgbanner"></image>
			<view class="comtainbox">
				<view class="comtain_top">
					<view class="top_left" v-on:click="topeopleNum()">
						<view class="people_num">
							<image :src="$util.img('public/static/youpin/pintuan/yqzc.png')" mode="" class="yqrs"></image>
							<view class="titlebox">
								<view class="title">
									邀请注册人数
								</view>
								<view class="num">
									<text class="truenum">{{bannerlist.inviteNum}}</text>
									<text class="smallnum">人</text>
								</view>
							</view>
							<view class="rigntimg">
								<image src="../../../static/imgs/back.png" mode=""></image>
							</view>
						</view>
					</view>
					<view class="top_right" v-on:click="toMoney()">
						<view class="people_num">
							<image :src="$util.img('public/static/youpin/pintuan/jlje.png')" mode="" class="yqrs"></image>
							<view class="titlebox">
								<view class="title">
									奖励金额
								</view>
								<view class="num">
									<text class="truenum">{{bannerlist.inviteSumMoney}}</text>
									<text class="smallnum">元</text>
								</view>
							</view>
							<view class="rigntimg">
								<image :src="$util.img('public/static/youpin/pintuan/back.png')" mode=""></image>
							</view>
						</view>
					</view>
				</view>
				<view class="comtain_bottom">
					<view class="bottitle">
						<image :src="$util.img('public/static/youpin/pintuan/right-tips.png')" mode=""></image>
						<text>邀请规则</text>
						<image :src="$util.img('public/static/youpin/pintuan/left-tips.png')" mode=""></image>
					</view>
					<view class="futext" v-html="fuwentext">

					</view>
				</view>
			</view>
		</view>
	</view>
</template>
<script>
	export default {
		components: {

		},
		data() {
			return {
				bannerlist:'',
				fuwentext:'',
			};
		},
		onLoad(data) {
			// type=0 为活动距离结束时间，type=1 为活动距离开始时间
			this.getBanner()
			// this.getTuijian();
		},
		onShow() {
			// this.mescroll.resetUpScroll(false);
			// 刷新多语言
			this.$langConfig.refresh();
			uni.setNavigationBarTitle({
				title: "邀请有礼"
			})
			// this.getbanner()
		},
		computed: {
			themeStyle() {
				return 'theme-' + this.$store.state.themeStyle
			},
		},
		watch: {},

		methods: {

			toMoney() {
				this.$util.redirectTo('/otherpages/member/balance/balance', {

				});
			},
			topeopleNum(){
				console.log("222")
				this.$util.redirectTo('/promotionpages/pintuan/gift_invitation_person/gift_invitation_person', {

				});
			},
			getBanner() {
				console.log("jinlail")
				console.log(this.$apiUrl.inviteAdv)

				this.$api.sendRequest({
					url: this.$apiUrl.inviteAdv,
					data: {

					},
					success: res => {
						let msg = res.message;
						if (res.code == 0 && res.data) {
							this.bannerlist = res.data
							//解决富文本图片宽度溢出问题
							res.data.invite_rule=res.data.invite_rule.replace(/<img/g,"<img style='width:100%'")
							this.fuwentext=res.data.invite_rule;
						} else {
							this.$util.showToast({
								title: msg
							})
						}
					},
					fail() {

					}
				});
			},
			toAd(url){
			  if(url){
          this.$util.diyRedirectTo({wap_url:url});
			  }
			},
      /**
       *分享参数组装
       */
      getSharePageParams() {
        return this.$util.unifySharePageParams('/otherpages/shop/home/<USER>', this.shareTitle || '先迈商城',
            '',{}, this.$util.img(this.shareImg) || this.$util.img('public/static/youpin/home_share.jpg'))
      }
		},
    /**
     * 自定义分享内容
     * @param {Object} res
     */
    onShareAppMessage(res) {
      let { title, link, imageUrl, query } = this.getSharePageParams()
      return this.$buriedPoint.pageShare(link , imageUrl, title);
    },
	};
</script>

<style lang="scss">
	page {
		background-color: #F5F5F5;
	}
	.main{
		position: relative;
	}
	.bgbanner{
		width: 100%;
		height: 800rpx;
	}
	.comtainbox{
		position: absolute;
		top: 380rpx;
		width: 100%;
	}
	.comtain_top{
		width: 702rpx;
		height: 148rpx;
		margin: 0 auto;
		background-color: #FFFFFF;
		border-radius: 20rpx;
		display: flex;
		align-items: center;
	}
	.top_left{
		width: 50%;
		border-right: 1rpx solid #EAE2D5;
		.people_num{
			height: 118rpx;
			display: flex;
			align-items: center;
			.yqrs{
				width: 84rpx;
				height: 84rpx;
				margin-left: 20rpx;
			}
			.titlebox{
				margin-left: 5rpx;
				.title{
					color: #895A31;
					font-size: 24rpx;
				}
				.num{
					.truenum{
						color: #FF501C;
						font-size: 36rpx;
					}
					.smallnum{
						color: #FF501C;
						font-size: 24rpx;
					}
				}
			}
			.rigntimg{
				margin-left: 50rpx;
				image{
					width: 22rpx;
					height: 22rpx;
				}
			}
		}
	}
	.top_right{
		width: 50%;
		.people_num{
			height: 118rpx;
			display: flex;
			align-items: center;
			.yqrs{
				width: 84rpx;
				height: 84rpx;
				margin-left: 20rpx;
			}
			.titlebox{
				margin-left: 5rpx;
				.title{
					color: #895A31;
					font-size: 24rpx;
				}
				.num{
					.truenum{
						color: #FF501C;
						font-size: 36rpx;
					}
					.smallnum{
						color: #FF501C;
						font-size: 24rpx;
					}
				}
			}
			.rigntimg{
				margin-left: 88rpx;
				image{
					width: 22rpx;
					height: 22rpx;
				}
			}
		}
	}
	.comtain_bottom{
		width: 702rpx;
		margin: 0 auto;
		background-color: #FFFFFF;
		border-radius: 20rpx;
		margin-top: 25rpx;
		.bottitle{
			display: flex;
			align-items: center;
			justify-content: center;
			padding: 30rpx 0 30rpx 0;
			image{
				width: 128rpx;
				height: 20rpx;
			}
			text{
				font-size: 28rpx;
				color: #895A31;
				font-weight: bold;
				padding: 0 20rpx;
			}
		}
		.futext{
			padding: 0 40rpx 40rpx 40rpx;
			rich-text img{
				width: 100%;
			}
		}
	}

</style>
