<template>
	<view class="page">
		<image class="page_bg" :src="$util.img('public/static/youpin/pintuan/new_forms/pintuan_page_bg.png')" mode="widthFix"></image>
		<view class="container">
			<view class="pintuan_number">
				<image :src="$util.img('public/static/youpin/pintuan/new_forms/pintuan_mbr.png')" mode="widthFix"></image><text>{{groupInfo.pintuan_num || ''}}人团</text>
			</view>
			<view class="notice">
				<view class="title">{{groupInfo.goods_name}}</view>正在疯抢中
			</view>
			<view class="body">
				<image class="body_bg" :src="$util.img('public/static/youpin/pintuan/new_forms/pintuan_bg.png')" mode="widthFix"></image>
				<view class="body_info">
					<navigator hover-class="none" :url="'/promotionpages/pintuan/detail/detail?id='+groupInfo.pintuan_goods_id" class="goods-detail">
						<view class="thumbImage">
							<image :src="$util.img(groupInfo.goods_image)" mode="" @error="imageError"></image>
							<view class="tag" v-if="groupInfo.promotion_type=='new'">{{groupInfo.promotion_type_desc}}</view>
						</view>

						<view class="detail">
							<view class="name">{{groupInfo.goods_name}}</view>
							<view class="progress">
								<view class="line">
									<view class="active" :style="{width:(groupInfo.pintuan_count/groupInfo.pintuan_num)*100 + '%'}"> </view>
								</view><text>{{groupInfo.pintuan_count}}/{{groupInfo.pintuan_num}}</text>
							</view>
<!--							<view class="bottom" v-if="groupInfo.status!=3 && groupInfo.status!=1 &&awardInfo.notWinMoney"> 预计获得红包￥<text>{{awardInfo.notWinMoney}}</text>-->
<!--							<view class="bottom" v-if="groupInfo.status!=3 && groupInfo.status!=1 &&awardInfo.notWinMoney"> 邀请好友参团可得￥<text>1</text>-->
							<view class="bottom" v-for="(item,index) in play_list" :key="index"> {{item.reward_str}}￥<text>{{item.money}}</text>
							</view>
						</view>
					</navigator>
					<image class="pintuan_line" :src="$util.img('public/static/youpin/pintuan/new_forms/pintuan_line.png')" mode="widthFix"></image>

					<image class="status_icon" v-if="groupInfo.status==3 && selfGroupInfo.win_status==1" :src="$util.img('public/static/youpin/pintuan/new_forms/win.png')"
					 mode="widthFix"></image>
					<image class="status_icon" v-if="groupInfo.status==3 && selfGroupInfo.win_status==0" :src="$util.img('public/static/youpin/pintuan/new_forms/lose.png')"
					 mode="widthFix"></image>
					<view class="countdown-timer" v-if="groupInfo.status==2 && Object.keys(selfGroupInfo).length==0">
						差<text>{{groupInfo.diff_num}}人</text>拼成，剩余
						<countdown-timer class="pintuan-countdown" ref="countdown" showColon :time="groupInfo.distance_time" @finish="onFinish"
						 autoStart></countdown-timer>
						结束
					</view>
					<view class="success_status" v-if="Object.keys(selfGroupInfo).length>0 && groupInfo.status!=1">
						<image :src="$util.img('public/static/youpin/pintuan/success-icon.png')" mode=""></image> 参团成功
					</view>
					<view class="success_status" v-if="Object.keys(selfGroupInfo).length==0 && groupInfo.status==3">
						<image :src="$util.img('public/static/youpin/pintuan/success-icon.png')" mode=""></image> 拼团成功
					</view>
					<view class="error_status" v-if="groupInfo.status==1">
						<image :src="$util.img('public/static/youpin/pintuan/error-icon.png')" mode=""></image> 拼团失败
					</view>
					<view class="tips" v-if="Object.keys(selfGroupInfo).length>0 || (Object.keys(selfGroupInfo).length==0 && groupInfo.status!=2)">
						<view v-if="groupInfo.status==2">还差<text>{{groupInfo.diff_num}}</text>份成团，请留意成团开奖通知！</view>
					</view>
					<navigator :url="`/promotionpages/pintuan/join_member/join_member?group_id=${group_id}`" class="mbr_list">
						<!-- <image :src="memberList[0].member_img" mode=""></image>
						<image :src="$util.img('public/static/youpin/pintuan/new_forms/more.png')" mode=""></image> -->
						<block v-for="(sub,index) in memberList" :key="index">
							<view class="share_heardimg" v-if="index<7">
								<image :src="sub.member_img" mode=""></image>
								<image class="redic_pay" :src="$util.img('public/static/youpin/order/pintuan/radicpay.png')" v-if="sub.pintuan_status==0"></image>
							</view>
						</block>
						<image class="moreheader" :src="$util.img('public/static/youpin/pintuan/new_forms/more.png')" mode="" v-if="memberList.length>7"></image>
					</navigator>
					<view class="btn_list">
						<block v-if="Object.keys(selfGroupInfo).length>0">
							<!-- #ifdef MP-WEIXIN -->
							<button open-type="share" class="share_btn" v-if="groupInfo.status==2 && groupInfo.diff_num>0">邀请好友参团</button>
							<!-- #endif -->
							<!-- #ifdef H5 -->
							<button class="share_btn" v-if="groupInfo.status==2 && groupInfo.diff_num>0" @click="toShare">邀请好友参团</button>
							<!-- #endif -->
						</block>
						<block v-if="Object.keys(selfGroupInfo).length==0">
							<!-- #ifdef MP-WEIXIN -->
							<button open-type="share" class="share_btn" v-if="groupInfo.status==2 && groupInfo.group_limit && groupInfo.promotion_type=='new'">立即分享</button>
							<!-- #endif -->
							<!-- #ifdef H5 -->
							<button class="share_btn" v-if="groupInfo.status==2 && groupInfo.group_limit && groupInfo.promotion_type=='new'" @click="toShare">立即分享</button>
							<!-- #endif -->
							<view class="share_btn" v-if="groupInfo.status==2 && groupInfo.group_limit==''" @click="joinin">立即参团</view>
						</block>
						<navigator class="share_btn" url="/promotionpages/pintuan/list/list" v-if="groupInfo.status!=2">更多拼团</navigator>
            <text class="home_btn" v-if="Object.keys(selfGroupInfo).length>0" @click="backHome">返回首页</text>
            <view class="btn_list-op" v-else>
              <text class="home_btn" @click="backHome">返回首页</text>
              <!-- #ifdef MP-WEIXIN -->
              <button open-type="share" class="home_btn" v-if="groupInfo.status==2 && groupInfo.diff_num>0">邀请参团</button>
              <!-- #endif -->
              <!-- #ifdef H5 -->
              <button class="home_btn" v-if="groupInfo.status==2 && groupInfo.diff_num>0" @click="toShare">邀请参团</button>
              <!-- #endif -->
            </view>
					</view>
				</view>
			</view>
			<view class="rule_box" v-if="pintuanRule">
				<view class="title">
					<image :src="$util.img('public/static/youpin/pintuan/new_forms/stage_line1.png')"></image>
					拼团规则
					<image :src="$util.img('public/static/youpin/pintuan/new_forms/stage_line2.png')"></image>
				</view>
				<view class="content">
					<rich-text :nodes="pintuanRule"></rich-text>
				</view>
			</view>
		</view>
		<diy-share :canvasOptions="canvasOptions_share" ref="sharePopup_share" :sharePopupOptions="sharePopupOptions_share"
		 @childByValue="getShareImg"></diy-share>
    <diy-share-navigate-h5 ref="shareNavigateH5"></diy-share-navigate-h5>
		<!-- 授权登录弹窗 -->
		<yd-auth-popup ref="ydauth"></yd-auth-popup>
    <ns-login ref="login"></ns-login>
    <uni-coupon-pop ref="couponPop"></uni-coupon-pop>
	</view>
</template>

<script>
	import htmlParser from '@/common/js/html-parser.js';
  import system from "../../../common/js/system";
  import diyShareNavigateH5 from '@/components/diy-share-navigate-h5/diy-share-navigate-h5'
  // #ifdef H5
  import {isOnXianMaiApp} from "../../../common/js/h5/appOP";
  // #endif
	export default {
		data() {
			return {
				group_id: 0,
				groupInfo: {
					pintuan_count: 0,
					pintuan_num: 0
				},
        play_list:[],
				memberList: [],
				selfGroupInfo: {},
				inviter_id: 0,
				awardInfo: {},
				pintuanRule: '',
				sharePopupOptions_share: [],
				canvasOptions_share: {
          width: '420',
          height: '336',
          borderRadius: '10',
          scale:0.5,
				},
        // #ifdef H5
        isOnXianMaiApp:isOnXianMaiApp,
        // #endif
			};
		},
    components:{
      diyShareNavigateH5
    },
		onLoad(options) {
			this.group_id = options.group_id;
		},
		async onShow() {
			this.$langConfig.refresh();
      await system.wait_staticLogin_success();
			this.inviter_id = uni.getStorageSync('recommend_member_id') || 0
			this.getGroupDetail();
			this.getPintuanRule()
			if (uni.getStorageSync('is_register')) {
				this.$util.toShowCouponPopup(this)
				uni.removeStorageSync('is_register');
			}
		},
		computed: {
			width() {
				let val = 0
				this.groupInfo.pintuan_num
				return val
			},
      themeStyle() {
        return 'theme-' + this.$store.state.themeStyle
      }
		},
		onPullDownRefresh() {
			this.getGroupDetail();
			uni.stopPullDownRefresh()
		},
		methods: {
      backHome(){
        this.$buriedPoint.diyReportGroupDetailInteractionEvent({sku_id:this.groupInfo.sku_id,goods_id:null,diy_group_activity_id:this.groupInfo.pintuan_goods_id,diy_group_buying_id: parseInt(this.group_id),diy_action_type:'return_home',is_goods_page: 0})
        this.$util.redirectTo('/otherpages/shop/home/<USER>')
      },
			getShareImg(value) {
				this.shareImgPath = value
			},
			getGroupDetail() {
        let path = '/promotionpages/pintuan/share/share?group_id=' + this.group_id + '&inviter_id=' + this.inviter_id +
            '&shop_id=' + uni.getStorageSync('shop_id')
				let token = uni.getStorageSync('token');
				if (!token) {
          this.$util.toShowLoginPopup(this,null,path);
					return
				}
				this.$api.sendRequest({
					url: this.$apiUrl.ptGroupDetailUrl,
					data: {
						group_id: this.group_id,
            share_id: this.inviter_id
					},
					success: res => {
						if (this.$refs.loadingCover) this.$refs.loadingCover.hide();
						if (res.code == 0) {
							res.data.groupInfo.distance_time = res.data.groupInfo.distance_time > 0 ? Math.abs(res.data.groupInfo.distance_time) *
								1000 : 0
							res.data.memberList.forEach(e => {
								e.close_time = e.close_time > 0 ? Math.abs(e.close_time) * 1000 : 0;
							})
							this.groupInfo = res.data.groupInfo;
							this.memberList = res.data.memberList;
							this.selfGroupInfo = res.data.selfGroupInfo;
							this.awardInfo = res.data.awardInfo
              this.play_list = res.data.play_list
							this.$forceUpdate()
							this.$forceUpdate()
							this.drawCanvas_share()
              setTimeout(()=>{
                this.$refs.sharePopup_share.open();
              },1000)
              // #ifdef H5
              this.setWechatShare();
              // #endif
						} else {
							this.$util.showToast({
								title: res.message
							})
						}
					}
				})
			},
			getPintuanRule() {
				this.$api.sendRequest({
					url: this.$apiUrl.pintuanRuleUrl,
					data: {},
					success: res => {
						if (res.code == 0) {
							this.pintuanRule = res.data ? htmlParser(res.data) : ''
						} else {
							this.$util.showToast({
								title: res.message
							})
						}
					}
				})
			},
			drawCanvas_share() {
				this.sharePopupOptions_share = [{
						background: "#fff",
						x: 0,
						y: 0,
						width: 420,
						height: 336,
						type: 'image',
					},
					{
						path: this.$util.img('https://www.xianmai88.com/static/youpin/share_bg.png'),
						x: 0,
						y: 0,
						width: 420,
						height: 336,
						type: 'image',
					},
					{
						path: this.$util.img(this.groupInfo.goods_image)+'?image_process=resize,s_200',
						x: 13,
						y: 90,
						width: 231,
						height: 231,
						type: 'image',
					},
					{
						text: '￥',
						size: 32,
						color: '#FF1010',
						x: 260,
						y: 160,
						type: 'text',
					},
					{
						text: this.groupInfo.pintuan_price,
						size: 38,
						color: '#FF1010',
						fontWeight: 'bold',
						x: 290,
						y: 160,
						type: 'text',
					},
					{
						text: '￥' + this.groupInfo.market_price,
						size: 26,
						color: '#999999',
						x: 280,
						y: 200,
						type: 'text',
						textBaseline: true
					},

				]

			},
			onFinish() {
				this.getGroupDetail()
			},
			changeTime() {
				this.getGroupDetail()
			},
			imageError() {
				setTimeout(() => {
					this.groupInfo.goods_image = this.$util.getDefaultImage().default_goods_img;
					this.$forceUpdate();
				}, 0)
			},
			rulePopup() {
				this.$refs.rule_popup.open()
			},
			joinin() {
				if (this.groupInfo.group_limit) {
					this.$util.showToast({
						title: this.groupInfo.group_limit
					})
					return
				}
				let token = uni.getStorageSync('token');
        let path = '/promotionpages/pintuan/share/share?group_id=' + this.group_id + '&inviter_id=' + this.inviter_id +
            '&shop_id=' + uni.getStorageSync('shop_id')
				if (!token) {
          this.$util.toShowLoginPopup(this,null,path);
					return
				}
				var data = {
					sku_id: this.groupInfo.sku_id,
					num: 1,
					group_id: this.group_id,
					pintuan_goods_id: this.groupInfo.pintuan_goods_id,
					pintuan_id: this.groupInfo.pintuan_id,
					goods_id: this.groupInfo.goods_id,
					inviter_id: this.inviter_id
				};
        this.$buriedPoint.diyReportGroupDetailInteractionEvent({sku_id:this.groupInfo.sku_id,goods_id:null,diy_group_activity_id:this.groupInfo.pintuan_goods_id,diy_group_buying_id: parseInt(this.group_id),diy_action_type:'join_now',is_goods_page: 0})
				uni.setStorage({
					key: 'pintuanOrderCreateData',
					data: data,
					success: () => {
						this.$util.redirectTo('/promotionpages/pintuan/payment/payment');
					}
				});
			},
      /**
       *分享参数组装(注意需要分享的那一刻再调此方法)
       */
      getSharePageParams(){
        let share_data=this.$util.unifySharePageParams('/promotionpages/pintuan/share/share','先迈商城',
            `邀请拼团抢"${this.groupInfo.goods_name}"`,{group_id:this.group_id},this.$util.img(this.groupInfo.goods_image))
        return share_data;
      },
      /**
       * 设置微信公众号分享
       */
      setWechatShare() {
        // 微信公众号分享
        // #ifdef H5
        let share_data=this.$util.deepClone(this.getSharePageParams());
        let link=window.location.origin+this.$router.options.base+share_data.link.slice(1);
        share_data.link=link;
        this.$util.publicShare(share_data,()=>{
          system.pintuanShareActionReport({pintuan_id:this.groupInfo.pintuan_id,goods_id:this.groupInfo.goods_id})
        });
        // #endif
      },
      toShare(){
        let share_data=this.getSharePageParams();
        this.$refs.shareNavigateH5.open(share_data,()=>{
          if(this.isOnXianMaiApp){
            system.pintuanShareActionReport({pintuan_id:this.groupInfo.pintuan_id,goods_id:this.groupInfo.goods_id})
          }
        });
      }
		},
		/**
		 * 自定义分享内容
		 * @param {Object} res
		 */
		onShareAppMessage(res) {
      let share_data=this.getSharePageParams();
      system.pintuanShareActionReport({pintuan_id:this.groupInfo.pintuan_id,goods_id:this.groupInfo.goods_id})
      this.$buriedPoint.diyReportGroupDetailInteractionEvent({sku_id:this.groupInfo.sku_id,goods_id:null,diy_group_activity_id:this.groupInfo.pintuan_goods_id,diy_group_buying_id: parseInt(this.group_id),diy_action_type:'invite_to_join',is_goods_page: 0})
			if (this.shareImgPath) {
        return this.$buriedPoint.pageShare(share_data.link, this.shareImgPath, share_data.desc,true,{goods_id:this.groupInfo.goods_id});
			} else {
        return this.$buriedPoint.pageShare(share_data.link, share_data.imageUrl, share_data.desc,true,{goods_id:this.groupInfo.goods_id});
			}

		}
	}
</script>

<style lang="scss" scoped>
	.page {
		background: #FC6054;
		width: 100%;
		height: 100vh;
		overflow-y: scroll;
		-webkit-overflow-scrolling: touch;
	}

	.page_bg {
		width: 100%;
	}

	.container {
		position: absolute;
		top: 0;
		left: 0;
		height: 100vh;
		overflow-y: scroll;
		-webkit-overflow-scrolling: touch;

		.pintuan_number {
			margin-top: 48rpx;
			background: #F8D3A7;
			width: 180rpx;
			height: 56rpx;
			border-radius: 0px 28rpx 28rpx 0px;
			display: flex;
			align-items: center;
			justify-content: center;

			image {
				width: 34rpx;
				margin-right: 10rpx;
			}

			text {
				color: #EF5939;
				font-size: 28rpx;
				font-weight: bold;
			}
		}

		.notice {
			width: 750rpx;
			color: #FCDFC2;
			font-size: 36rpx;
			display: flex;
			justify-content: center;
			line-height: 1;
			margin: 40rpx 0;

			.title {
				line-height: 1;
				color: #FCDFC2;
				font-size: 36rpx;
				max-width: 460rpx;
				white-space: nowrap;
				text-overflow: ellipsis;
				overflow: hidden;
				word-break: break-all;
			}
		}

		.body {
			width: 702rpx;
			margin: 0 auto;
			position: relative;

			.body_bg {
				width: 100%;
				display: block;
			}

			.body_info {
				position: absolute;
				top: 0;
				left: 0;
				width: 100%;

				.goods-detail {
					padding: 30rpx 24rpx;
					display: flex;
					justify-content: space-between;

					.thumbImage {
						width: 240rpx;
						height: 240rpx;
						border-radius: 20rpx;
						margin-right: 20rpx;
						overflow: hidden;
						position: relative;

						image {
							width: 100%;
							height: 100%;
						}

						.tag {
							position: absolute;
							bottom: 0;
							left: 0;
							width: 100%;
							line-height: 40rpx;
							color: #fff;
							text-align: center;
							background: rgba(0, 0, 0, 0.5);
							font-size: 26rpx;
						}
					}

					.detail {
						width: calc(100% - 260rpx);

						.name {
							color: #333;
							font-size: 28rpx;
							font-weight: bold;
							text-overflow: -o-ellipsis-lastline;
							overflow: hidden;
							text-overflow: ellipsis;
							display: -webkit-box;
							-webkit-line-clamp: 2;
							line-clamp: 2;
							-webkit-box-orient: vertical;
							line-height: 1.2;
						}

						.progress {
							display: flex;
							align-items: center;
							margin-top: 30rpx;
              margin-bottom: 36rpx;
							.line {
								width: 214rpx;
								height: 20rpx;
								background: #F9C1C1;
								border-radius: 10rpx;
								margin-right: 15rpx;

								.active {
									background: #F84346;
									height: 20rpx;
									border-radius: 10rpx;
								}
							}

							text {
								color: #666;
								font-size: 24rpx;
								line-height: 1;
							}
						}

						.bottom {
							font-size: 24rpx;
              line-height: 1.6;
							text {
								color: #F84346;
								//font-size: 36rpx;
								font-weight: bold;
							}
						}
					}
				}

				.pintuan_line {
					width: 660rpx;
					margin: 0 auto;
					display: block;
					margin-bottom: 30rpx;
				}

				.thumbImage_line {
					width: 100%;
				}

				.status_icon {
					width: 108rpx;
					height: 108rpx;
					position: absolute;
					right: 44rpx;
					top: 287rpx;
				}

				.success_status,
				.error_status {
					font-size: 30rpx;
					color: #49AD97;
					font-weight: bold;
					display: flex;
					align-items: center;
					justify-content: center;

					image {
						width: 36rpx;
						height: 36rpx;
						margin-right: 15rpx;
					}
				}

				.error_status {
					color: #F84346;
				}

				.tips {
					text-align: center;
					margin: 24rpx 0 30rpx 0;
					color: #333;
					font-size: 24rpx;
					font-weight: bold;
					min-height: 30rpx;

					text {
						color: #F84346;
						font-size: 24rpx;
					}
				}

				.mbr_list {
					margin-bottom: 20rpx;
					display: flex;
					.share_heardimg{
						margin-left: 20rpx;
						position: relative;
						image{
							width: 60rpx;
							height: 60rpx;
							// margin-left: 20rpx;
							border-radius: 50%;
						}
						.redic_pay{
							width: 60rpx;
							height: 60rpx;
							border-radius: 50%;
							position: absolute;
							left: 0;
							bottom: 15rpx;
						}
					}
					.moreheader {
						width: 60rpx;
						height: 60rpx;
						margin-left: 20rpx;
						border-radius: 50%;
					}
				}


				.btn_list {
					.share_btn {
						width: 654rpx;
						height: 80rpx;
						background: #F84346;
						border-radius: 40rpx;
						margin: 0 auto;
						margin-bottom: 24rpx;
						line-height: 80rpx;
						color: #fff;
						font-size: 32rpx;
						text-align: center;
					}

					.home_btn {
						width: 654rpx;
						height: 80rpx;
						border-radius: 40rpx;
						margin: 0 auto;
						margin-bottom: 24rpx;
						line-height: 80rpx;
						color: #F84346;
						font-size: 32rpx;
						border: 1px solid #F84346;
						text-align: center;
					}
          &-op{
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 654rpx;
            margin: 0 auto;
            .share_btn,.home_btn{
              width: 320rpx;
            }
          }
				}
			}
		}

		.rule_box {
			.title {
				font-size: 36rpx;
				color: #F3F6CB;
				font-weight: bold;
				line-height: 100rpx;
				text-align: center;

				image {
					width: 128rpx;
					height: 20rpx;
					margin: 0 35rpx
				}
			}

			.content {
				width: 702rpx;
				margin: 0 auto;
				border-radius: 10rpx;
				box-sizing: border-box;
				padding: 30rpx 24rpx;
				margin-bottom: 50rpx;
				background: #fff;
				color: #333;
				font-size: 26rpx;

				img {
					max-width: 100%;
				}
			}
		}

		.countdown-timer {
			display: flex;
			justify-content: center;
			padding: 30rpx 0;

			text {
				color: #F84346;
			}

			.pintuan-countdown {
				/deep/.custom {
					view:nth-child(odd) {
						background-color: #F84346;
						color: #FFFFFF;
						min-width: 46rpx;
						padding: 0 4rpx;
						box-sizing: border-box;
						height: 40rpx;
						line-height: 40rpx;
						color: white;
						border-radius: 6rpx;
						font-size: 28rpx;
						text-align: center;
						overflow: hidden;
						margin: 0 5rpx
					}
				}
			}
		}
	}
</style>
