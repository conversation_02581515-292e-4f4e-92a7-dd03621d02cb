<template>
	<view>
		<mescroll-uni ref="mescroll" @getData="getListData">
			<block slot="list">
				<view class="join-list" v-if="joinList.length > 0">
					<view class="join-item" v-for="(joinItem, joinIndex) in joinList" :key="joinIndex">
						<view class="user">
							<view class="head">
								<image class="head-img" :src="$util.img(joinItem.member_img)" @error="imageError(joinIndex)" mode="aspectFill"
									 :lazy-load="true"></image>
									 <image class="mengceng" :src="$util.img('public/static/youpin/order/pintuan/radicpay.png')" v-if="joinItem.pintuan_status==0"></image>
								<image v-if="joinItem.win_status" class="tag" :src="$util.img('public/static/youpin/icon-win-status.png')"></image>
							</view>
							<view class="name">{{ joinItem.mobile }}</view>
							<view class="win-status" v-if="joinItem.win_status">{{ joinItem.win_status_desc }}</view>
						</view>
						<view class="date">{{ joinItem.add_time }}</view>
					</view>
				</view>
				<view v-else>
					<ns-empty :isIndex="!1" :text="text"></ns-empty>
				</view>
			</block>
		</mescroll-uni>
		<loading-cover ref="loadingCover"></loading-cover>
	</view>
</template>

<script>
	import apiurls from "@/common/js/apiurls";

	export default {
		components: {
		},
		data() {
			return {
				joinList: [],
				group_id: null
			};
		},
		mixins: [],
		onLoad(option) {
			// 获取链接订单状态
			if (option.group_id) this.group_id = option.group_id;
		},
		onShow() {
			// 刷新多语言
			this.$langConfig.refresh();

			if (uni.getStorageSync('token')) {
				if (this.$refs.mescroll) this.$refs.mescroll.refresh();
			} else {
				if(!uni.getStorageSync('token')){
				 //  this.$refs.ydauth.init(()=>{
					// this.$util.redirectTo('promotionpages/pintuan/order/list/list', {
					//   status: this.orderStatus
					// }, 'reLaunch');
				 //  });
				}
			}
		},
		methods: {
			// 获取订单数据
			getListData(mescroll) {
				this.$api.sendRequest({
					url: '/api/Pintuan/groupMemList',
					data: {
						page: mescroll.num,
						page_size: mescroll.size,
						group_id: this.group_id
					},
					success: res => {
						let newArr = []
						let msg = res.message;
						if (res.code == 0 && res.data) {
							newArr = res.data.list;
						} else {
						  if(!uni.getStorageSync('token') && res.code==-10009){

						  }else{
							this.$util.showToast({
							  title: msg
							})
						  }
						}
						mescroll.endSuccess(newArr.length);
						//设置列表数据
						if (mescroll.num == 1) this.joinList = []; //如果是第一页需手动制空列表
						this.joinList = this.joinList.concat(newArr); //追加新数据
						if (this.$refs.loadingCover) this.$refs.loadingCover.hide();

					},
					fail: res => {
						mescroll.endErr();
						if (this.$refs.loadingCover) this.$refs.loadingCover.hide();
					}
				});
			},
			// 前往参团详情
			toPintuanDetail(data) {
				console.log(data.group_id)
				this.$util.redirectTo('/promotionpages/pintuan/share/share', {
					group_id: data.group_id,
				});
			},
			// 订单列表商品图片缺省
			imageError(joinIndex) {
				this.joinList[joinIndex].sku_image = this.$util.getDefaultImage().default_goods_img;
				this.$forceUpdate();
			},
			navigateBack() {
				this.$util.redirectTo('/pages/member/index/index','','redirectTo');
			},
      /**
       *分享参数组装
       */
      getSharePageParams() {
        return this.$util.unifySharePageParams('/otherpages/shop/home/<USER>', this.shareTitle || '先迈商城',
            '',{}, this.$util.img(this.shareImg) || this.$util.img('public/static/youpin/home_share.jpg'))
      }
		},
    /**
     * 自定义分享内容
     * @param {Object} res
     */
    onShareAppMessage(res) {
      let { title, link, imageUrl, query } = this.getSharePageParams()
      return this.$buriedPoint.pageShare(link , imageUrl, title);
    },
		computed: {
			//vueX页面主题
			themeStyle() {
				return 'theme-' + this.$store.state.themeStyle
			}
		},

	};
</script>

<style lang="scss">
	.join-list {
		padding-top: 20rpx;
		.join-item {
			height: 140rpx;
			padding-left: 34rpx;
			padding-right: 29rpx;
			display: flex;
			align-items: center;
			justify-content: space-between;
			background-color: #FFFFFF;
			.user {
				display: flex;
				align-items: center;
				.head {
					position: relative;
					display: flex;
					.head-img {
						position: relative;
						width: 80rpx;
						height: 80rpx;
						margin-right: 20rpx;
						box-sizing: border-box;
						border: 2rpx solid #FF0000;
						border-radius: 50%;
					}
					.mengceng{
						width: 80rpx;
						height: 80rpx;
						border-radius: 50%;
						position: absolute;
						top: 0;
						bottom: 0;
					}
					.tag {
						position: absolute;
						top: -17rpx;
						left: -2rpx;
						width: 35rpx;
						height: 30rpx;
					}
				}
				.name {
					color: #333333;
					font-size: 28rpx;
				}
				.win-status {
					margin-left: 36rpx;
					padding: 0 15rpx;
					height: 32rpx;
					line-height: 32rpx;
					font-size: 20rpx;
					color: #fff;
					background: linear-gradient(90deg, #FB331D 0%, #FE5838 100%);
					border-radius: 16rpx;

				}
			}
			.date {
				color: #999999;
				font-size: 24rpx;
			}
		}
	}
</style>
