<template>
	<view class="page">
    <view class="bannerimg" v-if="advList.length>0">
      <image :src="$util.img(advList[0].image_url)" mode="" v-on:click="toAd(advList[0])"></image>
    </view>
    <view class="container" :style="containerHas ? 'height:calc(100vh - 280rpx);' : 'margin-top: 0;height:100vh;'">
      <view class="nav_tab">
        <view class="tab_item" :class="{active:sort_key == 'default'}" @click="changTab('default')">
          <text>综合</text>
        </view>
        <view class="tab_item" :class="{active:sort_key == 'group_num'}" @click="changTab('group_num')">
          <text>在途拼团数</text>
          <view class="iconfont-wrap">
            <view class="iconfont iconiconangledown-copy" :class="{ 'ns-text-color': saleNumOrder === 'asc' && sort_key === 'group_num' }"></view>
            <view class="iconfont iconiconangledown" :class="{ 'ns-text-color': saleNumOrder === 'desc' && sort_key === 'group_num' }"></view>
          </view>
        </view>
        <view class="tab_item" :class="{active:sort_key == 'price'}" @click="changTab('price')">
          <text>价格</text>
          <view class="iconfont-wrap">
            <view class="iconfont iconiconangledown-copy" :class="{ 'ns-text-color': priceOrder === 'asc' && sort_key === 'price' }"></view>
            <view class="iconfont iconiconangledown" :class="{ 'ns-text-color': priceOrder === 'desc' && sort_key === 'price' }"></view>
          </view>
        </view>
      </view>
      <mescroll-uni @getData="getData" ref="mescroll" :size="8" v-if="addonIsExit.pintuan" :top="containerHas ? headerTop :100" @scroll="scrollTouch" :height="containerHas ? 'calc(100% - 360rpx)' : 'calc(100% - 110rpx)'">
        <block slot="list">
          <view class="pintuan-page">
            <view class="pintuan-list" v-if="dataList.length">
              <view class="list-item ns-margin-bottom" v-for="(item, index) in dataList" :key="index" @click="toDetail(item)">
                <view class="item-image expose_goods_index" :data-expose_goods_sku="item.sku_id">
                  <image :src="$util.img(item.sku_image)" @error="imageError(index)" mode='aspectFit'></image>
                  <text class="item-image-new" v-if="item.promotion_type=='new'">{{item.promotion_type_desc}}</text>
                  <!--								<text class="item-image-over" v-if="item.stock==0 && item.can_join==0">已抢完</text>-->
                  <image :src="$util.img('public/static/youpin/product-sell-out.png')" class="over" v-if="item.goods_stock == 0"></image>
                </view>
                <view class="item-desc">
                  <view class="desc-title">{{ item.goods_name }}</view>
                  <!-- <view class="pin_num">
                    <text class="renpin">{{ item.pintuan_num }}人团</text>
                    <text class="yipin">已开{{item.order_num}}团</text>
                  </view> -->
                  <view class="price_box">
                    <view class="price_box_left">
                      <view class="true_prcie">
                        <text class="true_prcie_icon">￥</text>
                        <text class="price_num">{{ item.pintuan_price }}</text>
                        <text class="old-price">￥{{item.market_price}}</text>
                      </view>
                      <!-- <view class="get_maidou">
                        最高得迈豆{{item.maidou}}
                      </view> -->
                    </view>
                    <view class="gototuan">
                      <image v-for="(items, idx) in item.memberList" :key="idx" :src="items.member_img" mode=""></image>
                      <!-- <image src="../../../static/imgs/gengduo.png" mode=""></image> -->
                      <image :src="$util.img('public/static/youpin/pintuan/new_forms/more.png')" mode=""></image>
                    </view>
                  </view>
                  <view class="desc-con">
                    <view class="ptbackg">
                      <image :src="$util.img('public/static/youpin/pintuan/ptbg.png')" mode=""></image>
                      <view class="ptbg_zi">
                        <view class="ptbg_num">
                          {{item.pintuan_num}}人拼团，{{item.winning_num}}人买到产品
                        </view>
                        <view class="ptbg_get" v-if="item.max_to_play">
                          <!--												{{item.pintuan_num - item.winning_num}}人奖励<text class="big_price">{{item.not_win_money}}</text>元红包-->
                          {{item.max_to_play.reward_str}}<text class="big_price">￥{{item.max_to_play.money}}</text>元
                        </view>
                      </view>
                    </view>
                  </view>
                </view>
              </view>
            </view>
          </view>
          <view v-if="!loading && !dataList.length">
            <ns-empty :siteId="siteId" :fixed="false"></ns-empty>
          </view>
        </block>
      </mescroll-uni>
    </view>
		<loading-cover ref="loadingCover"></loading-cover>
	</view>
</template>

<script>
	// import nsAdv from '@/components/ns-adv/ns-adv.vue';
	import system from "@/common/js/system.js";
  import wx_expose_goods from '@/common/mixins/wx_expose_goods.js';
  // #ifdef H5
  import {isOnXianMaiApp} from "../../../common/js/h5/appOP";
  // #endif
	export default {
		components: {
			// nsAdv
		},
    mixins: [wx_expose_goods],
		data() {
			return {
				dataList: [],
				siteId: 0,
				advList: [],
        headerTop:360,
        sort_key: 'default',  // default综合，group_num在途拼团数，price价格，默认default
        saleNumOrder: 'asc', //asc 拼团数从低到高 desc从高到低
        priceOrder: 'asc', //asc 价格从低到高 desc价格从高到低
        sort_type: '',
        loading: true,
			};
		},
		onLoad(options) {
			if (options.site_id) {
				this.siteId = options.site_id;
			}
      // #ifdef H5
      if(isOnXianMaiApp){
        this.headerTop=88;
      }
      // #endif
			this.getAdvList()
		},
		async onShow() {
			// if(!this.addonIsExit.pintuan){
			// 	this.$util.showToast({
			// 		title:'拼团插件未安装',
			// 		mask:true,
			// 		duration:2000
			// 	})
			// 	setTimeout(()=>{
			// 		this.$util.redirectTo('/pages/index/index/index',{},'redirectTo')
			// 	},2000);
			// 	return;
			// }
			// 刷新多语言
			// this.$langConfig.refresh();
      await system.wait_staticLogin_success();
			// 修改顶部标题
			this.getDiyInfo()

			// #ifdef H5
      let share_data = this.$util.deepClone(this.getSharePageParams())
      let link = window.location.origin+this.$router.options.base+share_data.link.slice(1)
      share_data.link=link
			share_data.desc=share_data.title
    	share_data.title='先迈商城'
      await this.$util.publicShare(share_data);
      // #endif
		},
		computed: {
			themeStyle() {
				return 'theme-' + this.$store.state.themeStyle
			},
			addonIsExit() {
				return this.$store.state.addonIsExit
			},
			containerHas() {
				return this.advList.length > 0;
			}
		},
		methods: {
			async getDiyInfo() {
				let res = await this.$api.sendRequest({
					url: '/api/diyview/info',
					async: false,
					data: {
						name: 'DIYVIEW_INDEX'
					}
				});

				if (res.data) {
					let data = JSON.parse(res.data.value).value
					data.forEach(v => {
						if(v.controller == 'Pintuan' && v.name){
							uni.setNavigationBarTitle({
								title: v.name || '拼团好物'
							})
						}
					})
				}
			},
			getData(mescroll) {
        if (mescroll.num == 1) this.dataList = [];
        this.loading = true;
				this.$api.sendRequest({
					url: '/api/Pintuan/goodsList',
					data: {
						page_size: mescroll.size,
						page: mescroll.num,
            sort_key: this.sort_key,
            sort_type: this.sort_type
						// site_id: this.siteId
					},
					success: res => {
            this.loading = false
						let newArr = [];
						let msg = res.message;
						if (res.code == 0 && res.data) {
							newArr = res.data.list;
						} else {
							this.$util.showToast({
								title: msg
							});
						}
						mescroll.endSuccess(newArr.length);
						//设置列表数据
						if (mescroll.num == 1) this.dataList = []; //如果是第一页需手动制空列表
						this.dataList = this.dataList.concat(newArr); //追加新数据
						this.$buriedPoint.exposeGoods( newArr , 'goods_id')
						if (this.$refs.loadingCover) this.$refs.loadingCover.hide();
					},
					fail() {
            this.loading = false
						//联网失败的回调
						mescroll.endErr();
						if (this.$refs.loadingCover) this.$refs.loadingCover.hide();
					}
				});
			},
			//获取广告位
			getAdvList() {
				this.$api.sendRequest({
					url: this.$apiUrl.specialBannerUrl,
					data: {
						sign: "pintuan-1",
					},
					success: res => {
						if (res.code == 0 && res.data) {
							this.advList = res.data.list;
						}
					},
					fail() {}
				});
			},
      changTab(type) {
        if (this.sort_key === type && (type !== 'price' && type !== 'group_num')) return;
        this.sort_key = type;
        if (type == 'group_num') {
          this.saleNumOrder = this.saleNumOrder === 'asc' ? 'desc' : 'asc';
          this.sort_type = this.saleNumOrder
        } else if (type == 'price') {
          this.priceOrder = this.priceOrder === 'asc' ? 'desc' : 'asc';
          this.sort_type = this.priceOrder
        }
        this.$refs.mescroll.refresh();
        this.againDealWith(true)
      },
			toDetail(e) {
				this.$util.redirectTo('/promotionpages/pintuan/detail/detail', {
					id: e.pintuan_goods_id,
					goods_id: e.goods_id
				});
			},
			imageError(index) {
				this.dataList[index].sku_image = this.$util.getDefaultImage().default_goods_img;
				this.$forceUpdate();
			},
			toAd(item) {
        this.$buriedPoint.diyReportAdEvent(
            {diy_ad_location_type:'list',diy_material_path:item.image_url,diy_ad_type:'image',diy_target_page:item.banner_url,diy_ad_id:item.id,diy_action_type:'click'})
        this.$util.specialBannerReportByClick(item.id)
        if (item.banner_url) {
          this.$util.diyCompateRedirectTo({
            wap_url: item.banner_url
          })
        }
			},
			getSharePageParams() {
        let share_data=this.$util.unifySharePageParams('/promotionpages/pintuan/list/list','拼团赢奖励，人人有份！',
            '', {},this.$util.img('public/static/youpin/pingtuan_share.jpg'))
        return share_data;
			}
		},
		onShareAppMessage(res) {
      let share_data=this.getSharePageParams();
      return this.$buriedPoint.pageShare( share_data.link, share_data.imageUrl, share_data.title);
		},
		// 分享到微信
		onShareTimeline(res){
			let { title, imageUrl, query } = this.getSharePageParams()
			return {
				title,
        imageUrl,
				query,
				success: res => {},
				fail: res => {}
			};
		}
	};
</script>

<style lang="scss">
	.page {
		background: #FFFFFF;
    position: fixed;
    left: 0;
    top: 0;
    right: 0;
    bottom: 0;
	}

	.pintuan-top {
		width: 100%;

		image {
			width: 100%;
		}
	}
  .container{
    width: 100%;
    background: linear-gradient(0deg, #F5F5F5 0%, #FFFFFF 100%);
    border-radius: 20rpx 20rpx 0px 0px;
    margin-top: -26rpx;
    position: relative;
    .nav_tab {
      width: 100%;
      height: 100rpx;
      line-height: 100rpx;
      display: flex;
      align-items: center;

      .tab_item {
        width: 33%;
        text-align: center;
        color: #999;
        font-size: 28rpx;
        display: flex;
        justify-content: center;
        align-items: center;

        .iconfont-wrap {
          display: flex;
          justify-content: center;
          align-items: center;
          flex-direction: column;
          margin-left: 10rpx;
          width: 40rpx;
          margin-bottom: 12rpx;
          .iconfont {
            float: left;
            font-size: 32rpx;
            line-height: 1;
            height: 20rpx;
          }
        }

        &.active text {
          color: $base-color
        }
      }
    }
  }

	.pintuan-page {
		width: 100%;
		padding: $ns-padding;
		box-sizing: border-box;
		//margin-bottom: $ns-margin;
		//position: absolute;
		z-index: 99;
		//background: #FFFFFF;
		border-radius: 15rpx 15rpx 0 0;
		//padding-bottom: 100rpx;

		.pintuan-list {
			width: 100%;

			.list-item {
				width: 100%;
				// height: 268rpx;
				background: yellow;
				border-radius: $ns-border-radius;
				padding: 20rpx 10rpx;
				box-sizing: border-box;
				border-bottom: $ns-margin;
				display: flex;
				background: #ffffff;
				border-bottom: 1rpx solid #EEEEEE;

				.item-image {
					position: relative;
					width: 240rpx;
					height: 240rpx;
					overflow: hidden;
					position: relative;

					image {
						width: 240rpx;
						height: 240rpx;
					}

					.over{
						width: 120rpx;
						height: 120rpx;
						position: absolute;
						left: 50%;
						top: 50%;
						transform: translate(-50%, -50%);
					}

					&-new {
						width: 240rpx;
						height: 40rpx;
						line-height: 40rpx;
						background: #000000;
						opacity: 0.5;
						border-radius: 0 0 20rpx 20rpx;
						font-size: 26rpx;
						font-weight: 500;
						color: #FFFFFF;
						box-sizing: border-box;
						text-align: center;
						position: absolute;
						left: 0;
						bottom: 0;
					}
				}

				.item-desc {
					width: 440rpx;
					height: 100%;
					margin-left: 18rpx;
					box-sizing: border-box;
					display: flex;
					justify-content: space-between;
					flex-direction: column;

					// padding-bottom: 20rpx;
					.desc-title {
						font-size: $ns-font-size-base;
						font-weight: bold;

						overflow: hidden;
						text-overflow: ellipsis;
						display: -webkit-box;
						-webkit-line-clamp: 1;
						-webkit-box-orient: vertical;
						line-height: 1.5;
					}

				}
			}
		}
	}

	.price_box {
		display: flex;
		align-items: center;
		justify-content: space-between;
		margin-top: 10rpx;
	}

	.true_prcie {
		font-weight: bold;
	}

	.true_prcie_icon {
		color: #F84346;
		font-size: 22rpx;
	}

	.price_num {
		font-size: 32rpx;
		color: #F84346;
	}

	.get_maidou {
		font-size: 22rpx;
		color: #999999;
	}

	.gototuan {
		display: flex;
		align-items: center;

		image {
			width: 36rpx;
			height: 36rpx;
			border-radius: 50%;
			margin-left: -7rpx;
		}
	}

	.renpin {
		font-size: 20rpx;
		color: #FFFFFF;
		background: linear-gradient(225deg, #FE5838, #FB331D);
		border-radius: 4rpx 0rpx 0rpx 4rpx;
		display: inline-block;
		padding: 0 4rpx;
	}

	.yipin {
		color: #F84346;
		font-size: 20rpx;
		background: #FEDDDE;
		display: inline-block;
		padding: 0 4rpx;
		border-radius: 0rpx 4rpx 4rpx 0rpx;
	}

	.pintuan_game {
		width: 100%;
	}

	.bannerimg image {
		width: 100%;
		height: 280rpx;
	}

	.old-price {
		font-size: 24rpx;
		text-decoration: line-through;
		color: #999999;
		font-weight: normal;
		margin-left: 5rpx;
	}

	.desc-con {
		width: 100%;
		margin-top: 40rpx;

		.ptbackg {
			width: 100%;
			position: relative;

			image {
				width: 100%;
				height: 90rpx;
			}

			.ptbg_zi {
				position: absolute;
				top: 8rpx;
				left: 15rpx;
				display: flex;
				flex-direction: column;

				.ptbg_num {
					font-size: 22rpx;
					color: #FFFFFF;
				}

				.ptbg_get {
					font-size: 24rpx;
					color: #FFFFFF;
				}
			}
		}
	}

	.big_price {
		font-weight: bold;
	}
	.item-image-over{
		    width: 120rpx;
		    height: 120rpx;
		    line-height: 120rpx;
		    background: #000000;
		    opacity: 0.5;
		    border-radius: 50%;
		    font-size: 30rpx;
		    font-weight: 500;
		    color: #FFFFFF;
		    box-sizing: border-box;
		    text-align: center;
		    position: absolute;
		    left: 60rpx;
		    top: 60rpx;
	}
</style>
