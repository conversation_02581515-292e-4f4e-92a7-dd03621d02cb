<template>
	<view class="order-container" :class="themeStyle">
		<scroll-view id="tab-bar" class="order-nav" :scroll-x="true" :show-scrollbar="false" :scroll-into-view="scrollInto">
			<view v-for="(statusItem, statusIndex) in statusList" :key="statusIndex" class="uni-tab-item" :id="statusItem.id"
			 :data-current="statusIndex" @click="ontabtap">
				<view class="uni-tab-item-title" :class="statusItem.status == orderStatus ? 'uni-tab-item-title-active high-text-color' : ''">{{ statusItem.name }}
					<view class="line"></view>
				</view>
			</view>
		</scroll-view>

		<mescroll-uni ref="mescroll" @getData="getListData" :top="(navHeight+statusBarHeight)*2 + 'rpx'">
			<block slot="list">
				<view class="order-list" v-if="orderList.length > 0">
					<view class="order-item" v-for="(orderItem, orderIndex) in orderList" :key="orderIndex">
						<view class="order-header" :class="{ waitpay: (orderStatus == 'waitpay' && orderItem.order_status == 0) }">
							<view class="site-name-box">
                				<view class="site-name">订单号：{{ orderItem.order_no }}</view>
							</view>
							<text class="status-name high-text-color">{{ orderItem.status_desc }}</text>
						</view>
						<view class="order-body" @click="orderDetail(orderItem)">
							<view class="goods-wrap">
								<view class="goods-img">
									<image :src="$util.img(orderItem.sku_image)" @error="imageError(orderIndex)" mode='aspectFit'
									 :lazy-load="true"></image>
								</view>
								<view class="goods-info">
									<view class="goods-name">{{ orderItem.sku_name }}</view>
									<view class="goods-sub-section">
										<view>{{ orderItem.spec_name }}</view>
										<view>x1</view>
									</view>
									<view class="goods-price">
										<text class="unit">¥</text>
										<text>{{ orderItem.pay_money }}</text>
									</view>

								</view>
							</view>
						</view>
						<view class="order-footer">
							<view class="order-base-info">
								<view class="total">
									<text>共1件商品</text>
									<text>
										总计：
											<text class="ns-font-size-sm">¥</text>
											<text class="strong">{{ orderItem.pay_money }}</text>
									</text>
								</view>
							</view>
							<!-- <view class="order-operation" v-if="orderItem.action.length > 0 || orderItem.pintuan_status==1 || orderItem.pintuan_status==3"> -->
							<view class="order-operation">
								<view class="order-box-btn" @click="orderDetail(orderItem)">订单详情</view>
								<view class="order-box-btn order-pay" v-if="orderItem.pintuan_status==2 && orderItem.diff_num > 0">
									<view class="inviter-diff-num">
										<button class="share-btn"
										:data-group_id="orderItem.group_id"
										:data-sku_name="orderItem.sku_name"
										:data-sku_image="orderItem.sku_image"
                    :data-pay_money="orderItem.pay_money"
					@click="toPintuanDetail(orderItem)"
										>
											差{{orderItem.diff_num}}人 邀请好友
										</button>
									</view>
								</view>
								<view class="order-box-btn"
								:class="{ 'order-pay' : operationItem.action == 'orderPay' }"
								v-for="(operationItem, operationIndex) in orderItem.action"
								v-if="operationItem.action != 'inviterMember'"
								 :key="operationIndex" @click="operation(operationItem, orderItem)">
									{{ operationItem.title }}
									<view class="countdown" v-if="operationItem.action == 'orderPay'">
										<view class="clockrun">
											<uni-count-down
												:day="orderItem.discountTimeMachine.d"
												:hour="orderItem.discountTimeMachine.h"
												:minute="orderItem.discountTimeMachine.i"
												:second="orderItem.discountTimeMachine.s"
												color="#FFFFFF"
												splitorColor="#FFFFFF"
												background-color="#F2280C"
											/>
										</view>
									</view>
								</view>
								<view @click="toPintuanDetail(orderItem)" v-if="orderItem.pintuan_status==1 || orderItem.pintuan_status==3" class="order-box-btn">拼团详情</view>
							</view>
						</view>
					</view>
				</view>
				<view v-else>
					<ns-empty :isIndex="!1" :text="text"></ns-empty>
				</view>
			</block>
		</mescroll-uni>
		<loading-cover ref="loadingCover"></loading-cover>
    <yd-auth-popup ref="ydauth"></yd-auth-popup>
    <ns-login ref="login"></ns-login>
    <uni-coupon-pop ref="couponPop"></uni-coupon-pop>
	</view>
</template>

<script>
	import apiurls from "@/common/js/apiurls";
	import uniCountDown from '@/components/uni-count-down/uni-count-down.vue';
	import orderMethod from '../../public/js/orderMethod.js';

	export default {
		components: {
			uniCountDown
		},
		data() {
			return {
				scrollInto: '',
				orderStatus: 'all',
				statusList: [],
				orderList: [],
				contentText: {},
				mergePayOrder: [],
				text:'您还暂无相关订单',
				statusBarHeight:0,
				navHeight:0,
				group_id: null,
        isFirstLoad: true, //第一次打开页面
			};
		},
		mixins: [orderMethod],
		onLoad(option) {
			// 获取链接订单状态
			if (option.status) this.orderStatus = option.status;
			uni.getSystemInfo({
			  success: res => {
			    //导航高度
			    this.navHeight = res.statusBarHeight + 46 - wx.getSystemInfoSync()['statusBarHeight'];
			  },
			  fail(err) {
			    console.log(err);
			  }
			})
		},
		onShow() {
			// 刷新多语言
			this.$langConfig.refresh();
			// 订单状态初始化
			this.getOrderStatus();

			if (uni.getStorageSync('token')) {
				if (this.$refs.mescroll && !this.isFirstLoad) this.$refs.mescroll.refresh();
			} else {
        this.$util.toShowLoginPopup(this,null,'/promotionpages/pintuan/order/list/list?status=' + this.orderStatus);
				// this.$util.redirectTo('/pages/login/login/login', {
				// 	back: '/pages/order/list/list?status=' + this.orderStatus
				// });
			}
			if (uni.getStorageSync('is_register')) {
				this.$util.toShowCouponPopup(this)
				uni.removeStorageSync('is_register');
			}
		},
		methods: {
			// 切换订单tab
			ontabtap(e) {
				let index = e.target.dataset.current || e.currentTarget.dataset.current;
				this.orderStatus = this.statusList[index].status;
				if(this.orderStatus == 'all' ){
					this.text = '您还暂无相关订单'
				}else if(this.orderStatus == '0' ){
					this.text = '您还暂无待付款订单'
				}else if(this.orderStatus == '2' ){
					this.text = '您还暂无拼团中订单'
				}else if(this.orderStatus == '3' ){
					this.text = '您还暂无拼团成功订单'
				}else if(this.orderStatus == '-1' ){
					this.text = '您还暂无已失效订单'
				}else if(this.orderStatus == '1' ){
					this.text = '您还暂无已失败订单'
				}
				if (this.orderStatus == '') this.mergePayOrder = [];
				// this.$refs.loadingCover.show();
				this.$refs.mescroll.refresh();
			},
			// 获取订单数据
			getListData(mescroll) {
				this.$api.sendRequest({
					url: '/api/Pintuan/orderList',
					// url: '/api/order/lists',
					data: {
						page: mescroll.num,
						page_size: mescroll.size,
						pintuan_status: this.orderStatus
						// order_status: this.orderStatus
					},
					success: res => {
						let newArr = []
						let msg = res.message;
						if (res.code == 0 && res.data) {
							newArr = res.data.list;
						} else {
						  if(!uni.getStorageSync('token') && res.code==-10009){

						  }else{
							this.$util.showToast({
							  title: msg
							})
						  }
						}
						mescroll.endSuccess(newArr.length);
						//设置列表数据
						if (mescroll.num == 1) this.orderList = []; //如果是第一页需手动制空列表
						this.orderList = this.orderList.concat(newArr); //追加新数据
						if (this.$refs.loadingCover) this.$refs.loadingCover.hide();
						for (let i = 0; i < this.orderList.length; i++) {
							if (this.orderList[i].close_time) {
								this.orderList[i].discountTimeMachine = this.$util.countDown(this.orderList[i].close_time);
							}
						}
            this.isFirstLoad = false
					},
					fail: res => {
						mescroll.endErr();
						if (this.$refs.loadingCover) this.$refs.loadingCover.hide();
					}
				});
			},
			/**
			 * 获取订单状态
			 */
			getOrderStatus() {
				this.statusList = [{
						status: 'all',
						name: this.$lang('all'),
						id: 'all'
					},
					{
						status: '0',
						name: this.$lang('waitPay'),
						id: '0'
					},
					{
						status: '2',
						name: this.$lang('underWay'),
						id: '2'
					},
					{
						status: '3',
						name: this.$lang('success'),
						id: '3'
					},
					// {
					// 	status: '-1',
					// 	name: '已失效',
					// 	id: '3'
					// },
					{
						status: '1',
						name: this.$lang('fail'),
						id: '1'
					}
				];
			},
			// 订单按钮操作
			operation(operationItem, orderData) {
				let action = operationItem.action
				let index = this.status;
				switch (action) {
					case 'orderPay': // 支付
						if(operationItem.disabled) {
							this.$util.showToast({
								title: operationItem.disabled_tips
							})
						}else{
							this.orderPay(orderData, () => {
								this.$refs.mescroll.refresh();
							});
						}
						break;
					case 'orderClose': //取消订单
						if(operationItem.disabled) {
							this.$util.showToast({
								title: operationItem.disabled_tips
							})
						}else{
							this.orderClose(orderData, () => {
								this.$refs.mescroll.refresh();
							});
						}
						break;
					case 'orderDel': //删除订单
						this.orderDel(orderData.order_id, () => {
							this.$refs.mescroll.refresh();
						});
						break;
				}
			},
			// 前往订单详情
			orderDetail(data) {
				this.$util.redirectTo('/promotionpages/pintuan/order/detail/detail', {
					order_id: data.id
				});
			},
			// 前往参团详情
			toPintuanDetail(data) {
				console.log(data.group_id)
				this.$util.redirectTo('/promotionpages/pintuan/share/share', {
					group_id: data.group_id,
				});
			},
			/**
			 * 合并支付
			 */
			mergePay() {
				if (this.mergePayOrder.length) {
					this.$api.sendRequest({
						url: '/api/order/pay',
						data: {
							order_ids: this.mergePayOrder.toString()
						},
						success: res => {
							if (res.code >= 0) {
								this.$util.redirectTo('/pages/pay/index/index', {
									code: res.data
								});
							} else {
								this.$util.showToast({
									title: res.message
								});
							}
						}
					});
				}
			},
			// 订单列表商品图片缺省
			imageError(orderIndex) {
				this.orderList[orderIndex].sku_image = this.$util.getDefaultImage().default_goods_img;
				this.$forceUpdate();
			},
			navigateBack() {
				this.$util.redirectTo('/pages/member/index/index','','redirectTo');
			},
      /**
       *分享参数组装
       */
      getSharePageParams() {
        return this.$util.unifySharePageParams('/otherpages/shop/home/<USER>', this.shareTitle || '先迈商城',
            '',{}, this.$util.img(this.shareImg) || this.$util.img('public/static/youpin/home_share.jpg'))
      }
		},
    /**
     * 自定义分享内容
     * @param {Object} res
     */
    onShareAppMessage(res) {
      let { title, link, imageUrl, query } = this.getSharePageParams()
      return this.$buriedPoint.pageShare(link , imageUrl, title);
    },
		/**
		 * 自定义分享内容
		 * @param {Object} res
		 */
		// onShareAppMessage(res) {
		// 	console.log(res)
		// 	let shop_id=uni.getStorageSync('shop_id');
		// 	let group_id=res.target.dataset.group_id;
		// 	let sku_image=res.target.dataset.sku_image;
		// 	let sku_name=res.target.dataset.sku_name;
		// 	let pay_money=res.target.dataset.pay_money;
		// 	let member_id=uni.getStorageSync('member_id');
		// 	var path = `/promotionpages/pintuan/share/share?group_id=${group_id}&shop_id=${shop_id}&recommend_member_id=${member_id}`;
		// 	return {
		// 		title:`邀请￥${pay_money} 拼团抢${sku_name}`,
		// 		imageUrl: this.$util.img(sku_image),
		// 		path: path,
		// 		success: res => {},
		// 		fail: res => {}
		// 	};
		// },
		computed: {
			mpOrderList() {
				if (!this.orderList[this.status]) return;
				return this.orderList[this.status].list || [];
			},
			//vueX页面主题
			themeStyle() {
				return 'theme-' + this.$store.state.themeStyle
			}
		},

	};
</script>

<style lang="scss">
	@import '../../public/css/orderList.scss';
	/* 标题栏 */
	.custom{
		background: #fff;
		display: flex;
		align-items: center;
		justify-content: center;
		position: fixed;
		top: 0;
		left: 0;
		width: 100%;
		.iconfont{
			font-size:$ns-font-size-base + 12;
			color:#333;
			font-weight: bold;
			position: absolute;
			left: 20rpx;
		}
		.custom-navbar{
			display: flex;
			border-radius: 30rpx;
			background: #FFF4F4;
			width: 360rpx;
			align-items: center;
			.navbar-item{
				height: 60rpx;
				line-height: 60rpx;
				width: 50%;
				text-align: center;
				color: $base-color;
				font-size: $ns-font-size-base + 2;
				&.active{
					background:$base-color;
					color: #FFFFFF;
					border-radius: 30rpx;
				}
			}
		}
	}

</style>
<style scoped>
	/deep/ .uni-page {
		overflow: hidden;
	}

	/deep/ .mescroll-upwarp {
		padding-bottom: 100rpx;
	}
	.countdown .clockrun >>> .uni-countdown  {
		display: flex;
		justify-content: center;
		align-items: center;
		height: 64rpx;
		padding: 0;
	}
	.countdown .clockrun >>> .uni-countdown__number {
		background: #000;
		/* // #690b08 */
		padding: 0;
		margin: 0;
		border: none;
	}

	.countdown .clockrun >>> .uni-countdown__splitor {
		padding: 0;
		color: #000;
	}

	.countdown .clockrun >>> .uni-countdown__splitor.day {
		width: initial;
	}
	/deep/.mescroll-upwarp{
		padding: 0 !important;
		margin-bottom: 0;
		min-height: 0;
		line-height: 0;
	}
	.inviter-diff-num {
		color: #fff;
	}
  .order-list{
    padding-bottom: 100rpx;
  }
</style>
