<template>
	<view class="detail-container" :class="{ themeStyle, 'safe-area': isIphoneX}">
		<!-- 订单状态 -->
		<!-- all全部 0未支付 1拼团失败 2组团中 3拼团成功 -1已失效【已关闭】 -->
		<!-- 1拼团失败 2组团中 3拼团成功 -->
		<!-- 0待付款、-1已失效 按照原来的样式 -->
		<view class="status-wrap" :class="{'status-success-or-fail':orderData.pintuan_status==1||orderData.pintuan_status==3,'status-underway':orderData.pintuan_status==2}">
			<view class="bg">
				<!-- <block v-if="orderData.pintuan_status==3">
					<image class="status-bg" v-if="orderData.win_status==1" :src="$util.img('public/static/youpin/order/pintuan/bg-success.png')"></image>
					<image class="status-bg" v-if="orderData.win_status==0" :src="$util.img('public/static/youpin/order/pintuan/bg-fail.png')"></image>
				</block>
				<image class="status-bg" v-if="orderData.pintuan_status==1" :src="$util.img('public/static/youpin/order/pintuan/bg-error.png')"></image>
				<image class="status-bg" v-if="orderData.pintuan_status!=1&&orderData.pintuan_status!=3" :src="$util.img('public/static/youpin/order/bg-detail.png')"></image> -->
				<image class="status-bg" :src="bg_image"></image>
			</view>
			<view class="status-box" v-if="orderData.pintuan_status == 0 || orderData.pintuan_status == -1">
				<view class="status-name-box">
					<image class="icon-pay-clock" :src="$util.img('public/static/youpin/order/icon-pay-clock.png')"/>
						<text class="status-name" v-if="orderData.status_desc=='待支付'">等待买家付款</text>
						<text class="status-name" v-else>{{ orderData.status_desc }}</text>
				</view>
				<block v-if="orderData.pintuan_status == 0">
					<view class="desc-box">
						<text class="desc">
							需支付：
							<text class="ns-font-size-sm">{{ $lang('common.currencySymbol') }}</text>
							<text class="ns-font-size-lg strong">{{ orderData.pay_money }}</text>
						</text>
						<view class="desc">
							<text>剩余：</text>
							<view class="countdown">
								<view class="clockrun">
									<uni-count-down :day="orderData.discountTimeMachine.d" :hour="orderData.discountTimeMachine.h" :minute="orderData.discountTimeMachine.i"
									 :second="orderData.discountTimeMachine.s" color="#FFFFFF" splitorColor="#FFFFFF" background-color="transparent" />
								</view>
							</view>
						</view>
					</view>
					<!-- <view class="operation-group"><view class="operation-btn ns-text-color" @click="operation('orderPay')">去支付</view></view> -->
				</block>
			</view>
			<view class="underway" v-if="orderData.pintuan_status == 2">
				<view class="underway-title">拼团中 (还差<text>{{orderData.diff_num}}人</text>)，剩余<countdown-timer autoStart class="pintuan-countdown"
					 :time="orderData.distance_time" showColon :format="'dd天hh:mm:ss'" @finish="getOrderData"></countdown-timer>
				</view>
				<navigator :url="`/promotionpages/pintuan/join_member/join_member?group_id=${orderData.group_id}`" class="member-list">
					<view v-for="(member,index,member_id) in orderData.member_list" :key="member_id">
						<view class="img-wrap" v-if="index < 4">
							<view v-if="member.member_id==orderData.head_id" class="head-tag">团长</view>
							<image class="member-list-img" :src="$util.img(member.member_img)" @error="" mode="aspectFill"></image>
							<image class="redic_pay" :src="$util.img('public/static/youpin/order/pintuan/radicpay.png')" v-if="member.pintuan_status==0"/>
						</view>
					</view>
					<view class="has-diff-num">
						<image class="img" :src="$util.img('public/static/youpin/pintuan/rule-icon1.png')"></image>
					</view>
					<view class="has-diff-num" v-if="orderData.diff_num>1">
						<image class="img" :src="$util.img('public/static/youpin/pintuan/more-icon.png')"></image>
					</view>
				</navigator>
				<view class="underway-inviter-btn">
          <!-- #ifdef MP-WEIXIN -->
					<button class="share-btn" :data-group_id="orderData.group_id" :data-sku_name="orderData.sku_name" :data-sku_image="orderData.sku_image"
					 open-type="share">
						邀请好友拼团
					</button>
          <!-- #endif -->
          <!-- #ifdef H5 -->
          <button class="share-btn" :data-group_id="orderData.group_id" :data-sku_name="orderData.sku_name" :data-sku_image="orderData.sku_image"
                  @click="toShare">
            邀请好友拼团
          </button>
          <!-- #endif -->
				</view>
			</view>
			<block v-if="orderData.pintuan_status==1 || orderData.pintuan_status==3 || orderData.pintuan_status==-2">
				<view class="rewards">
					<view class="bg-rewards" :style="'background-image:url(' + $util.img('public/static/youpin/order/pintuan/bg-border.png') + ');'">
						<view :class=" awardList.length>3 ?  'bg-rewards-scroll' : 'bg-rewards-center'">
							<block v-for="(award,index) in awardList" v-bind:key="index">
								<view class="rewards-wrap" v-if="!award.content">
									<image class="sku_image" :src="award.image" @error="awardImageError()" mode="aspectFill"></image>
								</view>
								<view v-else>
									<image class="rewards-img" :src="award.image"></image>
									<view>{{award.content}}</view>
								</view>
							</block>
						</view>
					</view>
				</view>
			</block>
			<!-- <block v-if="orderData.pintuan_status==1">
				<view class="rewards">
					<view class="bg-rewards" :style="'background-image:url(' + $util.img('public/static/youpin/order/pintuan/bg-border.png') + ');'">
						<view>
							<image class="rewards-img" :src="$util.img('public/static/youpin/order/pintuan/img-refund.png')"></image>
							<view>原路退款</view>
						</view>
					</view>
				</view>
			</block>
			<block v-if="orderData.pintuan_status==3">
				<view class="rewards" v-if="orderData.win_status == 0 || orderData.win_status == 1">
					<view class="bg-rewards" :style="'background-image:url(' + $util.img('public/static/youpin/order/pintuan/bg-border.png') + ');'">
						<view class="rewards-wrap" v-if="orderData.win_status == 1">
							<image class="sku_image" :src="orderData.awardInfo.sku_image" @error="awardImageError()" mode="aspectFill"></image>
						</view>
						<view v-if="orderData.win_status == 0">
							<image class="rewards-img" :src="$util.img('public/static/youpin/order/pintuan/img-refund.png')"></image>
							<view>原路退款</view>
						</view>
						<view v-if="orderData.awardInfo.length!=0 && orderData.awardInfo.award_balance!=0">
							<image class="rewards-img" :src="$util.img('public/static/youpin/order/pintuan/img-reward.png')"></image>
							<view>{{orderData.awardInfo.award_balance}}元奖励</view>
						</view>
						<view v-if="orderData.awardInfo.length!=0 && orderData.awardInfo.award_maidou!=0">
							<image class="rewards-img" :src="$util.img('public/static/youpin/order/pintuan/img-maidou.png')"></image>
							<view>{{orderData.awardInfo.award_maidou}}迈豆</view>
						</view>
					</view>
				</view>
			</block> -->
		</view>

		<!-- 地址信息 -->
		<view class="address-wrap active">
			<view class="icon">
				<image class="icon-pay-clock" :src="$util.img('public/static/youpin/order/icon-no-pay-address.png')">
			</view>
			<view class="address-info">
				<view class="info">
					<view class="info-name">{{ orderData.name }}</view>
					<view>{{ orderData.mobile || orderData.telephone }}</view>
				</view>
				<view class="detail">
					<text>{{ orderData.full_address }} {{ orderData.address }}</text>
				</view>
			</view>
		</view>
		<!-- 店铺 -->
		<view class="site-wrap">
			<view class="site-header">
				<!--				<view class="iconfont icondianpu"></view>-->
				<text class="site-name">订单号：{{ orderData.order_no }}</text>
			</view>
			<view class="site-body">
				<view class="goods-wrap">
					<view hover-class="none" class="goods-img">
						<image :src="$util.img(orderData.sku_image)" @error="imageError()" mode="aspectFill"></image>
					</view>
					<view class="goods-info">
						<view hover-class="none" class="goods-name"><text class="pintuan-tag">拼团</text>{{ orderData.sku_name }}</view>
						<view class="goods-sub-section">
							<view>{{ orderData.spec_name }}</view>
							<view>x1</view>
						</view>
						<view class="goods-price">
							<text class="unit">¥</text>
							<text>{{ orderData.pay_money }}</text>
						</view>
					</view>
				</view>
			</view>
		</view>
		<!-- 订单金额 -->
		<view class="order-money">
			<view class="order-cell">
				<text class="tit">共1件商品</text>
				<view class="box align-right">
					<text class="order-cell-right">
						<text class="ns-font-size-sm">¥</text>
						{{ orderData.pay_money }}
					</text>
				</view>
			</view>
			<view class="order-cell">
				<text class="tit">运费</text>
				<view class="box align-right">
					<text class="order-cell-right">
						<text class="ns-font-size-sm">¥</text>
						<text>0.00</text>
					</text>
				</view>
			</view>
			<view class="order-cell">
				<text class="tit">实付金额</text>
				<view class="box align-right">
					<text class="order-cell-right">
						<text class="pay-money">¥</text>
						<text class="pay-money">{{ orderData.pay_money }}</text>
					</text>
				</view>
			</view>
		</view>
		<!-- 订单操作 -->
		<view>
			<view class="order-operation" v-if="orderData.action.length > 0">
				<!-- <view class="order-box-btn order-pay" v-if="orderData.pintuan_status==2 && orderData.diff_num > 0">
					<view class="inviter-diff-num">
						<button class="share-btn"
						:data-group_id="orderData.group_id"
						:data-sku_name="orderData.sku_name"
						:data-sku_image="orderData.sku_image"
						open-type="share">
							差{{orderData.diff_num}}人 邀请好友
						</button>
					</view>
				</view> -->
				<!-- 已支付待发货状态详情不显示再次购买按钮 -->
				<view class="order-box-btn" :class="{ 'order-pay' : operationItem.action == 'orderPay' }" v-for="(operationItem, operationIndex) in orderData.action"
				 v-if="operationItem.action != 'inviterMember'" :key="operationIndex" @click="operation(operationItem, orderData)">
					{{ operationItem.title }}
				</view>
			</view>
		</view>

		<!-- 订单概况 -->
		<view class="order-summary">
			<view class="order-cell" v-if="orderData.order_no">
				<text class="tit">订单号：</text>
				<view class="box">
					<text class="ns-text-color-black">{{ orderData.order_no }}</text>
					<view class="copy" @click="to_copy_order_no">复制</view>
				</view>
			</view>
			<view class="order-cell" v-if="orderData.out_trade_no">
				<text class="tit">支付单号：</text>
				<view class="box">
					<text class="ns-text-color-black">{{ orderData.out_trade_no }}</text>
				</view>
			</view>
			<view class="order-cell" v-if="orderData.add_time">
				<text class="tit">下单时间：</text>
				<view class="box">
					<text class="ns-text-color-black">{{ $util.timeStampTurnTime(orderData.add_time) }}</text>
				</view>
			</view>
			<block v-if="orderData.pay_time">
				<view class="order-cell">
					<text class="tit">支付时间：</text>
					<view class="box">
						<text class="ns-text-color-black">{{ $util.timeStampTurnTime(orderData.pay_time) }}</text>
					</view>
				</view>
				<view class="order-cell" v-if="orderData.app_type_name">
					<text class="tit">支付方式：</text>
					<view class="box">
						<text class="ns-text-color-black">{{ orderData.pay_type_name }}</text>
					</view>
				</view>
			</block>
		</view>
		<loading-cover ref="loadingCover"></loading-cover>
    <diy-share-navigate-h5 ref="shareNavigateH5"></diy-share-navigate-h5>
	</view>
</template>

<script>
	import uniCountDown from '@/components/uni-count-down/uni-count-down.vue';
	import orderMethod from '../../public/js/orderMethod.js';
	import globalConfig from '@/common/mixins/golbalConfig.js'
  import system from "../../../../common/js/system";
  import diyShareNavigateH5 from '@/components/diy-share-navigate-h5/diy-share-navigate-h5'
  // #ifdef H5
  import {isOnXianMaiApp} from "../../../../common/js/h5/appOP";
  // #endif
	export default {
		components: {
			uniCountDown,
      diyShareNavigateH5
		},
		data() {
			return {
				isIphoneX: false,
				orderId: 0,
				orderData: {
					action: []
				},
				refundRemark: "",
				keyHeight: 0,
				bg_image: '',
				awardList: [],
        // #ifdef H5
        isOnXianMaiApp:isOnXianMaiApp,
        // #endif
			};
		},
		onLoad(option) {
			if (option.order_id) this.orderId = option.order_id;
			// #ifdef MP-WEIXIN
			uni.hideShareMenu()
			// #endif
		},
		async onShow() {
			// 刷新多语言
			this.$langConfig.refresh();
      await system.wait_staticLogin_success();

			this.isIphoneX = this.$util.uniappIsIPhoneX();

			if (uni.getStorageSync('token')) {
				this.getOrderData();
			} else {
				this.$util.redirectTo('/pages/login/login/login', {
					back: '/promotionpages/pintuan/order/detail/detail?order_id=' + this.orderId
				});
			}
		},
		mixins: [globalConfig, orderMethod],
		methods: {
			getOrderData() {
				this.$api.sendRequest({
					url: '/api/Pintuan/orderDetail',
					data: {
						id: this.orderId
					},
					success: res => {
						uni.stopPullDownRefresh();
						if (res.code >= 0) {
							if (this.$refs.loadingCover) this.$refs.loadingCover.hide();
							this.orderData = res.data;
							this.bg_image = res.data.bg_image;
							this.awardList = res.data.awardList;
							if (this.orderData.close_time) {
								this.orderData.discountTimeMachine = this.$util.countDown(this.orderData.close_time);
							}
							if (this.orderData.distance_time) {
								this.orderData.distance_time = Math.abs(this.orderData.distance_time) * 1000
							}
              // #ifdef H5
              this.setWechatShare();
              // #endif
						} else {
							this.$util.showToast({
								title: '未获取到订单信息!！',
								success: () => {
									// setTimeout(() => {
									// 	this.getOrderData();
									// }, 1500);
								}
							});
						}
					},
					fail: res => {
						uni.stopPullDownRefresh();
						if (this.$refs.loadingCover) this.$refs.loadingCover.hide();
					}
				});
			},
      /**
       *分享参数组装(注意需要分享的那一刻再调此方法)
       */
      getSharePageParams(){
        let share_data=this.$util.unifySharePageParams('/promotionpages/pintuan/share/share','先迈商城',
            `邀请拼团抢${this.orderData.sku_name}`,{group_id:this.orderData.group_id},this.$util.img(this.orderData.sku_image))
        return share_data;
      },
      /**
       * 设置微信公众号分享
       */
      setWechatShare() {
        // 微信公众号分享
        // #ifdef H5
        let share_data=this.$util.deepClone(this.getSharePageParams());
        let link=window.location.origin+this.$router.options.base+share_data.link.slice(1);
        share_data.link=link;
        this.$util.publicShare(share_data,()=>{
          system.pintuanShareActionReport({pintuan_id:this.orderData.pintuan_id,goods_id:this.orderData.goods_id})
        });
        // #endif
      },
      toShare(){
        let share_data=this.getSharePageParams();
        this.$refs.shareNavigateH5.open(share_data,()=>{
          if(this.isOnXianMaiApp){
            system.pintuanShareActionReport({pintuan_id:this.orderData.pintuan_id,goods_id:this.orderData.goods_id})
          }
        });
      },
			/**
			 * 下拉刷新
			 */
			onPullDownRefresh() {
				this.getOrderData();
			},
			operation(operationItem, orderData) {
				let action = operationItem.action
				switch (action) {
					case 'orderPay': // 支付
						if(operationItem.disabled) {
							this.$util.showToast({
								title: operationItem.disabled_tips
							})
						}else{
							this.orderPay(this.orderData, () => {
								this.getOrderData();
							});
						}
						break;
					case 'orderClose': //取消订单
						if(operationItem.disabled) {
							this.$util.showToast({
								title: operationItem.disabled_tips
							})
						}else{
							this.orderClose(orderData, () => {
								this.getOrderData();
							});
						}
						break;
				}
			},
			imageError() {
				this.orderData.sku_image = this.$util.getDefaultImage().default_goods_img;
				this.$forceUpdate();
			},
			awardImageError() {
				this.orderData.awardInfo.sku_image = this.$util.getDefaultImage().default_goods_img;
				this.$forceUpdate();
			},
			toShopDetail(e) {
				this.$util.redirectTo('/otherpages/shop/index/index', {
					site_id: e
				});
			},
      to_copy_order_no(){
        this.$util.copy(this.orderData.order_no);
      },
		},
		/**
		 * 自定义分享内容 邀请好友
		 * @param {Object} res
		 */
		onShareAppMessage(res) {
      let share_data=this.getSharePageParams();
      system.pintuanShareActionReport({pintuan_id:this.orderData.pintuan_id,goods_id:this.orderData.goods_id})
      return this.$buriedPoint.pageShare(share_data.link, share_data.imageUrl, share_data.desc,true,{goods_id:this.orderData.goods_id});
		},
		filters: {
			abs(value) {
				return Math.abs(parseFloat(value)).toFixed(2);
			},
			// 转化时间字符串
			timeStr(val) {
				var h = parseInt(val / 3600).toString();
				var m = parseInt((val % 3600) / 60).toString();
				if (m.length == 1) {
					m = '0' + m;
				}
				if (h.length == 1) {
					h = '0' + h;
				}
				return h + ':' + m;
			},
			/**
			 * 金额格式化输出
			 * @param {Object} money
			 */
			moneyFormat(money) {
				return parseFloat(money).toFixed(2);
			},
		}
	};
</script>

<style lang="scss">
	@import '../../public/css/orderDetail.scss';
</style>
<style scoped>
	/deep/ .uni-page {
		overflow: hidden;
	}

	.countdown .clockrun>>>.uni-countdown {
		display: flex;
		justify-content: center;
		align-items: center;
		height: 64rpx;
		padding: 0;
	}

	.countdown .clockrun>>>.uni-countdown__number {
		background: #000;
		/* // #690b08 */
		padding: 0;
		margin: 0;
		border: none;
		font-weight: bold;
		font-size: 36rpx;
	}

	.countdown .clockrun>>>.uni-countdown__splitor {
		padding: 0;
		color: #000;
		font-weight: bold;
		font-size: 36rpx;
	}

	.countdown .clockrun>>>.uni-countdown__splitor.day {
		width: initial;
		font-weight: bold;
		font-size: 36rpx;
	}

	/deep/ .uni-popup__wrapper-box {
		max-width: 620rpx;
		width: 620rpx;
	}
</style>
<style lang="scss" scoped>
	.my-popup {
		position: fixed;
		top: 0;
		left: 0;
		width: 100%;
		height: 100vh;
		z-index: 2;
		display: flex;
		align-items: center;
		justify-content: center;
	}

	.popup-mask {
		position: absolute;
		top: 0;
		left: 0;
		width: 100%;
		height: 100vh;
		background-color: rgba($color: #000000, $alpha: 0.75);
	}

	.popup-box {
		width: 620rpx;
		z-index: 3;
	}

	.popup-dialog {
		overflow: hidden;
		height: 506rpx;
		background: #FFFFFF;
		border-radius: 20rpx;
		box-sizing: border-box;

		.popup-dialog-header {
			height: 106rpx;
			line-height: 106rpx;
			text-align: center;
			font-size: 36rpx;
			color: #333333;
			font-weight: bold;
		}

		.popup-dialog-body {
			textarea {
				width: 556rpx;
				padding: 15rpx;
				box-sizing: border-box;
				margin: 0 auto;
				border: 1px solid #CCCCCC;
				height: 260rpx;
				border-radius: 8rpx;
			}
		}

		.popup-dialog-footer {
			margin: 0 32rpx;
			height: 140rpx;
			display: flex;
			align-items: center;
			justify-content: space-around;

			.button {
				width: 220rpx;
				height: 68rpx;
				line-height: 68rpx;
				text-align: center;
				border-radius: 34rpx;
				box-sizing: border-box;
				margin: 0;

				&.white {
					color: #F2270C;
					background: #FFFFFF;
					border: 1rpx solid #F2270C;
				}

				&.red {
					color: #FFFFFF;
					background: #F2270C;

				}
			}
		}
	}

	.heightvh {
		overflow-y: hidden;
	}

	.refund-apply-money {
		display: flex;
		align-items: center;
		justify-content: space-between;
		padding: 0 24rpx;
		margin: 0 24rpx;
		margin-bottom: 20rpx;
		height: 100rpx;
		border-radius: 20rpx;
		background-color: #FFFFFF;

		.name {
			font-weight: bold;
			color: #333333;
			font-size: 28rpx;
		}

		.money {
			.text {
				font-weight: normal;
				font-size: 26rpx;
			}

			font-weight: bold;
			color: #F2270C;
			font-size: 36rpx;
		}
	}
</style>
