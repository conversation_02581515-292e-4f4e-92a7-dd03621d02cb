@mixin wrap {
	margin: $ns-margin;
	padding: $ns-padding;
	border-radius: $ns-border-radius;
	background: #fff;
	position: relative;
}
.uni-list-cell {
	display: flex;
	justify-content: space-between;
}
.align-right {
	text-align: right;
}

.inline {
	display: inline !important;
}

.order-container {
	padding-bottom: 120rpx;

	&.safe-area {
		padding-bottom: 188rpx;
	}
}

.address-wrap {
	background: #fff;
	position: relative;
	min-height: 100rpx;
	max-height: 140rpx;
	display: flex;
	align-items: center;
	.icon {
		width: 94rpx;
		height: 100rpx;
		display: flex;
		justify-content: center;
		align-items: center;
		image {
		  width: 48rpx;
		  height: 48rpx;
		}
	}

	.address-info {
		width: 656rpx;
		.info {
			display: flex;
			padding-top: 10rpx;
			text {
				overflow: hidden;
				display: -webkit-box;
				-webkit-line-clamp: 1;
				-webkit-box-orient: vertical;
				word-break: break-all;
				font-weight: bold;
				&:first-of-type {
					max-width: 380rpx;
					margin-right: 32rpx;
				}
			}
		}

		.detail {
			width: 508rpx;
			padding-bottom: 17rpx;
			line-height: 1.3;
			font-size: 26rpx;
		}
	}

	.address-empty {
		line-height: 100rpx;
		color: #999999;
		font-size: 26rpx;
	}

	.cell-more {
		position: absolute;
		top: 50%;
		transform: translateY(-50%);
		right: 20rpx;

		.iconfont {
			color: #999;
		}
	}
}

.mobile-wrap {
	@include wrap;

	.form-group {
		.form-item {
			display: flex;
			line-height: 50rpx;

			.text {
				display: inline-block;
				line-height: 50rpx;
				padding-right: 10rpx;
			}

			.placeholder {
				line-height: 50rpx;
			}

			.input {
				flex: 1;
				height: 50rpx;
				line-height: 50rpx;
			}
		}
	}
}

.order-cell {
	display: flex;
	margin: 48rpx 0;
	align-items: center;
	background: #fff;
	line-height: 40rpx;

	.tit {
		text-align: left;
	}

	.box {
		flex: 1;
		line-height: inherit;

		.textarea {
			height: 40rpx;
		}
	}
	.iconfont {
		color: #bbb;
		font-size: 28rpx;
	}

	.order-pay {
		padding: 0;
		font-size: 26rpx;
		color: #333;
		text {
			display: inline-block;margin-left: 20rpx;
		}
		.pay-money {
			font-size: 32rpx;
			font-weight: bold;
			margin-left: 2rpx;
		}
	}
}

.site-wrap {
	margin: 24rpx;
	padding: 0 24rpx;
	border-radius: 20rpx;
	background: #fff;
	position: relative;

	.site-header {
		display: flex;
		align-items: center;
		height: 88rpx;
		.icondianpu {
			display: inline-block;
			line-height: 1;
			margin-right: 17rpx;
			font-size: 28rpx;
			font-weight: bold;
		}
	}

	.site-body {
		.goods-wrap {
			margin-bottom: 20rpx;
			display: flex;
			position: relative;

			&:last-of-type {
				margin-bottom: 0;
			}

			.goods-img {
				width: 180rpx;
				height: 180rpx;
				margin-right: 20rpx;

				image {
					width: 100%;
					height: 100%;
					border-radius: 20rpx;
				}
			}

			.goods-info {
				flex: 1;
				position: relative;
				max-width: calc(100% - 200rpx);

				.goods-name {
					display: -webkit-box;
					-webkit-box-orient: vertical;
					-webkit-line-clamp: 2;
					overflow: hidden;
					line-height: 1.5;
					font-size: 28rpx;
					margin-bottom: 30rpx;
				}

				.goods-sub-section {
					width: 100%;
					line-height: 1.3;
					display: flex;
					justify-content: space-between;
					.unit {
						font-weight: normal;
						font-size: 24rpx;
						margin-right: 2rpx;
					}

					view {
						color: #999999;
						font-size: 24rpx;
						line-height: 1.3;
						&:last-of-type {
							text-align: right;
						}
					}
				}
				.goods-price {
					color: #333333;
					text-align: right;
					font-size: 24rpx;
					.price {
						font-size: 28rpx;
					}
				}
			}
		}
	}

	.site-footer {
		padding-bottom: 30rpx;
		.order-cell {
			.tit {
				width: 180rpx;
			}
			.store-promotion-box {
				overflow: hidden;
				text-overflow: ellipsis;
				white-space: nowrap;
			}
			.box {
				input {
					text-align: right;
					font-size: 26rpx;
				}
				&.text-overflow {
					max-width: calc(100% - 180rpx);
				}
			}
			&.my-coupon {
				padding: 26rpx 0;
				margin: 0;
				text-align: right;
				.box {
					&.text-overflow {
							max-width: unset;
					}
				}
			}


			&:last-of-type {
				margin-bottom: 0;
			}
		}
	}
}

.order-checkout {
	@include wrap;

	.order-cell {
		.iconyuan_checkbox,
		.iconyuan_checked {
			font-size: 38rpx;
		}
	}
}

.order-money {

	.order-cell {
		.box {
			padding: 0;

			.operator {
				font-size: 24rpx;
				margin-right: 6rpx;
			}
		}
	}
}

.order-submit {
	position: fixed;
	z-index: 5;
	left: 0;
	bottom: 0;
	width: 100vw;
	height: 98rpx;
	background: #fff;
	box-shadow: 0 0px 10px rgba(0, 0, 0, 0.1);
	display: flex;
	align-items: center;

	&.bottom-safe-area {
		padding-bottom: 0 !important;
		padding-bottom: constant(safe-area-inset-bottom) !important;
		padding-bottom: env(safe-area-inset-bottom) !important;
	}

	.order-settlement-info {
		flex: 1;
		height: 98rpx;
		line-height: 98rpx;
		padding-left: 25rpx;
		font-size: 28rpx;
		.money {
			font-size: 48rpx;
		}
	}

	.submit-btn {
		height: 80rpx;
		margin-right: 24rpx;
		display: flex;
		justify-content: center;
		align-items: center;
		button {
			padding: 0;
			width: 200rpx;
			background-color: #F2270C!important;
			height: 80rpx;
			line-height: 80rpx;
			font-size: 28rpx;
			border-radius: 40rpx;
		}
	}
}

.popup {
	width: 100vw;
	background: #fff;
	border-top-left-radius: 24rpx;
	border-top-right-radius: 24rpx;

	.popup-header {
		height: 90rpx;
		display: flex;
		align-items: center;
		padding: 0 30rpx;

		& > view {
			flex: 1;
			line-height: 1;
		}

		.tit {
			font-size: 32rpx;
			font-weight: 600;
		}

		.vice-tit {
			margin-right: 20rpx;
		}
	}

	.popup-body {
		height: calc(100% - 210rpx);
		height: calc(100% - 210rpx - constant(safe-area-inset-bottom));
		height: calc(100% - 210rpx - env(safe-area-inset-bottom));
	}

	.popup-footer {
		// height: 120rpx;
		padding-bottom: 0 !important;
		padding-bottom: constant(safe-area-inset-bottom) !important;
		padding-bottom: env(safe-area-inset-bottom) !important;

		.confirm-btn {
			height: 72rpx;
			line-height: 72rpx;
			color: #fff;
			text-align: center;
			margin: 20rpx 40rpx;
			border-radius: 40rpx;
		}
	}
}

.invoice-popup {
	height: 65vh;

	.popup-body {
		.invoice-cell {
			margin: 0 30rpx;
			padding: 30rpx 0;
			border-top: 1px solid #f5f5f5;

			&:first-of-type {
				border-top: none;
			}

			.tit {
				font-size: 28rpx;
				font-weight: 600;
			}

			.option-grpup {
				padding-top: 20rpx;

				.option-item {
					display: inline-block;
					line-height: 1;
					font-size: 28rpx;
					padding: 16rpx 40rpx;
					background: #eee;
					border: 1px solid #eee;
					border-radius: 32rpx;
					margin: 0 20rpx 20rpx 0;

					&:nth-of-type(1),
					&:nth-of-type(2),
					&:nth-of-type(3) {
						margin-bottom: 0;
					}

					&.active {
						// background: opacify($base-color-rgba, 0.01);
					}

					&.disabled {
						color: #aaa;
					}
				}
			}

			.form-group {
				padding-top: 20rpx;

				.form-item {
					display: flex;
					line-height: 50rpx;

					.text {
						display: inline-block;
						width: 200rpx;
						line-height: 50rpx;
					}

					.placeholder {
						line-height: 50rpx;
					}

					.input {
						flex: 1;
						height: 50rpx;
						line-height: 50rpx;
					}
				}
			}
		}
	}
}

// 优惠券弹窗
.coupon-popup-father /deep/ .uni-popup__wrapper {
	background:transparent !important;
	.uni-popup__wrapper-box{
		border-radius: 20rpx 20rpx 0 0 !important;
	}
}

.coupon-popup {
	border-radius: 25rpx 25rpx 0 0;
	height: 65vh;
	overflow: hidden;

	.popup-body{
		height: calc(100% - 272rpx);
		height: calc(100% - 272rpx - constant(safe-area-inset-bottom));
		height: calc(100% - 272rpx - env(safe-area-inset-bottom));
	}

	// 切换tab栏
	.uni-tab-item {
		display: inline-block;
		flex-wrap: nowrap;
		padding-left: 24rpx;
		padding-right: 24rpx;
	}

	.uni-tab-item-title {
		color: #555;
		font-size: 30rpx;
		display: block;
		height: 64rpx;
		line-height: 64rpx;
		// border-bottom: 2px solid #fff;
		padding: 0 10rpx;
		flex-wrap: nowrap;
		white-space: nowrap;
		.line {
			width: 36rpx;
			height: 6rpx;
			background: transparent;
			border-radius: 3rpx;
			margin: 0 auto;
		}
	}

	.uni-tab-item-title-active {
		display: block;
		height: 64rpx;
		// border-bottom: 2px solid #ffffff;
		padding: 0 10rpx;
		.line {
			background: #F2280C;
		}
	}

	.popup-body {
		background: #f5f5f5;
	}

	.coupon-item {
		position: relative;
		margin: 24rpx;
		font-size: 0;
		.coupon_ysy {
			width: 702rpx;
			height: 200rpx;
		}
		& > .iconfont {
			font-size: 40rpx;
			position: absolute;
			top: 50%;
			right: 20rpx;
			transform: translateY(-50%);
		}
		& > .iconyuan_checkbox {
			color: $ns-text-color-gray;
		}

		.circular {
			position: absolute;
			top: 50%;
			left: 0;
			transform: translate(-50%, -50%);
			background: #f5f5f5;
			width: 30rpx;
			height: 30rpx;
			border-radius: 50%;
			z-index: 5;
		}

		.coupon-info {
			height: 200rpx;
			display: flex;
			width: 702rpx;
			position: absolute;
			top: 0;
			.coupon-money {
				width: 218rpx;
				height: 200rpx;
				display: flex;
				flex-direction: column;
				justify-content: center;
				align-items: center;
				margin-right: 24rpx;

				text {
					font-size: 50rpx;
					line-height: 1;
				}
				.ns-font-size-sm-left {
					font-size: $ns-font-size-sm !important;
					margin-top: 14rpx;
					margin-left: 4rpx;
				}
				.ns-font-size-sm-right {
					font-size: $ns-font-size-sm !important;
					margin-top: 14rpx;
					margin-right: 4rpx;
				}
			}

			.info {
				flex: 1;
				max-width: calc(100% - 240rpx);
				.coupon-name {
					margin-top: 18rpx;
					margin-bottom: 50rpx;
					font-weight: bold;
					color: #333333;
				}
				.coupon-time {
					color: #999999;
					font-size: 22rpx;
				}
				view {
					white-space: nowrap;
					overflow: hidden;
					text-overflow: ellipsis;
				}
			}
		}
	}
}

.promotion-popup {
	height: 65vh;

	.order-cell {
		margin: 20rpx 30rpx;
		display: flex;
		flex-direction: column;
		align-items: flex-start;
		.tit {
			line-height: 60rpx;
		}
		.box {
			padding: 0;
		}
	}
}

.delivery-popup {
	height: 80vh;

	.delivery-cell {
		margin: 0 30rpx;
		padding: 30rpx 0;
		border-top: 1px solid #f5f5f5;

		&:first-of-type {
			border-top: none;
		}

		.tit {
			font-size: 28rpx;
			font-weight: 600;
		}

		.option-grpup {
			.option-item {
				display: inline-block;
				line-height: 1;
				font-size: 28rpx;
				padding: 16rpx 40rpx;
				background: #eee;
				border: 1px solid #eee;
				border-radius: 32rpx;
				margin: 0 20rpx 20rpx 0;

				&:nth-of-type(1),
				&:nth-of-type(2),
				&:nth-of-type(3) {
					margin-bottom: 0;
				}

				&.active {
					// background: opacify($base-color-rgba, 0.01);
				}

				&.disabled {
					color: #aaa;
				}
			}
		}
	}

	.delivery-cont {
		height: calc(100% - 180rpx);
		overflow-y: scroll;

		.pickup-point {
			padding: 20rpx 0;
			border-top: 1px solid #f5f5f5;

			.name {
				display: flex;

				.icon {
					flex: 1;
					text-align: right;

					.iconfont {
						line-height: 1;
					}
				}
			}

			&:first-of-type {
				padding-top: 0;
				border-top: none;
			}

			.info {
				line-height: 1.2;

				.ns-text-color-gray {
					&:last-of-type {
						margin-left: 10rpx;
					}
				}
			}
		}
	}
}

.pay-password {
	width: 80vw;
	background: #fff;
	box-sizing: border-box;
	border-radius: 10rpx;
	overflow: hidden;
	padding: 30rpx 40rpx;
	transform: translateY(-200rpx);
	.popup-title{
		display: flex;
		align-items: center;
		.title {
			font-size: 28rpx;
			text-align: center;
			width: calc(100% - 40rpx);
			text-align: center;
		}
	}
	.error-tips{
		text-align: center;
		width: 100%;
	}
	.money-box{
		margin-top: 50rpx;
		.total-fee{
			text-align: center;
			font-size: 48rpx;
			font-weight: bold;
			color: #333333;
		}
		.balance{
			font-size: 24rpx;
			color: #999999;
			text-align: center;
		}
	}
	

	.tips {
		font-size: 24rpx;
		color: #999;
		text-align: center;
	}

	.btn {
		width: 60%;
		margin: 0 auto;
		margin-top: 30rpx;
		height: 70rpx;
		line-height: 70rpx;
		border-radius: 70rpx;
		color: #fff;
		text-align: center;
		border: 1px solid #ffffff;

		&.white {
			margin-top: 20rpx;
			background-color: #fff !important;
		}
	}

	.password-wrap {
		padding-top: 20rpx;
		width: 90%;
		margin: 0 auto;

		.forget-password {
			margin-top: 20rpx;
			display: inline-block;
		}
	}
}
.text-color {
	color: #F1270C;
	.money {
		font-weight: bold;
		font-size: 36rpx !important;
	}
}
.nav{
  width: 100%;
  overflow: hidden;
  position: fixed;
  top: 0;
  left: 0;
  z-index: 10;
  background-color: #ffffff;
}
.nav-title{
  width: 100%;
  height: 88rpx;
  line-height: 88rpx;
  text-align: center;
  position: absolute;
  bottom: 0;
  left: 0;
  z-index: 10;
}
.nav .back{
  width: 42rpx;
  height: 70rpx;
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  left: 0rpx;
  padding:0 24rpx;
}
.wrapper {
	padding-top: 24rpx;
}

.coupon-instructions-close {
	display: flex;
	justify-content: flex-end;
	align-items: center;
}
.coupon-instructions-btn {
	margin-right: 20rpx;
	color: #999999;
	font-size: 24rpx;
}
.coupon-close {
	color: #A0A1A7;
}
.coupon-default {
	display: flex;
	align-items: center;
	justify-content: center;
	height: 100%;
	view {
		color: #999999;
	}
}

.payment-methods{
	.item{
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding: 25rpx 0;
		&.disable{
			.title{
				text{
					color: #CCCCCC;
				}
			}
		}
		.icon{
			width: 48rpx;
			height: 48rpx;
		}
		.title{
			flex: 1;
			font-size: 26rpx;
			margin: 0 10rpx;
			.desc{
				color: #F2270C;
				margin-left: 10rpx;
			}
		}
		.checkbox{
			width: 32rpx;
			height: 32rpx;
		}
	}
}
