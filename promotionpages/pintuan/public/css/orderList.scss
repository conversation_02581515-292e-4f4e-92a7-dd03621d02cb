.order-container {
	width: 100vw;
	height: 100vh;
}

.order-nav {
	width: 100vw;
	height: 70rpx;
	flex-direction: row;
	white-space: nowrap;
	background: #fff;
	padding: 15rpx 0;
	position: fixed;
	left: 0;
	z-index: 998;

	.uni-tab-item {
		display: inline-block;
		flex-wrap: nowrap;
		padding: 0 24rpx;
	}

	.uni-tab-item-title {
		color: #555;
		font-size: 30rpx;
		display: block;
		height: 64rpx;
		line-height: 64rpx;
		// border-bottom: 2px solid #fff;
		padding: 0 10rpx;
		flex-wrap: nowrap;
		white-space: nowrap;
		.line {
			width: 36rpx;
			height: 6rpx;
			background: transparent;
			border-radius: 3rpx;
			margin: 0 auto;
		}
	}

	.uni-tab-item-title-active {
		display: block;
		height: 64rpx;
		// border-bottom: 2px solid #ffffff;
		padding: 0 10rpx;
		.line {
			background: #F2280C;
		}
	}

	::-webkit-scrollbar {
		width: 0;
		height: 0;
		color: transparent;
	}
}

.order-item {
	margin: 20rpx 24rpx;
	padding: 28rpx 24rpx;
	border-radius: 20rpx;
	background: #fff;
	position: relative;

	.order-header {
		display: flex;
		align-items: center;
		position: relative;
		justify-content: space-between;
		&.waitpay {
			.iconyuan_checked,
			.iconyuan_checkbox {
				font-size: 36rpx;
				position: absolute;
				top: 50%;
				left: 0;
				transform: translateY(-50%);
			}
			.iconyuan_checkbox {
				color: $ns-text-color-gray;
			}
		}

		.icondianpu {
			display: inline-block;
			line-height: 1;
			margin-right: 12rpx;
			font-size: 30rpx;
			color: #333333;
		}

		.status-name {
			text-align: right;
		}
	}

	.order-body {
		.goods-wrap {
			margin-bottom: 20rpx;
			display: flex;
			position: relative;

			&:last-of-type {
				margin-bottom: 0;
			}

			.goods-img {
				width: 180rpx;
				height: 180rpx;
				padding: 28rpx 0 0 0;
				margin-right: 20rpx;

				image {
					width: 100%;
					height: 100%;
					border-radius: 20rpx;
				}
			}

			.goods-info {
				flex: 1;
				position: relative;
				padding: 28rpx 0 0 0;
				max-width: calc(100% - 200rpx);

				.goods-name {
					display: -webkit-box;
					-webkit-box-orient: vertical;
					-webkit-line-clamp: 2;
					overflow: hidden;
					line-height: 1.5;
					font-size: 28rpx;
				}

				.goods-sub-section {
					width: 100%;
					line-height: 1.3;
					display: flex;
					justify-content: space-between;
					view {
						color: #9A9A9A;
						font-size: 24rpx;
					}
				}
				.goods-price {
					color: #343434;
					font-size: 28rpx;
					text-align: right;
				}
				.unit {
					font-weight: normal;
					font-size: 24rpx;
					margin-right: 2rpx;
				}
			}
		}
	}

	.order-footer {
		.order-base-info {
			display: flex;

			.total {
				text-align: right;
				padding-top: 20rpx;
				flex: 1;

				& > text {
					line-height: 1;
					margin-left: 10rpx;
				}
				text {
					font-size: 26rpx;
					color: #343434;
					text:last-of-type {
						margin-left: 0;
					}
					&.strong {
						font-weight: bold;
						font-size: 32rpx;
					}
				}
				
			}
		}

		.order-operation {
			display: flex;
			justify-content: flex-end;
			text-align: right;
			padding-top: 20rpx;

			.operation-btn {
				line-height: 1;
				padding: 20rpx 26rpx;
				color: #333;
				display: inline-block;
				border-radius: 32rpx;
				background: #fff;
				border: 0.5px solid #999;
				font-size: 24rpx;
				margin-left: 10rpx;
			}
		}
	}
}

.empty {
	padding-top: 200rpx;
	text-align: center;

	.empty-image {
		width: 180rpx;
		height: 180rpx;
	}
}

.order-batch-operation {
	position: fixed;
	z-index: 5;
	left: 0;
	bottom: 0;
	width: 100vw;
	height: 100rpx;
	background: #fff;
	box-shadow: 0 0px 10px rgba(0, 0, 0, 0.1);
	text-align: right;

	&.bottom-safe-area {
		padding-bottom: 0;
		padding-bottom: constant(safe-area-inset-bottom);
		padding-bottom: env(safe-area-inset-bottom);
	}

	.operation-btn {
		height: 68rpx;
		line-height: 68rpx;
		background: #fff;
		padding: 0 40rpx;
		display: inline-block;
		text-align: center;
		margin: 16rpx 20rpx 16rpx 0;
		border-radius: 40rpx;
		border: 0.5px solid #ffffff;

		&.white {
			height: 68rpx;
			line-height: 68rpx;
			color: #333;
			border: 0.5px solid #999;
			background: #fff;
		}
	}
}
.high-text-color {
	color: #F2280C!important;
}
.order-box-btn {
	min-width: 160rpx;
	padding: 0 16rpx;
	box-sizing: border-box;
	display: flex;
	height: 64rpx;
	line-height: 64rpx;
	align-items: center;
	justify-content: center;
	border-color: #CCCCCC;
	color: #666666;
	font-size: 26rpx;
	&.order-pay {
			background: #F2280C!important;
			color: #fff;
	}
	.share-btn {
		background-color: transparent;
		color: #ffffff;
		font-size: 26rpx;
		margin: 0;
		padding: 0;
		line-height: normal;
	}
}
.site-name-box {
	display: flex;
	align-items: center;
	.site-name {
		width: 530rpx;
		display: -webkit-box;
		-webkit-box-orient: vertical;
		-webkit-line-clamp: 1;
		overflow: hidden;
		font-weight: bold;
	}
}