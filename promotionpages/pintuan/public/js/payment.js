import AdaPay from 'common/js/adaPay.js'
import store from '../../../../store';
import system from "@/common/js/system";
import apiUrl from "../../../../common/js/apiurls";
export default {
	data() {
		return {
			orderCreateData: {
				is_balance: 0,
				pay_password: '',
				platform_coupon_id:0,
				buyer_message:{},
			},
			errMsg:'',
			paymentMethod:'WECHAT',
			orderPaymentData: {
				member_account: {
					balance: 0,
					is_pay_password: 0
				},
				platform_coupon_list:[]
			},
			isSub: false,
			sitePromotion: [],
			siteDelivery: {
				site_id: 0,
				data: []
			},
			siteCoupon: {
				site_id: 0,
				data: []
			},
			shopCoupon: {},
			myCoupon: [],
			isFocus:false,
			tempData: null,


			selectCouponId:"",
			selectCouponMoney:'0.00',
			selectCouponHaveChoose: false,

			selectPlatCouponId:0,
			selectPlatCouponMoney:'0.00',
			limit_pay_tips:'',

			push_data:{},
			isSubscribed:false,

		};
	},
	methods: {
		/**
		 * 显示弹出层
		 * @param {Object} ref
		 */
		openPopup(ref) {
			if(ref=='PlatcouponPopup'){
				this.selectPlatCouponId=this.orderPaymentData.platform_coupon_id;
				this.selectPlatCouponMoney=this.orderPaymentData.platform_coupon_money;
			}
			this.$refs[ref].open();
		},
		/**
		 * 关闭弹出层
		 * @param {Object} ref
		 */
		closePopup(ref) {
			if (this.tempData) {
				Object.assign(this.orderCreateData, this.tempData);
				Object.assign(this.orderPaymentData, this.tempData);
				this.tempData = null;
				this.$forceUpdate();
			}
			this.$refs[ref].close();
		},
		/**
		 * 选择收货地址
		 */
		selectAddress() {
			this.$util.redirectTo('/otherpages/member/address/address', {
				'back': '/promotionpages/pintuan/payment/payment'
			});
		},
		/**
		 * 获取订单初始化数据
		 */
		getOrderPaymentData() {
			this.orderCreateData = uni.getStorageSync('pintuanOrderCreateData');
			if (!this.orderCreateData) {
				// this.$util.showToast({
				// 	title: '未获取到创建订单所需数据!！',
				// 	success: () => {
				// 		setTimeout(() => {
				// 			console.log('!this.orderCreateData')
				// 			console.log(this.orderCreateData)
				// 			this.$util.redirectTo('/pages/index/index/index', {}, 'reLaunch');
				// 		}, 1500)
				// 	}
				// });
				uni.showModal({
					title: '提示',
					content: '订单信息已过期，请到订单列表查看最新状态',
					confirmText: '查看订单',
					showCancel: false,
					success: (res) => {
						this.$util.redirectTo('/promotionpages/pintuan/order/list/list?status=all', {}, 'redirectTo');
					},
					fail: (err) => {
					}
				});
				return;
			}
			if(this.selectCouponId) this.orderCreateData.coupon_id = this.selectCouponId;

			this.$api.sendRequest({
				url: '/api/PintuanOrder/payment',
				data: this.orderCreateData,
				success: res => {
					if (res.code >= 0) {
						this.orderPaymentData = res.data;
						this.limit_pay_tips = res.data.limit_pay_tips
						uni.setStorageSync('limit_pay_tips',res.data.limit_pay_tips)
						var member_address = uni.getStorageSync('member_address');
						if(member_address) {
							//本地存储的地址id和服务器地址id一直时，使用服务器的,否则使用本地
							if(member_address.id==this.orderPaymentData.member_address.id){
								uni.setStorageSync('member_address',this.orderPaymentData.member_address)
							}else{
								this.orderPaymentData.member_address = member_address
							}
						} else {
							uni.setStorageSync('member_address',this.orderPaymentData.member_address)
						}
						if(res.data.coupon_list&&res.data.coupon_list.coupon_id&&!this.selectCouponHaveChoose) {
							this.orderCreateData.coupon_id = res.data.coupon_list.coupon_id;
							this.selectCouponId = res.data.coupon_list.coupon_id;
						} else {
							this.orderCreateData.coupon_id = this.selectCouponId
							this.selectCouponHaveChoose = true
						}
						this.handlePaymentData();
						this.getOrderCouponList();
						let list = Object.values(res.data.shop_goods_list)
						list.length > 0 && list.forEach(it => {
							let item = it.goods_list_str
							this.splitfn(item,item.indexOf(';') !== -1?';':':')
						})
						if (this.$refs.loadingCover) this.$refs.loadingCover.hide();
					} else {
						this.$util.showToast({
							title: '未获取到创建订单所需数据!！',
							success: () => {
								setTimeout(() => {
									console.log('if (res.code !>= 0)')
									console.log(res)
									this.$util.redirectTo('/pages/index/index/index', {}, 'reLaunch');
								}, 1500)
							}
						});
					}
				},
				fail: res => {
					if (this.$refs.loadingCover) this.$refs.loadingCover.hide();
				}
			})
		},
		splitfn(value,str){
			let	sku_ids = [],
				num = [];

			if(str == ';'){
				value.split(str).forEach(item =>{
					item.split(':').forEach((it,index) => {
						if((index)%2){
							num.push(it)
						}else {
							sku_ids.push(it)
						}

					})
				})
			}else {
				sku_ids = [value.split(':')[0]]
				num = [value.split(':')[1]]
			}
			if(sku_ids.length > 0 && num.length > 0){
				this.$buriedPoint.submitOrderContent({ sku_ids, num, pages:1 })
			}
		},
		/**
		 * 获取可用、不可用优惠券列表
		 */
		getOrderCouponList() {
			var sku_idsArr = [];
			var sku_numsArr = [];
			var shop_goods_listArr = []
			var data = JSON.parse(JSON.stringify(this.orderPaymentData));
			for (let i in data.shop_goods_list) {
			    shop_goods_listArr.push(data.shop_goods_list[i]); //属性
			}
			for (var i = 0; i < shop_goods_listArr.length; i++) {
				shop_goods_listArr[i].goods_list.map(item=>{
					sku_idsArr.push(item.sku_id)
					sku_numsArr.push(item.num)
				})
			}
			this.$api.sendRequest({
				url:this.$apiUrl.orderCoupon,
				data: {
					sku_ids: sku_idsArr.toString(),
					sku_nums: sku_numsArr.toString()
				},
				success: res=>{
					if(res.code == 0){
						console.log(res.data)
						this.shopCoupon = res.data;
						if(this.currentTab==1) {
							this.myCoupon = this.shopCoupon.enable_list
						} else if(this.currentTab==0) {
							this.myCoupon = this.shopCoupon.disable_list
						}
					}
				},
				fali: res=>{

				}
			})

		},
		/**
		 * 切换可用不可用优惠券
		 */
		ontabtapCoupon(e) {
			this.currentTab = e.status;
			if(this.currentTab==1) {
				this.myCoupon = this.shopCoupon.enable_list
			} else if(this.currentTab==0) {
				this.myCoupon = this.shopCoupon.disable_list
			}
		},

		/**
		 * 处理结算订单数据
		 */
		handlePaymentData() {
			this.orderCreateData.delivery = {};
			this.orderCreateData.coupon = {};
			this.orderCreateData.buyer_message = {};

			this.orderCreateData.is_balance = 0;
			this.orderCreateData.pay_password = '';

			console.log('orderPaymentData',this.orderPaymentData)
			var data = this.orderPaymentData;

			let h = new Date().getHours().toString();
			let m = new Date().getMinutes().toString();
			if (h.length == 1) {
				h = '0' + h;
			}
			if (m.length == 1) {
				m = '0' + m;
			}
			let nowTime = h + ':' + m;

			let val = JSON.parse(JSON.stringify(data.shop_goods_list))
			data.shop_goods_list = {}
			data.shop_goods_list.val = val

			Object.keys(data.shop_goods_list).forEach((key, index) => {
				console.log(key,data.shop_goods_list[key])
				let siteItem = data.shop_goods_list[key];
				// 店铺配送方式
				this.orderCreateData.delivery[key] = {};
				if(siteItem.local_config){
					if (siteItem.local_config.info && siteItem.local_config.info.time_is_open == 1) {
						this.orderCreateData.delivery[key].showTimeBar=true;
						this.orderCreateData.delivery[key].buyer_ask_delivery_time=nowTime;
					}else{
						this.orderCreateData.delivery[key].showTimeBar=false;
					}
				}

					console.log('data',siteItem.express_type)
				if (siteItem.express_type && siteItem.express_type[0] != undefined) {
					this.orderCreateData.delivery[key].delivery_type = siteItem.express_type[0].name;
					this.orderCreateData.delivery[key].delivery_type_name = siteItem.express_type[0].title;
					this.orderCreateData.delivery[key].store_id = 0;
					this.orderCreateData.delivery[key].store_id = 0;

					// 如果是门店配送
					if (siteItem.express_type[0].name == 'store') {
						if (siteItem.express_type[0].store_list[0] != undefined) {
							this.orderCreateData.delivery[key].store_id = siteItem.express_type[0].store_list[0].store_id;
						}
					}
				}

				// 店铺优惠券
				this.orderCreateData.coupon[key] = {};
				if (siteItem.coupon_list&&siteItem.coupon_list[0] != undefined) {
					this.orderCreateData.coupon[key].coupon_id = siteItem.coupon_list[0].coupon_id;
					this.selectCouponId=siteItem.coupon_list[0].coupon_id;
					this.orderCreateData.coupon[key].coupon_money = siteItem.coupon_list[0].money;
					this.selectCouponMoney=siteItem.coupon_list[0].coupon_id;
				}

				this.orderCreateData.buyer_message[key] = '';
			})

			if (this.orderPaymentData.is_virtual) this.orderCreateData.member_address = {
				mobile: ''
			};


			if(this.orderPaymentData.platform_coupon_list&&this.orderPaymentData.platform_coupon_list.length>0){
				this.orderPaymentData.platform_coupon_id=this.orderPaymentData.platform_coupon_list[0].platformcoupon_id;
				this.orderCreateData.platform_coupon_id=this.orderPaymentData.platform_coupon_list[0].platformcoupon_id;
				this.orderPaymentData.platform_coupon_money=this.orderPaymentData.platform_coupon_list[0].money;
				this.orderCreateData.platform_coupon_money=this.orderPaymentData.platform_coupon_list[0].money;

				this.selectPlatCouponId=this.orderPaymentData.platform_coupon_list[0].platformcoupon_id;
				this.selectPlatCouponMoney=this.orderPaymentData.platform_coupon_list[0].money;
			}


			Object.assign(this.orderPaymentData, this.orderCreateData);
			this.orderCalculate();
		},
		/**
		 * 订单计算
		 */
		orderCalculate() {
			var data = this.$util.deepClone(this.orderCreateData);
			data.delivery = JSON.stringify(data.delivery);
			data.coupon = JSON.stringify(data.coupon);
			data.member_address = data.member_address ? JSON.stringify(data.member_address) : JSON.stringify([]);
			data.buyer_message=JSON.stringify(data.buyer_message);
			this.$api.sendRequest({
				url: '/api/PintuanOrder/calculate',
				data,
				success: res => {
					if (res.code >= 0) {
						this.orderPaymentData.delivery_money = res.data.delivery_money;
						this.orderPaymentData.coupon_money = res.data.coupon_money;
						this.orderPaymentData.invoice_money = res.data.invoice_money;
						this.orderPaymentData.promotion_money = res.data.promotion_money;
						this.orderPaymentData.order_money = res.data.order_money;
						this.orderPaymentData.balance_money = res.data.balance_money;
						this.orderPaymentData.pay_money = res.data.pay_money;
						this.orderPaymentData.goods_money = res.data.goods_money;
						this.member_account = res.data.member_account;
						this.maidou = {
							maidou_tag: res.data.maidou_tag,
							maidou_pay_num: res.data.maidou_pay_num,
							maidou_pay: res.data.maidou_pay,
							canuse_maidou: res.data.member_account.canuse_maidou
						}
						var val = this.$util.deepClone(res.data.shop_goods_list);
						res.data.shop_goods_list = {}
						res.data.shop_goods_list.val = val
						Object.keys(res.data.shop_goods_list).forEach((key, index) => {
							let siteItem = res.data.shop_goods_list[key];
							this.orderPaymentData.shop_goods_list[key].pay_money = siteItem.pay_money;
							this.orderPaymentData.shop_goods_list[key].coupon_money = siteItem.coupon_money;
						})
					} else {
						this.$util.showToast({
							title: res.message
						});
					}
				},
			})
		},
		/**
		 * 订单创建
		 */
		async orderCreate() {
			if (this.verify()) {
				if (this.isSub) return;
				this.isSub = true;
				if(!this.isSubscribed){
					let scene_type='';
					//参团
					if(this.orderCreateData.group_id){
						if(this.paymentMethod=='WECHAT'){
							scene_type='join_pintuan_before';
						}else{
							scene_type='other_join_pintuan_before';
						}
					}else{
						//开团
						if(this.paymentMethod=='WECHAT'){
							scene_type='open_pintuan_before';
						}else{
							scene_type='other_pintuan_before';
						}

					}
					try{
						this.push_data=await this.$util.subscribeMessage({
							source:'out_trade_no',
							source_id:'',
							scene_type:scene_type
						},true);
						this.isSubscribed=true;
					}catch (e) {

					}
				}
				uni.showLoading({
					mask: true,
					title: '加载中'
				});
				// 获取收货地址选择后存储的地址
				var member_address = uni.getStorageSync('member_address')
				var data = this.$util.deepClone(this.orderCreateData);


				data.delivery = JSON.stringify(data.delivery);
				data.coupon = JSON.stringify(data.coupon);
				data.member_address = JSON.stringify(member_address.id);
				data.buyer_message = JSON.stringify(data.buyer_message);
				data.pay_type = this.paymentMethod;
				// 分享赚
				if(this.activity_id) {
					data.activity_id = this.activity_id;
					data.activity_type = this.activity_type;
				}
				this.$api.sendRequest({
					url: '/api/PintuanOrder/create',
					data,
					success: async res => {
						if (res.code == -10100) {
							uni.hideLoading();
							this.$refs.payPassword.close();
							uni.showModal({
								content: "开团名额已满，请参团",
								showCancel: false,
								confirmText: "去参团",
								success: () => {
									this.$util.redirectTo(
										`/promotionpages/pintuan/detail/detail?id=${this.orderCreateData.pintuan_goods_id}`, {}, 'redirectTo');
								},
								fail: () => {

								}
							})
						} else if (res.code >= 0) {
							uni.removeStorage({
								key: 'pintuanOrderCreateData',
								success: () => {}
							});
							this.createBuriedPoint(res.data.out_trade_no,3)

							this.push_data.source_id=res.data.out_trade_no;
							await this.$util.subscribeMessageMethod(this.push_data);
							// 当支付金额为0元时，调用微信支付接口，直接支付成功跳转到支付成功页面
							if(res.data.is_free == 1){

								this.createBuriedPoint(res.data.out_trade_no,11)

								// 跳转支付成功页面
								this.$util.redirectTo('/promotionpages/pintuan/order/list/list?status=all', {}, 'redirectTo');
								// this.$util.redirectTo('/pages/pay/result/result?order_ids='+res.data.order_ids, {
								// 	code: res.data.out_trade_no
								// }, 'redirectTo');
								// uni.removeStorage({
								// 	key: 'pintuanOrderCreateData',
								// 	success: () => {}
								// });
							}else{
								this.orderPayPay(res.data.out_trade_no,res.data.order_id)
							}
							uni.removeStorageSync('member_address')
							uni.removeStorageSync('recommend_member_id')
						} else {
							this.isSub = false;
							uni.hideLoading();
							if(this.$refs.payPassword){
								this.isFocus=false;
								this.$refs.payPassword.close();
							}
							if (res.data.error_code == 10 || res.data.error_code == 12) {
								uni.showModal({
									title: '订单未创建',
									content: res.message,
									confirmText: '去设置',
									success: res => {
										if (res.confirm) {
											this.selectAddress();
										}
									}
								})
							} else {
								this.$util.showToast({
									title: res.message
								});
							}
						}
						this.getCartNumber();
					},
					fail: res => {
						uni.hideLoading();
						this.isSub = false;
					}
				})
			}
		},
		/**
		 * 订单支付
		 */
		orderPayPay(out_trade_no,order_ids) {
			uni.showLoading({
				mask: true,
				title: '加载中'
			});
			var that = this;
				this.$api.sendRequest({
					url: '/api/pay/pay',
					data: {
						out_trade_no: out_trade_no,
						pay_type: 'adapay'
					},
					success: res => {
						uni.hideLoading();
						if (res.code >= 0) {

							this.$util.wechatPay(res.data.pay_type,res.data.pay_type=='adapay'? res.data.payment : res.data.pay_info, (res)=>{

								this.createBuriedPoint(out_trade_no,11)

								uni.hideLoading();
								// 跳转支付成功页面
								this.$util.redirectTo('/promotionpages/pintuan/order/list/list?status=all', {}, 'redirectTo');
							},(err)=>{

								this.createBuriedPoint(out_trade_no,9001)

								uni.hideLoading();
								this.$refs.popupToList.open()
							},(err)=>{
								setTimeout(() => {
									this.$util.redirectTo("/promotionpages/pintuan/order/list/list?status=0", {}, "redirectTo")
								}, 2000)
							})
						} else {
							console.log(res)
							// this.isSub = false;
							if(res.message) {
								this.$util.showToast({
									title: res.message
								});
							} else {
								uni.hideLoading();
							}
							if(this.$refs.popupToList) this.$refs.popupToList.open();
						}
					},
					fail: res => {
						this.$util.showToast({
							title: 'request:fail'
						});
					}
				})
		},
		// 订单埋点
		createBuriedPoint(out_trade_no,status){
			this.$buriedPoint.orderStatus({out_trade_no,status,orderType:true})
		},
		/**
		 * 订单验证
		 */
		verify() {
			if (this.orderPaymentData.is_virtual == 1) {
				if (!this.orderCreateData.member_address.mobile.length) {
					this.$util.showToast({
						title: '请输入您的手机号码'
					});
					return false;
				}
				var reg = /^[1](([3][0-9])|([4][5-9])|([5][0-3,5-9])|([6][5,6])|([7][0-8])|([8][0-9])|([9][1,8,9]))[0-9]{8}$/;
				if (!reg.test(this.orderCreateData.member_address.mobile)) {
					this.$util.showToast({
						title: '请输入正确的手机号码'
					});
					return false;
				}
			}

			if (this.orderPaymentData.is_virtual == 0) {
				if (!this.orderPaymentData.member_address) {
					this.$util.showToast({
						title: '请先选择您的收货地址'
					});
					return false;
				}

				let deliveryVerify = true;

				for (let key in this.orderCreateData.delivery) {
					if (JSON.stringify(this.orderCreateData.delivery[key]) == "{}") {
						deliveryVerify = false;
						this.$util.showToast({
							title: '店铺"' + this.orderPaymentData.shop_goods_list[key].site_name + '"未设置配送方式'
						});
						break;
					}
					if (this.orderCreateData.delivery[key].delivery_type == 'store' && this.orderCreateData.delivery[key].store_id ==
						0) {
						deliveryVerify = false;
						this.$util.showToast({
							title: '店铺"' + this.orderPaymentData.shop_goods_list[key].site_name + '"没有可提货的门店,请选择其他配送方式'
						});
						break;
					}
				}
				if (!deliveryVerify) return false;
			}
			if(this.paymentMethod == "BALANCE"){
				this.orderCreateData.is_balance = 1
			}

			if (this.orderCreateData.is_balance == 1 && this.orderCreateData.pay_password == '') {
				setTimeout(() => {
					this.$refs.input.clear();
				}, 0)
				// this.$refs.payPassword.open();
				this.openPasswordPopup();
				return false;
			}
			return true;
		},
		/**
		 * 显示店铺优惠信息
		 * @param {Object} data
		 */
		openSitePromotion(data) {
			this.sitePromotion = data;
			this.$refs.sitePromotionPopup.open();
		},
		/**
		 * 显示店铺配送信息
		 * @param {Object} index
		 */
		openSiteDelivery(siteId, deliveryData) {
			this.tempData = {
				delivery: this.$util.deepClone(this.orderPaymentData.delivery)
			};

			this.siteDelivery.site_id = siteId;
			this.siteDelivery.data = deliveryData;
			this.$refs.deliveryPopup.open();
		},
		/**
		 * 选择配送方式
		 */
		selectDeliveryType(data) {
			this.orderCreateData.delivery[this.siteDelivery.site_id].delivery_type = data.name;
			this.orderCreateData.delivery[this.siteDelivery.site_id].delivery_type_name = data.title;
			// 如果是门店配送
			if (data.name == 'store') {
				if (data.store_list[0] != undefined) {
					this.orderCreateData.delivery[this.siteDelivery.site_id].store_id = data.store_list[0].store_id;
				}
			}
			Object.assign(this.orderPaymentData, this.orderCreateData);
			this.$forceUpdate();
		},
		/**
		 * 选择自提点
		 */
		selectPickupPoint(store_id) {
			this.orderCreateData.delivery[this.siteDelivery.site_id].store_id = store_id;
			Object.assign(this.orderPaymentData, this.orderCreateData);
			this.$forceUpdate();
		},
		/**
		 * 显示店铺优惠券信息
		 * @param {Object} siteId
		 * @param {Object} couponData
		 */
		openSiteCoupon() {
			// this.tempData = {
			// 	coupon: this.$util.deepClone(this.orderPaymentData.coupon)
			// };
			// this.selectCouponId=this.orderCreateData.coupon[siteId].coupon_id;
			// this.selectCouponMoney=this.orderCreateData.coupon[siteId].coupon_money;
			// this.siteCoupon.data = couponData;
			this.$refs.couponPopup.open();
		},
		/**
		 * 选择优惠券
		 * @param {Object} item
		 */
		selectCoupon(item) {
			if (this.selectCouponId != item.coupon_id) {
				this.selectCouponId = item.coupon_id;
			} else {
				this.selectCouponId = "";
			}
			Object.assign(this.orderPaymentData, this.orderCreateData);
			this.$forceUpdate();
		},
		popupConfirm(ref,item) {
			this.$refs[ref].close();
			this.selectCouponHaveChoose = true
			this.getOrderPaymentData();
			// this.orderCalculate();
		},
		imageError(siteIndex, goodsIndex) {
			this.orderPaymentData.shop_goods_list[siteIndex].goods_list[goodsIndex].sku_image = this.$util.getDefaultImage().default_goods_img;
			this.$forceUpdate();
		},
		// 购物车数量
		getCartNumber() {
			if (uni.getStorageSync("token")) {
				this.$store.dispatch('getCartNumber')
			}
		},
		openPasswordPopup(){
			this.$refs.payPassword.open();
			setTimeout(()=>{
				this.isFocus = true;
			},500)
		},
		/**
		 * 设置支付密码
		 */
		setPayPassword() {
			this.$refs.payPassword.close();
			this.$util.redirectTo('/otherpages/member/setting/setting_password', {
				back: '/promotionpages/pintuan/payment/payment',
				phonenum: this.orderPaymentData.member_account.mobile
			});
		},
		/**
		 * 暂不设置支付密码
		 */
		noSet() {
			this.orderCreateData.is_balance = 0;
			this.$refs.payPassword.close();
			this.orderCalculate();
			this.$forceUpdate();
		},
		/**
		 * 支付密码输入
		 */
		input(pay_password) {
			this.errMsg = ''
			if (pay_password.length == 6) {
				uni.showLoading({
					title: '加载中...',
					mask: true
				})

				this.$api.sendRequest({
					url: apiUrl.checkpaypasswordUrl,
					data: {
						pay_password
					},
					success: res => {
						if (res.code >= 0) {
							this.orderCreateData.pay_password = pay_password;
							this.orderCreate();
						} else {
							uni.hideLoading();
							this.errMsg = res.message
							// this.$util.showToast({
							// 	title: res.message
							// });
						}
					},
					fail: res => {
						uni.hideLoading();
					}
				})
			}
		},
		init(){
			// 刷新多语言
			this.$langConfig.refresh();
			var member_address = uni.getStorageSync('member_address')
			// 判断登录
			if (!uni.getStorageSync('token')) {

			} else {
				this.orderPaymentData= {
					member_account: {
						balance: 0,
						// is_pay_password: 0
					}
				}
				this.orderCreateData= {
					is_balance: 0,
					pay_password: '',
				}
				if(member_address) {
					this.orderCreateData.member_address=member_address.id
				}
				if (!this.isSub) {
					this.getOrderPaymentData();
				};
			}
		},
		changePayment(e){
			if(e.key == 'BALANCE'){
				this.orderCreateData.is_balance = !e.disable ? this.orderCreateData.is_balance == 1 ? 0 : 1 : 0;
			}else{
				this.orderCreateData.is_balance = 0
			}
			if(!e.disable){
				this.paymentMethod = e.key
			}
			this.$forceUpdate()
		}
	},
	onLoad(options) {
		uni.removeStorageSync('member_address')
	},
	async onShow() {
		await system.wait_staticLogin_success();
		if(this.ischoiceWechatAdder){
			let wechatAdderInterval=setInterval(()=>{
				if(this.postWechatAdder){
					console.log("this.choiceWechatAdderError",this.choiceWechatAdderError)
					if(this.choiceWechatAdderError){
						//微信收获地址提交到服务器报错时，需要跳转到地址管理页面
						if(this.choiceWechatAdderError){
							if (this.$refs.loadingCover) this.$refs.loadingCover.hide();
							this.$util.showToast({
								title: '获取微信地址失败，请手动添加地址',
								success: () => {
									setTimeout(() => {
										this.selectAddress()
									}, 1500)
								}
							});
						}
					}
					this.ischoiceWechatAdder=false;
					this.postWechatAdder=false;
					this.choiceWechatAdderError=false;
					clearInterval(wechatAdderInterval);
					this.init();
				}
			},100)
		}else{
			this.init()
		}
	},
	onHide() {
		if (this.$refs.loadingCover) this.$refs.loadingCover.show();
	},
	filters: {
		/**
		 * 金额格式化输出
		 * @param {Object} money
		 */
		moneyFormat(money) {
			return parseFloat(money).toFixed(2);
		},
		/**
		 * 店铺优惠摘取
		 */
		promotion(data) {
			let promotion = '';
			if (data) {
				Object.keys(data).forEach((key) => {
					promotion += data[key].content + '　';
				})
			}
			return promotion;
		}
	}
}
