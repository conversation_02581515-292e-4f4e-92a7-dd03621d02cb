import AdaPay from 'common/js/adaPay.js'
export default {
	methods: {
		/**
		 * 取消订单
		 * @param {Object} out_trade_no
		 */
		orderClose(orderData, callback) {
			let out_trade_no = orderData.out_trade_no
			uni.showModal({
				title: '提示',
				content: '确定要取消该订单吗？',
				cancelText:"我再想想",
				confirmText:"确定",
				success: res => {
					if (res.confirm) {
						this.$api.sendRequest({
							url: '/api/PintuanOrder/close',
							data: {
								id:orderData.id
							},
							success: res => {
								if(res.code>=0){
									this.createBuriedPoint('out_trade_no',out_trade_no,250)

									this.$util.showToast({
										'title':'取消订单成功！',
										success: () => {
											// setTimeout(() => {
												typeof callback == 'function' && callback();
											// }, 3000);
										}

									})
								}else{
									if(res.code == -11) {
										this.$util.showToast({
											'title':res.message
										})
										setTimeout(() => {
											typeof callback == 'function' && callback();
										}, 1500)
										return false
									}
									this.$util.showToast({
										'title':res.message
									})
								}
							}
						})
					}
				}
			})
		},

		/**
		 * 支付订单
		 * @param {Object} out_trade_no orderData
		 */
		async orderPay(orderData, callback) {
			let scene_type='';
			//参团
			if(orderData.group_id){
				scene_type='join_pintuan_before';
			}else{
				//开团
				scene_type='open_pintuan_before';
			}
			try{
				await this.$util.subscribeMessage({
					source:'out_trade_no',
					source_id:orderData.out_trade_no,
					scene_type:scene_type
				});
			}catch (e) {

			}
				this.$api.sendRequest({
					url: '/api/pay/pay',
					data: {
						out_trade_no: orderData.out_trade_no,
						pay_type: 'adapay'
					},
					success: res => {
						if (res.code >= 0) {
							uni.hideLoading();

							this.$util.wechatPay(res.data.pay_type,res.data.pay_type=='adapay'? res.data.payment : res.data.pay_info, (res)=>{
								this.createBuriedPoint('out_trade_no',orderData.out_trade_no,11)

								// 跳转支付成功页面
								typeof callback == 'function' && callback();
							},(err)=>{
								this.createBuriedPoint('out_trade_no',orderData.out_trade_no,9001)

								uni.hideLoading();
							},(err)=>{
								uni.hideLoading();
							},this)
						} else {
							if(res.code == -11) {
								this.$util.showToast({
									'title':res.message
								})
								setTimeout(() => {
									typeof callback == 'function' && callback();
								}, 1500)
								return false
							}
							if(res.message) {
								this.$util.showToast({
									title: res.message
								});
							} else {
								uni.hideLoading();
							}
						}
					},
					fail: res => {
						this.$util.showToast({
							title: 'request:fail'
						});
					}
				})
		},
		// 订单埋点
		createBuriedPoint(idName,out_trade_no,status){
			this.$buriedPoint.orderStatus({[idName]:out_trade_no,status,orderType:true})
		}
	}
}
