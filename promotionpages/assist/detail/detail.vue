<template>
<view class="assist">
	<view class="title">{{votesDetail.title}}</view>
	<view class="radio-list">
		<radio-group @change="changeRadio">
			<view class="item" v-for="(item,index) in votesDetail.item_info" :key="index">
				<view class="left">
					<radio :value="item.item_id" color="#F2270C" :checked="selectValue == item.item_id" :disabled="isSuccess && selectValue != item.item_id" /><text>{{item.name}}</text>
				</view>
				<view class="right">
					<text class="votesNum"> {{item.cnt}}票 </text>
					<text class="percent"> {{item.rate}} </text>
				</view>
			</view>
		</radio-group>
	</view>
	<view class="submit-btn" :class="selectValue && !isSuccess?'':'disabled'" @click="selectValue && !isSuccess?submit():''">{{selectValue && isSuccess?'已投票':'投票'}}</view>
	<block v-if="votesDetail.wechat_qrcode_img">
		<image class="image" :src="votesDetail.wechat_qrcode_img" show-menu-by-longpress mode="widthFix"></image>
		<view class="tips">长按识别店主二维码</view>
	</block>

	<!-- 助力成功弹窗 -->
	<uni-popup ref="popup" class="my-popup-dialog" :maskClick=false>
	    <view class="popup-dialog">
			<view class="popup-dialog-header">提示</view>
			<view class="popup-dialog-body">助力成功</view>
			<view class="popup-dialog-footer">
				<view class="button white" @click="$refs.popup.close()">关闭</view>
				<button class="button red" open-type="share">分享</button>
			</view>
		</view>
	</uni-popup>

	<!-- 授权登录弹窗 -->
	<yd-auth-popup ref="ydauth"></yd-auth-popup>
  <ns-login ref="login"></ns-login>
	<uni-coupon-pop ref="couponPop"></uni-coupon-pop>
</view>
</template>

<script>
import system from "@/common/js/system.js";
export default{
	name:'assist',
	data(){
		return{
			share_code:0,
			shopId:'',
			selectValue:'',
			votesDetail:null,
			isSuccess:false,
			member_id:0,
			token:'',
			isDisabled:false
		}
	},
	onLoad(options){
		this.share_code = options.share_code || 0
		this.share_shop_id = options.shop_id || ''
	},
	onShow(){
		this.$langConfig.refresh();
		this.token = uni.getStorageSync('token');
		this.shopId = uni.getStorageSync('shop_id');
		if(this.token != ''){
			this.$api.sendRequest({
				url: "/api/member/id",
				success: res => {
					if (res.code >= 0) {
						this.member_id = res.data;
					}
				}
			});
		}
		this.init()
		if (uni.getStorageSync('is_register')) {
			this.$util.toShowCouponPopup(this)
			uni.removeStorageSync('is_register');
		}
	},
	onShareAppMessage: function() {
    let path='/promotionpages/assist/detail/detail?share_code='+this.share_code+'&shop_id='+this.share_shop_id;
    let recommend_member_id=uni.getStorageSync('member_id');
    if(recommend_member_id){
      path+=`&recommend_member_id=${recommend_member_id}`;
    }
		let obj = {
			title: this.votesDetail.title,
			path: path,
			imageUrl:this.votesDetail.img_url
		}
		return obj
	},
	methods:{
		checkToken(){
			system.checkToken().then(res=>{
				this.shopId = uni.getStorageSync('shop_id')
			})
		},
		async init(){
			await this.checkToken()
			wx.showLoading()
			uni.login({
				provider: 'weixin',
				success:(result)=>{
					this.$api.sendRequest({
						url:'/api/Activity/wxRetailVoteDetail',
						data:{
							code:result.code,
							shop_id:this.shopId,
							assist_id:this.share_code,
							member_id:this.member_id
						},
						success:(res)=>{
							wx.hideLoading()
							if(res.code==0){
								this.votesDetail = res.data;
								if(res.data.vote_item_id!=0){
									this.isSuccess = true;
									this.selectValue = res.data.vote_item_id
								}
							}else{
								this.$util.showToast({
									title: res.message
								});
							}
						},
						fail:(err)=>{
							wx.hideLoading()
							this.$util.showToast({
								title: err.message
							});
						}
					})
				},
				fail: () => {
				  console.log("未授权");
				}
			})

		},
		changeRadio(e){
			this.selectValue = e.detail.value
		},
		submit(){
			if(!this.selectValue) return
			if(!this.token){
        let path = `/promotionpages/assist/detail/detail?shop_id=${this.share_shop_id}&share_code=${this.share_code}`
        this.$util.toShowLoginPopup(this,null,path);
				return
			}
			if(this.isDisabled) return
			uni.login({
				provider: 'weixin',
				success:(result)=>{
					this.isDisabled = true
					this.$api.sendRequest({
						url:'/api/Activity/retailVote',
						data:{
							code:result.code,
							shop_id:this.shopId,
							assist_id:this.share_code,
							item_id:this.selectValue,
							member_id:this.member_id
						},
						success:(res)=>{
							this.isDisabled = false
							if(res.code == 0){
								this.isSuccess = true
								this.$refs.popup.open()
							}else{
								this.$util.showToast({
									title:res.message
								})
							}
						},
						fail:(err)=>{
							this.isDisabled = false
							this.$util.showToast({
								title:err.message
							})
						}
					})
				},
				fail: () => {
				  console.log("未授权");
				}
			})


		}
	}
}
</script>

<style lang="scss" scoped>
.assist{
	height: 100vh;
	background: #fff;
	padding: 0 25rpx;
	.title{
		padding: 35rpx 0;
		font-size: $ns-font-size-lg - 2rpx;
		color: $ns-text-color-black;
	}
	.radio-list{
		.item{
			border:2rpx solid #EEEEEE;
			min-height: 80rpx;
			display: flex;
			align-items: center;
			justify-content: space-between;
			padding: 0 16rpx;
			box-sizing: border-box;
			margin-bottom: 20rpx;
			.left{
				width: 65%;
				flex-shrink: 0;
				display: flex;
				align-items: center;
			}
			.right{
				display: flex;
				justify-content: space-between;
				align-items: center;
				width: 35%;
			}
		}
	}
	.submit-btn{
		width: 650rpx;
		height: 80rpx;
		border-radius: 40rpx;
		background: $base-color;
		margin:0 auto;
		margin-top: 68rpx;
		text-align: center;
		line-height: 80rpx;
		color: #fff;
		&.disabled{
			background:#ccc !important
		}
	}
	.image{
		width: 360rpx;
		display: block;
		margin:0 auto;
		margin-top: 108rpx;
	}
	.tips{
		font-size: $ns-font-size-xm;
		color: #999;
		text-align: center;
		padding: 30rpx 0;
	}
}
.popup-dialog {
	overflow: hidden;
	background: #FFFFFF;
	box-sizing: border-box;
	.popup-dialog-header {
		height: 106rpx;
		line-height: 106rpx;
		text-align: center;
		font-size: 36rpx;
		color: #333333;
		font-weight: bold;
	}
	.popup-dialog-body {
		color: #656565;
		text-align: center;
		padding: 0 30rpx;
	}
	.popup-dialog-footer {
		margin: 0 32rpx;
		height: 140rpx;
		display: flex;
		align-items: center;
		justify-content: space-around;
		.button {
			width: 220rpx;
			height: 68rpx;
			line-height: 68rpx;
			text-align: center;
			border-radius: 34rpx;
			box-sizing: border-box;
			margin: 0;
			margin-left: 20rpx;
			&.white {
				color: #F2270C;
				background: #FFFFFF;
				border: 1rpx solid #F2270C;
			}
			&.red {
				color: #FFFFFF;
				background: #F2270C;

			}
		}
	}
}
</style>
