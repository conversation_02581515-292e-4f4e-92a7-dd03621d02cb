<template>
	<view :class="[themeStyle,FixedHeight]">
    <!-- #ifdef H5 -->
    <uni-nav-bar left-icon="back" :border="false" @clickLeft="appGoBack" v-if="isOnXianMaiApp">
      <template>
        <view class="page-title">{{comboDetailData.name}}</view>
      </template>
    </uni-nav-bar>
    <!-- #endif -->
    <view class="goods-detail" v-if="!isError">
      <view class="goods-container">
        <!-- 商品媒体信息 -->
        <view class="goods-media">
          <!-- 商品图片 -->
          <view class="goods-img" :class="{ show: switchMedia == 'img' }">
            <swiper class="swiper" @change="swiperChange" :interval="swiperInterval" :autoplay="swiperAutoplay">
              <swiper-item v-for="(item, index) in comboDetailData.images" :key="index">
                <view class="item" @click="previewMedia(index)">
                  <image :src="$util.img(item)" @error="swiperImageError(index)" mode="aspectFit" />
                </view>
              </swiper-item>
            </swiper>
            <view class="img-indicator-dots">
              <text>{{ swiperCurrent }}</text>
              <text v-if="comboDetailData.images">/{{ comboDetailData.images.length }}</text>
            </view>
          </view>
          <!-- 商品视频 -->
          <view class="goods-video" :class="{ show: switchMedia == 'video' }">
            <!-- #ifndef H5 -->
            <video :src="$util.img(comboDetailData.video_url)" :poster="$util.img(comboDetailData.image)" objectFit="cover"></video>
            <!-- #endif -->
            <!-- #ifdef H5 -->
            <view class="video-img">
              <image :src="$util.img(comboDetailData.image)" mode=""></image>
              <view class="video-open">
                <view class="iconfont iconarrow-" @click="openVideo"></view>
              </view>
            </view>
            <!-- #endif -->
          </view>

          <!-- 切换视频、图片 -->
          <view class="media-mode" v-if="comboDetailData.video_url">
            <text :class="{ 'ns-bg-color': switchMedia == 'video' }" @click="switchMedia = 'video'">{{ $lang('video') }}</text>
            <text :class="{ 'ns-bg-color': switchMedia == 'img' }" @click="switchMedia = 'img'">{{ $lang('image') }}</text>
          </view>
          <image v-if="comboDetailData.stock==0" :src="$util.img('public/static/youpin/product-sell-out.png')" class="over"></image>
        </view>
        <view @touchmove.prevent.stop class="videoPopup-box">
          <uni-popup ref="videoPopup" type="center">
            <view class="pop-video">
              <video :src="$util.img(comboDetailData.video_url)" :poster="$util.img(comboDetailData.image)" objectFit="cover"></video>
            </view>
          </uni-popup>
        </view>

        <view class="group-wrap">
          <view class="goods-module-wrap" :class="{ discount:  comboDetailData.promotion_type == 1 }">
            <view class="goods-module-wrap-box">
              <view>
                <template v-if="comboDetailData.promotion_type == 0">
                  <text class="price-symbol ns-text-color">{{ $lang('common.currencySymbol') }}</text>
                  <text class="price ns-text-color">{{ comboDetailData.retail_price }}</text>
                  <text class="market-price-symbol" v-if="comboDetailData.market_price > 0">{{ $lang('common.currencySymbol') }}</text>
                  <text class="market-price" v-if="comboDetailData.market_price > 0">{{ comboDetailData.market_price }}</text>
                </template>
              </view>
            </view>
          </view>

          <view class="goods-module-wrap" style="border-radius: 0 0 10px 10px;">
            <view>
              <view class="sku-name-box">
                <text class="sku-name" style="display: inline; font-size: 32rpx; line-height: 44rpx; position: relative" @click="longpress" @longpress="longpress" >{{ comboDetailData.name }}</text>
                <view class="showCopybox" v-if="copytextShow">
                  <view class="copytext"><text  @click="$util.copy(comboDetailData.name,copyCallback)" class="fuzhi">复制</text><text class="quxiao" @click="copytextShow = false">取消</text></view>
                </view>
              </view>
              <text class="introduction ns-text-color" v-if="comboDetailData.introduction">{{ comboDetailData.introduction }}</text>
            </view>
            <view class="combo-info">
              <view class="combo-info-left">
                <view class="combo-price">
                  <text class="combo-price-one"><text class="symbol">{{ $lang('common.currencySymbol') }}</text>{{comboDetailData.combo_price_min}}</text>
                  <template v-if="comboDetailData.combo_price_min != comboDetailData.combo_price_max">
                    <text class="combo-price-interval">~</text>
                    <text class="combo-price-one"><text class="symbol">{{ $lang('common.currencySymbol') }}</text>{{comboDetailData.combo_price_max}}</text>
                  </template>
                </view>
                <view class="combo-info-left-bottom">
                  <text class="combo-info-left-bottom-name">组合价</text>
                  <text class="combo-info-left-bottom-thrift">最高可省￥{{comboDetailData.cheap_price}}</text>
                </view>
              </view>
              <view class="combo-info-right">
                <view class="combo-info-right-name">活动剩余时间</view>
                <countdown-timer ref="countdown" :time="time" :show-day-symbol="true" @finish="onFinish" autoStart></countdown-timer>
              </view>
            </view>

            <view class="adds-wrap">
              <text class="adds-wrap-volume hide-sales">已售 {{ comboDetailData.sale_nums }} {{ comboDetailData.unit }}</text>
            </view>
          </view>
        </view>
        <view class="group-wrap combo-products" v-if="comboDetailData.select_sku">
          <view class="combo-products-title">
            <view>组合商品</view>
          </view>
          <view class="combo-products-box">
            <view class="combo-products-box-one" v-for="(item,index) in comboDetailData.select_sku" :key="index">
              <template>
                <view class="combo-products-box-one-left">
                  <view class="combo-products-box-one-left-image" @click="toProductDetail(item,index)">
                    <image :src="$util.img(item.sku_image)" mode="" class="combo-products-box-one-left-image-index" @error="imageError(index)"/>
                    <image :src="$util.img('public/static/youpin/product-sell-out.png')" class="over"
                           v-if="item.stock == 0"></image>
                  </view>
                  <view class="combo-products-box-one-left-info">
                    <view class="combo-products-box-one-left-info-title overtext-hidden-one">{{item.goods_name}}</view>
                    <view class="combo-products-box-one-left-info-format" @click.stop="chooseSkuspecFormat(item,index)" v-if="item.sku_spec_format">规格：
                      <text class="combo-products-box-one-left-info-format-spec">{{item.spec_name ? `${item.spec_name}` : '未选择'}}
                        <text class="iconfont iconunfold"></text>
                      </text>
                    </view>
                    <view class="combo-products-box-one-left-info-text">
                      <text class="combo-products-box-one-left-info-text-price"><text class="combo-products-box-one-left-info-text-price-symbol">￥</text>{{item.combo_price}}</text>
                      <text class="combo-products-box-one-left-info-text-thrift">已省{{item.cheap_price}}元</text>
                      <text class="combo-products-box-one-left-info-text-number">X {{item.buy_nums}}</text>
                    </view>
                  </view>
                </view>
              </template>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- SKU选择 -->
    <ns-goods-sku  ref="goodsSku" @refresh="refreshGoodsSkuDetail" :limitBuy="true" :comboId="combo_id" entrance="combo_buy"
                  :goods-detail="goodsSkuDetail" @openModel="openModel" @closeModel="closeModel" :key="sku_index"></ns-goods-sku>



    <!-- 商品底部导航 -->
    <ns-goods-action>
        <ns-goods-action-icon text="首页" icon="iconshouye" :imgicon="$util.img('public/static/youpin/home-icon.png')" @click="goHome" />
        <ns-goods-action-icon text="客服" icon="iconkefu" @click="$util.getCustomerService()" />
        <ns-goods-action-icon text="购物车" icon="icongouwuche" :imgicon="$util.img('public/static/youpin/shop-icon.png')" :corner-mark="cartCount > 0 ? cartCount + '' : ''"
                              @click="goCart" />

<!--        <ns-goods-action-button class="goods-action-button" :class="'active1'" :text="`已省￥${all_cheap_price}`"-->
<!--                                background="#FFFFFF"/>-->
        <ns-goods-action-button class="goods-action-button" :class="'active2'" text="组合购买" :textPrice="`已省￥${all_cheap_price}`"
                                disabledText="立即购买" :disabled="isAllowBuy ? false : true" background="linear-gradient(129.21deg, rgba(252, 48, 181, 1) 0%, rgba(255, 123, 61, 1) 100%)" @click="buyNow" />
    </ns-goods-action>
    <!-- 分享的页面不是用户绑定的id,提示无法购物弹窗 -->
    <uni-popup ref="popupBan" :maskClick="false">
      <view class="popup-dialog">
        <view class="popup-dialog-header">提示</view>
        <view class="popup-dialog-body">哎哟~系统好像出了点问题，暂时不能支付，请联系客服。</view>
        <view class="popup-dialog-footer">
          <button class="button red" @click="$refs.popupBan.close()">知道了</button>
        </view>
      </view>
    </uni-popup>
    <loading-cover ref="loadingCover"></loading-cover>
    <uni-coupon-pop ref="couponPop"></uni-coupon-pop>
    <!-- 授权登录弹窗 -->
    <yd-auth-popup ref="ydauth"></yd-auth-popup>
    <ns-login ref="login"></ns-login>
    <!-- 返回顶部按钮 -->
    <image :src="$util.img('public/static/youpin/to-top.png')" class="to-top" v-show="showTop" @click="scrollToTopNative"></image>
    <diy-share-navigate-h5 ref="shareNavigateH5"></diy-share-navigate-h5>
	</view>
</template>

<script>
import uniCouponPop from '@/components/uni-coupon-pop/uni-coupon-pop.vue';
import diyShareNavigateH5 from '@/components/diy-share-navigate-h5/diy-share-navigate-h5'
import countdownTimer from '@/components/countdown-timer/countdown-timer.vue';
// #ifdef H5
import {isOnXianMaiApp} from "../../../common/js/h5/appOP";
// #endif
import detail from "../public/js/detail";
import scroll from '../../../common/mixins/scroll-view.js';
import globalConfig from "../../../common/mixins/golbalConfig";
import appInlineH5 from "../../../common/mixins/appInlineH5";
export default {
	components: {
    uniCouponPop,
    diyShareNavigateH5,
    countdownTimer
	},
	data() {
		return {
      combo_id: 0, //组合id
      // #ifdef H5
      isOnXianMaiApp:isOnXianMaiApp,
      // #endif
      fixedHeight: false, // 是否固定高度禁止上下滑动
      btnSwitch:true,
      copytextShow: false,
      timeFinish:false,
      isError:false,
		};
	},
	onHide() {
		this.btnSwitch = true;
	},
	computed: {
		//vueX页面主题
		themeStyle() {
			return 'theme-' + this.$store.state.themeStyle;
		},
    // 弹窗后固定高度禁止上下滑动
    FixedHeight() {
      return this.fixedHeight ? 'FixedHeight' : ''
    },
	},
  mixins:[detail,scroll,globalConfig,appInlineH5],
	methods: {
    openModel() {
      this.fixedHeight = true
    },
    closeModel() {
      this.fixedHeight = false
    },
		imageError(index) {
			this.comboDetailData.select_sku[index].sku_image = this.$util.getDefaultImage().default_goods_img;
			this.$forceUpdate();
		},
    longpress() {
      this.copytextShow = true
    },
    copyCallback() {
      this.copytextShow = false
    }
	}
};
</script>

<style lang="scss">
@import '../public/css/detail.scss';

.ns-text-color {
  color: #FF1010 !important;
}
/deep/ .goods-action-button.active1 {
  padding-left: 10px;
}
/deep/ .goods-action-button.active2 {
  padding-right: 10px;
}
/deep/ .goods-action-button.active1 .action-buttom-wrap {
  color: #F2270C;
  border: 1px solid #F2270C;
  border-radius: 40rpx;
  box-sizing: border-box;
  margin-right: 14rpx;
}
/deep/ .goods-action-button.active2 .action-buttom-wrap {
  border-radius: 40rpx;
  box-sizing: border-box;
}
.page-title{
  width: 360rpx;
  overflow:hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  -o-text-overflow:ellipsis;
  text-align: center;
}
.to-top {
  width: 144rpx;
  height: 152rpx;
  position: fixed;
  right: 0;
  bottom: 200rpx;
}
.showCopybox {
  position: absolute;
  top: -66rpx;
  left: 45%;
  .copytext {
    text-align: center;
    border-radius: 10rpx;
    color: #fff;
    font-size: 24rpx;
    position: relative;
    &::after {
      content: '';
      display: block;
      width: 20rpx;
      height: 20rpx;
      background: #1F2022;
      position: absolute;
      bottom: -16rpx;
      left: 30rpx;
      transform: rotate(45deg);
    }
  }
  .fuzhi {
    border-right: 1px solid rgba(255,255,255,0.7);
    padding:16rpx 24rpx;
    background: #1F2022;
    border-radius: 5px 0 0 5px;
  }
  .quxiao {
    padding:16rpx 24rpx;
    background: #1F2022;
    border-radius: 0 5px 5px 0;
  }
}
.combo-price{
  display: flex;
  align-items: center;
  &-one{
    font-size: 56rpx;
    font-weight: 700;
    line-height: 56rpx;
    color: rgba(246, 93, 114, 1);
    .symbol{
      font-size: 24rpx;
    }
  }
  &-interval{
    margin: 0 6rpx;
    color: rgba(246, 93, 114, 1);
  }
}
.combo-info{
  display: flex;
  justify-content: space-between;
  align-items: center;
  &-left{
    &-bottom{
      margin-top: 10rpx;
      display: flex;
      align-items: center;
      &-name{
        width: 112rpx;
        height: 36rpx;
        border-radius: 20rpx;
        background: linear-gradient(129.21deg, rgba(252, 48, 181, 1) 0%, rgba(255, 123, 61, 1) 100%);
        display: flex;
        justify-content: center;
        align-items: center;
        font-size: 24rpx;
        font-weight: 400;
        line-height: 32rpx;
        color: rgba(255, 255, 255, 1);
        position: relative;
        &:after{
          content: " ";
          width: 0;
          height: 0;
          border-left: 10rpx solid transparent;  /* left arrow slant */
          border-right: 10rpx solid transparent; /* right arrow slant */
          border-bottom: 12rpx solid rgba(255, 51, 51, 1); /* bottom, add background color here */
          font-size: 0;
          line-height: 0;
          position: absolute;
          left: 50%;
          top: -10rpx;
          transform: translateX(-50%);
        }
      }
      &-thrift{
        margin-left: -42rpx;
        width: 278rpx;
        height: 36rpx;
        border-radius: 40rpx;
        background: rgba(246, 93, 114, 0.1);
        font-size: 24rpx;
        font-weight: 400;
        line-height: 32rpx;
        color: rgba(246, 93, 114, 1);
        display: flex;
        align-items: center;
        justify-content: center;
      }
    }
  }
  &-right{
    display: flex;
    flex-direction: column;
    align-items: flex-end;
    &-name{
      font-size: 26rpx;
      font-weight: 400;
      line-height: 30.26rpx;
      color: rgba(128, 128, 128, 1);
      margin-bottom: 6rpx;
    }
    /deep/.custom {
      .day{
        padding: 4rpx 8rpx;
        box-sizing: border-box;
        font-size: 28rpx;
        font-weight: 700;
        color: rgba(246, 93, 114, 1);
      }
      .hour,.minute,.second{
        border-radius: 10rpx;
        background: rgba(245, 245, 245, 1);
        padding: 4rpx 8rpx;
        box-sizing: border-box;
        font-size: 28rpx;
        font-weight: 700;
        color: rgba(246, 93, 114, 1);
      }
      .day-symbol{
        font-size: 28rpx;
        font-weight: 700;
        color: rgba(246, 93, 114, 1);
      }
      .hour-symbol,.minute-symbol,.second-symbol{
        font-size: 28rpx;
        font-weight: 700;
        color: rgba(246, 93, 114, 1);
        margin: 0 6rpx;
      }
    }
  }
}
</style>
