import {scenePare} from "../../../../common/js/scene_handle";
import system from "@/common/js/system.js";

export default {
    data(){
      return{
          switchMedia: 'img',
          swiperInterval: 1,
          swiperAutoplay: false,
          swiperCurrent: 1,
          cartCount: 0, // 购物车商品数量
          goodsSkuDetail:{},
          sku_index:0,
          time:999999,
          comboDetailData:{
              images:[]
          },
          isAllowBuy:true,  //是否允许购买
          all_cheap_price:0,
          is_choiceing:false,  //是否选择规格中
      }
    },
    onLoad(data) {
        // 小程序扫码进入
        if (data.scene) {
            scenePare(false, data);
        }
        this.combo_id = data.combo_id || 0;
    },
    async onShow() {
        // 刷新多语言
        this.$langConfig.refresh();
        await system.wait_staticLogin_success();
        await this.getComboData();

        if (this.$refs.loadingCover) this.$refs.loadingCover.hide();
        if (uni.getStorageSync('is_register')) {
            this.$util.toShowCouponPopup(this)
            uni.removeStorageSync('is_register');
        }
        if(uni.getStorageSync('token')){
            this.getCartCount();
        }

    },
    methods:{
        swiperChange(e) {
            this.swiperCurrent = e.detail.current + 1;
        },
        // 预览图片
        previewMedia(index) {
            var paths = [];
            for (let i = 0; i < this.comboDetailData.images.length; i++) {
                paths.push(this.$util.img(this.comboDetailData.images[i]));
            }
            uni.previewImage({
                current: index,
                urls: paths,
            });
        },
        //h5播放视频
        openVideo(){
            this.$refs.videoPopup.open();
        },
        swiperImageError(index) {
            this.comboDetailData.images[index] = this.$util.getDefaultImage().default_goods_img;
            this.$forceUpdate();
        },
        goHome() {
            this.$util.redirectTo('/otherpages/shop/home/<USER>', {}, 'reLaunch');
        },
        //获取购物车数量
        getCartCount() {
            this.$store.dispatch('getCartNumber').then((e)=>{
                this.cartCount = e;
            })
        },
        goCart() {
            this.$util.redirectTo('/pages/goods/cart/cart', {}, 'reLaunch');
        },
        redirectDetailsUrl(){
            let shop_id=uni.getStorageSync('shop_id');
            let path = `/promotionpages/combo/detail/detail?combo_id=${this.combo_id}&shop_id=${shop_id}`;
            return path
        },
        checkShoppingStatus(){
            let is_shopping_status = uni.getStorageSync('is_shopping_status')
            if(is_shopping_status == 0){
                this.$refs.popupBan.open()
                return true
            }
        },
        // 立即购买
        async buyNow() {
            if (!uni.getStorageSync('token')) {
                // this.$refs.login.open('/pages/goods/detail/detail?sku_id=' + this.skuId);
                let path = this.redirectDetailsUrl()
                this.$util.toShowLoginPopup(this,null,path);
                return;
            }
            //检查会员是否禁止购物
            if(this.checkShoppingStatus()) return false

            for (let i = 0; i < this.comboDetailData.select_sku.length; i++) {
                if(!this.comboDetailData.select_sku[i].sku_id && this.comboDetailData.select_sku[i].sku_spec_format){
                    this.chooseSkuspecFormat(this.comboDetailData.goods_sku_detail[i],i);
                    return ;
                }
            }


            if (this.btnSwitch == false) return;

            this.btnSwitch = false;

            var data = {
                combo_id: this.combo_id,
                limitType:true,
                combo_sku_ids:JSON.stringify(this.comboDetailData.select_sku.map(item=>item.sku_id))
            };
            uni.setStorage({
                key: 'orderCreateData',
                data: data,
                success: () => {
                    this.$util.redirectTo('/pages/order/payment/payment');
                    this.btnSwitch = false;
                }
            });
        },
        onFinish(){
            let path='/promotionpages/combo/detail/detail?combo_id=' + this.combo_id
            this.$util.redirectTo(path, {}, 'redirectTo');
        },
        //检测是否可以购买
        checkAllowBuy(){
            for (let i = 0; i < this.comboDetailData.select_sku.length; i++) {
                if(this.comboDetailData.select_sku[i].stock<1){
                    this.isAllowBuy=false;
                    break;
                }
            }
        },
        //计算合计优惠
        async calculateDiscount(){
            let sku_ids=[];
            for (let i = 0; i < this.comboDetailData.select_sku.length; i++) {
                if(this.comboDetailData.select_sku[i].sku_id){
                    sku_ids.push(this.comboDetailData.select_sku[i].sku_id)
                }
            }
            let datas={
                token:uni.getStorageSync('token'),
                combo_id:this.combo_id,
                sku_ids:JSON.stringify(sku_ids)
            }
            let res = await this.$api.sendRequest({
                url: this.$apiUrl.comboCheapPriceUrl,
                async: false,
                data: datas
            });
            if (res.code != 0) {
                uni.showToast({
                    title: res.message,
                    mask: true,
                    icon: "none",
                    duration: 3000
                });
            }else{
                this.all_cheap_price=res.data.cheap_price;
            }
        },
        /**
         * 刷新商品详情数据
         * @param {Object} goodsSkuDetail
         */
        async refreshGoodsSkuDetail(goodsSkuDetail) {
            if(this.sku_index!=undefined){
                this.$set(this.comboDetailData.select_sku,this.sku_index,goodsSkuDetail);
                this.$set(this.comboDetailData.goods_sku_detail,this.sku_index,goodsSkuDetail);
            }
            this.goodsSkuDetail=goodsSkuDetail;
            await this.calculateDiscount();
            this.checkAllowBuy();
        },
        chooseSkuspecFormat(skuDict,index){
            if(this.is_choiceing){
                return;
            }
            this.is_choiceing=true;
            this.sku_index=index;
            if (!uni.getStorageSync('token')) {
                let path=this.redirectDetailsUrl();
                this.$util.toShowLoginPopup(this,null,path);
                return;
            }
            if(skuDict.sku_id!=this.goodsSkuDetail.sku_id){
                this.goodsSkuDetail=this.comboDetailData.goods_sku_detail[this.sku_index];
            }
            this.$nextTick(()=>{
                this.$refs.goodsSku.show("combo_buy", () => {
                });
            })
            setTimeout(()=>{
                this.is_choiceing=false;
            },200)
        },
        toProductDetail(item,index) {
            let sku_id = item.sku_id;
            if(sku_id==0){
                sku_id=this.comboDetailData.goods_sku_detail[index].sku_id;
            }
            this.$util.redirectTo('/pages/goods/detail/detail', {
                sku_id
            });
        },
        async getComboData(){
            let datas={
                token:uni.getStorageSync('token'),
                combo_id:this.combo_id
            }
            let res = await this.$api.sendRequest({
                url: this.$apiUrl.comboDetailUrl,
                async: false,
                data: datas
            });
            if (res.code != 0) {
                uni.showToast({
                    title: res.message,
                    mask: true,
                    icon: "none",
                    duration: 3000
                });
                this.isError=true;
            }else{
                let data = res.data;
                this.comboDetailData=data;
                this.time=this.comboDetailData.surplus_time > 0 ? this.comboDetailData.surplus_time*1000 : 0;
                for (let i = 0; i < this.comboDetailData.goods_sku_detail.length; i++) {
                    let goods_spec_format=this.comboDetailData.goods_sku_detail[i].goods_spec_format;
                    let sku_spec_format=this.comboDetailData.goods_sku_detail[i].sku_spec_format;
                    // 当前商品SKU规格
                    if (goods_spec_format) this.comboDetailData.goods_sku_detail[i].goods_spec_format = JSON.parse(goods_spec_format);

                    // 商品SKU格式
                    if (sku_spec_format) this.comboDetailData.goods_sku_detail[i].sku_spec_format = JSON.parse(sku_spec_format);
                }
                for (let i = 0; i < this.comboDetailData.select_sku.length; i++) {
                    let sku_spec_format=this.comboDetailData.select_sku[i].sku_spec_format;
                    // 商品SKU格式
                    if (sku_spec_format) this.comboDetailData.select_sku[i].sku_spec_format = JSON.parse(sku_spec_format);

                }
                uni.setNavigationBarTitle({
                    title: this.comboDetailData.name
                });
                await this.calculateDiscount();
                this.checkAllowBuy();
                // #ifdef H5
                this.setWechatShare();
                // #endif
            }

        },
        /**
         *分享参数组装(注意需要分享的那一刻再调此方法)
         */
        getSharePageParams(){
            let share_data=this.$util.unifySharePageParams('/promotionpages/combo/detail/detail','先迈商城',
                this.comboDetailData.name,{combo_id:this.combo_id},this.$util.img(this.comboDetailData.images[0]))
            return share_data;
        },
        /**
         * 设置微信公众号分享
         */
        setWechatShare() {
            // 微信公众号分享
            // #ifdef H5
            let share_data=this.$util.deepClone(this.getSharePageParams());
            let link=window.location.origin+this.$router.options.base+share_data.link.slice(1);
            share_data.link=link;
            this.$util.publicShare(share_data);
            // #endif
        },
    },
    /**
     * 自定义分享内容
     * @param {Object} res
     */
    onShareAppMessage(res) {
        let { title, link, imageUrl, desc } = this.getSharePageParams()
        return this.$buriedPoint.pageShare(link , imageUrl, desc);
    },
    /**
     * 自定义分享朋友圈-暂时只支持安卓
     * @param {Object} res
     */
    onShareTimeline(res) {
        let share_data=this.getSharePageParams();
        return {
            title: share_data.desc,
            imageUrl: share_data.imageUrl,
            query: share_data.query,
            success: res => {},
            fail: res => {}
        };
    },
}
