<template>
	<view class="detail">
		<image class="detail-bgImg" :src="$util.img('/public/static/youpin/bg-bargain.png')" mode="widthFix"></image>
		<view class="detail-page">
			<view class="bargain-info">
				<image class="thumbImg" :src="$util.img(activityInfo.sku_image)" @error="imageError()" mode="widthFix"></image>
				<!-- 已发起砍价用launch_status，未发起用status判断 -->
				<!-- 砍价完成 （发起人，砍价已完成（未购买）或者已购买 或者砍价失败）-->
				<block v-if="is_apply_user && (activityInfo.launch_status==1 || activityInfo.launch_status==4 || activityInfo.launch_status==2)">
					<view class="countdown" v-if="activityInfo.launch_status!=2">
						<diyCountdown :time="activityInfo.countdown" format="dd天 hh:mm:ss 后过期" custom-style="color:#8F5806;font-weight:bold" @finish="changeTime"></diyCountdown>
					</view>
					<view class="price"><text>{{activityInfo.curr_price}}</text>元</view>
					<view class="btn-list">
						<view class="btn bargaining" v-if="activityInfo.launch_status==1" @click="buyNow(activityInfo.sku_id,1)">砍价已完成，立即购买</view>
						<view class="btn disabled" v-else-if="activityInfo.launch_status==4">砍价已完成</view>
						<view class="btn disabled" v-else-if="activityInfo.launch_status==2">砍价失败</view>
					</view>
				</block>
				<!-- （活动进行中并且砍价进行中，已发起砍价launch_id不为0，launch_status==1:砍价已完成 launch_status==0，砍价中） -->
				<block v-else-if="activityInfo.status==1 && activityInfo.launch_id && (activityInfo.launch_status==0 || activityInfo.launch_status==1|| activityInfo.launch_status==4)">
					<view class="countdown">
						<diyCountdown :time="activityInfo.countdown" format="dd天 hh:mm:ss 后过期" custom-style="color:#8F5806;font-weight:bold" @finish="changeTime"></diyCountdown>
					</view>
					<view class="money" v-if="activityInfo.launch_status==0">离最低价<text>{{activityInfo.distance_price}}</text>元</view>
					<view class="price">当前<text>{{activityInfo.curr_price}}</text>元</view>

					<!-- 活动发起者 start(发起人) -->
					<view class="btn-list" v-if="is_apply_user">
						<view class="btn" :class="{bargaining:activityInfo.buy_type==1}"><button open-type="share">邀请好友砍价</button></view>
						<view class="btn" v-if="activityInfo.buy_type==0" @click="buyNow(activityInfo.sku_id,1)">立即购买</view>
					</view>
					<!-- 活动发起者 end -->

					<!-- 好友砍价 start -->
					<view class="btn-list" v-if="!is_apply_user">
						<view class="btn bargaining" @click="toBargain" v-if="activityInfo.help_status">帮好友砍一刀</view>
						<view class="btn bargaining" v-else @click="likeTo">{{activityInfo.launch_status!=0?'砍价已完成，我要参与':'我要参与'}}</view>
					</view>
					<!-- 好友砍价 end -->
				</block>

				<!-- 活动已结束 start -->
				<block v-else>
					<view class="explain">活动已结束</view>
					<view class="btn-list">
						<view class="btn disabled">活动已结束</view>
					</view>
				</block>
				<!-- 活动已结束 end -->
			</view>
			<!-- 砍价记录 -->
			<view class="bargain-list">
				<view class="title">砍价记录</view>
				<view class="list" v-if="recordList.length>0">
					<view class="item" v-for="item in recordList" :key="item.id">
						<view class="left">
							<image :src="item.headimg" mode="widthFix"></image>
							<text>{{item.nickname}}</text>
						</view>
						<view class="right">
							<image :src="$util.img('public/static/youpin/jinbi-icon.png')" mode="widthFix"></image>
							<text>砍掉{{item.money}}元</text>
						</view>
					</view>
				</view>
				<view class="noData" v-else>暂无砍价记录~</view>
			</view>
			<!-- 店主二维码 -->
			<view class="qrcode">
				<view class="title">保存添加店主微信，了解更多活动信息</view>
				<image :src="$util.img(shopInfo.wechat_qrcode_img)" show-menu-by-longpress mode="widthFix"></image>
			</view>

		</view>
		<loading-cover ref="loadingCover"></loading-cover>
		<!-- 授权登录弹窗 -->
		<yd-auth-popup ref="ydauth"></yd-auth-popup>
    <ns-login ref="login"></ns-login>
    <uni-coupon-pop ref="couponPop"></uni-coupon-pop>

		<view class="bargain-tip" v-if="isShowPopup">
			<view class="bargain-tip-content">
				<image :src="$util.img('public/static/youpin/close-btn.png')" class="bargain-tip-content-close" @click="isShowPopup=false"></image>
				<image :src="bargain.headimg" class="bargain-tip-content-head"></image>
				<view class="bargain-tip-content-desc">谢谢你帮我砍掉<text>{{bargain.bargain_money}}</text>元</view>
				<button class="bargain-tip-content-op" @click="likeTo">我也想要</button>
				<view class="bargain-tip-content-info">添加店主微信，了解更多活动信息</view>
				<image :src="$util.img(shopInfo.wechat_qrcode_img)" show-menu-by-longpress class="bargain-tip-content-qrcode"></image>
			</view>
		</view>
	</view>
</template>

<script>
	import diyCountdown from '@/components/diy-countdown/diy-countdown'
	import system from "@/common/js/system.js";
	export default {
		name: 'detail',
		components: {
			diyCountdown
		},
		data() {
			return {
				activityInfo:{},//活动详情
				shopInfo:{}, //店铺信息
				recordList:[],//帮砍记录
				is_apply_user:true, //是否为申请人
				bargain:{}, // 帮砍

				path: "/promotionpages/bargain/detail/detail",
				isShowPopup:false,
			}
		},
		onLoad(options) {
			this.activity_id = options.activity_id;
			this.apply_id = options.apply_id;
			this.init()
		},
		onShow() {
			// 刷新多语言
			this.$langConfig.refresh();

			this.token = uni.getStorageSync('token');
			if (uni.getStorageSync('is_register')) {
				this.$util.toShowCouponPopup(this)
				uni.removeStorageSync('is_register');
			}
		},
		onShareAppMessage() {
			system.goodsShare(this.activityInfo.bargain_goods_id)

			let shop_id = wx.getStorageSync('shop_id') ? wx.getStorageSync('shop_id') : "";
			let path = `${this.path}?activity_id=${this.activity_id}&shop_id=${shop_id}`;
			let apply_id = this.activityInfo.launch_id
			path = `${path}&apply_id=${apply_id}`
      let recommend_member_id=uni.getStorageSync('member_id');
      if(recommend_member_id){
        path+=`&recommend_member_id=${recommend_member_id}`;
      }
			let obj = {
				title:`拜托帮我点一下好吗？我正在领取${this.activityInfo.sku_name}`,
				path:path,
				imageUrl:this.$util.img(this.activityInfo.sku_image)
			}
			return obj
		},
		methods: {
			checkToken(){
				system.checkToken().then(res=>{})
			},
			async init(id){
				let data = {
					activity_id:this.activity_id
				}
				if(!id){
					await this.checkToken()
				}else{
					data.launch_id = id
				}

				if(this.apply_id){
					data.launch_id = this.apply_id
				}
				this.$api.sendRequest({
					url:this.$apiUrl.bargainActivityInfo,
					data,
					success:(res)=>{
						if (this.$refs.loadingCover) this.$refs.loadingCover.hide();
						if(res.code!=0){
							this.$util.showToast({
								title:res.message
							})
							setTimeout(()=>{
								this.$util.redirectTo('/pages/index/index/index', {}, 'reLaunch');
							},1000)
							return
						}
						let data = res.data

						data.activityInfo.countdown = Math.abs(data.activityInfo.countdown)* 1000
						this.shopInfo = data.shopInfo;
						this.is_apply_user = data.activityInfo.member_id == uni.getStorageSync('member_id')
						this.activityInfo = data.activityInfo;
						this.getRecordList(data.activityInfo.launch_id)
					},
					fail:(err)=>{
						this.$util.showToast({
							title:err.message
						})
						if (this.$refs.loadingCover) this.$refs.loadingCover.hide();
					}
				})
			},
			getRecordList(id){
				if(!id){
					this.getLaunch()
					return
				}
				this.$api.sendRequest({
					url:this.$apiUrl.recordList,
					data:{ id },
					success:(res)=>{
						if(res.code!=0){
							this.$util.showToast({
								title:res.message
							})
							return
						}
						this.recordList = res.data.list
					},
					fail:(err)=>{
						this.$util.showToast({
							title:err.message
						})
					}
				})
			},
			getLaunch(){
				if(this.activityInfo.status==0) return
				this.$api.sendRequest({
					url:this.$apiUrl.launch,
					data:{
						id:this.activityInfo.bargain_goods_id
					},
					success:(res)=>{
						if(res.code!=0){
							this.$util.showToast({
								title:res.message
							})
							return
						}
						this.activityInfo.launch_id = res.data;
						this.init(this.activityInfo.launch_id)
					},
					fail:(err)=>{}
				})
			},
			toBargain() {
				if (!this.token) {
          let path = `${this.path}?activity_id=${this.activity_id}`
          if(this.apply_id){
            path = `${path}&apply_id=${this.apply_id}`
          }
          this.$util.toShowLoginPopup(this,null,path);
					return
				}
				uni.showLoading({ })
				this.$api.sendRequest({
					url:this.$apiUrl.bargain,
					data:{
						id:this.apply_id
					},
					success:(res)=>{
						uni.hideLoading()
						if(res.code!=0){
							this.$util.showToast({
								title:res.message
							})
							return
						}
						this.bargain = res.data;
						this.isShowPopup = true;
						this.activityInfo.help_status = 0;
						this.init(this.apply_id)
					},
					fail:(err)=>{
						uni.hideLoading()
						this.$util.showToast({
							title:res.message
						})
					}
				})

			},
			buyNow(sku_id,num){
				this.$util.redirectTo('/promotionpages/bargain/payment/payment', {sku_id:sku_id,num:1,launch_id:this.activityInfo.launch_id}, '');
			},
			likeTo:function(){
			    let shop_id = uni.getStorageSync('shop_id') ? uni.getStorageSync('shop_id') : "";
			    let path=`${this.path}?shop_id=${shop_id}&activity_id=${this.activity_id}`;
			    this.$util.redirectTo(path,{},'');
			},
			changeTime(e) {
				this.activityInfo.launch_status = 2
			},
			imageError() {
				this.activityInfo.sku_image = this.$util.getDefaultImage().default_goods_img;
				this.$forceUpdate();
			}
		}
	}
</script>
<style>

</style>
<style lang="scss" scoped>
	view {
		line-height: 1;
	}

	.detail {
		min-height: 100vh;
		background: #FFE8C2;
		position: relative;
		overflow: scroll;
		-webkit-overflow-scrolling: touch;

		.detail-bgImg {
			width: 100%;
		}

		.detail-page {
			position: absolute;
			left: 50%;
			top: 60rpx;
			transform: translateX(-50%);
			padding-bottom: 27rpx;
			box-sizing: border-box;

			.bargain-info {
				width: 702rpx;
				border-radius: 20rpx;
				background: linear-gradient(0deg, #FFFFFF 0%, #FFF7E4 100%);
				box-shadow: 0px -4px 0px 0px #FFFFFF;
				padding: 50rpx 0 40rpx 0;

				.thumbImg {
					width: 280rpx;
					height: 280rpx;
					border-radius: 20rpx;
					display: block;
					margin: 0 auto;
				}

				.countdown {
					width: 280rpx;
					height: 54rpx;
					line-height: 54rpx;
					background: #FFE8C2;
					box-shadow: 0px -2px 1px 0px #FDEED8;
					border-radius: 10rpx;
					margin: 60rpx auto 40rpx auto;
					font-size: 28rpx;
					padding: 0 18rpx;
					text-align: center;
				}

				.money {
					color: #5D330E;
					font-size: 30rpx;
					text-align: center;
					font-weight: bold;

					text {
						color: $base-color
					}
				}

				.price {
					color: #5D330E;
					font-size: $ns-font-size-sm;
					text-align: center;
					margin-top: 34rpx;

					text {
						color: $base-color;
						font-weight: bold;
						font-size: 80rpx;
						margin: 0 10rpx
					}
				}

				.explain {
					font-size: 30rpx;
					font-weight: bold;
					color: #5D330E;
					margin: 60rpx 0;
					text-align: center;
				}

				.btn-list {
					margin-top: 60rpx;
					display: flex;
					justify-content: space-between;
					align-items: center;
					padding: 0 50rpx;

					.btn {
						width: 280rpx;
						height: 80rpx;
						background: linear-gradient(90deg, #FB331D 0%, #FE5838 100%);
						border-radius: 40rpx;
						line-height: 80rpx;
						text-align: center;
						color: #fff;
						font-size: 28rpx;

						button {
							background: none;
							border: none;
							margin: 0;
							padding: 0;
							color: #fff;
							font-size: 28rpx;
						}
					}

					.disabled {
						width: 600rpx;
						background: #ccc;
					}

					.bargaining {
						width: 600rpx;
					}
				}
			}
		}

		// 砍价记录
		.bargain-list {
			width: 702rpx;
			margin-top: 24rpx;
			background: linear-gradient(0deg, #FFFFFF 0%, #FFF7E4 100%);
			box-shadow: 0px -4px 0px 0px #FFFFFF;
			border-radius: 20rpx;

			.title {
				width: 280rpx;
				height: 54rpx;
				background: #FFFFFF;
				box-shadow: 0px 2px 1px 0px #FDEED8;
				border-radius: 10rpx 10rpx 20rpx 20rpx;
				line-height: 54rpx;
				text-align: center;
				margin: 0 auto;
				color: #8F5806;
				font-size: 28rpx;
			}

			.list {
				padding-top: 22rpx;

				.item {
					display: flex;
					justify-content: space-between;
					align-items: center;
					box-sizing: border-box;
					padding: 18rpx 30rpx;

					.left {
						display: flex;
						align-items: center;
						color: #8F5806;
						font-size: 28rpx;

						image {
							width: 72rpx;
							height: 72rpx;
							margin-right: 20rpx;
							border-radius: 50%;
						}
					}

					.right {
						display: flex;
						align-items: center;
						color: #8F5806;
						font-size: 28rpx;

						image {
							width: 40rpx;
							height: 40rpx;
							margin-right: 14rpx;
						}
					}
				}
			}
		}

		.qrcode {
			width: 702rpx;
			background: #FFFFFF;
			border-radius: 20px;
			margin: 20px auto;
			padding-bottom: 40rpx;

			.title {
				color: #8F5806;
				font-size: 28rpx;
				font-weight: bold;
				text-align: center;
				padding: 30rpx 0;
			}

			image {
				width: 680rpx;
				height: 680rpx;
				display: block;
				margin: 0 auto
			}
		}
	}

	// 砍价弹窗
	.bargain-tip {
		width: 100vw;
		height: 100vh;
		position: fixed;
		left: 0;
		top: 0;
		background-color: rgba(0, 0, 0, 0.5);
		display: flex;
		justify-content: center;
		align-items: center;
	}

	.bargain-tip-content {
		width: 620rpx;
		background: #FFFFFF;
		border-radius: 20rpx;
		position: relative;
		display: flex;
		flex-direction: column;
		align-items: center;
		padding-bottom: 40rpx;
		box-sizing: border-box;
	}

	.bargain-tip-content-close {
		width: 60rpx;
		height: 60rpx;
		position: absolute;
		top: 0;
		left: 0;
		transform: translate(-50%, -50%);
	}

	.bargain-tip-content-head {
		width: 128rpx;
		height: 128rpx;
		border-radius: 50%;
		background: #FFFFFF;
		box-shadow: 0px 4rpx 0px 0px rgba(0, 0, 0, 0.1);
		position: absolute;
		left: 50%;
		top: 0;
		transform: translate(-50%, -50%);
	}

	.bargain-tip-content-desc {
		font-size: 34rpx;
		font-weight: 500;
		color: #333333;
		margin-top: 112rpx;
	}

	.bargain-tip-content-desc text {
		color: #F2270C;
	}

	.bargain-tip-content-op {
		width: 500rpx;
		height: 80rpx;
		background: #F2270C;
		border-radius: 40rpx;
		font-size: 32rpx;
		font-weight: 500;
		color: #FFFFFF;
		margin-top: 47rpx;
	}

	.bargain-tip-content-info {
		font-size: 28rpx;
		font-weight: 500;
		color: #666666;
		margin-top: 60rpx;
	}

	.bargain-tip-content-qrcode {
		width: 360rpx;
		height: 360rpx;
		margin-top: 20rpx;
	}
	.noData{
		text-align: center;
		padding: 30rpx 0;
		font-size: $ns-font-size-xm;
		color: #999;
	}
</style>
