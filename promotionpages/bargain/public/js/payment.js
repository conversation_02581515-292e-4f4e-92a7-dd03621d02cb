import AdaPay from 'common/js/adaPay.js'
import store from '../../../../store';
export default {
	data() {
		return {
			orderCreateData: {
				is_balance: 0,
				pay_password: '',
				platform_coupon_id:0,
				buyer_message:{},
			},
			orderPaymentData: {
				member_account: {
					balance: 0,
					is_pay_password: 0
				},
				platform_coupon_list:[]
			},
			isSub: false,
			sitePromotion: [],
			siteDelivery: {
				site_id: 0,
				data: []
			},
			siteCoupon: {
				site_id: 0,
				data: []
			},
			shopCoupon: {},
			myCoupon: [],
			isFocus:false,
			tempData: null,


			selectCouponId:"",
			selectCouponMoney:'0.00',
			selectCouponHaveChoose: false,

			selectPlatCouponId:0,
			selectPlatCouponMoney:'0.00',

			// 分享赚
			activity_id: "",
			activity_type: "share"

		};
	},
	methods: {
		/**
		 * 显示弹出层
		 * @param {Object} ref
		 */
		openPopup(ref) {
			if(ref=='PlatcouponPopup'){
				this.selectPlatCouponId=this.orderPaymentData.platform_coupon_id;
				this.selectPlatCouponMoney=this.orderPaymentData.platform_coupon_money;
			}
			this.$refs[ref].open();
		},
		/**
		 * 关闭弹出层
		 * @param {Object} ref
		 */
		closePopup(ref) {
			if (this.tempData) {
				Object.assign(this.orderCreateData, this.tempData);
				Object.assign(this.orderPaymentData, this.tempData);
				this.tempData = null;
				this.$forceUpdate();
			}
			this.$refs[ref].close();
		},
		/**
		 * 选择收货地址
		 */
		selectAddress() {
			this.$util.redirectTo('/otherpages/member/address/address', {
				'back': '/pages/order/payment/payment'
			});
		},
		/**
		 * 获取订单初始化数据
		 */
		getOrderPaymentData() {
			this.orderCreateData = uni.getStorageSync('orderCreateData');
			if (!this.orderCreateData) {
				this.$util.showToast({
					title: '未获取到创建订单所需数据!！',
					success: () => {
						setTimeout(() => {
							console.log('!this.orderCreateData')
							console.log(this.orderCreateData)
							this.$util.redirectTo('/pages/index/index/index', {}, 'reLaunch');
						}, 1500)
					}
				});
				return;
			}
			this.$api.sendRequest({
				url: '/api/activity/bargainpayment',
				data: {'launch_id':this.orderCreateData.launch_id},
				success: res => {
					if (res.code >= 0) {
						this.orderPaymentData = res.data;
						var member_address = uni.getStorageSync('member_address');
						if(member_address) {
							this.orderPaymentData.member_address = member_address
						} else {
							uni.setStorageSync('member_address',this.orderPaymentData.member_address)
						}
						this.handlePaymentData();
						// this.getOrderCouponList();
						if (this.$refs.loadingCover) this.$refs.loadingCover.hide();
					} else if (res.code == -10001) {
						this.$util.showToast({
							title: res.message,
							success: () => {
								setTimeout(() => {
									console.log('if (res.code !>= 0)')
									console.log(res)
									this.$util.redirectTo('/pages/index/index/index', {}, 'reLaunch');
								}, 1500)
							}
						});
					} else {
						this.$util.showToast({
							title: '未获取到创建订单所需数据!！',
							success: () => {
								setTimeout(() => {
									console.log('if (res.code !>= 0)')
									console.log(res)
									this.$util.redirectTo('/pages/index/index/index', {}, 'reLaunch');
								}, 1500)
							}
						});
					}
				},
				fail: res => {
					if (this.$refs.loadingCover) this.$refs.loadingCover.hide();
				}
			})
		},
		/**
		 * 获取可用、不可用优惠券列表
		 */
		getOrderCouponList() {
			var sku_idsArr = [];
			var sku_numsArr = [];
			var shop_goods_listArr = []
			var data = JSON.parse(JSON.stringify(this.orderPaymentData));
			for (let i in data.shop_goods_list) {
			    shop_goods_listArr.push(data.shop_goods_list[i]); //属性
			}
			for (var i = 0; i < shop_goods_listArr.length; i++) {
				shop_goods_listArr[i].goods_list.map(item=>{
					sku_idsArr.push(item.sku_id)
					sku_numsArr.push(item.num)
				})
			}
			this.$api.sendRequest({
				url:this.$apiUrl.orderCoupon,
				data: {
					sku_ids: sku_idsArr.toString(),
					sku_nums: sku_numsArr.toString()
				},
				success: res=>{
					if(res.code == 0){
						console.log(res.data)
						this.shopCoupon = res.data;
						if(this.currentTab==1) {
							this.myCoupon = this.shopCoupon.enable_list
						} else if(this.currentTab==0) {
							this.myCoupon = this.shopCoupon.disable_list
						}
					}
				},
				fali: res=>{

				}
			})

		},

		/**
		 * 处理结算订单数据
		 */
		handlePaymentData() {
			this.orderCreateData.delivery = {};
			this.orderCreateData.coupon = {};


			if (this.orderPaymentData.is_virtual) this.orderCreateData.member_address = {
				mobile: ''
			};


			if(this.orderPaymentData.platform_coupon_list&&this.orderPaymentData.platform_coupon_list.length>0){
				this.orderPaymentData.platform_coupon_id=this.orderPaymentData.platform_coupon_list[0].platformcoupon_id;
				this.orderCreateData.platform_coupon_id=this.orderPaymentData.platform_coupon_list[0].platformcoupon_id;
				this.orderPaymentData.platform_coupon_money=this.orderPaymentData.platform_coupon_list[0].money;
				this.orderCreateData.platform_coupon_money=this.orderPaymentData.platform_coupon_list[0].money;

				this.selectPlatCouponId=this.orderPaymentData.platform_coupon_list[0].platformcoupon_id;
				this.selectPlatCouponMoney=this.orderPaymentData.platform_coupon_list[0].money;
			}


			Object.assign(this.orderPaymentData, this.orderCreateData);
			this.orderCalculate();
		},
		/**
		 * 订单计算
		 */
		orderCalculate() {
			var data = this.$util.deepClone(this.orderCreateData);
			console.log(JSON.stringify(data))
			data.member_address = JSON.stringify(data.member_address);
			data.launch_id = data.launch_id;
			this.$api.sendRequest({
				url: '/api/activity/bargaincalculate',
				data,
				success: res => {
					if (res.code >= 0) {
						this.orderPaymentData.delivery_money = res.data.delivery_money;
						this.orderPaymentData.coupon_money = res.data.coupon_money;
						this.orderPaymentData.invoice_money = res.data.invoice_money;
						this.orderPaymentData.promotion_money = res.data.promotion_money;
						this.orderPaymentData.order_money = res.data.order_money;
						this.orderPaymentData.balance_money = res.data.balance_money;
						this.orderPaymentData.pay_money = res.data.pay_money;
						this.orderPaymentData.goods_money = res.data.goods_money;

						// Object.keys(res.data.shop_goods_list).forEach((key, index) => {
							let siteItem = res.data.shop_goods_list;
							this.orderPaymentData.shop_goods_list.pay_money = siteItem.pay_money;
							this.orderPaymentData.shop_goods_list.coupon_money = siteItem.coupon_money;
						// })
					} else {
						this.$util.showToast({
							title: res.message
						});
					}
				},
			})
		},
		/**
		 * 订单创建
		 */
		orderCreate() {
			if (this.verify()) {
				if (this.isSub) return;
				this.isSub = true;
				uni.showLoading({
					mask: true,
					title: '加载中'
				});
				// 获取收货地址选择后存储的地址
				var member_address = uni.getStorageSync('member_address')
				var data = this.$util.deepClone(this.orderCreateData);

				data.member_address = JSON.stringify(member_address.id);
				this.$api.sendRequest({
					url: '/api/activity/bargainordercreate',
					data,
					success: res => {
						if (res.code >= 0) {

							// 当支付金额为0元时，调用微信支付接口，直接支付成功跳转到支付成功页面
							if(res.data.is_free == 1){
								// 跳转支付成功页面
								this.$util.redirectTo('/pages/pay/result/result?order_ids='+res.data.order_ids, {
									code: res.data.out_trade_no
								}, 'redirectTo');
								uni.removeStorage({
									key: 'orderCreateData',
									success: () => {}
								});
							}else{
								this.orderPayPay(res.data.out_trade_no,res.data.order_ids)
							}
							uni.removeStorageSync('member_address')
						} else {
							this.isSub = false;
							uni.hideLoading();
							if(this.$refs.payPassword){
								this.$refs.payPassword.close();
							}
							if (res.data.error_code == 10 || res.data.error_code == 12) {
								uni.showModal({
									title: '订单未创建',
									content: res.message,
									confirmText: '去设置',
									success: res => {
										if (res.confirm) {
											this.selectAddress();
										}
									}
								})
							} else {
								this.$util.showToast({
									title: res.message
								});
							}
						}
						this.getCartNumber();
					},
					fail: res => {
						uni.hideLoading();
						this.isSub = false;
					}
				})
			}
		},
		/**
		 * 订单支付
		 */
		orderPayPay(out_trade_no,order_ids) {
			var that = this;
				this.$api.sendRequest({
					url: '/api/pay/pay',
					data: {
						out_trade_no: out_trade_no,
						pay_type: 'adapay'
					},
					success: res => {
						uni.hideLoading();
						if (res.code >= 0) {
							this.$util.wechatPay(res.data.pay_type,res.data.pay_type=='adapay'? res.data.payment : res.data.pay_info, (res)=>{
								uni.hideLoading();
								// 跳转支付成功页面
								this.$util.redirectTo('/pages/pay/result/result?order_ids='+order_ids, {
									code: out_trade_no
								}, 'redirectTo');
								uni.removeStorage({
									key: 'orderCreateData',
									success: () => {}
								});
							},(err)=>{
								uni.hideLoading();
								this.$refs.popupToList.open()
							},(err)=>{
								setTimeout(() => {
									this.$util.redirectTo("/pages/order/list/list", {}, "redirectTo")
								}, 2000)
							})
							// 移除分享赚本地存储
							var activity_id = uni.getStorageSync('activity_id')
							// var activity_id = {"95":"1"}
							if (activity_id) {
								var sku_id_arr = []
								var list = this.orderPaymentData.shop_goods_list
								list = JSON.parse(JSON.stringify(list))
								list = Object.values(list)
								Object.keys(list).forEach(function(key){
									 for (var i = 0; i < list[key].goods_list.length; i++) {
										sku_id_arr.push(list[key].goods_list[i].sku_id)
									 }
								});
								console.log('sku_id_arr')
								console.log(sku_id_arr)
								for (var i = 0; i < sku_id_arr.length; i++) {
									delete activity_id[sku_id_arr[i]]
								}
								if(JSON.stringify(activity_id)!="{}") {
									uni.setStorageSync('activity_id',activity_id)
								} else {
									uni.removeStorageSync('activity_id')
									console.log('activity_id')
									console.log(activity_id)
								}
							}
							// return
						} else {
							console.log(res)
							this.isSub = false;
							if(res.message) {
								this.$util.showToast({
									title: res.message
								});
							} else {
								uni.hideLoading();
							}
						}
					},
					fail: res => {
						this.$util.showToast({
							title: 'request:fail'
						});
					}
				})
		},

		/**
		 * 订单验证
		 */
		verify() {
			if (this.orderPaymentData.is_virtual == 1) {
				if (!this.orderCreateData.member_address.mobile.length) {
					this.$util.showToast({
						title: '请输入您的手机号码'
					});
					return false;
				}
				var reg = /^[1](([3][0-9])|([4][5-9])|([5][0-3,5-9])|([6][5,6])|([7][0-8])|([8][0-9])|([9][1,8,9]))[0-9]{8}$/;
				if (!reg.test(this.orderCreateData.member_address.mobile)) {
					this.$util.showToast({
						title: '请输入正确的手机号码'
					});
					return false;
				}
			}

			if (this.orderPaymentData.is_virtual == 0) {
				if (!this.orderPaymentData.member_address) {
					this.$util.showToast({
						title: '请先选择您的收货地址'
					});
					return false;
				}

				let deliveryVerify = true;

				// for (let key in this.orderCreateData.delivery) {
				// 	if (JSON.stringify(this.orderCreateData.delivery[key]) == "{}") {
				// 		deliveryVerify = false;
				// 		this.$util.showToast({
				// 			title: '店铺"' + this.orderPaymentData.shop_goods_list.site_name + '"未设置配送方式'
				// 		});
				// 		break;
				// 	}
				// 	if (this.orderCreateData.delivery[key].delivery_type == 'store' && this.orderCreateData.delivery[key].store_id ==
				// 		0) {
				// 		deliveryVerify = false;
				// 		this.$util.showToast({
				// 			title: '店铺"' + this.orderPaymentData.shop_goods_list.site_name + '"没有可提货的门店,请选择其他配送方式'
				// 		});
				// 		break;
				// 	}
				// }
				if (!deliveryVerify) return false;
			}

			if (this.orderCreateData.is_balance == 1 && this.orderCreateData.pay_password == '') {
				setTimeout(() => {
					this.$refs.input.clear();
				}, 0)
				// this.$refs.payPassword.open();
				this.openPasswordPopup();
				return false;
			}
			return true;
		},
		/**
		 * 显示店铺优惠信息
		 * @param {Object} data
		 */
		openSitePromotion(data) {
			this.sitePromotion = data;
			this.$refs.sitePromotionPopup.open();
		},
		/**
		 * 显示店铺配送信息
		 * @param {Object} index
		 */
		openSiteDelivery(siteId, deliveryData) {
			this.tempData = {
				delivery: this.$util.deepClone(this.orderPaymentData.delivery)
			};

			this.siteDelivery.site_id = siteId;
			this.siteDelivery.data = deliveryData;
			this.$refs.deliveryPopup.open();
		},
		/**
		 * 选择配送方式
		 */
		selectDeliveryType(data) {
			this.orderCreateData.delivery[this.siteDelivery.site_id].delivery_type = data.name;
			this.orderCreateData.delivery[this.siteDelivery.site_id].delivery_type_name = data.title;
			// 如果是门店配送
			if (data.name == 'store') {
				if (data.store_list[0] != undefined) {
					this.orderCreateData.delivery[this.siteDelivery.site_id].store_id = data.store_list[0].store_id;
				}
			}
			Object.assign(this.orderPaymentData, this.orderCreateData);
			this.$forceUpdate();
		},
		/**
		 * 选择自提点
		 */
		selectPickupPoint(store_id) {
			this.orderCreateData.delivery[this.siteDelivery.site_id].store_id = store_id;
			Object.assign(this.orderPaymentData, this.orderCreateData);
			this.$forceUpdate();
		},
		/**
		 * 显示店铺优惠券信息
		 * @param {Object} siteId
		 * @param {Object} couponData
		 */
		openSiteCoupon() {
			// this.tempData = {
			// 	coupon: this.$util.deepClone(this.orderPaymentData.coupon)
			// };
			// this.selectCouponId=this.orderCreateData.coupon[siteId].coupon_id;
			// this.selectCouponMoney=this.orderCreateData.coupon[siteId].coupon_money;
			// this.siteCoupon.data = couponData;
			this.$refs.couponPopup.open();
		},
		/**
		 * 选择优惠券
		 * @param {Object} item
		 */
		selectCoupon(item) {
			if (this.selectCouponId != item.coupon_id) {
				this.selectCouponId = item.coupon_id;
			} else {
				this.selectCouponId = "";
			}
			Object.assign(this.orderPaymentData, this.orderCreateData);
			this.$forceUpdate();
		},
		popupConfirm(ref,item) {
			this.$refs[ref].close();
			this.selectCouponHaveChoose = true
			this.getOrderPaymentData();
			// this.orderCalculate();
		},
		imageError(siteIndex, goodsIndex) {
			this.orderPaymentData.shop_goods_list[siteIndex].goods_list[goodsIndex].sku_image = this.$util.getDefaultImage().default_goods_img;
			this.$forceUpdate();
		},
		// 购物车数量
		getCartNumber() {
			if (uni.getStorageSync("token")) {
				this.$store.dispatch('getCartNumber')
			}
		},
		openPasswordPopup(){
			this.$refs.payPassword.open();
			setTimeout(()=>{
				this.isFocus = true;
			},500)
		},
	},
	onLoad(options) {
		if(options.launch_id) {
			uni.setStorageSync('orderCreateData',{launch_id: options.launch_id})
		}
		// 分享赚
		var activity_id = uni.getStorageSync('activity_id')
		if(activity_id) {
			this.activity_id = JSON.stringify(activity_id)
		}
		uni.removeStorageSync('member_address')
	},
	onShow() {
		// 刷新多语言
		this.$langConfig.refresh();
		var member_address = uni.getStorageSync('member_address')
		// 判断登录
		if (!uni.getStorageSync('token')) {
			this.$util.redirectTo('/pages/login/login/login');
		} else {
			this.orderPaymentData= {
				member_account: {
					balance: 0,
					is_pay_password: 0
				}
			}
			this.orderCreateData= {
				is_balance: 0,
				pay_password: '',
				coupon_id:this.selectCouponId,
				buyer_message:{},
			}
			if(member_address) {
				this.orderCreateData.member_address=member_address.id
			}
			if (!this.isSub) {
				this.getOrderPaymentData();
			};
		}
	},
	onHide() {
		if (this.$refs.loadingCover) this.$refs.loadingCover.show();
	},
	filters: {
		/**
		 * 金额格式化输出
		 * @param {Object} money
		 */
		moneyFormat(money) {
			return parseFloat(money).toFixed(2);
		},
		/**
		 * 店铺优惠摘取
		 */
		promotion(data) {
			let promotion = '';
			if (data) {
				Object.keys(data).forEach((key) => {
					promotion += data[key].content + '　';
				})
			}
			return promotion;
		}
	}
}
