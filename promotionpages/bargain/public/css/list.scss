.bargain-list{
	padding: 0 24rpx;
	.bargin-item-title{
		display: flex;
		justify-content: center;
		align-items: center;
		height: 76rpx;
		color: #FF7200;
	}
}
.bargain-list .bargin-item{
	display: flex;
	background-color: #fff;
	border-radius: 10rpx;
	height: 250rpx;
	padding: 24rpx;
	margin-bottom: 24rpx;
	box-sizing: border-box;
	image{		
		width: 200rpx;
		height: 200rpx;
		margin-right: 22rpx;
		border-radius: 10rpx;
	}
	.content{
		position: relative;
		line-height: 1;
		flex: 1;
		.title{
			font-size: $ns-font-size-sm;
			overflow: hidden;
			text-overflow: ellipsis;
			display: -webkit-box;
			-webkit-line-clamp: 2;
			-webkit-box-orient: vertical;
			line-height: 36rpx;
		}
		.operation{
			position: absolute;
			bottom: 0;
			display: flex;
			justify-content: space-between;
			align-items: flex-end;
			width: 100%;
		}
		.residue-price{
			display: inline-block;
			margin-top: 6rpx;
			background: rgba($base-color,0.08) !important;
			font-size: $ns-font-size-sm;
			padding: 2rpx 4rpx;
		}
		.price-box{
			line-height: 1.6;
			font-size: $ns-font-size-sm;
			.original-price{
				color: #777;
				text-decoration: line-through;
			}
			.time{
				font-size: 22rpx;
				text{
					padding: 2rpx 4rpx;
					margin: 0 4rpx;
					color: #fff;
					border-radius: 4rpx;
					line-height: 1;
				}
			}
		}
		.btn{
			display: flex;
			justify-content: center;
			align-items: center;
			margin: 0;
			padding: 0;
			width: 136rpx;
			height: 48rpx;
			line-height: 1;
			color: #fff;
			font-size: $ns-font-size-sm;
			background-color: #FF7200;
			border-radius: 40rpx;
		}
	}
}
.no-participation{
	.price-box{
		display: flex;
		flex-direction: column;
	}
	.floor-price{
		font-weight: 500;
	}
}
