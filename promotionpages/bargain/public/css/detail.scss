[data-theme] {
	.go-top {
		position: fixed;
		right: 30rpx;
		bottom: 220rpx;
		z-index: 1;
		background: #fff;
		padding: 10rpx;
		border: 1px solid;
		border-radius: 20px;
		width: 57rpx;
		height: 270rpx;
		// height: 180rpx;
		text-align: center;
		font-size: $ns-font-size-sm;
		.goods-share,
		.collection {
			margin-bottom: 10rpx;
			font-size: $ns-font-size-sm;
		}
		.icontop {
			font-size: 40rpx;
		}
	}

	.follow-and-share {
		height: 100rpx;
		position: absolute;
		right: 20rpx;
		top: 0;
		z-index: 100;
		display: flex;
		justify-content: space-between;
		align-items: center;
		.follow,
		.share {
			width: 60rpx;
			height: 60rpx;
			background: rgba($color: #000000, $alpha: 0.4);
			border-radius: 50%;
			display: flex;
			justify-content: center;
			align-items: center;
			.iconfont {
				font-size: 36rpx;
				color: #ffffff;
				line-height: 1;
			}
		}
	}

	.goods-detail {
		height: 100%;
		padding-bottom: 100rpx;
	}
	.goods-detail.active {
		height: 100%;
		padding-bottom: 170rpx;
	}
	// 商品媒体信息
	.goods-media {
		width: 100%;
		position: relative;
		overflow: hidden;

		&:after {
			padding-top: 100%;
			display: block;
			content: '';
		}

		.pop-video {
			line-height: 1;
		}
		.goods-img,
		.goods-video {
			position: absolute;
			width: 100%;
			height: 100%;
			left: 0;
			top: 0;
			transition-property: transform;
			transition-timing-function: cubic-bezier(0, 0, 0.25, 1);
			transition-duration: 350ms;
			transform: translate3d(0, 0, 0);
			.video-img {
				width: 100%;
				height: 100%;
				position: relative;
			}
			image {
				width: 100%;
				height: 100%;
			}
			.video-open {
				width: 100%;
				height: 100%;
				position: absolute;
				left: 0;
				top: 0;
				display: flex;
				justify-content: center;
				align-items: center;
				.iconfont {
					font-size: 90rpx;
					color: #fff;
				}
			}
		}

		.goods-img {
			transform: translateX(100%);
		}

		.goods-video {
			transform: translateX(-100%);
		}

		.goods-img.show,
		.goods-video.show {
			transform: translateX(0);
		}

		.goods-img .swiper {
			width: 100%;
			height: 100%;

			.item {
				width: 100%;
				height: 100%;
			}

			image {
				width: 100%;
				height: 100%;
			}
		}

		.goods-img .img-indicator-dots {
			position: absolute;
			z-index: 5;
			bottom: 40rpx;
			right: 40rpx;
			background: rgba(100, 100, 100, 0.6);
			color: #fff;
			font-size: $ns-font-size-sm;
			line-height: 40rpx;
			border-radius: 20rpx;
			padding: 0 20rpx;
		}

		.goods-video video {
			width: 100%;
			height: 100%;
		}

		.goods-video .uni-video-cover {
			background: none;
		}

		.media-mode {
			position: absolute;
			width: 100%;
			z-index: 5;
			bottom: 40rpx;
			//#ifdef MP
			bottom: 80rpx;
			//#endif
			text-align: center;
			line-height: 50rpx;

			text {
				background: rgba(100, 100, 100, 0.6);
				color: #fff;
				font-size: $ns-font-size-sm;
				line-height: 50rpx;
				border-radius: 20rpx;
				padding: 0 30rpx;
				display: inline-block;

				&:last-child {
					margin-left: 40rpx;
				}
			}
		}
	}

	.line {
		width: 100%;
		height: 1rpx;
		background: #e5e5e5;
	}
	.group-wrap {
		margin-bottom: 20rpx;
		padding: 0 24rpx;
		box-sizing: border-box;
		background: #ffffff;
	}
	.goods-module-wrap.info {
		padding: 30rpx 0 30rpx;
	}

	.goods-module-wrap {
		background-color: #fff;
		padding: 0 20rpx;
		&.discount {
			padding-top: 20rpx;
		}

		.price-symbol {
			font-size: 36rpx;
			position: relative;
			top: 4rpx;
		}

		.price {
			font-size: 48rpx;
			position: relative;
			top: 4rpx;
			margin-right: 10rpx;
		}
		.market-price-symbol {
			position: relative;
			top: 4rpx;
			text-decoration: line-through;
			color: $ns-text-color-gray;
		}
		.market-price {
			position: relative;
			top: 4rpx;
			margin-right: 20rpx;
			color: $ns-text-color-gray;
			text-decoration: line-through;
		}
		.sku-name {
			font-weight: bold;
		}
		.sku-name,
		.introduction {
			overflow: hidden;
			text-overflow: ellipsis;
			display: -webkit-box;
			-webkit-line-clamp: 2;
			-webkit-box-orient: vertical;
		}
		.introduction {
			margin-bottom: 20rpx;
		}

		.adds-wrap {
			display: flex;
			text {
				flex: 1;
				font-size: $ns-font-size-sm;
				color: #999;
				text-align: center;

				&:first-of-type {
					text-align: left;
				}

				&:last-of-type {
					text-align: right;
				}
			}
		}
	}

	.goods-cell {
		display: flex;
		padding: 20rpx 0;
		align-items: center;
		background: #fff;
		line-height: 40rpx;
		justify-content: space-between;

		.tit {
			color: #999;
			font-size: $ns-font-size-sm;
			margin-right: 10rpx;
		}

		.box {
			width: 90%;
			font-size: $ns-font-size-sm;
			line-height: inherit;
			overflow: hidden;
			text-overflow: ellipsis;
			white-space: nowrap;
		}

		.iconfont {
			font-size: $ns-font-size-lg;
		}
	}

	.goods-cell.service {
		.box {
			display: -webkit-box;
			-webkit-line-clamp: 1;
			-webkit-box-orient: vertical;
			-webkit-box-pack: center;
			overflow: hidden;
			word-break: break-all;
			text {
				&::after {
					content: ' · ';
					display: inline-block;
				}
				&:last-child::after {
					content: '';
				}
			}
		}
	}

	.shop-wrap {
		padding: 20rpx;
		background: #fff;

		.box {
			display: flex;
		}

		.shop-logo {
			width: 120rpx;
			height: 120rpx;
			border-radius: 10rpx;
			border: 1rpx solid $ns-border-color-gray;

			image {
				width: 100%;
				height: 100%;
			}
		}

		.shop-info {
			padding-left: 20rpx;
			flex: 1;
			.description {
				color: $ns-text-color-gray;
			}
		}

		.shop-score {
			margin-top: 20rpx;
			text {
				flex: 1;
				text-align: center;
				color: #999;
			}
		}

		.goods-action {
			margin-top: 20rpx;
			text-align: center;
			width: 100%;

			navigator {
				display: inline-block;
				line-height: 40rpx;
				padding: 2rpx 40rpx;
				border: 1px solid #ffffff;
				border-radius: 40rpx;

				&:last-of-type {
					margin-left: 30rpx;
				}
			}
		}
	}

	.goods-evaluate {
		padding: 20rpx 0;
		background: #fff;

		.tit {
			padding-bottom: 20rpx;
			display: flex;
			align-items: center;
			border-bottom: 1rpx solid #f1f1f1;
			font-size: $ns-font-size-sm;
			font-weight: 500;

			view {
				flex: 1;
				line-height: 40rpx;
				text-align: left;
			}
			navigator {
				text-align: right;

				.iconfont {
					font-size: $ns-font-size-sm;
				}
			}
		}

		.evaluate-item {
			padding: 30rpx 0;
			.evaluator {
				display: flex;
				align-items: center;
				justify-content: space-between;
				.evaluator-info {
					display: flex;
					align-items: center;
				}
				.evaluator-face {
					width: 64rpx;
					height: 64rpx;
					border-radius: 50%;
					overflow: hidden;

					image {
						width: 100%;
						height: 100%;
						border-radius: 50%;
					}
				}

				.evaluator-name {
					margin-left: 20rpx;
					color: #000;
					font-size: $ns-font-size-sm;
					max-width: 50%;
				}
				.creatTime {
					font-size: 22rpx;
				}
			}

			.cont {
				text-align: justify;
				display: -webkit-box;
				-webkit-line-clamp: 2;
				-webkit-box-orient: vertical;
				-webkit-box-pack: center;
				overflow: hidden;
				word-break: break-all;
				font-size: $ns-font-size-sm;
				color: #000000;
			}

			.evaluate-img {
				display: inline-flex;
				margin-top: 20rpx;

				.img-box {
					width: 100rpx;
					height: 100rpx;
					overflow: hidden;
					margin: 0 20rpx 20rpx 0;
					border: 1px solid #e5e5e5;

					image {
						width: 100%;
						height: 100%;
					}
				}
			}

			.time {
				color: #999;

				text {
					margin-right: 20rpx;
				}
			}
		}
		.evaluate-item-empty {
			width: 100%;
			height: 150rpx;
			display: flex;
			justify-content: center;
			align-items: center;
			color: $ns-text-color-gray;
		}
		.againEvaluate {
			width: 100%;
			font-size: $ns-font-size-sm;
		}
		.evaluateMore {
			width: 100%;
			display: flex;
			justify-content: center;
			align-items: center;
			margin-top: 10rpx;
			height: 58rpx;
			line-height: 58rpx;
			text {
				display: inline-block;
				padding: 3rpx 26rpx;
				border: 1rpx solid #7f7f7f;
				border-radius: 40rpx;
				font-size: $ns-font-size-sm;
			}
		}
	}

	.goods-action-button {
		flex: 1;
	}

	.line {
		height: 1px;
	}

	// 优惠券
	.goods-coupon {
		position: relative;
		.get-coupon {
			border: 1px solid;
			border-radius: 20rpx;
			display: block;
			height: 42rpx;
			width: 84rpx;
			position: absolute;
			top: 50%;
			right: 20rpx;
			text-align: center;
			transform: translateY(-50%);
			font-size: $ns-font-size-sm;
		}
	}

	// 优惠券弹出层
	.goods-coupon-popup-layer {
		background: #fff;
		height: 800rpx;
		.tax-title {
			text-align: center;
			font-size: $ns-font-size-lg;
			line-height: 120rpx;
			height: 120rpx;
			display: block;
			font-weight: bold;
			text {
				position: absolute;
				float: right;
				right: 22px;
				font-size: 40rpx;
				font-weight: 500;
			}
		}
		.coupon-body {
			position: absolute;
			left: 0;
			right: 0;
			height: 60%;
			.item {
				overflow: hidden;
				margin: 0 20rpx 20rpx;
				border-radius: 12rpx;
				display: flex;
				&:last-child {
					margin-top: 0;
				}
				.main {
					flex: 1;
					padding: 20rpx 0 20rpx 20rpx;
					.price {
						text-overflow: ellipsis;
						white-space: nowrap;
						overflow: hidden;
						.money {
							font-size: 48rpx;
							font-weight: 700;
						}
					}
					.sub {
						font-size: $ns-font-size-sm;
						overflow: hidden;
						text-overflow: ellipsis;
						display: -webkit-box;
						-webkit-line-clamp: 2;
						-webkit-box-orient: vertical;
					}
				}
				.tax-split {
					border: 1px dotted;
					position: relative;
					border-right: 0;
					&::before {
						content: '';
						position: absolute;
						width: 10px;
						height: 10px;
						background: #fff;
						border-radius: 50%;
						left: -5px;
						top: -5px;
						z-index: 1;
					}
					&::after {
						content: '';
						position: absolute;
						width: 10px;
						height: 10px;
						background: #fff;
						border-radius: 50%;
						left: -5px;
						bottom: -5px;
						z-index: 1;
					}
				}
				.tax-operator {
					flex: 0.4;
					text-align: center;
					height: 220rpx;
					line-height: 220rpx;
					font-size: $ns-font-size-base;
				}
			}
		}
		button {
			margin: 0 !important;
			height: 96rpx;
			text-align: center;
			line-height: 96rpx;
			width: 100%;
			position: absolute;
			bottom: 0;
			color: #fff;
			z-index: 1;
			border-radius: 0 !important;
		}
	}

	// 商家服务
	.goods-merchants-service-popup-layer {
		background: #fff;
		height: 660rpx;
		.tax-title {
			text-align: center;
			font-size: $ns-font-size-lg;
			line-height: 120rpx;
			height: 120rpx;
			display: block;
			font-weight: bold;
			text {
				position: absolute;
				float: right;
				right: 22px;
				font-size: 40rpx;
				font-weight: 500;
			}
		}
		scroll-view {
			position: absolute;
			left: 0;
			right: 0;
			height: 65%;
			.item {
				padding: 20rpx 40rpx;
				position: relative;
				.iconfont {
					vertical-align: top;
					display: inline-block;
					margin-right: $ns-margin;
					font-size: $ns-font-size-lg;
				}
				.info-wrap {
					display: inline-block;
					vertical-align: middle;
					width: 90%;
					.title {
						display: block;
						font-size: $ns-font-size-base;
						// font-weight: bold;
					}
					.describe {
						font-size: $ns-font-size-sm;
						color: $ns-text-color-gray;
						display: block;
						padding: 10rpx 0;
					}
				}
			}
		}
		.button-box {
			width: 100%;
			position: absolute;
			bottom: 0;
			z-index: 1;
			margin-bottom: $ns-margin;
		}
	}

	// 商品属性
	.goods-attribute-popup-layer {
		background: #fff;
		height: 660rpx;

		.title {
			font-size: $ns-font-size-lg;
			line-height: 120rpx;
			height: 120rpx;
			display: block;
			font-weight: bold;
			padding-left: $ns-padding;
		}
		.goods-attribute-body {
			position: absolute;
			left: 0;
			right: 0;
			height: 60%;
			.item {
				padding: $ns-padding;
				border-bottom: 1px solid;
				.value {
					margin-left: 20rpx;
				}
				&:last-child {
					border-bottom: 0;
				}
			}
		}

		.button-box {
			width: 100%;
			position: absolute;
			bottom: 0;
			z-index: 1;
			margin-bottom: $ns-margin;
		}
	}

	// 满减
	.manjian-popup-layer {
		background: #fff;
		height: 660rpx;

		.title {
			font-size: $ns-font-size-lg;
			line-height: 120rpx;
			height: 120rpx;
			display: block;
			font-weight: bold;
			padding-left: $ns-padding;
		}
		.manjian-body {
			position: absolute;
			left: 0;
			right: 0;
			height: 60%;
			.item {
				padding: $ns-padding;
				border-bottom: 1px solid;
				.value {
					margin-left: 20rpx;
				}
				&:last-child {
					border-bottom: 0;
				}
			}
		}

		button {
			margin: 0 !important;
			height: 96rpx;
			text-align: center;
			line-height: 96rpx;
			width: 100%;
			position: absolute;
			bottom: 0;
			color: #fff;
			z-index: 1;
			border-radius: 0 !important;
		}
	}

	// 详情
	.goods-detail-tab {
		width: 100%;
		padding-top: $ns-padding;
		background: #ffffff;
		.detail-tab {
			width: 100%;
			display: flex;
			justify-content: center;
			align-items: center;
			.tab-item {
				height: 70rpx;
				color: $ns-text-color-black;
				line-height: 70rpx;
				box-sizing: border-box;
			}
			.tab-item.active {
				position: relative;
			}
			.tab-item.active::after {
				content: '';
				display: inline-block;
				width: 100%;
				height: 4rpx;
				position: absolute;
				left: 0;
				bottom: 0;
				border-radius: 3rpx;
			}
			.tab-item:nth-child(1) {
				margin-right: 25%;
			}
		}
		.detail-content {
			width: 100%;
		}
		.goods-details {
			padding: $ns-padding;
			margin-bottom: 100rpx;
			overflow: hidden;
			background: #ffffff;
		}
		.goods-details.active {
			min-height: 150rpx;
			width: 100%;
			display: flex;
			justify-content: center;
			align-items: center;
			color: $ns-text-color-gray;
		}
	}

	// 海报
	// .uni-popup__wrapper-box
	.poster-layer {
		.generate-poster {
			padding: 40rpx 0;
			.iconfont {
				font-size: 80rpx;
				color: #07c160;
				line-height: initial;
			}
			> view {
				text-align: center;
				&:last-child {
					margin-top: 20rpx;
				}
			}
		}
		.image-wrap {
			width: 70%;
			margin: 30px auto 20px auto;
			box-shadow: 0 0 16px rgba(100, 100, 100, 0.3);
			image {
				width: 100%;
				height: 100%;
				height: 750rpx;
			}
		}
		.msg {
			padding: 40rpx;
		}
		.save {
			text-align: center;
			height: 80rpx;
			line-height: 80rpx;
		}
		.close {
			position: absolute;
			top: 0;
			right: 20rpx;
			width: 40rpx;
			height: 80rpx;
			font-size: 50rpx;
		}
	}

	.share-popup,
	.uni-popup__wrapper-box {
		.share-title {
			line-height: 60rpx;
			font-size: $ns-font-size-lg;
			padding: 15rpx 0;
			text-align: center;
		}

		.share-content {
			display: flex;
			display: -webkit-flex;
			-webkit-flex-wrap: wrap;
			-moz-flex-wrap: wrap;
			-ms-flex-wrap: wrap;
			-o-flex-wrap: wrap;
			flex-wrap: wrap;
			padding: 15rpx;

			.share-box {
				flex: 1;
				text-align: center;

				.share-btn {
					margin: 0;
					padding: 0;
					border: none;
					line-height: 1;
					height: auto;
					text {
						margin-top: 20rpx;
						font-size: $ns-font-size-sm;
						display: block;
						color: $ns-text-color-black;
					}
				}

				.iconfont {
					font-size: 80rpx;
					line-height: initial;
				}
				.iconpengyouquan,
				.iconhaowuquan,
				.iconiconfenxianggeihaoyou {
					color: #07c160;
				}
			}
		}

		.share-footer {
			height: 90rpx;
			line-height: 90rpx;
			border-top: 2rpx #f5f5f5 solid;
			text-align: center;
			color: #666;
		}
	}

	.selected-sku-spec {
		.box {
			text {
				margin-right: 10rpx;
				white-space: nowrap;
				overflow: hidden;
				margin-right: 10px;
				text-overflow: ellipsis;
			}
		}
	}

	// 限时折扣价格
	.goods-pintuan {
		// 限时折扣价格
		position: relative;
		height: 107rpx;
		overflow: hidden;
		.price-info {
			position: relative;
			margin-right: 288rpx;
			height: 107rpx;

			.discount-price {
				padding: 12rpx 0 0 25rpx;
				height: 60rpx;
				line-height: 60rpx;
				white-space: nowrap;
				overflow: hidden;
				font-size: 52rpx;
				color: #fff;
				.symbol {
					margin-right: 4rpx;
					font-size: $ns-font-size-sm;
				}
			}
			.original-price {
				// height: 32rpx;
				// line-height: 32rpx;
				padding-left: 25rpx;
				font-size: $ns-font-size-sm;
				white-space: nowrap;
				overflow: hidden;
				color: #fff;
				line-height: 1;
				.price {
					text-decoration: line-through;
					opacity: 0.85;
				}
			}

			.groupbuy-flag {
				font-size: $ns-font-size-sm;
				margin-left: 20rpx;
			}
		}
		.price-info-box {
			width: 100%;
			height: 100%;
			display: flex;
			align-items: center;
			padding: 0 24rpx;
			box-sizing: border-box;
			.groupbuy-price {
				color: #ffffff;
				.symbol {
					font-size: $ns-font-size-sm;
				}
				.price-big {
					font-size: 36rpx;
				}
				.price-min {
					font-size: $ns-font-size-sm;
				}
			}
			.line-price-box {
				display: flex;
				justify-content: center;
				flex-direction: column;
				margin-left: 20rpx;
				.line-price-detail {
					font-size: $ns-font-size-sm;
					text-decoration: line-through;
					color: rgba(255, 255, 255, 1);
					line-height: 1;
					margin-bottom: 10rpx;
				}
				.line-price-bottom {
					display: flex;
					border-radius: 2rpx;
					border: 1rpx solid #ffffff;
					box-sizing: border-box;
					.group-price-name {
						height: 100%;
						font-size: 17rpx;
						padding: 4rpx 10rpx;
						background: #ffffff;
						line-height: 22rpx;
					}
					.buy-num {
						height: 100%;
						font-size: 17rpx;
						padding: 0 10rpx;
						line-height: 22rpx;
						color: #ffffff;
					}
				}
			}
		}
		.countdown {
			position: absolute;
			right: 10rpx;
			top: 50%;
			width: 264rpx;
			text-align: center;
			transform: translateY(-50%);
			.txt {
				height: 32rpx;
				text-align: center;
				line-height: 32rpx;
				font-size: 20rpx;
				// color: #9e495b;
			}
			.clockrun {
				margin-top: 10rpx;
				height: 40rpx;
				line-height: 40rpx;
				text-align: center;
				font-size: $ns-font-size-sm;
				color: #fff;
			}
		}
	}

	.spelling-block {
		font-size: $ns-font-size-base;
		width: 100%;
		height: 130rpx;
		background: #fff;

		.item {
			padding: 20rpx 0;
			display: flex;
			align-items: center;
			.user-logo {
				display: inline-block;
				margin: 0 30rpx 0 0;
				vertical-align: top;
				image {
					width: 85rpx;
					vertical-align: middle;
					border-radius: 50%;
					height: 85rpx;
				}
			}
			.user-name {
				width: 208rpx;
				display: inline-block;
				white-space: nowrap;
				overflow: hidden;
				text-overflow: ellipsis;
				vertical-align: top;
				// padding: 20rpx 20rpx 0 0;
				font-size: $ns-font-size-base;
			}
			.info {
				font-size: $ns-font-size-sm;
				margin-right: 10rpx;
				display: inline-block;
				text-align: right;
				flex: 1;
				.tip {
					text-align: right;
				}
			}
			button {
				// color: #fff;
				// border: 0;
				// padding: 10rpx 16rpx;
				// padding: 0 20rpx;
				// margin: 10rpx 0 0 0;
				// height: fit-content;
			}
		}
	}

	// 参与拼团
	.pintuan-popup-layer {
		.layer {
			padding: 40rpx;
			width: 422rpx;
		}
		.title {
			text-align: center;
			padding: 20rpx;
			font-size: 36rpx;
			font-weight: bold;
		}
		.info {
			font-size: $ns-font-size-sm;
			text-align: center;
		}
		.mask-layer-spelling-close {
			position: absolute;
			right: -20rpx;
			top: -20rpx;
			width: 60rpx;
			height: 60rpx;
		}
		.user-list {
			padding: 40rpx 0;
			text-align: center;
			.item {
				position: relative;
				margin-right: 20rpx;
				display: inline-block;
				.boss {
					position: absolute;
					left: -24rpx;
					top: 0rpx;
					color: #fff;
					border-radius: 20rpx;
					font-size: $ns-font-size-sm;
					padding: 0 10rpx;
					z-index: 1;
					line-height: 1;
				}
				image {
					width: 80rpx;
					height: 80rpx;
					vertical-align: middle;
					border-radius: 50%;
				}
			}
			.imgX {
				width: 100%;
				/*white-space 不能丢  */
				white-space: nowrap;
				box-sizing: border-box;
			}
		}
		button {
			color: #fff;
			border: 0;
			margin: 16rpx auto;
			display: block;
			width: 90%;
		}
	}

	.newdetail {
		width: 100%;
		background: yellow;
		padding: 0 24rpx;
		background: #ffffff;
		box-sizing: border-box;
		.coupon {
			width: 100%;
			height: 90rpx;
			display: flex;
			justify-content: space-between;
			align-items: center;
			border-bottom: 1rpx solid #f1f1f1;
			.coupon-info {
				display: flex;
				align-items: center;
			}
			.coupon-image {
				width: 70rpx;
				height: 100%;
				.iconfont {
					font-size: 36rpx;
				}
			}
			.coupon-content {
				font-size: $ns-font-size-sm;
			}
			.coupon-more {
				color: #737373;
				padding: 10rpx 15rpx;
				font-size: $ns-font-size-sm;
				border: 1rpx solid #dfdfdf;
				border-radius: 40rpx;
				line-height: 1;
			}
		}
		.free {
			width: 100%;
			height: 90rpx;
			display: flex;
			justify-content: space-between;
			align-items: center;
			border-bottom: 1rpx solid #f1f1f1;
			.free-info {
				display: flex;
				align-items: center;
			}
			.free-image {
				width: 70rpx;
				height: 100%;
				text {
					// padding: 5rpx;
					font-size: 20rpx;
					border: 1rpx solid #dfdfdf;
					border-radius: 2rpx;
					line-height: 1;
				}
			}
			.free-content {
				font-size: $ns-font-size-sm;
			}
			.free-more {
				color: #737373;
				padding: 6rpx 10rpx;
				font-size: $ns-font-size-sm;
				border: 1rpx solid #dfdfdf;
				border-radius: 40rpx;
				line-height: 1;
			}
		}
		.service {
			width: 100%;
			height: 90rpx;
			display: flex;
			align-items: center;
			justify-content: space-between;
			border-bottom: 1rpx solid #f1f1f1;
			.service-info {
				display: -webkit-box;
				-webkit-box-orient: vertical;
				-webkit-line-clamp: 1;
				overflow: hidden;
				.tit {
					color: #999;
					font-size: $ns-font-size-sm;
					margin-right: 20rpx;
				}
				.service-item {
					font-size: $ns-font-size-sm;
					color: #7f7f7f;
					margin-right: 50rpx;
					.icondui {
						font-size: $ns-font-size-base;
						margin-right: 6rpx;
						line-height: 30rpx;
					}
				}
			}
		}
		.iconright {
			color: #656565;
		}
		.newdetail-item:last-child {
			border-bottom: none;
		}
	}
}
