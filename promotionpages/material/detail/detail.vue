<template>
  <view class="content">
    <view class="material">
      <view class="material-title">{{contentDict.title}}</view>
      <view class="material-date">{{contentDict.update_time}}</view>
      <view class="material-desc">
        <view class="material-desc-text"><image class="quotes-left" :src="$util.img('public/static/youpin/quotes-left.png')"></image>{{contentDict.content}}<image class="quotes-right" :src="$util.img('public/static/youpin/quotes-right.png')"></image></view>
        <text class="material-desc-op" @click="to_copy_text">复制文案</text>
      </view>
      <view class="material-content">
        <view class="material-content-imgs-list" v-if="type==0">
          <image :src="item.path" @error="imageError(index)" mode='aspectFit' v-for="(item,index) in contentDict.resources" :key="index"></image>
        </view>
        <view class="material-content-video" v-if="type==1">
          <video id="myVideo" :src="contentDict.resources[0].path" :poster="contentDict.resources[0].cover" controls></video>
        </view>
        <view class="material-content-op">
			<!-- #ifdef MP-WEIXIN -->
			<text class="material-content-op-download" @click="saveFiles(type)">下载图片素材</text>
			<!-- #endif -->
        </view>
      </view>
    </view>
    <loading-cover ref="loadingCover"></loading-cover>
  </view>
</template>

<script>
import apiurls from "../../../common/js/apiurls";
import system from "../../../common/js/system";
import {scenePare} from "../../../common/js/scene_handle";

export default {
  data(){
    return{
      material_id:null,
      type:0, //0图片  1视频
      contentDict:{},
    }
  },
  async onLoad(options){
    // 小程序扫码进入
    if (options.scene) {
      scenePare(false,options);
    }
    this.material_id=options.material_id;
  },
  async onShow(){
    // 刷新多语言
    this.$langConfig.refresh();
    await system.wait_staticLogin_success();
    await this.getData();
    // #ifdef H5
    let share_data = this.$util.deepClone(this.getSharePageParams());
    let link = window.location.origin + this.$router.options.base + share_data.link.slice(1);
    share_data.link = link;
    await this.$util.publicShare(share_data);
    // #endif
  },
  methods:{
    async getData(){
      let res = await this.$api.sendRequest({
        url: apiurls.materialDetailUrl,
        async: false,
        data: {
          material_id:this.material_id
        },
      });
      if (this.$refs.loadingCover) this.$refs.loadingCover.hide();
      if (res.code != 0) {
        uni.showToast({
          title: res.message,
          mask: true,
          icon: "none",
          duration: 3000
        });
        return
      }
      this.contentDict=res.data;
      if(this.contentDict.type=='friend'){
        if(this.contentDict.resources_type=='image'){
          this.type=0
        }
        if(this.contentDict.resources_type=='video'){
          this.type=1
        }
      }
    },
    imageError(index) {
      this.contentDict.resources[index].path = this.$util.getDefaultImage().default_goods_img;
      this.$forceUpdate();
    },
    to_copy_text(){
      this.$util.copy(this.contentDict.content);
    },
    saveFiles(type){
      this.$util.downloadFilesSavePhotosAlbum(this.contentDict.resources.map(item=>item.path),type);
    },
    /**
     *分享参数组装
     */
    getSharePageParams() {
      let share_data=this.$util.unifySharePageParams('/promotionpages/material/detail/detail','先迈商城',
          this.contentDict.title,{material_id:this.material_id},this.contentDict.resources_type=='image' ? this.contentDict.resources[0].path : this.contentDict.resources[0].cover)
      return share_data;
    }
  },
  onShareAppMessage(res) {
    let share_data = this.getSharePageParams();
    return this.$buriedPoint.pageShare(share_data.link, share_data.imageUrl,share_data.desc);
  },
}
</script>

<style lang="scss">
page{
  background-color: white;
}
</style>

<style scoped lang="scss">
.material{
  padding: 24rpx 24rpx;
  padding-bottom: 50rpx;
  box-sizing: border-box;
  &-title{
    font-size: 30rpx;
    font-weight: bold;
    line-height: 40rpx;
    color: #333333;
  }
  &-date{
    font-size: 24rpx;
    font-weight: 400;
    line-height: 42rpx;
    color: #999;
    margin: 0;
    margin-top: 20rpx;
  }
  &-desc{
    margin-top: 20rpx;
    border: 2rpx solid #aaa;
    padding: 24rpx;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
    align-items: center;
    border-radius: 20rpx;
    &-text{
      position: relative;
      font-size: 30rpx;
      font-weight: 400;
      line-height: 40rpx;
      color: #333333;
      text-indent: 2em;
      .quotes-left{
        width: 18rpx;
        height: 18rpx;
        position: absolute;
        left: 0;
        top: 0;
      }
      .quotes-right{
        width: 18rpx;
        height: 18rpx;
        position: absolute;
        right: 0;
        bottom: 0rpx;
      }
      //&:before{
      //  content: '"';
      //  position: absolute;
      //  left: -14rpx;
      //  top: -16rpx;
      //}
      //&:after{
      //  content: '"';
      //  position: absolute;
      //  right: 0rpx;
      //  bottom: -10rpx;
      //}
    }
    &-op{
      font-size: 24rpx;
      font-weight: 400;
      color: #fff;
      padding: 4rpx 32rpx;
      box-sizing: border-box;
      background: #f33;
      border-radius: 24rpx;
      display: inline-block;
      margin-top: 20rpx;
      line-height: 40rpx;
    }
  }
  &-content{
    margin-top: 20rpx;
    border: 2rpx solid #aaa;
    padding: 24rpx;
    box-sizing: border-box;
    border-radius: 20rpx;
    &-imgs-list{
      margin-left: -4rpx;
      display: flex;
      flex-wrap: wrap;
      image{
        width: 200rpx;
        height: 200rpx;
        display: block;
        margin-left: 14rpx;
        margin-top: 14rpx;
      }
    }
    &-video{
      display: flex;
      justify-content: center;
      video{
        width: 600rpx;
        height: 500rpx;
      }
    }
    &-op{
      margin-top: 20rpx;
      text-align: center;
      &-download{
        font-size: 24rpx;
        font-weight: 400;
        color: #fff;
        padding: 4rpx 32rpx;
        box-sizing: border-box;
        background: #f33;
        border-radius: 24rpx;
        display: inline-block;
        line-height: 40rpx;
      }
    }
  }
}
</style>
