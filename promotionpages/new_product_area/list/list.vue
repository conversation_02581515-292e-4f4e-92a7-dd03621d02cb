<template>
  <view class="main" :style="[themeColorVar]">
    <!-- #ifdef H5 -->
    <uni-nav-bar left-icon="back" :border="false" :fixed="true" @clickLeft="appGoBack" v-if="isOnXianMaiApp">
      <template>
        <view class="page-title">{{topic_name}}</view>
      </template>
    </uni-nav-bar>
    <!-- #endif -->
    <mescroll-uni ref="mescroll" @getData="getData" @scroll="scrollTouch" :size="10" :top="headerTop">
      <block slot="list">
        <view class="container">
          <view class="goods_list" v-if="dataList.length">
            <view class="goods_item" v-for="(row,index) in dataList" :key="row.goods_id" @click="toProductDetail(row)">
              <view class="thumbImage">
                <image :src="$util.img(row.goods_image)" @error="imageError(index,dataList)" mode="aspectFill" class="expose_goods_index" :data-expose_goods_sku="row.sku_id"></image>
                <image v-if="row.goods_stock==0" :src="$util.img('public/static/youpin/product-sell-out.png')" class="over"></image>
              </view>
              <view class="goods_info">
                <view class="goods_name">
                  <text class="tag-cross-border">跨境<image src="/static/imgs/flag.png" mode="aspectFit" class="tag-cross-border--flag"></image></text>
                  {{row.goods_name}}
                </view>
                <view class="bottom">
                  <view class="distributor" v-if="is_shopper"><text>分销商价</text> <text>￥{{row.vip_price}}</text></view>
                  <view class="sale_price"><text>￥</text>{{row.sale_price}}</view>
                  <view class="market_price">￥{{row.market_price}}</view>
                  <view class="buy_btn">立即抢购</view>
                </view>
              </view>
            </view>
          </view>
          <view v-if="!loading && !dataList.length">
            <ns-empty :fixed="false"></ns-empty>
          </view>
        </view>
      </block>
    </mescroll-uni>

    <loading-cover ref="loadingCover"></loading-cover>
  </view>
</template>

<script>
import system from "@/common/js/system.js";
import wx_expose_goods from '@/common/mixins/wx_expose_goods.js'
import golbalConfig from "../../../common/mixins/golbalConfig";
// #ifdef H5
import {isOnXianMaiApp} from "@/common/js/h5/appOP";
// #endif
import appInlineH5 from "../../../common/mixins/appInlineH5";
import uniNavBar from '@/components/uni-nav-bar/uni-nav-bar.vue';
export default {
  data() {
    return {
      topic_name: '新品专区',
      dataList:[],
      headerTop:0,
      is_shopper:0,  //是否是店主
      loading:true,
      // #ifdef H5
      isOnXianMaiApp:isOnXianMaiApp,
      // #endif
    };
  },
  components:{
    uniNavBar
  },
  mixins: [wx_expose_goods,golbalConfig,appInlineH5],
  async onLoad() {
    // #ifdef H5
    if(isOnXianMaiApp){
      this.headerTop = 88;
    }
    // #endif
    await system.wait_staticLogin_success();
  },
  async onShow(){
    this.$langConfig.refresh();
    uni.setNavigationBarTitle({
      title: '新品专区列表'
    })
    if (uni.getStorageSync('is_shopper')) {
      this.is_shopper = uni.getStorageSync('is_shopper');
    }
  },
  methods:{
    getSharePageParams() {
      let share_data=this.$util.unifySharePageParams('/promotionpages/new_product_area/list/list',this.topic_name ? this.topic_name:'活动已结束',
          '',{},this.$util.img('public/static/youpin/task_share.jpg'))
      return share_data;
    },
    async h5Share(info) {
      // #ifdef H5
      let share_data = this.$util.deepClone(this.getSharePageParams())
      let link = window.location.origin+this.$router.options.base+share_data.link.slice(1)
      share_data.link=link
      share_data.desc= share_data.title
      share_data.title='先迈商城'
      await this.$util.publicShare(share_data);
      // #endif
    },
    getData(mescroll){
      this.mescroll = mescroll;
      if(mescroll.num == 1) this.dataList = [];
      this.loading = true;
      this.$api.sendRequest({
        url: this.$apiUrl.newProductAreaGoodsList,
        data: {
          page_size: mescroll.size,
          page: mescroll.num,
          topic_id: 93,
        },
        success:(res)=>{
          this.loading = false
          if (this.$refs.loadingCover) this.$refs.loadingCover.hide();
          this.h5Share(res)
          if(res.code!=0){
            this.$util.showToast({
              title: res.message
            })
            this.loading = false
            mescroll.endErr();
            return
          }
          mescroll.endSuccess(res.data.list.length);
          res.data.list = res.data.list.map(item=>{
            item.goods_image = this.$util.imageCdnResize(item.goods_image);
            return item;
          })
          this.dataList = this.dataList.concat(res.data.list);
        },
        fail:(err)=>{
          this.loading = false
          mescroll.endErr();
          this.$util.showToast({
            title: err.message
          })
        }
      })
    },
    toProductDetail(item){
      this.$util.toProductDetail(item,(wap_url)=>{
        this.$buriedPoint.diyReportTopicPageInteractionEvent({diy_action_type:'click_goods',diy_template_name:'template_1',diy_product_name:item.goods_name,diy_activity_title:this.topic_name,diy_product_link:wap_url})
      })
    },
    imageError(index,dataList) {
      if(dataList && dataList[index]){
        dataList[index].goods_image = this.$util.getDefaultImage().default_goods_img;
        this.$forceUpdate();
      }
    },
  },
  // 分享
  onShareAppMessage(res) {
    let { title, link, imageUrl, query } = this.getSharePageParams()
    return this.$buriedPoint.pageShare(link, imageUrl, title);
  },
  // 分享到微信
  onShareTimeline(res){
    let { title, imageUrl, query } = this.getSharePageParams()
    return {
      title,
      imageUrl,
      query,
      success: res => {},
      fail: res => {}
    };
  }
}
</script>

<style lang="scss">
.main{
  //min-height: 100vh;
}
/deep/.mescroll-upwarp{
  padding: 0 !important;
  margin-bottom: 0;
  min-height: 0;
  line-height: 0;
}
.container{
  width: 100%;
  //height: calc(100vh - 0rpx);
  //min-height: 100vh;
  background: transparent;
  border-radius: 20rpx 20rpx 0px 0px;
  overflow: hidden;
  padding-bottom: 20rpx;
  padding-top: 20rpx;
  box-sizing: border-box;
  z-index: 1;
  .nav_tab{
    width: 100%;
    height: 100rpx;
    line-height: 100rpx;
    display: flex;
    align-items: center;
    .tab_item{
      width: 33%;
      text-align: center;
      color: #999;
      font-size: 28rpx;
      &.active{
        color:var(--custom-brand-color);
      }
    }
  }
  .goods_list{
    .goods_item{
      width:690rpx;
      box-sizing: border-box;
      background: #fff;
      margin:0 auto;
      margin-bottom: 20rpx;
      padding: 20rpx 24rpx;
      display: flex;
      align-items: center;
      justify-content: space-between;
      border-radius: 20rpx;
      &:last-child{
        margin-bottom: 100rpx;
      }
      .thumbImage{
        position: relative;
        width: 240rpx;
        height: 240rpx;
        border-radius: 8rpx;
        margin-right: 26rpx;
        overflow: hidden;
        image{
          width: 100%;
          height: 100%;
        }
        .over{
          width: 120rpx;
          height: 120rpx;
          position: absolute;
          left: 50%;
          top: 50%;
          transform: translate(-50%,-50%);
        }
      }

      .goods_info{
        width: calc(100% - 270rpx);
        height: 240rpx;
        position: relative;
        .goods_name{
          font-size: 26rpx;
          color: #333;
          display: -webkit-box;
          -webkit-box-orient: vertical;
          -webkit-line-clamp: 2;
          overflow: hidden;
          .tag{
            padding: 5rpx;
            background: linear-gradient(270deg, var(--custom-brand-color-70) 0%, var(--custom-brand-color) 100%);
            border-radius: 8rpx;
            font-size: 20rpx;
            color: #fff;
            text-align: center;
            line-height: 24rpx;
            margin-right: 10rpx;
          }
        }
        .bottom{
          position: absolute;
          bottom: 0;
          width: 100%;
          line-height: 1;
          .distributor{
            text:first-child{
              //width: 84px;
              height: 40rpx;
              background: linear-gradient(90deg, var(--custom-brand-color-70) 0%, var(--custom-brand-color) 100%);
              border-radius: 4rpx 0px 0px 4rpx;
              padding: 0 9rpx 0 4rpx;
              box-sizing: border-box;
              font-size: 24rpx;
              font-weight: 500;
              color: #FFFFFF;
              display: inline-block;
            }
            text:last-child{
              height: 40rpx;
              background: var(--custom-brand-color-10);
              border-radius: 0rpx 4rpx 4rpx 0rpx;
              padding: 0 8rpx;
              box-sizing: border-box;
              font-size: 24rpx;
              font-weight: bold;
              color: var(--custom-brand-color);
              display: inline-block;
            }
          }
          .sale_price{
            font-size:36rpx;
            color: var(--custom-brand-color);
            font-weight: bold;
            line-height: 1;
            text{
              font-size: 26rpx;
              font-weight: normal;
            }
          }
          .market_price{
            line-height: 1;
            text-decoration: line-through;
            color: #999;
            font-size: 24rpx;
          }
          .buy_btn{
            position: absolute;
            right: 0;
            bottom: 0;
            background: linear-gradient(-90deg, var(--custom-brand-color-70) 0%, var(--custom-brand-color) 100%);
            height: 56rpx;
            line-height: 56rpx;
            border-radius: 28rpx;
            width: 148rpx;
            text-align: center;
            font-size: 24rpx;
            color: #fff;
          }
        }
      }
    }
  }
}
</style>
