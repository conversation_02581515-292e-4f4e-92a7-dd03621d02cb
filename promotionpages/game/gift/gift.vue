<template>
<view class="gift">
	<mescroll-uni ref="mescroll" @getData="getListData" top="20rpx">
		<block slot="list">
			<view class="list" v-if="dataList.length>0">
				<view class="item" v-for="(item,index) in dataList" :key="index">
					<!-- type:2-优惠券，3-现金红包，4-商品 -->
					<view class="left" v-if="item.type==2">
						<view class="price" v-if="item.coupon_type!='discount'">￥<text>{{item.amount}}</text></view>
						<view class="price" v-if="item.coupon_type=='discount'"><text>{{item.discount}}</text>折</view>
						<view class="type" v-if="item.goods_type==1">通用券</view>
						<view class="type" v-else-if="item.type=='reward' && item.at_least == 0">无门槛优惠券</view>
						<view class="type" v-else-if="item.type=='reward' && item.at_least > 0">满{{ item.at_least }}元可用</view>
						<view class="type" v-else-if="item.goods_type=='discount'">折扣券</view>
						<view class="type" v-else-if="item.goods_type=='deduction'">抵扣券</view>
						<view class="type" v-else-if="item.goods_type=='fixed'">固定券</view>
						<view class="type" v-else-if="item.goods_type=='random'">随机券</view>
					</view>
					<view class="left" v-if="item.type==3">
						<image :src="$util.img('/public/static/youpin/gift-redpack-bg.png')" mode="widthFix"></image>
						<view class="red-price">￥<text>{{item.amount}}</text></view>
					</view>
					<view class="left" v-if="item.type==4">
						<image :src="$util.img(item.img_url)" mode="widthFix"></image>
					</view>
					<view class="right">
						<view class="name">{{item.prize_name || '--'}}</view>
						<view class="summary"  v-if="item.type==2 && !(item.state==2 || item.state==3)" >
							<text v-if="item.last_days != 0 ">还剩{{item.last_days}}天到期</text>
							<text v-else>今天到期</text>
						</view>
						<view class="time">{{item.instime}}</view>
						<!-- <template v-if="item.type==2">
							<view class="navigator disabled" v-if="item.state==2 || item.state==3">
								{{item.state==2?'已使用':item.state==3?'已过期':''}}
							</view>
							<view class="navigator" @click="redirectLink(item.type,0)" v-else>立即使用</view>
						</template>
						<template v-if="item.type==4">
							<view class="navigator disabled" v-if="item.state==2 || item.state==3">
								{{item.state==2?'已使用':item.state==3?'已过期':''}}
							</view>
							<view class="navigator" @click="redirectLink(item.type,item.sku_id)" v-else>去使用</view>
						</template> -->


						<template v-if="item.type==2 || item.type==4">
							<view class="navigator disabled" v-if="item.state==2 || item.state==3">
								{{item.state==2?'已使用':item.state==3?'已过期':''}}
							</view>
							<template v-else>
								<view class="navigator" @click="redirectLink(item.type,0)" v-if="item.type==2">立即使用</view>
								<view class="navigator" @click="redirectLink(item.type,item.sku_id)" v-if="item.type==4" >去使用</view>
							</template>
						</template>

					</view>
				</view>
			</view>
			<view v-else>
				<ns-empty text="抱歉，还没抽中礼物！" entrance='collection' :isIndex='false'></ns-empty>
			</view>
		</block>
	</mescroll-uni>
	<loading-cover ref="loadingCover"></loading-cover>
</view>
</template>

<script>
import apiurls from "../../../common/js/apiurls";
export default{
	name:'gift',
	data(){
		return{
			dataList:[],
			activity_id:0
		}
	},
	onLoad(options) {
	},
	onShow() {
		this.$langConfig.refresh();
	},
	methods:{
		getListData(mescroll){
			this.$api.sendRequest({
				url:this.$apiUrl.getMemberAwardPage,
				data:{
					page:mescroll.num,
					page_size:mescroll.size
				},
				success:(res)=>{
					if (this.$refs.loadingCover) this.$refs.loadingCover.hide();
					if(res.code!=0){
						this.$util.showToast({
							title: err.message
						});
						return
					}
					let data = res.data || []
					mescroll.endSuccess(data.list.length);
					if (mescroll.num == 1) this.dataList = [];
					this.dataList = this.dataList.concat(data.list);
					
				},
				fail:(err)=>{
					this.$util.showToast({
						title: err.message
					});
					mescroll.endErr();
					if (this.$refs.loadingCover) this.$refs.loadingCover.hide();
				}
			})
			if (this.$refs.loadingCover) this.$refs.loadingCover.hide();
			 mescroll.endSuccess(0);
		},
		redirectLink(type,id){
			if(type==2){
				this.$util.redirectTo('/pages/index/index/index', {}, '');
			}else if(type ==4){
				this.$util.redirectTo('/pages/goods/detail/detail', {sku_id:id}, '');
			}
			
		}
	}
}
</script>

<style lang="scss" scoped>
.gift{
	min-height: 100vh;
	background: #f5f5f5;
}
.item{
	width: 702rpx;
	padding: 30rpx 30rpx 20rpx 30rpx;
	background: #fff;
	border-radius: 20rpx;
	box-sizing: border-box;
	margin: 0 auto;
	margin-bottom: 20rpx;
	display: flex;
	align-items: center;
	justify-content: space-between;
	.left{
		width: 180rpx;
		height: 180rpx;
		text-align: center;
		position: relative;
		image{
			width: 180rpx;
			height: 180rpx;
		}
		.red-price{
			position: absolute;
			top: 24rpx;
			width: 100%;
			text-align: center;
			font-size:22rpx;
			color: $base-color;
			line-height: 1;
			text{
				font-size: 35rpx;
				font-weight: bold;
			}
		}
		.price{
			font-size:30rpx;
			color:$base-color;
			line-height: 1;
			margin-top: 40rpx;
			text{
				font-size: 44rpx;
				font-weight: bold;
			}
		}
		.type{
			font-size: 26rpx;
			color: $base-color;
		}
	}
	.right{
		width: calc(100% - 200rpx);
		position: relative;
		height: 180rpx;
		line-height: 1;
		.name{
			font-size: 28rpx;
			color: $ns-text-color-black;
			display: -webkit-box;
			-webkit-box-orient: vertical;
			-webkit-line-clamp: 2;
			overflow: hidden;
		}
		.summary{
			font-size:$ns-font-size-sm;
			color:#FF5706
		}
		.time{
			font-size: $ns-font-size-sm;
			color: #999;
			position: absolute;
			left: 0;
			bottom: 0;
		}
		.navigator{
			width: 136rpx;
			height: 60rpx;
			line-height: 60rpx;
			text-align: center;
			border-radius: 30rpx;
			background: $base-color;
			color: #fff;
			font-size: $ns-font-size-xm;
			position: absolute;
			bottom: 30rpx;
			right: 0;
		}

		.disabled{
			background-color:$ns-btn-color-gray;
		}
	}
}
</style>
