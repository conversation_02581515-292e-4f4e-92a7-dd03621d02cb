<template>
  <view class="turntable">
    <image class="turntable-bg" :src="$util.img('public/static/youpin/turntable/turntable_bg.png')" mode="widthFix"></image>
		<view class="turntable-title">
      <image class="turntable-title-img" :src="$util.img('public/static/youpin/turntable/turntable_header_tit.png')"></image>
    </view>

    <template v-if="prizeList && prizeList.length != 0">
      <!-- 轮播中奖名单 -->
      <view class="winning-name" v-if="noticeMsg">
        <an-notice-bar :text="noticeMsg" color="#5F2637" bgColor="rgba(255, 255, 255, 0.5)" :showNoticeIcon="false" :showMore="false"></an-notice-bar>
      </view>

      <!-- 大转盘 -->
      <view class="turntable-wheel">
        <almost-lottery
          ref="almostLottery"
          :prize-list="prizeList"
          :ring-count="10"
          :duration="4"
          fontColor="#6D202D"
          str-key="prize_name"
          :canvas-width="canvasData.width"
          :canvas-height="canvasData.height"
          :prize-index="prizeIndex"
          @reset-index="prizeIndex = -1"
          @draw-start="handleDrawStart"
          @draw-end="handleDrawEnd"
          @finish="handleDrawFinish"
          v-if="prizeList.length"
        />
        <text class="almost-lottery__tip" v-else>奖品准备中...</text>
      </view>

      <!-- 中奖历史模块 -->
      <view class="winning-history" @click="$refs.winningPopup.open()">中奖历史</view>

      <!-- 活动规则模块 -->
      <view class="activity-rule">
        <view class="activity-rule-bg"></view>
        <view class="activity-main">
          <view class="activity-rule-tit">
            <text>活动规则</text>
          </view>

          <view class="activity-main-li">
            <view class="tit">活动时间</view>
            <view class="activity-main-content"> {{activityInfo.start_time ? activityInfo.start_time : '-'}} 至 {{activityInfo.end_time ? activityInfo.end_time : '-'}}</view>
          </view>
          <view class="activity-main-li">
            <view class="tit">活动内容</view>
            <view class="activity-main-content">{{activityInfo.crowd ? activityInfo.crowd :'-'}}</view>
          </view>
          <view class="activity-main-li">
            <view class="tit">活动对象</view>
            <view class="activity-main-content">{{activityInfo.content ? activityInfo.content :'-'}}</view>
          </view>
          <view class="activity-main-li">
            <view class="tit">温馨提示</view>
            <view class="activity-main-content">{{activityInfo.tips ? activityInfo.tips :'-'}}</view>
          </view>
        </view>
      </view>

    </template>
    <template v-else>
      <view v-if="message && token" class="text-center hint-message">{{message}}</view>
      <view v-if="!token" class="winning-history winning-login" @click="redirectDetailsUrl">请登录进行活动抽奖</view>
    </template>


    <!-- 广告模块 -->
    <view class="adv-box" v-if="token && prizeList && prizeList.length != 0">
      <view class="q-code">
        <image :src="$util.img(shopInfo.wechat_qrcode_img)" @error="$util.getDefaultImage().default_goods_img" show-menu-by-longpress mode="aspectFill"
									 :lazy-load="true"></image>
        <text>长按保存店主二维码</text>
      </view>
      <image class="adv-img" :src="$util.img(activityInfo.banner)" @error="$util.getDefaultImage().default_goods_img"></image>
    </view>

    <!-- 中奖等弹窗 -->
		<uni-popup ref="popup" :maskClick="true">
		  <view class="popup-dialog">
        <template v-if="targetInfo && (targetInfo.type == 2 || targetInfo.type == 3 || targetInfo.type == 4 )">
          <view class="popup-dialog-header">恭喜中奖了</view>
          <view class="popup-dialog-body" >
            <!-- 优惠券 -->
            <view class="coupons" v-if="targetInfo.type == 2 ">
              <view class="coupons-main flex-start-center">
                <view class="price flex1"><text>¥</text> {{targetInfo.amount ? targetInfo.amount : '-'}}</view>
                <view class="coupons-other flex2 flex-column-start">
                  <view class="coupons-other-up">{{targetInfo.prize_name ? targetInfo.prize_name : '-'}}</view>
                  <view class="coupons-other-down text-left">有效期至：{{targetInfo.expire_time ? targetInfo.expire_time : '-'}}</view>
                </view>
              </view>
              <image :src="$util.img('public/static/youpin/coupons_bg.png')" ></image>
            </view>

            <!-- 红包 -->
            <view class="hong-bao" v-if="targetInfo.type == 3 ">
              <image :src="$util.img('public/static/youpin/gift-redpack-bg.png')"></image>
              <view class="price"><text>¥</text>{{targetInfo.amount ? targetInfo.amount:'-' }}</view>
            </view>

            <!-- 获奖商品 -->
            <view class="goods-item" v-if="targetInfo.type == 4 ">
              <view class="goods-img">
                <image :src="targetInfo.img_url" @error="$util.getDefaultImage().default_goods_img" mode="aspectFill"></image>
              </view>
              <view class="goods-content">
                <text class="title">{{targetInfo.prize_name ? targetInfo.prize_name : '-'}}</text>
                <view class="goods-time">有效期至：{{targetInfo.expire_time ? targetInfo.expire_time : '-'}}</view>
              </view>
            </view>


          </view>
          <view class="popup-dialog-footer" v-if="targetInfo.type == 4 ">
            <button class="button red" @click="goGoodsDetails(targetInfo)">去使用</button>
          </view>
        </template>

        <template v-if="targetInfo && targetInfo.type == 1 ">
          <view class="wei-xiao-box">
            <image class="wei-xiao" :src="$util.img('public/static/youpin/thanks.png')"></image>
            <view class="popup-dialog-header">谢谢参与</view>
          </view>
        </template>


			</view>
		</uni-popup>

    <!-- 中奖历史 -->
    <uni-popup ref="winningPopup" type="bottom" class="winning-popup-father">
			<view class="winning-popup popup">
				<view class="popup-header">
					<view class="text-center tit">中奖历史</view>
          <text class="iconfont iconguanbi winning-close" @click="$refs.winningPopup.close()"></text>
				</view>
				<scroll-view scroll-y="true" class="popup-body">
          <template v-if="winningList && winningList.length != 0">
            <view class="flex-space-between align-items-center popup-body-li" v-for="(item,index) in winningList" :key="index">
              <view class="popup-body-winning flex-space-between flex-column">
                <text class="name">{{item.prize_name}}</text>
                <text class="time">{{item.instime}}</text>
              </view>
              <view class="go-look" @click="goLook">去看看</view>
            </view>
          </template>
          <ns-empty v-else :isIndex="0" entrance="collection" text="抱歉，还没抽中礼物！"></ns-empty>
				</scroll-view>


			</view>
		</uni-popup>

    <!-- 授权登录弹窗 -->
    <yd-auth-popup ref="ydauth"></yd-auth-popup>
    <ns-login ref="login"></ns-login>
    <uni-coupon-pop ref="couponPop"></uni-coupon-pop>

    <!-- 加载窗口 -->
    <loading-cover ref="loadingCover"></loading-cover>


  </view>
</template>

<script>
  import AlmostLottery from '@/promotionpages/components/almost-lottery/almost-lottery'
  import AnNoticeBar from '@/promotionpages/components/an-notice-bar/an-notice-bar'
  export default {
    name: 'turntable',
    components: {
      AlmostLottery,
      AnNoticeBar
    },
    data () {
      return {
        // canvas 宽高
        canvasData: {
          width: 250,
          height: 250
        },
        // 奖品数据
        prizeList: [],
        // 中奖下标
        prizeIndex: -1,
        // 中奖数据
        targetInfo: {},
        // 中奖名单
        noticeMsg:'',
        // 中奖历史
        winningList:[],
        // 活动信息
        activityInfoData:{},
        // 店铺信息
        shopInfo:{},
        // 活动信息
        activityInfo:{},
        // 获取的活动Id
        activity_id:7,
        // 获取的用户分享id
        share_member: null,
        // token
        token: null,
        //提示时间
        message: '',
      }
    },
    onShow () {
      this.token = uni.getStorageSync('token');
      this.redirectDetailsUrl()
      if (uni.getStorageSync('is_register')) {
				this.$util.toShowCouponPopup(this)
				uni.removeStorageSync('is_register');
			}
    },
    onLoad (data) {
      // 查看是否有
      if(data && data.share_member_id){
        this.share_member = data.share_member_id
      }

      if(data && data.activity_id){
        this.activity_id = data.activity_id
      }

      this.getProcessData()
      this.getHistory()
    },
    onUnload () {
      uni.hideLoading()
    },
    methods: {
      // 获得中奖历史数据
      getHistory(){
         let params = {
          activity_id : this.activity_id
        }
        this.$api.sendRequest({
          url:this.$apiUrl.getMmemberAwardList,
          data: params,
          success:res=>{
            if(res.code >= 0){
              let data = res.data
              this.winningList = res.data
            }
          }
        })
      },
      getPrizeImage(data){
        //  异步获取大转盘图片
        return new Promise((relove,reject)=>{
          uni.downloadFile({
            url:data,
            success:(res) => {
              let path = res.tempFilePath
              relove(path)
            },
            fail:(err) => {
              console.log('下载图片有问题！')
              reject(err)
            }
          })
        })
      },
      // 获取大转盘基本信息
      getPrizeList(){
        return new Promise((relove,reject)=>{
          uni.showLoading({
            title: '奖品准备中...'
          })
          let params = {
            activity_id: this.activity_id,
            share_member: this.share_member ? this.share_member :''
          }
          this.$api.sendRequest({
            url: this.$apiUrl.getTurntableActivityInfo,
            data: params,
            success: async res=>{
              let data = res.data
              if(res.code >= 0){
                /* 活动信息 */
                if(data.activityInfo){
                  this.activityInfo = data.activityInfo
                }
                /* 店铺信息 */
                if(data.shopInfo){
                  this.shopInfo = data.shopInfo
                }
                /* 处理中奖人数轮播的 */
                if(data.awardList && data.awardList.length != 0){
                  let str = ''

                  if(data.awardList.length == 1){
                    str = '恭喜刚刚手机尾号为' + data.awardList[0].mobile_end+ '的用户中奖了' + '| 恭喜刚刚手机尾号为' + data.awardList[0].mobile_end+ '的用户中奖了'
                  }else{
                    data.awardList.forEach((item,index)=>{
                      if(index == 0){
                        str = '恭喜刚刚手机尾号为' + item.mobile_end+ '的用户中奖了'
                      }else{
                        str = str + '| 恭喜刚刚手机尾号为' + item.mobile_end+ '的用户中奖了'
                      }
                    })
                  }
                  this.noticeMsg = str
                  console.log(str)
                }
              }else{
                /* uni.showToast({
                  title: res.message
                }) */
                this.message = res.message
              }
              relove(res.data)
            },
          })
        })

      },
      // 处理数据
      async getProcessData(){
        let data = await this.getPrizeList()
        /* 抽奖数据 */
        let newActivityPrize = data.activityPrize
        if(newActivityPrize && newActivityPrize.length != 0){
          this.message = ''
          for(let i = 0; i < newActivityPrize.length; i++ ){
            if(newActivityPrize[i].img_url){
              let restImg = this.$util.img(newActivityPrize[i].img_url)
              let imgs = await this.getPrizeImage(restImg);
              newActivityPrize[i].imgSrc = imgs
            }
          }
          this.prizeList = newActivityPrize
        }
        setTimeout(()=>{
          uni.hideLoading()
          if (this.$refs.loadingCover) this.$refs.loadingCover.hide();
        },500)
      },
      // 本次抽奖开始
      handleDrawStart () {
        this.targetInfo = {}
        let list = [...this.prizeList]

        let params = {
          activity_id : this.activity_id
        }

        this.$api.sendRequest({
          url:this.$apiUrl.getTurntableLottery,
          data: params,
          success:res=>{
            let data = res.data
            if(res.code >= 0){
              let prizeId = '';
              list.forEach((item,index)=>{
                if(item.id == data.id){
                  prizeId = index
                }
              })
              this.prizeIndex = prizeId
              // 旋转结束后，可以执行拿到结果后的逻辑
              this.targetInfo = data
            }else{
              /* this.$util.showToast({
                title: res.message,
                icon: 'none',
              }) */
              uni.showModal({
                title: '温馨提示',
                content: res.message,
                showCancel: false,
                success: function (res) {
                    if (res.confirm) {
                    } else if (res.cancel) {
                    }
                }
            });
              // 解决没有抽奖次数不能重复点问题
              this.$refs.almostLottery.isRotate = false
            }
          }
        })
      },
      // 本次抽奖结束
      handleDrawEnd () {
        console.log('本次抽中奖品信息 =>')
        console.log(this.targetInfo)
        this.$refs.popup.open()
        this.getHistory()
      },
      // 抽奖转盘绘制完成
      handleDrawFinish () {
        uni.hideLoading()
        uni.showToast({
          title: '奖品准备就绪'
        })
      },
      // 前往商品详情下单
      goGoodsDetails(item){
        let sku_id=item.sku_id;
        this.$util.redirectTo(`/pages/goods/detail/detail?sku_id=${sku_id}`)
      },
      // 登录跳转路线
      redirectDetailsUrl(){
        // 登录验证
        if (!this.token) {
          let share_member_id = this.share_member;
          let path = `/promotionpages/game/turntable/turntable?activity_id=${this.activity_id}`;
          // 只要之前有传值，普通用户重新登录后还是得重新传一遍
          if (share_member_id) path += '&share_member_id=' + share_member_id;
          this.$util.toShowLoginPopup(this,null,path);
          return;
        }
      },
      // 跳转到我的礼品页面
      goLook(){
        this.$util.redirectTo('/promotionpages/game/gift/gift');
      }
    },
    /**
	 * 自定义分享内容
	 * @param {Object} res
	 */
	onShareAppMessage(res) {
		let share_member_id = uni.getStorageSync('member_id');
    var path = `/promotionpages/game/turntable/turntable?share_member_id=${share_member_id}&activity_id=${this.activity_id}`;
    let recommend_member_id=uni.getStorageSync('member_id');
    if(recommend_member_id){
      path+=`&recommend_member_id=${recommend_member_id}`;
    }
    console.log(this.$util.img(this.activityInfo.banner))
		return {
			title: `${this.activityInfo.name}`,
      path: path,
      imageUrl: this.$util.img(this.activityInfo.banner),
			success: res => {},
			fail: res => {}
		};
	},
	/**
	 * 自定义分享朋友圈-暂时只支持安卓
	 * @param {Object} res
	 */
	onShareTimeline(res) {
		let share_member_id = uni.getStorageSync('member_id');
    var querys = `share_member_id=${share_member_id}&activity_id=${this.activity_id}`;
		return {
      title: `${this.activityInfo.name}`,
      imageUrl: this.$util.img(this.activityInfo.banner),
			query: querys,
			success: res => {},
			fail: res => {}
		};
	},
  }
</script>

<style lang="scss" scoped>


	.turntable {
    width:100%;
    position:relative;
  }
  .turntable-bg{
    position:absolute;
    top:0;
    left:0;
    width:100%;
    z-index:-1;
  }
	.turntable-title {
    padding-top:28rpx;
    text-align: center;

    .turntable-title-img {
      width:702rpx;
      height:200rpx;
      margin:0 auto;
    }
  }
  /* 轮播文字 */
	.winning-name{
    width:480rpx;
    margin:0 auto;
    text-align: center;
    margin-bottom:30rpx;

    /deep/ .an-notice-box{
      padding: 0 30rpx;
      border-radius: 48rpx;
      overflow:hidden;
      height:48rpx;;
      margin:0 !important;
      box-sizing: border-box;
      box-shadow: 0 0 10rpx 0 rgba(249, 66, 96, 0.2);
      .swiper-item{
        font-size:24rpx;
      }
      .an-notice-content{
        width:100% !important;
      }
      .swiper{
        height:48rpx !important;
      }
      .an-notice-content-item{
        text-align: center;
        height:48rpx;
        line-height:48rpx;
      }
      .an-notice-content-item-text{
        line-height:48rpx;
      }
    }
	}
  .turntable-wheel {
    text-align: center;
    .almost-lottery__tip {
      font-size: 24rpx;
    }
  }
  /* 中奖历史 */
  .winning-history,.winning-login{
    width:480rpx;
    line-height:64rpx;
    border-radius: 64rpx;
    color:#ffffff;
    background-color:#FE6594;
    margin:55rpx auto 0;
    font-size:34rpx;
    text-align: center;
  }
  /* 登录抽奖按钮 */
  .winning-login{
    margin-bottom:100rpx;
  }
  /* 活动规则 */
  .activity-rule{
    margin-top:48rpx;
    padding:24rpx;
    position:relative;

    .activity-rule-bg{
      position:absolute;
      left:0;
      top:0;
      z-index:-1;
      width:100%;
      height:155rpx;
      background-color:#FE6594;
    }

    .activity-main{
      border-radius: 20rpx;
      background-color:#ffffff;
      padding:24rpx;
      box-shadow: 0 0 40rpx 0 rgba(249, 66, 96, 0.33);

    }

    .activity-rule-tit{
      color:#FE6594;
      text-align: center;
      padding-bottom:20rpx;

      text{
        position: relative;
        font-size:36rpx;
      }
      text:before{
        content: ".";
        font-size:36rpx;
        display: inline-block;
        position: absolute;
        left:-20rpx;
        top:-20rpx;
        color:#FE6594;
      }
      text:after{
        content: ".";
        font-size:36rpx;
        display: inline-block;
        position: absolute;
        right:-20rpx;
        top:-20rpx;
        color:#FE6594;
      }
    }

    .activity-main-li{
      padding-bottom:20rpx;
      .tit{
        font-size:$ns-font-size-base;
        font-weight: bold;
        padding-bottom:6rpx;
      }
      .activity-main-content{
        font-size:$ns-font-size-xm;
      }
    }

  }
  /* 底部广告模块 */
  .adv-box{
    box-shadow: 0 0 40rpx 0 rgba(249, 66, 96, 0.33);
    margin:0 24rpx 24rpx 24rpx;
    padding:24rpx;
    background-color:#fff;
    border-radius: 20rpx;
    overflow:hidden;
    position: relative;
    .q-code{
      position: relative;
      z-index:2;
      float:right;
      display:flex;
      align-items: center;
      flex-direction: column;
      image{
        width:200rpx;
        height:200rpx;
      }
      text{
        padding-top:4rpx;
        font-size:22rpx;
      }
    }

    .adv-img{
      position:absolute;
      z-index:1;
      left:0;
      top:0;
      height:100%;
      width:100%;
    }
  }
  /* 获奖弹窗 */
  .popup-dialog {
    overflow: hidden;
    background: linear-gradient(0deg, #FDD8DB 0%, #F7E4E6 100%);
    box-sizing: border-box;
    min-width:600rpx;
    .popup-dialog-header {
      height: 106rpx;
      line-height: 106rpx;
      text-align: center;
      font-size: 46rpx;
      color: #F03652;
      font-weight: bold;
    }
    .popup-dialog-body {
      color: #656565;
      text-align: center;
      padding: 0 30rpx;

      /* 优惠券 */
      .coupons{
        width:540rpx;
        height:181rpx;
        position:relative;
        margin-bottom:20rpx;
        image{
          width:100%;
          height:100%;
          position:absolute;
          z-index:1;
          top:0;
          left:0;
        }
        .coupons-main{
          width:100%;
          height:100%;
          position:absolute;
          top:0;
          left:0;
          z-index:2;

          .price{
            padding-left:10rpx;
            color:#F03652;
            font-size:36rpx;
            text{
              font-size:$ns-font-size-sm;
            }
          }

          .coupons-other{
            box-sizing: border-box;
            padding-left:20rpx;
            .coupons-other-up{
              font-size:$ns-font-size-base;
              color:#F03652;
              padding-bottom:10rpx;
            }
            .coupons-other-down{
              font-size:22rpx;
              color:$ns-text-color-gray;
              line-height:1.4;
            }
          }
        }
      }

      /* 商品模块 */
      .goods-item {
        display: flex;
        flex-direction: row;
        background-color: #fff;
        border-radius: 20rpx;
        width: 100%;
        overflow: hidden;
        box-sizing: border-box;
        padding:14rpx;

        .goods-img {
          width: 140rpx;
          height: 140rpx;
          margin-right:20rpx;
          image {
            width: 100%;
            height: 100%;
            border-radius: 20rpx;
          }
        }

        .goods-content {
          display: flex;
          flex:1;
          flex-direction: column;
          justify-content: space-between;
          text-align: left;

          .title {
            overflow: hidden;
            display: inline-block;
            font-size: $ns-font-size-base;
            color: #333333;
            line-height: 1.3;
            text-overflow: ellipsis;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            margin-bottom:5rpx;
          }

          .goods-time{
            font-size:$ns-font-size-sm;
            color:$ns-text-color-gray;
          }
        }
      }

      /* 红包模块 */
      .hong-bao{
        width:194rpx;
        height:194rpx;
        position: relative;
        margin:0 auto;
        margin-bottom:40rpx;
        image{
          position: absolute;
          left:0;
          top:0;
          width:100%;
          height:100%;
          z-index:1;
        }
        .price{
          width:100%;
          position: absolute;
          top:10rpx;
          left:0;
          z-index:2;
          text-align: center;
          color:#F03652;
          font-size:36rpx;
          text{
            font-size:$ns-font-size-sm;
          }
        }
      }

    }
    .wei-xiao-box{
      width:600rpx;
      padding:50rpx 0;
      .wei-xiao{
        width:106rpx;
        height:106rpx;
        display: block;
        margin:0 auto;
      }
    }
    .popup-dialog-footer {
      margin: 0 32rpx;
      height: 140rpx;
      display: flex;
      align-items: center;
      justify-content: space-around;
      .button {
        width: 360rpx;
        height: 68rpx;
        line-height: 68rpx;
        text-align: center;
        border-radius: 34rpx;
        box-sizing: border-box;
        background: linear-gradient(0deg, #F52572 0%, #FD6F68 100%);
        margin: 0;
        &.red {
          color: #FFFFFF;
        }
      }
    }
  }
  /* 中奖历史弹窗 */
  .winning-popup-father /deep/ .uni-popup__wrapper {
    background:transparent !important;
    .uni-popup__wrapper-box{
      border-radius: 20rpx 20rpx 0 0 !important;
    }
  }
  .winning-popup {
    border-radius: 25rpx 25rpx 0 0;

    .popup-header{
      position: relative;
      padding: 24rpx 0;
      .tit{
        text-align: center;
        font-size: 36rpx;
        font-weight: bold;
      }

      .winning-close {
        position: absolute;
        right:24rpx;
        top:24rpx;
        font-size:40rpx;
        color: #A0A1A7;
      }
    }

    .popup-body{
      height: 50vh;
      padding:0 36rpx;
      box-sizing: border-box;

      .popup-body-li{
        border-bottom:1px solid $ns-border-color-gray;
        padding:20rpx 0;
      }
      .popup-body-winning{
        flex:1;
        .name{
          overflow: hidden;
          display: inline-block;
          font-size: $ns-font-size-base;
          color: #333333;
          line-height: 1.3;
          text-overflow: ellipsis;
          display: -webkit-box;
          -webkit-line-clamp: 1;
          -webkit-box-orient: vertical;
          margin-bottom:5rpx;
        }
        .time{
          font-size:24rpx;
          color:$ns-text-color-gray;
        }
      }
      .go-look{
        width:128rpx;
        height:56rpx;
        line-height:56rpx;
        font-size:26rpx;
        color:#ffffff;
        background-color:$base-color;
        border-radius: 56rpx;
        text-align: center;
      }

      /deep/ .fixed{
        top:14vh;
      }
    }

  }

  .hint-message{
    font-size:30rpx;
    margin:50rpx;
  }


</style>
