<template>
	<view :class="themeStyle">

		<view class="container" v-bind:class="{'container-has': containerHas}">

			<mescroll-uni ref="mescroll" v-bind:top="0" @getData="getData" :size="10" :top="navHeight">
				<block slot="list">

					<view class="banner" v-if="bannerdata && !classifyPopStatus">
						<image :src="bannerdata.image_url" mode="" v-on:click="toAd(bannerdata)" mode="widthFix"></image>
					</view>

					<view class="search_product" v-if="!classifyPopStatus">
						<view class="search-box">
							<text class="iconfont iconIcon_search"></text>
							<input class="uni-input" maxlength="50" v-model="inputValue" confirm-type="search"
								@focus="inputFocus" @confirm="searchProduct()" placeholder="搜索你喜欢的商品"
								placeholder-style="color:#999999" />
						</view>
						<view class="text" @click="searchProduct()">搜索</view>
					</view>

					<view :class="{'fixed_box':classifyPopStatus}">

						<view class="nav_tab">
							<view class="tab_item" :class="type == 0 ? 'active' : ''" @click="changetab(0)">综合</view>
							<view class="tab_item" :class="type == 1 ? 'active' : ''" @click="changetab(1)">销量</view>
							<view class="tab_item" :class="type == 2 ? 'active' : ''" @click="changetab(2)">
								价格<image class="tab_img" :src="sortImg" mode=""></image>
							</view>
							<view class="tab_item" :class="type == 3 ? 'active' : ''" @click="changetab(3)">
								<text class="maxwid overtext-hidden-one">{{classifyName}}</text>
								<image class="tab_img"
									:src="type == 3 ?$util.img('public/static/youpin/select-classify.png'):$util.img('public/static/youpin/classify-normol.png')"
									mode=""></image>
							</view>

							<!-- 分类弹窗 -->
							<view class="classifyPop" v-if="classifyPopStatus">
								<scroll-view scroll-y="true" class="scroll_view_one" :scroll-with-animation="true"
									:scroll-top="(showIndex*50)">
									<view class="scroll_item overtext-hidden-one"
										:class="selectindex[0] == index ? 'select':''"
										v-for="(item,index) in classifyList" :key="index" @click="selectChange(index)">
										{{item.category_name}}
										<view v-if="selectindex[0] == index" class="selectbor"></view>
									</view>
								</scroll-view>

								<scroll-view v-if="scrollOneStatus" scroll-y="true" class="scroll_view_two">
									<view v-for="(item,index) in classifyItemList" :key="index">
										<view class="item_head" :class="index != 0?'margintops':''">
											<view class="itemsName">{{item.category_name}}</view>
											<view class="selectAll" @click="selectAllItem(index)">
												<image class="itemsImg"
													:src="item.selectTrue ?' ../../../static/imgs/maidou/get.png':'../../../static/imgs/maidou/noneget.png'"
													mode=""></image>
												<view class="itemclo">全选</view>
											</view>
										</view>
										<view class="itemListsBox" v-if="item.child_list.length > 0">
											<view class="itemLists" v-for="(items,indexs) in item.child_list"
												:key="indexs" @click="selectItems(index,indexs)">
												{{items.category_name}}
												<image v-if="items.selectTrue" class="selectImgs"
													:src="$util.img('public/static/youpin/classifySelect.png')" mode="">
												</image>
											</view>
										</view>
									</view>
								</scroll-view>

							</view>
							<view class="popMask" @touchmove.stop.prevent="moveHandle" v-if="classifyPopStatus" @click="closePop"></view>

						</view>
					</view>

					<view class="goods_list">
						<view class="goods_item" v-for="(item, key) in dataList" :key="key"
							@click="toGoodsDetail(item)">
							<view class="thumbImage">
								<image :src="$util.img(item.goods_image)" @error="imageError(key)" mode='aspectFit'>
								</image>
								<image v-if="item.goods_stock==0"
									:src="$util.img('public/static/youpin/product-sell-out.png')" class="over">
								</image>
							</view>
							<view class="goods_info">
								<view class="goods_name"><text v-for="(tag, key) in item.tags" :key="key"
										class="tag">{{tag.tag_name == '优选单品'?tag.tag_name:'优选单品'}}</text>{{item.goods_name}}
								</view>
								<view class="bottom">
									<view class="sale_price"><text>￥</text>{{item.retail_price}}</view>
									<view class="market_price">￥{{item.market_price}}</view>
									<view class="buy_btn">立即购买</view>
								</view>
							</view>
						</view>
					</view>


					<view v-if="!dataList.length && !classifyPopStatus">
						<ns-empty :fixed="false"></ns-empty>
					</view>


				</block>
			</mescroll-uni>
		</view>

	</view>
</template>

<script>
	import system from "@/common/js/system.js";
  // #ifdef H5
  import {isOnXianMaiApp} from "../../../common/js/h5/appOP";
  // #endif
	export default {
		data() {
			return {
				mescroll: null,
				dataList: [],
				sf: 'all',
				type: 0,
				bannerdata: '',
				bannerUrl: '',
				tag_key: 'excellent',
				sign: 'excellent-1',
				sortStatus: false, // true 从大到小
				inputValue: '',
				sortImg: '',
				classifyPopStatus: false,
				selectindex: [0], // 分类选择对应索引数组
				firstIndex: 0, // 记录一级分类
				classifyName: '分类',
				classifyList: [],
				classifyItemList: [], // 分类二三级列表
				scrollOneStatus: false, // 二三级显示控制
				showIndex:0,
        navHeight:0,
        // #ifdef H5
        isOnXianMaiApp:isOnXianMaiApp,
        // #endif
			}
		},
		computed: {
			themeStyle() {
				return 'theme-' + this.$store.state.themeStyle
			},
			containerHas() {
				return this.bannerdata;
			}
		},
		onLoad() {
      // #ifdef H5
      if(this.isOnXianMaiApp){
        this.navHeight=92;
      }
      // #endif
			this.getBanner()
		},
		async onShow() {
			this.sortImg = this.$util.img('public/static/youpin/sort-normol.png')
			this.$langConfig.refresh();
			this.dealWithClassify()

			await system.wait_staticLogin_success();

			// #ifdef H5
      let share_data = this.$util.deepClone(this.getSharePageParams())
      let link = window.location.origin+this.$router.options.base+share_data.link.slice(1)
      share_data.link=link
			share_data.desc=share_data.title
    	share_data.title='先迈商城'
      await this.$util.publicShare(share_data);
      // #endif
		},
		methods: {
			moveHandle(){

			},
			// 搜索商品
			searchProduct() {
				this.$refs.mescroll.refresh();
			},
			//input框获取焦点事件
			inputFocus(e) {

			},
			// 关闭
			closePop() {
				this.classifyPopStatus = false
				this.selectindex[0] = this.showIndex
				if (!this.classifyPopStatus && this.classifyName != '分类') {
					this.$refs.mescroll.refresh();
				}
			},
			// 选择第一级分类
			selectChange(index) {

				if (!this.classifyList[index].child_list || this.classifyList[index].child_list.length <= 0) {
					this.classifyPopStatus = false
					this.scrollOneStatus = false
					this.classifyName = this.classifyList[index].category_name
					this.selectindex = [index]
					this.showIndex = index
					this.$refs.mescroll.refresh();
				} else {
					this.scrollOneStatus = true

					this.selectindex[0] = index

					this.dealWithData(false)

				}

			},
			// 全选
			selectAllItem(index) {
				this.classifyPopStatus = false
				this.scrollOneStatus = false
				this.classifyName = this.classifyItemList[index].category_name

				if (this.classifyItemList.length > 0) {
					this.showIndex = this.selectindex[0]
					this.selectindex[1] = index
					this.selectindex.length == 3 && this.selectindex.pop()
				} else {
					this.selectindex.push(index)
				}
				this.$refs.mescroll.refresh();
			},
			// 三级分类选择
			selectItems(index, indexs) {
				this.classifyPopStatus = false
				this.scrollOneStatus = false

				this.classifyName = this.classifyItemList[index].child_list[indexs].category_name

				if (this.classifyItemList.length > 0) {
					this.showIndex = this.selectindex[0]
					this.selectindex[1] = index
					this.selectindex[2] = indexs
				} else {
					this.selectindex.push(index, indexs)
				}

				this.$refs.mescroll.refresh();
			},
			// 获取分类并处理分类数据
			dealWithClassify() {
				let url = "/api/goodscategory/excellentGoodsCategory"
				let shopid = uni.getStorageSync('shop_id');
				let data = {
					shop_id: shopid
				};

				if(this.classifyList.length <= 0){

					this.$api.sendRequest({
						url: url,
						data: data,
						success: res => {
							if (res.code == 0) {
								res.data = [{ category_name: '全部商品' }].concat(res.data)

								this.classifyList = res.data
								this.scrollOneStatus = true
								this.dealWithData(true)

							} else {
								this.$util.showToast({
									title: res.message
								});
							}
						},
					});

				}else {
					this.scrollOneStatus = true
					this.dealWithData(true)
				}
			},
			// 处理分类选中显示
			dealWithData(status) {
				let indexList = this.selectindex
				let list = JSON.parse(JSON.stringify(this.classifyList))
				let arr = list[indexList[0]].child_list

				status && (this.showIndex = indexList[0])

				if (arr && arr.length > 0) {

					for (let i = 0; i < arr.length; i++) {
						if (indexList.length == 2 && arr[indexList[1]] && arr[indexList[1]].category_name == this.classifyName) {
							arr[indexList[1]].selectTrue = true
						} else if (indexList.length == 3 && arr[indexList[1]] && arr[indexList[1]].child_list[indexList[2]].category_name == this.classifyName ) {
							arr[indexList[1]].child_list[indexList[2]].selectTrue = true
						}
					}

					this.classifyItemList = JSON.parse(JSON.stringify(arr))

				} else {
					this.scrollOneStatus = false
				}

			},
			// 点击修改tab
			changetab(type) {
				this.type = type

				if (type == 0) {
					this.sf = "all"
				} else if (type == 1) {
					this.sf = "sale_num"
				} else if (type == 2) {
					this.sf = "retail_price"
					this.sortStatus = !this.sortStatus
				} else {
					this.sf = "classify"
				}

				if (type != 2 && this.sortImg != this.$util.img('public/static/youpin/sort-normol.png')) {
					this.sortImg = this.$util.img('public/static/youpin/sort-normol.png')
				}

				if (type != 3) {
					this.dataList = [];
					this.classifyPopStatus = false
					this.$refs.mescroll.refresh();

				} else {
					this.classifyPopStatus = !this.classifyPopStatus
					this.classifyPopStatus && this.dealWithClassify()

				}
				this.selectindex[0] = this.showIndex

			},

			// 获取banner
			getBanner() {
				this.$api.sendRequest({
					url: this.$apiUrl.specialBannerUrl,
					data: {
						sign: this.sign
					},
					success: (res) => {
						if (res.code != 0) {
							this.$util.showToast({
								title: res.message
							})
							return
						}
						if (res.data.list.length > 0) {
							this.bannerdata = res.data.list[0];
							this.bannerUrl = res.data.list[0].banner_url;
						}

					},
					fail: (err) => {
						this.$util.showToast({
							title: err.message
						})
					}
				})
			},
			// 获取商品列表
			getData(mescroll) {
				this.mescroll = mescroll;

				if (mescroll.size == 1) {
					this.dataList = [];
				}
				let data = {
					page_size: mescroll.size,
					page: mescroll.num,
					sf: this.sf,
					tag_key: this.tag_key
				}

				if (this.classifyName != '分类' && this.classifyName != '全部商品') {
					let num = this.selectindex.length
					let arr = this.classifyList[this.showIndex]

					data.category_level = num
					if (num == 1) {
						data.category_id = arr.category_id
					} else if (num == 2) {
						data.category_id = arr.child_list[this.selectindex[1]].category_id
					} else {
						data.category_id = arr.child_list[this.selectindex[1]].child_list[this.selectindex[2]].category_id
					}
				}

				this.inputValue != '' && (data.goods_name = this.inputValue)
				this.sf == 'retail_price' && (data.st = this.sortStatus ? 'ASC' : 'DESC') // DESC 降序   ASC 升序

				this.$api.sendRequest({
					url: this.$apiUrl.goodsListUrl,
					data,
					success: res => {
						let newArr = []
						let msg = res.message;
						if (res.code == 0 && res.data) {
							if (res.data.list && res.data.list.length != 0) {
								newArr = res.data.list;
							}
						} else {
							this.$util.showToast({
								title: msg
							})
						}

						mescroll.endSuccess(newArr.length);
						//设置列表数据
						if (mescroll.num == 1) this.dataList = []; //如果是第一页需手动制空列表

						this.dataList = this.dataList.concat(newArr); //追加新数据
						this.$buriedPoint.exposeGoods( newArr , 'sku_id')

						// 修改价格高低排序
						if (this.sf == "retail_price") {
							this.sortImg = this.sortStatus ? this.$util.img('public/static/youpin/up.png') :
								this.$util.img('public/static/youpin/dowm.png')
						}

						if (this.$refs.loadingCover) this.$refs.loadingCover.hide();

					},
					fail() {
						//联网失败的回调
						mescroll.endErr();
						if (this.$refs.loadingCover) this.$refs.loadingCover.hide();
					}
				});

			},
			// 跳转商品详情
			toGoodsDetail(res) {
				this.$util.redirectTo('/pages/goods/detail/detail', {
					sku_id: res.sku_id
				});
			},
      toAd(item){
        this.$buriedPoint.diyReportAdEvent(
            {diy_ad_location_type:'list',diy_material_path:item.image_url,diy_ad_type:'image',diy_target_page:item.banner_url,diy_ad_id:item.id,diy_action_type:'click'})
        this.$util.specialBannerReportByClick(item.id)
        if (item.banner_url) {
          this.$util.diyCompateRedirectTo({
            wap_url: item.banner_url
          })
        }
      },
			imageError(index) {
				console.log(index)
				this.dataList[index].goods_image = this.$util.getDefaultImage().default_goods_img;
				this.$forceUpdate();
			},
			getSharePageParams() {
        let share_data=this.$util.unifySharePageParams('/promotionpages/preferred_list/list/list','快来领无门槛代金券！',
            '',{},this.$util.img('public/static/youpin/preferred_share.jpg'))
        return share_data;
			}
		},
		// 分享朋友
		onShareAppMessage(res) {
			let { title, link, imageUrl, query } = this.getSharePageParams()
			return this.$buriedPoint.pageShare(link , imageUrl, title);
		},
		// 分享到微信
		onShareTimeline(res){
			let { title, imageUrl, query } = this.getSharePageParams()
			return {
				title,
        imageUrl,
				query,
				success: res => {},
				fail: res => {}
			};
		}
	}
</script>

<style lang="scss" scoped>
	.banner {
		width: 100%;
		height: 100%;

		image {
			width: 100%;
			border-radius: 0 !important;
		}
	}

	.fixed_box {
		width: 100%;
		height: 100vh;
		position: fixed;
		top: 0;
		left: 0;
		z-index: 1;
	}

	.container {
		width: 100%;
		height: calc(100vh - 0rpx);
		background: linear-gradient(180deg, #FFFFFF 0%, #F5F5F5 100%);
		border-radius:  0px 0px 20rpx 20rpx;
		overflow: hidden;
		position: fixed;
		z-index: 1;
		top: 0;

		&-has {
			height: 100vh;
		}

		.nav_tab {
			width: 100%;
			height: 100rpx;
			line-height: 100rpx;
			display: flex;
			align-items: center;
			position: relative;
			background-color: white;

			.tab_item {
				width: 33%;
				text-align: center;
				display: flex;
				align-items: center;
				justify-content: center;
				color: #999;
				font-size: 28rpx;

				&.active {
					color: $base-color
				}

				.tab_img {
					width: 30rpx;
					height: 30rpx;
					margin-left: 8rpx;
				}

				.maxwid {
					max-width: 115rpx;
				}
			}
		}

		.goods_list {
			margin-top: 20rpx;
			.goods_item {
				width: 690rpx;
				box-sizing: border-box;
				background: #fff;
				margin: 0 auto;
				margin-bottom: 20rpx;
				padding: 20rpx 24rpx;
				display: flex;
				align-items: center;
				justify-content: space-between;
				border-radius: 20rpx;

				.thumbImage {
					position: relative;
					width: 240rpx;
					height: 240rpx;
					border-radius: 8rpx;
					margin-right: 26rpx;
					overflow: hidden;

					image {
						width: 100%;
						height: 100%;
					}

					.over {
						width: 120rpx;
						height: 120rpx;
						position: absolute;
						left: 50%;
						top: 50%;
						transform: translate(-50%, -50%);
					}
				}

				.goods_info {
					width: calc(100% - 270rpx);
					height: 240rpx;
					position: relative;

					.goods_name {
						font-size: 26rpx;
						color: #333;
						display: -webkit-box;
						-webkit-box-orient: vertical;
						-webkit-line-clamp: 2;
						overflow: hidden;

						.tag {
							padding: 5rpx;
							background: linear-gradient(270deg, #FE5838 0%, #FB331D 100%);
							border-radius: 8rpx;
							font-size: 20rpx;
							color: #fff;
							text-align: center;
							line-height: 24rpx;
							margin-right: 10rpx;
						}
					}

					.bottom {
						position: absolute;
						bottom: 0;
						width: 100%;
						line-height: 1;

						.sale_price {
							font-size: 36rpx;
							color: $base-color;
							font-weight: bold;
							line-height: 1;

							text {
								font-size: 26rpx;
								font-weight: normal;
							}
						}

						.market_price {
							line-height: 1;
							text-decoration: line-through;
							color: #999;
							font-size: 24rpx;
						}

						.buy_btn {
							position: absolute;
							right: 0;
							bottom: 0;
							background: linear-gradient(-90deg, #FF2127 0%, #FF5A2F 100%);
							height: 56rpx;
							line-height: 56rpx;
							border-radius: 28rpx;
							width: 148rpx;
							text-align: center;
							font-size: 24rpx;
							color: #fff;
						}
					}
				}
			}
		}
	}

	.search_product {
		width: 100%;
		// background: #ffffff;
		padding: 20rpx 0 20rpx 30rpx;
		box-sizing: border-box;
		display: flex;
		align-items: center;

		input {
			font-size: $ns-font-size-base;
			height: 60rpx;
			line-height: 60rpx;
			width: calc(100% - 120rpx);
		}

		text {
			font-size: 40rpx;
			color: $ns-text-color-gray;
			width: 64rpx;
			text-align: center;

		}

		.text {
			color: #333;
			font-size: 28rpx;
			width: 115rpx;
			text-align: center;
		}

		.search-box {
			width: 605rpx;
			height: 60rpx;
			background: #F5F5F5;
			// background: $uni-bg-color-grey;
			display: flex;
			align-items: center;
			border-radius: 40rpx;
		}
	}

	.classifyPop {
		position: absolute;
		top: 100rpx;
		// top: 480rpx;
		left: 0;
		z-index: 1;
		background-color: white;
		width: 100%;
		height: 600rpx;
		border-radius: 0rpx 0rpx 30rpx 30rpx;
		display: flex;

		.scroll_view_one {
			width: 184rpx;
			height: 600rpx;
			background-color: #EEEEEE;

			.scroll_item {
				width: 184rpx;
				height: 100rpx;
				line-height: 100rpx;
				text-align: center;
				font-size: 24rpx;
				position: relative;

				&.select {
					color: #FF0000;
					background-color: white;
				}

				.selectbor {
					position: absolute;
					left: 0;
					top: 16rpx;
					width: 6rpx;
					height: 68rpx;
					background: #FF0000;
					border-radius: 6rpx;
				}
			}

		}

		.scroll_view_two {
			width: 566rpx;
			height: 600rpx;
			background: linear-gradient(180deg, #FFFFFF 0%, #F5F5F5 100%);
			box-sizing: border-box;
			padding: 24rpx;
			padding-left: 26rpx;

			.item_head {
				width: 100%;
				height: 50rpx;
				line-height: 50rpx;
				display: flex;
				justify-content: space-between;
				align-items: center;
				font-size: 24rpx;

				&.margintops {
					margin-top: 40rpx;
				}

				.itemsName {
					color: #666666;
				}

				.selectAll {
					font-size: 24rpx;
					display: flex;
					align-items: center;

					.itemsImg {
						width: 32rpx;
						height: 32rpx;
						margin-right: 4rpx;
					}

					.itemclo {
						color: #666666;
					}
				}
			}

			.itemListsBox {
				width: 100%;
				display: flex;
				flex-wrap: wrap;

				.itemLists {
					width: 160rpx;
					height: 60rpx;
					line-height: 30rpx;
					text-align: center;
					font-size: 26rpx;
					margin: 18rpx 18rpx 0 0;
					background-color: white;
					border: 2rpx solid #EEEEEE;
					border-radius: 4rpx;
					display: flex;
					align-items: center;
					justify-content: center;
					position: relative;

					.selectImgs {
						position: absolute;
						top: 0;
						left: 0;
						width: 160rpx;
						height: 64rpx;
					}
				}
			}

		}

	}

	.popMask {
		width: 100%;
		height: 50vh;
		position: absolute;
		top: 700rpx;
		left: 0;
		background: rgba(0, 0, 0, 0.5);
		z-index: 1;
	}
</style>
