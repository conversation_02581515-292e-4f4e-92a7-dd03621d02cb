.order-container {
	width: 100vw;
	height: 100vh;
}

.order-nav {
	width: 100vw;
	height: 70rpx;
	flex-direction: row;
	white-space: nowrap;
	background: #fff;
	border-bottom-left-radius: 24rpx;
	border-bottom-right-radius: 24rpx;
	padding-bottom: 30rpx;
	position: fixed;
	left: 0;
	z-index: 998;

	.uni-tab-item {
		display: inline-block;
		flex-wrap: nowrap;
		padding-left: 24rpx;
		padding-right: 24rpx;
	}

	.uni-tab-item-title {
		color: #555;
		font-size: 30rpx;
		display: block;
		height: 64rpx;
		line-height: 64rpx;
		border-bottom: 2px solid #fff;
		padding: 0 10rpx;
		flex-wrap: nowrap;
		white-space: nowrap;
	}

	.uni-tab-item-title-active {
		display: block;
		height: 64rpx;
		border-bottom: 2px solid #ffffff;
		padding: 0 10rpx;
	}

	::-webkit-scrollbar {
		width: 0;
		height: 0;
		color: transparent;
	}
}

.order-item {
	margin: $ns-margin;
	padding: $ns-padding;
	border-radius: $ns-border-radius;
	background: #fff;
	position: relative;

	.order-header {
		display: flex;
		align-items: center;
		position: relative;

		&.waitpay {
			padding-left: 50rpx;

			.iconyuan_checked,
			.iconyuan_checkbox {
				font-size: 36rpx;
				position: absolute;
				top: 50%;
				left: 0;
				transform: translateY(-50%);
			}
			.iconyuan_checkbox {
				color: $ns-text-color-gray;
			}
		}

		.icondianpu {
			display: inline-block;
			line-height: 1;
			margin-right: 12rpx;
			font-size: 30rpx;
		}

		.status-name {
			flex: 1;
			text-align: right;
		}
	}

	.order-body {
		.goods-wrap {
			margin-bottom: 20rpx;
			display: flex;
			position: relative;

			&:last-of-type {
				margin-bottom: 0;
			}

			.goods-img {
				width: 180rpx;
				height: 180rpx;
				padding: 20rpx 0 0 0;
				margin-right: 20rpx;

				image {
					width: 100%;
					height: 100%;
					border-radius: $ns-border-radius;
				}
			}

			.goods-info {
				flex: 1;
				position: relative;
				padding: 20rpx 0 0 0;
				max-width: calc(100% - 200rpx);

				.goods-name {
					display: -webkit-box;
					-webkit-box-orient: vertical;
					-webkit-line-clamp: 2;
					overflow: hidden;
					line-height: 1.5;
					font-size: 28rpx;
				}

				.goods-sub-section {
					width: 100%;
					line-height: 1.3;
					display: flex;

					.goods-price {
						font-weight: 700;
						font-size: 15px;
					}

					.unit {
						font-weight: normal;
						font-size: 24rpx;
						margin-right: 2rpx;
					}

					view {
						flex: 1;
						line-height: 1.3;
						&:last-of-type {
							text-align: right;

							.iconfont {
								line-height: 1;
								font-size: 26rpx;
							}
						}
					}
				}

				.goods-operation {
					text-align: right;
					padding-top: 20rpx;

					.operation-btn {
						line-height: 1;
						padding: 14rpx 20rpx;
						color: #333;
						display: inline-block;
						border-radius: 28rpx;
						background: #fff;
						border: 0.5px solid #999;
						font-size: 24rpx;
						margin-left: 10rpx;
					}
				}
			}
		}
	}

	.order-footer {
		.order-base-info {
			display: flex;

			.total {
				text-align: right;
				padding-top: 20rpx;
				flex: 1;

				& > text {
					line-height: 1;
					margin-left: 10rpx;
				}
			}

			.order-type {
				padding-top: 20rpx;
				flex: 0.5;

				& > text {
					line-height: 1;
				}
			}
		}

		.order-operation {
			text-align: right;
			padding-top: 20rpx;

			.operation-btn {
				line-height: 1;
				padding: 20rpx 26rpx;
				color: #333;
				display: inline-block;
				border-radius: 32rpx;
				background: #fff;
				border: 0.5px solid #999;
				font-size: 24rpx;
				margin-left: 10rpx;
			}
		}
	}
}

.empty {
	padding-top: 200rpx;
	text-align: center;

	.empty-image {
		width: 180rpx;
		height: 180rpx;
	}
}

.order-batch-operation {
	position: fixed;
	z-index: 5;
	left: 0;
	bottom: 0;
	width: 100vw;
	height: 100rpx;
	background: #fff;
	box-shadow: 0 0px 10px rgba(0, 0, 0, 0.1);
	text-align: right;

	&.bottom-safe-area {
		padding-bottom: 0;
		padding-bottom: constant(safe-area-inset-bottom);
		padding-bottom: env(safe-area-inset-bottom);
	}

	.operation-btn {
		height: 68rpx;
		line-height: 68rpx;
		background: #fff;
		padding: 0 40rpx;
		display: inline-block;
		text-align: center;
		margin: 16rpx 20rpx 16rpx 0;
		border-radius: 40rpx;
		border: 0.5px solid #ffffff;

		&.white {
			height: 68rpx;
			line-height: 68rpx;
			color: #333;
			border: 0.5px solid #999;
			background: #fff;
		}
	}
}
