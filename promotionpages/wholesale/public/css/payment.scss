@mixin wrap {
	margin: $ns-margin;
	padding: $ns-padding;
	border-radius: $ns-border-radius;
	background: #fff;
	position: relative;
}
.uni-list-cell {
	display: flex;
	justify-content: space-between;
}
.align-right {
	text-align: right;
}

.inline {
	display: inline !important;
}

.order-container {
	padding-bottom: 120rpx;
	padding-bottom: calc(120rpx + constant(safe-area-inset-bottom));
	padding-bottom: calc(120rpx + env(safe-area-inset-bottom));
}

.address-wrap {
	@include wrap;
	min-height: 100rpx;

	.icon {
		width: 80rpx;
		height: 80rpx;
		display: flex;
		justify-content: center;
		align-items: center;
		border-radius: 50%;
		position: absolute;
		top: 50%;
		transform: translateY(-50%);
		margin-right: 20rpx;

		.iconfont {
			line-height: 1;
			color: #fff;
			font-size: 36rpx;
		}
	}

	.address-info {
		padding-left: 100rpx;
		padding-right: 40rpx;

		.info {
			display: flex;

			text {
				flex: 1;

				&:last-of-type {
					text-align: right;
					color: #999;
				}
			}
		}

		.detail {
			line-height: 1.3;
		}
	}

	.address-empty {
		line-height: 100rpx;
	}

	.cell-more {
		position: absolute;
		top: 50%;
		transform: translateY(-50%);
		right: 10rpx;

		.iconfont {
			color: #999;
		}
	}
}

.mobile-wrap {
	@include wrap;

	.form-group {
		.form-item {
			display: flex;
			line-height: 50rpx;

			.text {
				display: inline-block;
				line-height: 50rpx;
				padding-right: 10rpx;
			}

			.placeholder {
				line-height: 50rpx;
			}

			.input {
				flex: 1;
				height: 50rpx;
				line-height: 50rpx;
			}
		}
	}
}

.order-cell {
	display: flex;
	margin: 20rpx 0;
	align-items: center;
	background: #fff;
	line-height: 40rpx;

	.tit {
		text-align: left;
	}

	.box {
		flex: 1;
		padding: 0 20rpx;
		line-height: inherit;

		.textarea {
			height: 40rpx;
		}
	}

	.iconfont {
		color: #bbb;
		font-size: 28rpx;
	}

	.order-pay {
		padding: 0;

		text {
			display: inline-block;
			margin-left: 6rpx;
		}
	}
}

.site-wrap {
	@include wrap;

	.site-header {
		display: flex;
		align-items: center;

		.icondianpu {
			display: inline-block;
			line-height: 1;
			margin-right: 12rpx;
			font-size: 30rpx;
		}
	}

	.site-body {
		.goods-wrap {
			margin-bottom: 20rpx;
			display: flex;
			position: relative;

			&:last-of-type {
				margin-bottom: 0;
			}

			.goods-img {
				width: 180rpx;
				height: 180rpx;
				padding: 20rpx 0 0 0;
				margin-right: 20rpx;

				image {
					width: 100%;
					height: 100%;
					border-radius: $ns-border-radius;
				}
			}

			.goods-info {
				flex: 1;
				position: relative;
				padding: 20rpx 0 0 0;
				max-width: calc(100% - 200rpx);

				.goods-name {
					display: -webkit-box;
					-webkit-box-orient: vertical;
					-webkit-line-clamp: 2;
					overflow: hidden;
					line-height: 1.5;
					font-size: 28rpx;
				}

				.goods-sub-section {
					width: 100%;
					line-height: 1.3;
					display: flex;

					.goods-price {
						font-weight: 700;
						font-size: 15px;
					}

					.unit {
						font-weight: normal;
						font-size: 24rpx;
						margin-right: 2rpx;
					}

					view {
						flex: 1;
						line-height: 1.3;
						&:last-of-type {
							text-align: right;

							.iconfont {
								line-height: 1;
								font-size: 26rpx;
							}
						}
					}
				}
			}
		}
	}

	.site-footer {
		.order-cell {
			.tit {
				width: 180rpx;
				text-align: right;
			}
			.store-promotion-box {
				overflow: hidden;
				text-overflow: ellipsis;
				white-space: nowrap;
			}
			.box {
				&.text-overflow {
					max-width: calc(100% - 248rpx);
				}
			}

			&:last-of-type {
				margin-bottom: 0;
			}
		}
	}
}

.order-checkout {
	@include wrap;

	.order-cell {
		.iconyuan_checkbox,
		.iconyuan_checked {
			font-size: 38rpx;
		}
	}
}

.order-money {
	@include wrap;

	.order-cell {
		.box {
			font-weight: 600;
			padding: 0;

			.operator {
				font-size: 24rpx;
				margin-right: 6rpx;
			}
		}
	}
}

.order-submit {
	position: fixed;
	z-index: 5;
	left: 0;
	bottom: 0;
	width: 100vw;
	height: 120rpx;
	background: #fff;
	box-shadow: 0 0px 10px rgba(0, 0, 0, 0.1);
	text-align: right;
	display: flex;

	&.bottom-safe-area {
		padding-bottom: 0 !important;
		padding-bottom: constant(safe-area-inset-bottom) !important;
		padding-bottom: env(safe-area-inset-bottom) !important;
	}

	.order-settlement-info {
		flex: 1;
		height: 120rpx;
		line-height: 120rpx;

		.money {
			font-size: 36rpx;
		}
	}

	.submit-btn {
		height: 80rpx;
		margin: 20rpx;
		display: flex;
		justify-content: center;
		align-items: center;
		button {
			line-height: 2.6;
		}
	}
}

.popup {
	width: 100vw;
	background: #fff;
	border-top-left-radius: 24rpx;
	border-top-right-radius: 24rpx;

	.popup-header {
		height: 90rpx;
		display: flex;
		align-items: center;
		padding: 0 30rpx;

		& > view {
			flex: 1;
			line-height: 1;
		}

		.tit {
			font-size: 32rpx;
			font-weight: 600;
		}

		.vice-tit {
			margin-right: 20rpx;
		}
	}

	.popup-body {
		height: calc(100% - 210rpx);
		height: calc(100% - 210rpx - constant(safe-area-inset-bottom));
		height: calc(100% - 210rpx - env(safe-area-inset-bottom));
	}

	.popup-footer {
		height: 120rpx;
		padding-bottom: 0 !important;
		padding-bottom: constant(safe-area-inset-bottom) !important;  
		padding-bottom: env(safe-area-inset-bottom) !important;  

		.confirm-btn {
			height: 72rpx;
			line-height: 72rpx;
			color: #fff;
			text-align: center;
			margin: 20rpx 40rpx;
			border-radius: 40rpx;
		}
	}
}

.invoice-popup {
	height: 65vh;

	.popup-body {
		.invoice-cell {
			margin: 0 30rpx;
			padding: 30rpx 0;
			border-top: 1px solid #f5f5f5;

			&:first-of-type {
				border-top: none;
			}

			.tit {
				font-size: 28rpx;
				font-weight: 600;
			}

			.option-grpup {
				padding-top: 20rpx;

				.option-item {
					display: inline-block;
					line-height: 1;
					font-size: 28rpx;
					padding: 16rpx 40rpx;
					background: #eee;
					border: 1px solid #eee;
					border-radius: 32rpx;
					margin: 0 20rpx 20rpx 0;

					&:nth-of-type(1),
					&:nth-of-type(2),
					&:nth-of-type(3) {
						margin-bottom: 0;
					}

					&.active {
						// background: opacify($base-color-rgba, 0.01);
					}

					&.disabled {
						color: #aaa;
					}
				}
			}

			.form-group {
				padding-top: 20rpx;

				.form-item {
					display: flex;
					line-height: 50rpx;

					.text {
						display: inline-block;
						width: 200rpx;
						line-height: 50rpx;
					}

					.placeholder {
						line-height: 50rpx;
					}

					.input {
						flex: 1;
						height: 50rpx;
						line-height: 50rpx;
					}
				}
			}
		}
	}
}

.coupon-popup {
	height: 65vh;

	.popup-body {
		background: #f5f5f5;
	}

	.coupon-item {
		@include wrap;

		& > .iconfont {
			font-size: 40rpx;
			position: absolute;
			top: 50%;
			right: 20rpx;
			transform: translateY(-50%);
		}
		& > .iconyuan_checkbox {
			color: $ns-text-color-gray;
		}

		.circular {
			position: absolute;
			top: 50%;
			left: 0;
			transform: translate(-50%, -50%);
			background: #f5f5f5;
			width: 30rpx;
			height: 30rpx;
			border-radius: 50%;
			z-index: 5;
		}

		.coupon-info {
			padding-right: 60rpx;
			height: 140rpx;
			display: flex;
			width: 100%;

			.coupon-money {
				width: 160rpx;
				height: 140rpx;
				display: flex;
				justify-content: center;
				align-items: center;
				margin-right: 20rpx;

				text {
					font-size: 50rpx;
					line-height: 1;
				}
				.ns-font-size-sm-left {
					font-size: $ns-font-size-sm !important;
					margin-top: 14rpx;
					margin-left: 4rpx;
				}
				.ns-font-size-sm-right {
					font-size: $ns-font-size-sm !important;
					margin-top: 14rpx;
					margin-right: 4rpx;
				}
			}

			.info {
				flex: 1;
				max-width: calc(100% - 240rpx);

				view {
					white-space: nowrap;
					overflow: hidden;
					text-overflow: ellipsis;
				}

				.ns-text-color-gray {
					line-height: 1.5;
				}
			}
		}
	}
}

.promotion-popup {
	height: 65vh;

	.order-cell {
		margin: 20rpx 30rpx;
		display: flex;
		flex-direction: column;
		align-items: flex-start;
		.tit {
			line-height: 60rpx;
		}
		.box {
			padding: 0;
		}
	}
}

.delivery-popup {
	height: 80vh;

	.delivery-cell {
		margin: 0 30rpx;
		padding: 30rpx 0;
		border-top: 1px solid #f5f5f5;

		&:first-of-type {
			border-top: none;
		}

		.tit {
			font-size: 28rpx;
			font-weight: 600;
		}

		.option-grpup {
			.option-item {
				display: inline-block;
				line-height: 1;
				font-size: 28rpx;
				padding: 16rpx 40rpx;
				background: #eee;
				border: 1px solid #eee;
				border-radius: 32rpx;
				margin: 0 20rpx 20rpx 0;

				&:nth-of-type(1),
				&:nth-of-type(2),
				&:nth-of-type(3) {
					margin-bottom: 0;
				}

				&.active {
					// background: opacify($base-color-rgba, 0.01);
				}

				&.disabled {
					color: #aaa;
				}
			}
		}
	}

	.delivery-cont {
		height: calc(100% - 180rpx);
		overflow-y: scroll;

		.pickup-point {
			padding: 20rpx 0;
			border-top: 1px solid #f5f5f5;

			.name {
				display: flex;

				.icon {
					flex: 1;
					text-align: right;

					.iconfont {
						line-height: 1;
					}
				}
			}

			&:first-of-type {
				padding-top: 0;
				border-top: none;
			}

			.info {
				line-height: 1.2;

				.ns-text-color-gray {
					&:last-of-type {
						margin-left: 10rpx;
					}
				}
			}
		}
	}
}

.pay-password {
	width: 80vw;
	background: #fff;
	box-sizing: border-box;
	border-radius: 10rpx;
	overflow: hidden;
	padding: 60rpx 40rpx;
	transform: translateY(-200rpx);

	.title {
		font-size: 28rpx;
		text-align: center;
	}

	.tips {
		font-size: 24rpx;
		color: #999;
		text-align: center;
	}

	.btn {
		width: 60%;
		margin: 0 auto;
		margin-top: 30rpx;
		height: 70rpx;
		line-height: 70rpx;
		border-radius: 70rpx;
		color: #fff;
		text-align: center;
		border: 1px solid #ffffff;

		&.white {
			margin-top: 20rpx;
			background-color: #fff !important;
		}
	}

	.password-wrap {
		padding-top: 20rpx;
		width: 90%;
		margin: 0 auto;

		.forget-password {
			margin-top: 20rpx;
			display: inline-block;
		}
	}
}
