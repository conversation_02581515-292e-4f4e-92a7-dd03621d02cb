.go-top {
	position: fixed;
	right: 30rpx;
	bottom: 220rpx;
	z-index: 1;
	background: #fff;
	padding: 10rpx;
	border: 1px solid;
	border-radius: 20px;
	width: 57rpx;
	// height: 270rpx;
	height: 180rpx;
	text-align: center;
	font-size: $ns-font-size-sm;
	.goods-share,
	.collection {
		margin-bottom: 10rpx;
		font-size: $ns-font-size-sm;
	}
	.icontop {
		font-size: 40rpx;
	}
}

.goods-detail {
	height: 100%;
	padding-bottom: 100rpx;
	padding-bottom: calc(100rpx + constant(safe-area-inset-bottom));
	padding-bottom: calc(100rpx + env(safe-area-inset-bottom));
}
// 商品媒体信息
.goods-media {
	width: 100%;
	position: relative;
	overflow: hidden;

	&:after {
		padding-top: 100%;
		display: block;
		content: '';
	}

	.pop-video{
			line-height: 1;
		}
		.goods-img,
		.goods-video {
			position: absolute;
			width: 100%;
			height: 100%;
			left: 0;
			top: 0;
			transition-property: transform;
			transition-timing-function: cubic-bezier(0, 0, 0.25, 1);
			transition-duration: 350ms;
			transform: translate3d(0, 0, 0);
			.video-img{
				width: 100%;
				height: 100%;
				position: relative;
			}
			image{
				width: 100%;
				height: 100%;
			}
			.video-open{
				width:100%;
				height: 100%;
				position: absolute;
				left: 0;
				top: 0;
				display: flex;
				justify-content: center;
				align-items: center;
				.iconfont{
					font-size: 90rpx;
					color: #fff;
				}
			}
		}
	
	


	.goods-img {
		transform: translateX(100%);
	}

	.goods-video {
		transform: translateX(-100%);
	}

	.goods-img.show,
	.goods-video.show {
		transform: translateX(0);
	}

	.goods-img .swiper {
		width: 100%;
		height: 100%;

		.item {
			width: 100%;
			height: 100%;
		}

		image {
			width: 100%;
			height: 100%;
		}
	}

	.goods-img .img-indicator-dots {
		position: absolute;
		z-index: 5;
		bottom: 40rpx;
		right: 40rpx;
		background: rgba(100, 100, 100, 0.6);
		color: #fff;
		font-size: 24rpx;
		line-height: 40rpx;
		border-radius: 20rpx;
		padding: 0 20rpx;
	}

	.goods-video video {
		width: 100%;
		height: 100%;
	}

	.goods-video .uni-video-cover {
		background: none;
	}

	.media-mode {
		position: absolute;
		width: 100%;
		z-index: 5;
		bottom: 40rpx;
		//#ifdef MP
		bottom: 80rpx;
		//#endif
		text-align: center;
		line-height: 50rpx;

		text {
			background: rgba(100, 100, 100, 0.6);
			color: #fff;
			font-size: 24rpx;
			line-height: 50rpx;
			border-radius: 20rpx;
			padding: 0 30rpx;
			display: inline-block;

			&:last-child {
				margin-left: 40rpx;
			}
		}
	}
}

.group-wrap {
	margin-bottom: 20rpx;
}

.goods-module-wrap {
	background-color: #fff;
	padding: 0 20rpx;
	position: relative;
	&:last-child {
		padding-bottom: 25rpx;
	}

	.sku-name {
		padding-top: 20rpx;
		font-weight: bold;
	}
	.sku-name,
	.introduction {
		overflow: hidden;
		text-overflow: ellipsis;
		display: -webkit-box;
		-webkit-line-clamp: 2;
		-webkit-box-orient: vertical;
	}
	.introduction {
		margin-bottom: 20rpx;
	}

	.topic-price {
		padding: 20rpx 0 0 0;
		height: 60rpx;
		line-height: 60rpx;
		white-space: nowrap;
		overflow: hidden;
		font-size: 60rpx;
		margin-bottom: 8rpx;
		display: inline-block;
		vertical-align: middle;
		font-weight: bold;
		.symbol {
			margin-right: 4rpx;
			font-size: $ns-font-size-sm;
		}
	}

	.original-price {
		padding-left: 25rpx;
		font-size: $ns-font-size-sm;
		white-space: nowrap;
		overflow: hidden;
		display: inline-block;
		vertical-align: middle;
		.price {
			text-decoration: line-through;
			opacity: 0.85;
		}
		.topic-save-price {
			display: block;
			padding: 2rpx 8rpx;
		}
	}

	.sale-num {
		display: inline-block;
		position: absolute;
		right: 20rpx;
		bottom: 10rpx;
	}
}

.goods-cell {
	display: flex;
	padding: 20rpx;
	align-items: center;
	background: #fff;
	line-height: 40rpx;
	justify-content: space-between;

	.tit {
		color: #999;
		font-size: $ns-font-size-sm;
		margin-right: 10rpx;
	}
	
	.box {
		width: 90%;
		font-size: $ns-font-size-sm;
		line-height: inherit;
		overflow: hidden;
		text-overflow:ellipsis;
		white-space: nowrap;
	}

	.iconfont {
		color: #bbb;
		font-size: 28rpx;
	}
}

.goods-cell.service {
	.box {
		display: -webkit-box;
		-webkit-line-clamp: 1;
		-webkit-box-orient: vertical;
		-webkit-box-pack: center;
		overflow: hidden;
		word-break: break-all;
		text {
			&::after {
				content: ' · ';
				display: inline-block;
			}
			&:last-child::after {
				content: '';
			}
		}
	}
}

.shop-wrap {
	padding: 20rpx;
	background: #fff;

	.box {
		display: flex;
	}

	.shop-logo {
		width: 120rpx;
		height: 120rpx;
		border-radius: 10rpx;
		border: 1rpx solid $ns-border-color-gray;

		image {
			width: 100%;
			height: 100%;
		}
	}

	.shop-info {
		padding-left: 20rpx;
		flex: 1;
		.description {
			color: $ns-text-color-gray;
		}
	}

	.shop-score {
		margin-top: 20rpx;
		text {
			flex: 1;
			text-align: center;
			color: #999;
		}
	}

	.goods-action {
		margin-top: 20rpx;
		text-align: center;
		width: 100%;

		navigator {
			display: inline-block;
			line-height: 40rpx;
			padding: 2rpx 40rpx;
			border: 1px solid #ffffff;
			border-radius: 40rpx;

			&:last-of-type {
				margin-left: 30rpx;
			}
		}
	}
}

.goods-evaluate {
	padding: 20rpx;
	background: #fff;

	.tit {
		padding-bottom: 20rpx;
		display: flex;
		align-items: center;

		view {
			flex: 1;
			line-height: 40rpx;
			text-align: left;
		}
		navigator {
			text-align: right;

			.iconfont {
				font-size: 24rpx;
			}
		}
	}

	.evaluate-item {
		.evaluator {
			display: flex;
			align-items: center;

			.evaluator-face {
				width: 50rpx;
				height: 50rpx;
				border-radius: 50%;
				overflow: hidden;

				image {
					width: 100%;
					height: 100%;
				}
			}

			.evaluator-name {
				margin-left: 20rpx;
				color: #999;
			}
		}

		.cont {
			text-align: justify;
			display: -webkit-box;
			-webkit-line-clamp: 2;
			-webkit-box-orient: vertical;
			-webkit-box-pack: center;
			overflow: hidden;
			word-break: break-all;
		}

		.evaluate-img {
			display: inline-flex;

			.img-box {
				width: 100rpx;
				height: 100rpx;
				overflow: hidden;
				margin: 0 20rpx 20rpx 0;

				image {
					width: 100%;
					height: 100%;
				}
			}
		}

		.time {
			color: #999;

			text {
				margin-right: 20rpx;
			}
		}
	}
	.evaluate-item-empty{
		width: 100%;
		height: 130rpx;
		display: flex;
		justify-content: center;
		align-items: center;
		color: $ns-text-color-gray;
	}
}

.goods-action-button {
	flex: 1;
}

// 商家服务
.goods-merchants-service-popup-layer {
	background: #fff;
	height: 660rpx;
	.tax-title {
		text-align: center;
		font-size: $ns-font-size-lg;
		line-height: 120rpx;
		height: 120rpx;
		display: block;
		font-weight: bold;
	}
	scroll-view {
		position: absolute;
		left: 0;
		right: 0;
		height: 65%;
		.item {
			padding: 20rpx 40rpx;
			position: relative;
			.iconfont {
				vertical-align: top;
				display: inline-block;
				margin-right: $ns-margin;
				font-size: $ns-font-size-lg;
			}
			.info-wrap {
				display: inline-block;
				vertical-align: middle;
				width: 90%;
				.title {
					display: block;
					font-size: $ns-font-size-base;
					font-weight: bold;
				}
				.describe {
					font-size: $ns-font-size-sm;
					color: $ns-text-color-gray;
					display: block;
					padding: 10rpx 0;
				}
			}
		}
	}
	.button-box{
		width: 100%;
		position: absolute;
		bottom: 0;
		z-index: 1;
		margin-bottom: $ns-margin;
	}
}

// 商品属性
.goods-attribute-popup-layer {
	background: #fff;
	height: 660rpx;

	.title {
		font-size: $ns-font-size-lg;
		line-height: 120rpx;
		height: 120rpx;
		display: block;
		font-weight: bold;
		padding-left: $ns-padding;
	}
	.goods-attribute-body {
		position: absolute;
		left: 0;
		right: 0;
		height: 60%;
		.item {
			padding: $ns-padding;
			border-bottom: 1px solid;
			.value {
				margin-left: 20rpx;
			}
			&:last-child {
				border-bottom: 0;
			}
		}
	}

	.button-box{
		width: 100%;
		position: absolute;
		bottom: 0;
		z-index: 1;
		margin-bottom: $ns-margin;
	}
}

// 详情
.goods-detail-tab{
	width: 100%;
	padding-top: $ns-padding;
	background: #ffffff;
	.detail-tab{
		width: 100%;
		display: flex;
		justify-content: center;
		align-items: center;
		.tab-item{
			height: 70rpx;
			color:$ns-text-color-black;
			line-height: 70rpx;
			box-sizing: border-box;
		}
		.tab-item.active{
			position: relative;
		}
		.tab-item.active::after{
			content: "";
			display: inline-block;
			width: 100%;
			height: 6rpx;
			position: absolute;
			left: 0;
			bottom: 0;
			border-radius: 3rpx;
		}
		.tab-item:nth-child(1){
			margin-right: 25%;
		}
	}
	.detail-content{
		width: 100%;
	}
	.goods-details {
		padding: $ns-padding;
		margin-bottom: 100rpx;
		overflow: hidden;
		background: #ffffff;
	}
	.goods-details.active{
		min-height: 150rpx;
		width: 100%;
		display: flex;
		justify-content: center;
		align-items: center;
		color: $ns-text-color-gray;
	}
}

// 海报
// .uni-popup__wrapper-box
.poster-layer {
	.generate-poster {
		padding: 40rpx 0;
		.iconfont {
			font-size: 80rpx;
			color: #07c160;
			line-height: initial;
		}
		> view {
			text-align: center;
			&:last-child {
				margin-top: 20rpx;
			}
		}
	}
	.image-wrap {
		width: 70%;
		margin: 30px auto 20px auto;
		box-shadow: 0 0 16px rgba(100, 100, 100, 0.3);
		image {
			width: 100%;
			height: 100%;
			height: 750rpx;
		}
	}
	.msg {
		padding: 40rpx;
	}
	.save {
		text-align: center;
		height: 80rpx;
		line-height: 80rpx;
	}
	.close {
		position: absolute;
		top: 0;
		right: 20rpx;
		width: 40rpx;
		height: 80rpx;
		font-size: 50rpx;
	}
}

.share-popup,
.uni-popup__wrapper-box {
	.share-title {
		line-height: 60rpx;
		font-size: $ns-font-size-lg;
		padding: 15rpx 0;
		text-align: center;
	}

	.share-content {
		display: flex;
		display: -webkit-flex;
		-webkit-flex-wrap: wrap;
		-moz-flex-wrap: wrap;
		-ms-flex-wrap: wrap;
		-o-flex-wrap: wrap;
		flex-wrap: wrap;
		padding: 15rpx;

		.share-box {
			flex: 1;
			text-align: center;

			.share-btn {
				margin: 0;
				padding: 0;
				border: none;
				line-height: 1;
				height: auto;
				text {
					margin-top: 20rpx;
					font-size: 24rpx;
					display: block;
					color: $ns-text-color-black;
				}
			}

			.iconfont {
				font-size: 80rpx;
				line-height: initial;
			}
			.iconpengyouquan,
			.iconiconfenxianggeihaoyou {
				color: #07c160;
			}
		}
	}

	.share-footer {
		height: 90rpx;
		line-height: 90rpx;
		border-top: 2rpx #f5f5f5 solid;
		text-align: center;
		color: #666;
	}
}

.selected-sku-spec {
	.box {
		text {
			margin-right: 10rpx;
			white-space: nowrap;
			overflow: hidden;
			margin-right: 10px;
			text-overflow: ellipsis;
		}
	}
}

.topic-wrap {
	height: 120rpx;
	line-height: 120rpx;
	position: relative;
	overflow: hidden;

	.promotion-wrap {
		line-height: 120rpx;
		margin-right: 330rpx;
		overflow: hidden;
		text-overflow: ellipsis;
		color: #fff;
		.iconfont {
			font-size: 40rpx;
			margin-left: 20rpx;
			margin-right: 20rpx;
		}
		.txt {
			white-space: nowrap;
			overflow: hidden;
			text-overflow: ellipsis;
			width: 65%;
			display: inline-block;
		}
		text {
			vertical-align: middle;
			color: #fff;
			font-weight: bold;
		}
	}
	.countdown {
		position: absolute;
		right: 0;
		top: 50%;
		width: 310rpx;
		text-align: center;
		transform: translateY(-50%);
		height: 100%;
		background: #fff;
		border-top-left-radius: 120rpx;
		padding: 2rpx 15rpx 0 60rpx;
		.txt {
			text-align: center;
			font-size: $ns-font-size-sm;
			margin-top: 14rpx;
		}
		.clockrun {
			text-align: center;
			font-size: $ns-font-size-sm;
		}
	}
}
// #ifdef APP-PLUS
.diy-back-btn{
	width: 64rpx;
	height: 64rpx;
	line-height: 65rpx;
	text-align: center;
	border-radius: 50%;
	background-color: rgba(0,0,0,.4);
	font-size: 42rpx;
	position: fixed;
	z-index: 2;
	top: calc(var(--status-bar-height) + 10rpx);
	left: 36rpx;
	color: #fff;
}
// #endif
