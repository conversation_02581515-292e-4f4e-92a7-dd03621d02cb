<template>
	<view class="page">
		<mescroll-uni ref="mescroll" @getData="getData" :size="5" @scroll="scrollTouch" :top="headerTop">
			<view class="banner" slot="list">
				<image :src="$util.img(bannerdata.banner)" mode=""></image>
				<view class="nav">
					<view class="less_maidou">
						当前迈豆余额：{{bannerdata.maidou_num}}
					</view>
					<view class="search_box">
						<view class="search">
							<image :src="$util.img('public/static/youpin/maidou/maidou-search.png')" mode=""></image>
							<input type="text" value="" @input="inputInput" placeholder="搜索你喜欢的商品" />
						</view>
						<view class="click-secrch" @click="search()">
							搜索
						</view>
					</view>
					<view class="paixu">
						<view class="paixu-item" :class="type == 0 ? 'on' : ''" @click="changetab(0)">
							综合
						</view>
						<view class="paixu-item" :class="type == 1 ? 'on' : ''" @click="changetab(1)">
							销量
						</view>
						<view class="paixu-item" :class="type == 2 ? 'on' : ''" @click="changetab(2)">
							价格
						</view>
					</view>
					<view class="datelist">
						<view class="list_item" v-for="(item, key) in dataList" :key="key" @click="toDetail(item)">
							<view class="item-image">
								<image class="expose_goods_index" :data-expose_goods_sku="item.sku_id" :src="$util.img(item.goods_image)" mode='aspectFit'></image>
								<image :src="$util.img('public/static/youpin/product-sell-out.png')" class="over"
									v-if="item.goods_stock == 0"></image>
							</view>
							<view class="item_right">
								<view class="right_title">
									<text class="tip">迈豆</text>
									<text>{{item.goods_name}}</text>
								</view>
								<view>
									<view class="songmaidou">
										<text class="getmd">送迈豆</text>{{item.send_maidou}}
									</view>
								</view>
								<view class="buynow">
									<view class="buy-price">
										<view class="bigprice">
											<text class="iconmoney">￥</text>
											<text class="bigmoney">{{item.retail_price}}</text>
										</view>
										<text class="huimoney">
											￥{{item.market_price}}
										</text>
									</view>
									<view class="buybotton">
										立即购买
									</view>
								</view>
							</view>
						</view>
						<view v-if="!dataList.length">
							<ns-empty :fixed="false"></ns-empty>
						</view>
					</view>
				</view>
			</view>
		</mescroll-uni>
	</view>
</template>
<script>
	import system from "@/common/js/system.js";
	import wx_expose_goods from '@/common/mixins/wx_expose_goods.js';
  // #ifdef H5
  import {isOnXianMaiApp} from "../../../common/js/h5/appOP";
  // #endif
	export default {
		components: {

		},
		mixins:[wx_expose_goods],
		data() {
			return {
				type: 0,
				dataList: [],
				sf: "all",
				keyword: '',
				shop_id: '',
				bannerdata: '',
        headerTop:0,
			};
		},
		onLoad(data) {
			// type=0 为活动距离结束时间，type=1 为活动距离开始时间
			// this.getbanner()
      // #ifdef H5
      if(isOnXianMaiApp){
        this.headerTop+=88;
      }
      // #endif
		},
		async onShow() {
			// this.mescroll.resetUpScroll(false);
			// 刷新多语言
			// this.$langConfig.refresh();
			// this.getbanner()
			await system.wait_staticLogin_success();
			// 修改顶部标题
			this.getDiyInfo()

			// #ifdef H5
			let share_data = this.$util.deepClone(this.getSharePageParams())
			let link = window.location.origin + this.$router.options.base + share_data.link.slice(1) + '?'
			share_data.link = link
			share_data.desc = share_data.title
			share_data.title = '先迈商城'
			await this.$util.publicShare(share_data);
			// #endif
		},
		computed: {

		},
		watch: {},

		methods: {
			async getDiyInfo() {
				let res = await this.$api.sendRequest({
					url: '/api/diyview/info',
					async: false,
					data: {
						name: 'DIYVIEW_INDEX'
					}
				});

				if (res.data) {
					let data = JSON.parse(res.data.value).value
					data.forEach(v => {
						if (v.controller == 'Maidou' && v.name) {
							uni.setNavigationBarTitle({
								title: v.name || '迈豆专区'
							})
						}
					})
				}
			},
			changetab(type) {
				this.type = type
				this.dataList = [];
				if (type == 0) {
					this.sf = "all"
				} else if (type == 1) {
					this.sf = "sale_num"
				} else {
					this.sf = "price"
				}
				this.$refs.mescroll.refresh();
				this.againDealWith(true)
			},
			toDetail(res) {
				this.$util.redirectTo('/pages/goods/detail/detail', {
					sku_id: res.sku_id
				});
			},
			inputInput(e) {
				this.keyword = e.target.value
			},
			search() {
				if (this.inputValue != '') {
					this.$refs.mescroll.refresh();
				} else {
					uni.showToast({
						title: "搜索内容不能为空",
						duration: 1000
					})
				}

			},
			getData(mescroll) {
				this.mescroll = mescroll;
				if (mescroll.size == 1) {
					this.dataList = [];
				}
				if (mescroll.num == 1) {
					this.getbanner();
				}
				this.$api.sendRequest({
					url: this.$apiUrl.maidouLidt,
					data: {
						page_size: mescroll.size,
						page: mescroll.num,
						type: this.type,
						sf: this.sf,
						st: 'DESC',
						goods_name: this.keyword
					},
					success: res => {
						let newArr = []
						let msg = res.message;
						if (res.code == 0 && res.data) {
							if (res.data.list && res.data.list.length != 0) {
								newArr = res.data.list;
							}

						} else {
							this.$util.showToast({
								title: msg
							})
						}
						mescroll.endSuccess(newArr.length);
						//设置列表数据
						if (mescroll.num == 1) this.dataList = []; //如果是第一页需手动制空列表
						this.dataList = this.dataList.concat(newArr); //追加新数据

						if (this.$refs.loadingCover) this.$refs.loadingCover.hide();
					},
					fail() {
						//联网失败的回调
						mescroll.endErr();
						if (this.$refs.loadingCover) this.$refs.loadingCover.hide();
					}
				});
			},
			getbanner() {
				this.$api.sendRequest({
					url: this.$apiUrl.getMaidouBanner,
					data: {

					},
					success: res => {
						let msg = res.message;
						if (res.code == 0 && res.data) {
							this.bannerdata = res.data
						} else {
							// this.$util.showToast({
							// 	title: msg
							// })
						}
					},
					fail() {

					}
				});
			},
			getSharePageParams() {
        let share_data=this.$util.unifySharePageParams('/promotionpages/maidou/list/list','精选好物，买一送一！',
            '',{},this.$util.img('public/static/youpin/maidou_share.jpg'))
        return share_data;
			}
		},
		// 分享朋友
		onShareAppMessage(res) {
      let { title, link, imageUrl, desc } = this.getSharePageParams()
      return this.$buriedPoint.pageShare(link , imageUrl, title);
		},
		// 分享到微信
		onShareTimeline(res) {
      let share_data=this.getSharePageParams();
      return {
        title: share_data.title,
        imageUrl: share_data.imageUrl,
        query: share_data.query,
        success: res => {},
        fail: res => {}
      };
		}
	};
</script>

<style lang="scss">
	.page {
		background-color: #F5F5F5;

		.banner {
			position: relative;

			image {
				height: 260rpx;
				width: 100%;
			}

			.nav {
				background: #FFFFFF;
				border-radius: 20rpx 20rpx 0 0;
				position: absolute;
				width: 100%;
				top: 230rpx;
				left: 0;

				.less_maidou {
					text-align: center;
					font-size: 32rpx;
					color: #333333;
					padding: 30rpx 0;
				}

				.search_box {
					display: flex;
					align-items: center;
					justify-content: center;

					.search {
						width: 605rpx;
						height: 60rpx;
						background: #F5F5F5;
						border-radius: 50px;
						display: flex;
						align-items: center;

						image {
							width: 30rpx;
							height: 30rpx;
							margin-left: 30rpx;
						}

						input {
							height: 100%;
							width: 70%;
							padding-left: 20rpx;
						}
					}

					.click-secrch {
						font-size: 28rpx;
						color: #333333;
						margin-left: 20rpx;
					}
				}

				.paixu {
					display: flex;
					align-items: center;
					justify-content: space-around;
					padding: 20rpx 0;

					.paixu-item {
						font-size: 28rpx;
						color: #999999;
					}

				}

				.datelist {
					background: #F5F5F5;
					padding: 20rpx;

					.list_item {
						border-radius: 20rpx;
						display: flex;
						padding: 20rpx;
						background: #FFFFFF;
						margin-bottom: 20rpx;
						&:last-child{
							margin-bottom: 100rpx;
						}
						image {
							width: 240rpx;
							height: 240rpx;
						}

						.item-image {
							position: relative;

							.over {
								width: 120rpx;
								height: 120rpx;
								position: absolute;
								left: 50%;
								top: 50%;
								transform: translate(-50%, -50%);
							}
						}

						.item_right {
							display: flex;
							flex-direction: column;
							margin-left: 30rpx;

							.right_title {
								width: 390rpx;
								height: 90rpx;
								font-size: 26rpx;
								color: #333333;
								overflow: hidden;
								text-overflow: ellipsis;
								display: -webkit-box;
								-webkit-line-clamp: 2;
								-webkit-box-orient: vertical;

								.tip {
									font-size: 20rpx;
									color: #FFFFFF;
									width: 48rpx;
									height: 30rpx;
									text-align: center;
									line-height: 30rpx;
									display: inline-block;
									background: #FB331D;
									border-radius: 8rpx;
								}
							}

							.songmaidou {
								display: inline-block;
								padding: 3rpx 10rpx 0 0;
								background: #FFEFEF;
								color: #FC3533;
								font-size: 22rpx;
								// width: 43%;
								border-radius: 5rpx;
								margin-top: 10rpx;

								.getmd {
									color: #333333;
									margin-left: 10rpx;
								}
							}

							.buynow {
								display: flex;
								align-items: center;
								justify-content: space-between;
								margin-top: 20rpx;

								.buy-price {
									display: flex;
									flex-direction: column;

									.bigprice {
										color: #F2270C;
										display: flex;
										align-items: center;
										height: 40rpx;

										.iconmoney {
											font-size: 26rpx;
										}

										.bigmoney {
											font-size: 36rpx;
										}
									}

									.huimoney {
										font-size: 24rpx;
										color: #999999;
										text-decoration: line-through;
									}
								}

								.buybotton {
									width: 148rpx;
									height: 56rpx;
									background: linear-gradient(-90deg, #FF2127 0%, #FF5A2F 100%);
									border-radius: 28rpx;
									font-size: 24rpx;
									color: #FFFFFF;
									text-align: center;
									line-height: 56rpx;
								}
							}
						}
					}
				}
			}
		}

	}

	.on {
		color: #F2270C !important;
	}
</style>
