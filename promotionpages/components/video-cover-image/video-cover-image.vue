<!--视频帧图选取 依赖阿里云oss-->
<template>
  <uni-popup type="center" ref="videoCoverRef" class="video-cover-image-father">
    <view class="video-cover-image">
      <image :src="$util.img(cover_img_list[current])" mode="aspectFit" class="video-cover-image-preview"></image>
      <view class="video-cover-image-op">
        <text class="video-cover-image-op-cancel" @click="hide">取消</text>
        <text class="video-cover-image-op-confirm" @click="confirm">确认封面图</text>
      </view>
      <view class="video-cover-image-list">
        <image class="video-cover-image-list-img" mode="aspectFill" v-for="(item,index) in cover_img_list" :key="index" :src="$util.img(item)"/>
        <text class="video-cover-image-list-move" :style="{left:box_left+'px'}" @touchstart="touchstart" @touchmove="touchmove" @touchend="touchend"></text>
      </view>
    </view>
  </uni-popup>
</template>

<script>
import UniPopup from "../../../components/uni-popup/uni-popup-sku.vue";

export default {
  name: "video-cover-image",
  components: {UniPopup},
  data(){
    return{
      duration: 0,  //单位毫秒
      cover_number:10,
      video_src:'',
      cover_img_list:[],
      current: 0,
      is_start: false,
      last_distance:0,
      box_left: 0,
      list_rect_size:{},
      img_rect_size_list:[]
    }
  },
  methods:{
    show(video_src,duration){
      this.video_src = video_src;
      this.duration = duration
      this.cover_img_list = [];
      this.getCoverImages();
      this.$refs.videoCoverRef.open();
      setTimeout(async ()=>{
        await this.getListRect()
        await this.getImageRect()
      },500)
    },
    hide(){
      this.$refs.videoCoverRef.close()
    },
    confirm(){
      this.$emit('confirm', this.cover_img_list[this.current]);
      this.$refs.videoCoverRef.close()
    },
    getCoverImages(){
      let step = parseInt(this.duration/this.cover_number);
      for(let i=0;i<this.cover_number;i++){
        let img_src = this.video_src + `?x-oss-process=video/snapshot,t_${i*step},f_jpg,w_360,ar_auto`;
        this.$set(this.cover_img_list,i,img_src);
      }
    },
    getListRect(){
      return new Promise((resolve, reject) => {
        const query = uni.createSelectorQuery().in(this);
        query.select('.video-cover-image-list').boundingClientRect(data=>{
          if(data){
            this.list_rect_size = data;
          }
          resolve()
        }).exec()
      })
    },
    getImageRect(){
      return new Promise((resolve, reject) => {
        const query = uni.createSelectorQuery().in(this);
        query.selectAll('.video-cover-image-list-img').boundingClientRect(data=>{
          if(data && data.length){
            this.img_rect_size_list = JSON.parse(JSON.stringify(data));
            this.list_rect_size = Object.assign(this.list_rect_size,
                {width: data[data.length-1].width*data.length, left:0});
          }
          resolve()
        }).exec()
      })
    },
    touchstart(e){
      this.last_distance = e.touches[0].clientX;
      this.is_start = true
    },
    touchmove(e){
      if(this.is_start){
        if((e.touches[0].clientX<0)){
          this.box_left = 0
          this.last_distance = 0
        } else if((e.touches[0].clientX + this.img_rect_size_list[0].width) <= (this.list_rect_size.left + this.list_rect_size.width)){
          this.box_left += (e.touches[0].clientX - this.last_distance);
          this.last_distance = e.touches[0].clientX;
        }else if(e.touches[0].clientX + this.img_rect_size_list[0].width > (this.list_rect_size.left + this.list_rect_size.width)){
          this.box_left = (this.list_rect_size.left + this.list_rect_size.width) - this.img_rect_size_list[0].width
          this.last_distance = (this.list_rect_size.left + this.list_rect_size.width) - this.img_rect_size_list[0].width
        }
        let width = this.img_rect_size_list[0].width;
        if(this.box_left <= 0){
          this.box_left = 0
          this.current = 0
        }else if(this.box_left >= this.img_rect_size_list[this.img_rect_size_list.length -1].left){
          this.current = this.cover_img_list.length - 1
          this.box_left = this.img_rect_size_list[this.img_rect_size_list.length -1].left
        }else{
          this.current = parseInt((this.box_left+3) / width)
        }
      }
    },
    touchend(){
      this.is_start = false
    }
  }
}
</script>

<style scoped lang="scss">
/* #ifdef H5 */
.video-cover-image-father /deep/ .uni-popup__wrapper.uni-custom.center .uni-popup__wrapper-box{
  max-width: 100%;
  max-height: 100%;
  overflow: unset;
}
/* #endif */
.video-cover-image {
  width: 100vw;
  height: 100vh;
  background-color: black;
  padding-top: 140rpx;
  box-sizing: border-box;
  &-op{
    display: flex;
    justify-content: space-around;
    align-items: center;
    width: 100%;
    position: absolute;
    left: 0;
    bottom: calc(40rpx + env(safe-area-inset-bottom));
    &-cancel{
      width: 240rpx;
      height: 80rpx;
      border-radius: 40rpx;
      background: var(--custom-brand-color-30);
      font-size: 28rpx;
      font-weight: 400;
      line-height: 40rpx;
      color: var(--custom-brand-color);
      display: flex;
      justify-content: center;
      align-items: center;
    }
    &-confirm{
      width: 240rpx;
      height: 80rpx;
      border-radius: 40rpx;
      background: var(--custom-brand-color);
      font-size: 28rpx;
      font-weight: 400;
      line-height: 40rpx;
      color: rgba(255, 255, 255, 1);
      display: flex;
      justify-content: center;
      align-items: center;
    }
  }
  &-preview{
    width: 650rpx;
    height: 650rpx;
    display: block;
    margin: 0 auto;
  }
  &-list{
    width: 100%;
    position: absolute;
    left: 20rpx;
    bottom: calc(200rpx + env(safe-area-inset-bottom));
    &-img{
      width: 70rpx;
      height: 70rpx;
      display: inline-block;
    }
    &-move{
      position: absolute;
      top: -3rpx;
      left: -3rpx;
      width: 76rpx;
      height: 76rpx;
      border: 6rpx solid white;
      box-sizing: border-box;
    }
  }
}
</style>
