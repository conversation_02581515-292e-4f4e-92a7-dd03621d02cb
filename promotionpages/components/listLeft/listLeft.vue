<!--listLeft.vue //左边组件的内容-->
<template>
  <view class="listLeft">
    <view class="user" @click="toSeedingHome(showData.member_id)">
      <image :src="showData.headimg" class="user-head"></image>
      <text class="user-name">{{showData.nickname}}</text>
    </view>
    <view class="info">
      <view class="info-title" v-if="showData.title != ''">{{showData.title}}</view>
<!--      <view class="info-desc">{{showData.content}}</view>-->
      <!-- <view class="info-desc" :class="{'info-desc-hide':is_hide}" @touchstart.stop @touchend.stop>{{showData.content}}</view> -->
      <view class="info-row">
        <scroll-view scroll-y="true" style="max-height: 380rpx;">
          <view class="info-desc" :class="{'info-desc-hide':is_hide}">{{showData.content}}</view>
        </scroll-view>
        <view class="info-more">
          <text class="info-more-text" @click="changeHide" v-if="show_hide">{{hide_text}}</text>
        </view>
      </view>
    </view>
    <view class="product" v-if="showData.goods && showData.goods.length>0&&false">
      <view class="product-item" v-for="(item,index) in showData.goods" @click="$util.toProductDetail(item)" :style="showData.goods.length==1 ? 'width:100%;margin-right: 24rpx;':''">
        <image :src="item.goods_image" alt="" class="product-item-img"/>
        <view class="product-item-info">
          <view class="product-item-info-text overtext-hidden-one">{{item.goods_name}}</view>
          <view class="product-item-info-price"><text>￥</text>{{item.retail_price}}</view>
        </view>
        <image :src="$util.img('public/static/youpin/redCart.png')" alt="" class="product-item-card" :class="{'product-item-card-one':showData.goods.length==1}"/>
      </view>
    </view>

    <uni-popup ref="videoDescRef" type="bottom" class="video-desc-father">
      <view class="video-desc">
        <view class="video-desc-header">
          <image :src="$util.img(showData.headimg)" class="video-desc-header-img"></image>
          <text class="video-desc-header-name">{{showData.nickname}}</text>
        </view>
        <view class="video-desc-content">{{showData.content}}</view>
        <view class="video-desc-op">
          <text class="video-desc-op-text" @click="$refs.videoDescRef.close()">收起</text>
        </view>
      </view>
    </uni-popup>
  </view>
</template>

<script>
import UniPopup from "../../../components/uni-popup/uni-popup.vue";

export default {
  components: {UniPopup},
  props:{
    showData:{
      type:Object
    }
  },
  data() {
    return {
      show_hide:false,
      is_hide:true,
      hide_text:'展开',
      textHeight:0,
    };
  },
  mounted(){
    this.getSize();
  },
  methods:{
    toSeedingHome(id){
      this.$util.redirectTo('/promotionpages/seeding/seeding_home_page/seeding_home_page',{other_mid:id});
    },
    changeHide(){
      // this.$refs.videoDescRef.open()
      this.is_hide=!this.is_hide;
      if(this.is_hide){
        this.hide_text='展开';
      }else{
        this.hide_text='收起';
      }
    },
    getSize(){
      let {screenWidth} = uni.getSystemInfoSync();
      const query = uni.createSelectorQuery().in(this);
      query.select('.info-desc').boundingClientRect((pos)=>{
        this.textHeight=pos.height;
        if(pos.height>=(screenWidth/375)*35){
          this.show_hide=true;
        }
      }).exec()
    }
  }
}
</script>

<style lang="scss" scoped>
.listLeft{
  width: 100vw;
  min-height: 1000rpx;
  background: linear-gradient(180deg, rgba(255, 255, 255, 0) 0%, rgba(0, 0, 0, 0.3) 100%);
  padding-bottom: 32rpx;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  justify-content: flex-end;
  .user{
    display: flex;
    align-items: center;
    padding-left: 24rpx;
    box-sizing: border-box;
    &-head{
      width: 44rpx;
      height: 44rpx;
      background: rgba(220, 220, 220, 0.39);
      border-radius: 50%;
    }
    &-name{
      font-size: 32rpx;
      font-weight: 400;
      line-height: 42rpx;
      color: #FFFFFF;
      margin-left: 14rpx;
    }
  }
  .info{
    margin-top: 36rpx;
    padding-left: 24rpx;
    box-sizing: border-box;
    width: 680rpx;
    &-title{
      font-size: 32rpx;
      font-weight: 500;
      line-height: 38rpx;
      color: #FFFFFF;
      margin-bottom: 12rpx
    }
    &-row{
      display: flex;
      align-items: flex-end;
    }
    &-desc{
      font-size: 28rpx;
      font-weight: 400;
      line-height: 38rpx;
      color: #FFFFFF;
      // margin-top: 12rpx;
      // max-height: 620rpx;
      // overflow-y: auto;
      box-sizing: border-box;
      word-break: break-all;
      // -webkit-overflow-scrolling: touch;
      &-hide{
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        overflow: hidden;
      }
    }
    &-more{
      text-align: right;
      &-text{
        width: 60rpx;
        height: 36rpx;
        background: rgba(255, 255, 255, 0.39);
        border-radius: 4rpx;
        font-size: 26rpx;
        font-weight: 400;
        line-height: 38rpx;
        color: #FFFFFF;
        text-align: center;
        display: inline-block;
      }
    }
  }
  .product{
    padding-left: 8rpx;
    box-sizing: border-box;
    margin-top: 40rpx;
    overflow-x: auto;
    display: flex;
    flex-wrap: nowrap;
    &-item{
      margin-left: 16rpx;
      display: flex;
      align-items: center;
      height: 120rpx;
      background: rgba(255, 255, 255, 1);
      border-radius: 8rpx;
      padding: 12rpx;
      box-sizing: border-box;
      position: relative;
      &-img{
        width: 96rpx;
        height: 96rpx;
        border-radius: 4rpx;
      }
      &-info{
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        height: 100%;
        width: 386rpx;
        margin-left: 12rpx;
        &-text{
          width: 386rpx;
          font-size: 28rpx;
          font-weight: 400;
          line-height: 42rpx;
          color: #333333;
        }
        &-price{
          font-size: 32rpx;
          font-weight: 400;
          line-height: 42rpx;
          color: #FF3333;
          text{
            font-size: 24rpx;
          }
        }
      }
      &-card{
        width: 48rpx;
        height: 48rpx;
        &-one{
          position: absolute;
          right: 24rpx;
          top: 50%;
          transform: translateY(-50%);
        }
      }
    }
  }
}
.video-desc{
  padding: 32rpx;
  box-sizing: border-box;
  background: rgba(0, 0, 0, 0.8);
  &-header{
    display: flex;
    align-items: center;
    &-img{
      width: 64rpx;
      height: 64rpx;
      border-radius: 50%;
      box-sizing: border-box;
      border: 2rpx solid rgba(255, 255, 255, 1);
    }
    &-name{
      font-size: 32rpx;
      font-weight: 700;
      line-height: 37.5rpx;
      color: rgba(255, 255, 255, 1);
      margin-left: 12rpx;
    }
  }
  &-content{
    font-size: 28rpx;
    font-weight: 400;
    line-height: 40rpx;
    color: rgba(255, 255, 255, 1);
  }
  &-op{
    display: flex;
    justify-content: flex-end;
    margin-top: 20rpx;
    &-text{
      font-size: 30rpx;
      font-weight: 400;
      line-height: 40rpx;
      color: rgba(229, 229, 229, 1);
    }
  }
}
/* #ifdef H5 */
.video-desc-father /deep/ .uni-popup__wrapper-box{
  border-radius: 0!important;
}
.video-desc-father /deep/ .bottom{
  padding-bottom: 0!important;
}
/* #endif */
</style>

