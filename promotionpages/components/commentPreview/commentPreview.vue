<!--评论展示组件-->
<template>
  <view class="comment">
    <view class="comment-count" v-if="comment_counts">共{{comment_counts}}条评论</view>
    <view class="comment-list">
      <view class="comment-list-item clearfix" v-for="(item,index) in list" :key="item.experience_comment_id">
        <view class="comment-list-item-one">
          <image :src="item.headimg" class="comment-list-item-one-head" @click="toSeedingHome(item.member_id)"></image>
          <view class="comment-list-item-one-info">
            <view class="comment-list-item-one-info-name" @click="toSeedingHome(item.member_id)">{{item.nickname}}<text class="comment-list-item-one-info-name-inner" v-if="item.is_author">作者</text></view>
            <view class="comment-list-item-one-info-content">{{item.comment_content}}</view>
            <view class="comment-list-item-one-info-date">{{item.time}}<text @click="toReply(index,null)">回复</text></view>
          </view>
        </view>
        <template v-if="item.reply.length>0">
          <template v-if="item.isShow">
            <view class="comment-list-item-two" v-for="(two,y) in item.reply" :key="two.experience_comment_id">
              <image :src="two.member_headimg" class="comment-list-item-two-head" @click="toSeedingHome(two.member_id)"></image>
              <view class="comment-list-item-two-info">
                <view class="comment-list-item-two-info-name" @click="toSeedingHome(two.member_id)">{{two.member_nickname}}<text class="comment-list-item-two-info-name-inner" v-if="two.is_author">作者</text></view>
                <view class="comment-list-item-two-info-content" v-if="two.reply_nickname">回复<text>{{ two.reply_nickname }}:</text>{{two.comment_content}}</view>
                <view class="comment-list-item-two-info-content" v-else>{{two.comment_content}}</view>
                <view class="comment-list-item-two-info-date">{{two.time}}<text @click="toReply(index,y)">回复</text></view>
              </view>
            </view>
          </template>
          <view class="comment-list-item-more" v-if="!item.isShow" @click="unfold(index,true)">展开{{item.reply.length}}条回复<text class="iconfont iconiconangledown"></text></view>
          <view class="comment-list-item-more" v-else @click="unfold(index,false)">收起<text class="iconfont iconiconangledown"></text></view>
        </template>
        <view class="comment-list-item-separate"></view>
      </view>
      <view class="comment-list-more" v-if="page<=page_count" @click="getData">{{loadMore ?　'加载中...' : '加载更多'}}</view>
    </view>
    <view class="comment-input" :style="commentInput.isFixed ? `position: fixed;left: 0;bottom: ${commentInput.bottom};` : ''">
      <view class="comment-input-text" @click="toComment" :style="!like.is_show ? 'width:100%;':''">留下你的精彩评论吧</view>
      <view class="comment-input-like" v-if="like.is_show" @click="changeLike" :style="like.is_like ? 'color:#FF3333;':''">
<!--        <text class="iconfont" :class="like.is_like ?　'iconlikefill':'iconlike' "></text>-->
        <image :src="like.is_like ? $util.img('public/static/youpin/two-like-yes.png') : $util.img('public/static/youpin/two-like-no.png')" class="comment-input-like-img"></image>
        <text>{{like.like_num}}</text>
      </view>
    </view>
    <view class="comment-popup" v-show="isShowPopup" @click.self="closePopup">
      <view class="comment-popup-inner" :style="{bottom:keyHeight}">
        <input type="text" placeholder="留下你的精彩评论吧" class="comment-popup-inner-input" placeholder-class="comment-popup-inner-input-placeholder"
          @focus="inputFocus"
          @blur="inputBlur"
          v-model="commentContent"
          :focus="focus"
          ref="commonetRef"
          :cursor-spacing="cursorSpacing"
          v-if="isShowPopup"
        />
        <image :src="$util.img('public/static/youpin/post.png')" @click.stop="postComment" class="comment-popup-inner-post"></image>
      </view>
    </view>
  </view>
</template>

<script>
import apiurls from "../../../common/js/apiurls";

export default {
  name: "commentPreview",
  props:{
    commentInput:{
      type:Object,
      default:function () {
          return {isFixed:true,bottom: 0};
      }
    },
    articleId:{
      type:[String,Number],
      required:true
    },
    like:{
      type:Object,
      default:function (){
        return{
          is_show:true,
          is_like:false,
          like_num:0
        }
      }
    }
  },
  watch:{
    comment_counts(val){
      this.$emit('updateCommentCounts',val);
    },
  },
  data(){
    return{
      inputWrapHeight: '0px',
      isShowPopup:false,
      focus:false,
      // placeholder:'留下你的精彩评论吧',
      reply_comment_id:null,
      reply_index:[],
      commentContent:'',
      systemInfo:{},
      page_size:10,
      page:1,
      page_count:1,
      list:[],
      comment_counts:0,
      loadMore:false,
      keyHeight:0,
      cursorSpacing:0,
    }
  },
  async created(){
    let {screenWidth} = uni.getSystemInfoSync();
    this.cursorSpacing=18/(screenWidth/375);
    await this.getData();
  },
  methods: {
    closePopup(){
      this.isShowPopup=false;
      this.focus=false;
    },
    changeLike(){
      this.$emit('changeLike',!this.like.is_like);
    },
    /**
     *
     * @param nickname {string} 评论人昵称
     * @param comment_id {String} 评论id
     */
    toComment(){
      // this.placeholder='留下你的精彩评论吧';
      this.reply_comment_id=null;
      this.reply_index=[];
      this.isShowPopup=true;
      this.focus=true;
    },
    toReply(one,two){
      let nickname='';
      let comment_id='';
      if(two!=null){
        nickname= this.list[one].reply[two].member_nickname;
        comment_id=this.list[one].reply[two].experience_comment_id;
      }else{
        nickname= this.list[one].nickname;
        comment_id=this.list[one].experience_comment_id;
      }
      this.reply_index=[one,two];
      this.placeholder=`回复 @${nickname}: `;
      this.reply_comment_id=comment_id;
      this.isShowPopup=true;
      this.focus=true;
    },
    inputFocus(e) {
      this.isShowPopup=true;
      this.focus=true;
      if(e.detail && e.detail.height){
        this.keyHeight=e.detail.height;
      }
      // this.systemInfo=wx.getSystemInfoSync();
      // let {screenHeight, screenWidth, statusBarHeight, windowHeight} = this.systemInfo;
      // let machineHeight = screenHeight - windowHeight; // 虚位高度
      // let dp = screenWidth / screenHeight;
    },
    inputBlur() {
      this.inputWrapHeight = 0;
      this.focus=false;
      this.keyHeight=0;
    },
    async getData(){
      if(this.page>this.page_count){
        return;
      }
      this.loadMore=true;
      let res = await this.$api.sendRequest({
        url: apiurls.commentListUrl,
        async: false,
        data: {
          page:this.page,
          user_share_experience_id:this.articleId
        }
      })
      if (res.code == 0) {
        this.comment_counts=res.data.comment_counts;
        if(res.data && Array.isArray(res.data.data)){
          this.page_count=res.data.last_page;
          this.list=this.list.concat(res.data.data);
          this.page+=1;
        }
      }
      this.loadMore=false;
    },
    async publishComment(){
      if(this.commentContent.trim()==''){
        this.$util.showToast({
          title:'请输入评论！',
          mask:true
        })
        return;
      }
      let res = await this.$api.sendRequest({
        url: apiurls.publishCommentUrl,
        async: false,
        data: {
          user_share_experience_id:this.articleId,
          content:this.commentContent
        }
      })
      if (res.code == 0) {
        this.$util.showToast({
          title:'发表评论成功',
          mask:true
        })
        this.commentContent='';
        this.list.unshift(res.data[0]);
        this.comment_counts+=1;
      }else{
        this.$util.showToast({
          title:res.message,
          mask:true
        })
      }
    },
    async commentReply(experience_comment_id){
      if(this.commentContent.trim()==''){
        this.$util.showToast({
          title:'请输入评论！',
          mask:true
        })
        return;
      }
      let res = await this.$api.sendRequest({
        url: apiurls.commentReplyUrl,
        async: false,
        data: {
          experience_comment_id,
          content:this.commentContent
        }
      })
      if (res.code == 0) {
        this.$util.showToast({
          title:'回复评论成功',
          mask:true
        })
        this.commentContent='';
        if(this.reply_index.length>1){
          let tmp=this.list[this.reply_index[0]];
          tmp.reply=tmp.reply.concat(res.data);
          this.$set(this.list,this.reply_index[0],tmp);
          this.comment_counts+=1;
        }
      }else{
        this.$util.showToast({
          title:res.message,
          mask:true
        })
      }
    },
    async postComment(){
      this.isShowPopup=false;
      this.focus=false;
      if(!uni.getStorageSync('token')){
        // this.$util.showToast({
        //   title:'请先登录在评论',
        //   mask:true
        // })
        this.$emit('toLogin');
        return;
      }
      if(this.reply_comment_id==null){
        await this.publishComment();
      }else{
        await this.commentReply(this.reply_comment_id);
      }
    },
    unfold(index,unfold){
      let item=this.list[index];
      item.isShow=unfold;
      this.$set(this.list,index,item);
    },
    toSeedingHome(id){
      this.$util.redirectTo('/promotionpages/seeding/seeding_home_page/seeding_home_page',{other_mid:id});
    }
  }
}
</script>

<style scoped lang="scss">
.comment{
  padding-left: 24rpx;
  box-sizing: border-box;
  padding-bottom: 100rpx;
  background-color: white;
  padding-top: 40rpx;
  &-count{
    font-size: 26rpx;
    font-weight: 400;
    line-height: 42rpx;
    color: #666666;
  }
  &-list{
    padding-bottom: 46rpx;
    box-sizing: border-box;
    &-item{
      margin-top: 24rpx;
      &-one{
        display: flex;
        justify-content: space-between;
        &-head{
          width: 68rpx;
          height: 68rpx;
          border-radius: 50%;
        }
        &-info{
          width: 642rpx;
          &-name{
            font-size: 26rpx;
            font-weight: 400;
            line-height: 42rpx;
            color: #999999;
            display: flex;
            align-items: center;
            &-inner{
              width: 64rpx;
              height: 36rpx;
              line-height: 36rpx;
              background: #FF3333;
              //background-color: red;
              border-radius: 20rpx;
              font-size: 22rpx;
              font-weight: 400;
              color: #ffffff;
              margin-left: 6rpx;
              display: block;
              text-align: center;
            }
          }
          &-content{
            font-size: 28rpx;
            font-weight: 400;
            line-height: 42rpx;
            color: #333333;
            margin-top: 4rpx;
            word-break: break-word;
            padding-right: 24rpx;
            box-sizing: border-box;
          }
          &-date{
            display: flex;
            align-items: center;
            font-size: 24rpx;
            font-weight: 400;
            line-height: 42rpx;
            color: #999999;
            margin-top: 4rpx;
            text{
              font-size: 22rpx;
              font-weight: 400;
              line-height: 42rpx;
              color: #7096D8;
              margin-left: 6rpx;
            }
          }
        }
      }
      &-two{
        display: flex;
        width: 642rpx;
        margin-top: 24rpx;
        float: right;
        &-head{
          width: 44rpx;
          height: 44rpx;
          border-radius: 50%;
        }
        &-info{
          margin-left: 10rpx;
          &-name{
            font-size: 26rpx;
            font-weight: 400;
            line-height: 42rpx;
            color: #999999;
            display: flex;
            align-items: center;
            &-inner{
              width: 64rpx;
              height: 36rpx;
              line-height: 36rpx;
              background: #FF3333;
              //background-color: red;
              border-radius: 20rpx;
              font-size: 22rpx;
              font-weight: 400;
              color: #ffffff;
              margin-left: 6rpx;
              display: block;
              text-align: center;
            }
          }
          &-content{
            font-size: 28rpx;
            font-weight: 400;
            line-height: 42rpx;
            color: #333333;
            margin-top: 4rpx;
            padding-right: 24rpx;
            box-sizing: border-box;
            text{
              color: #999999;
              margin: 0 10rpx;
            }
          }
          &-date{
            display: flex;
            align-items: center;
            font-size: 24rpx;
            font-weight: 400;
            line-height: 42rpx;
            color: #999999;
            margin-top: 4rpx;
            text{
              font-size: 22rpx;
              font-weight: 400;
              line-height: 42rpx;
              color: #7096D8;
              margin-left: 6rpx;
            }
          }
        }
      }
      &-more{
        font-size: 24rpx;
        font-weight: 400;
        line-height: 42rpx;
        color: #0B055E;
        float: right;
        width: 642rpx;
        display: flex;
        align-items: center;
        margin-top: 24rpx;
      }
      &-separate{
        width: 642rpx;
        height: 1px;
        background-color: #eeeeee;
        float: right;
        margin-top: 16rpx;
      }
    }
    &-more{
      font-size: 26rpx;
      font-weight: 400;
      line-height: 42rpx;
      color: #999999;
      text-align: center;
      margin-top: 24rpx;
    }
  }
  &-input{
    padding: 8rpx 0;
    padding-left: 24rpx;
    padding-right: 24rpx;
    box-sizing: border-box;
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100vw;
    background-color: white;
    &-text{
      font-size: 32rpx;
      font-weight: 400;
      line-height: 42rpx;
      color: #666666;
      width: 560rpx;
      height: 84rpx;
      background: rgba(246, 246, 246, 1);
      border-radius: 42rpx;
      display: flex;
      align-items: center;
      padding-left: 24rpx;
      box-sizing: border-box;
    }
    &-like{
      font-size: 28rpx;
      font-weight: 600;
      line-height: 40rpx;
      color: #333333;
      margin-left: 16rpx;
      display: flex;
      align-items: center;
      &-img{
        width: 56rpx;
        height: 56rpx;
        margin-right: 4rpx;
      }
      .iconfont{
        font-size: 48rpx;
        margin-right: 8rpx;
      }
    }
  }
  &-popup{
    width: 100vw;
    height: 100vh;
    background: rgba(0, 0, 0, 0.39);
    position: fixed;
    left: 0;
    top: 0;
    z-index: 10;
    &-inner{
      position: fixed;
      left: 0;
      bottom: 0;
      width: 750rpx;
      height: 120rpx;
      background: rgba(255, 255, 255, 1);
      display: flex;
      justify-content: center;
      align-items: center;
      &-input{
        width: 606rpx;
        height: 84rpx;
        background: rgba(246, 246, 246, 1);
        border-radius: 42rpx;
        margin-right: 24rpx;
        padding-left: 24rpx;
        box-sizing: border-box;
        &-placeholder{
          font-size: 32rpx;
          font-weight: 400;
          color: #666666;
        }
      }
      &-post{
        width: 72rpx;
        height: 72rpx;
      }
    }
  }
}
</style>
