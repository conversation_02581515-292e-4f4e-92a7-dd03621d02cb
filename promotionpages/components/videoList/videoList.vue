<!--videoList.vue //主要组件-->
<template>
  <view class="videoList">
    <view class="swiper-box">
      <swiper class="swiper"  :vertical="true" :circular="true" @change="changeplay" @touchstart="touchStart" @touchend="touchEnd">
        <swiper-item v-for="(item,index) in videos" :key="item.id" :data-id="item.id">
          <view class="swiper-item"  style="color: #000000;">
            <video-player @changeClick='changeClick' ref="player" :video="item" :index="index" :key="item.id"></video-player>
          </view>
          <view class="listleftbox">
            <list-left :show-data="item"></list-left>
          </view>
          <view class="listrightbox">
<!--            <list-right ref="right" :show-data="item" @toComment="toComment" @changeLike="changeLike" @toShare="toShare"></list-right>-->
          </view>
        </swiper-item>
      </swiper>
    </view>

    <view ref="commentPopup" class="comment-popup" v-if="showCommnetPopup">
      <view class="comment-popup-content" @click.stop>
        <comment-preview :article-id="videos[page].id" v-if="videos[page].id" :like="{is_show:false}" @updateCommentCounts="update_comment_counts" @toLogin="toLogin"></comment-preview>
        <image :src="$util.img('public/static/youpin/clase-x.png')" alt="" class="comment-popup-content-close" @click="closeComment"></image>
      </view>
    </view>
  </view>
</template>

<script>
import videoPlayer from '../videoPlayer/videoPlayer'
import listLeft from '../listLeft/listLeft'
import listRight from '../listRight/listRight'
import commentPreview from "../commentPreview/commentPreview";
import UniIcons from "../../../components/uni-icons/uni-icons.vue";
var time = null
export default {
  props:['list'],
  components:{
    UniIcons,
    videoPlayer,
    listLeft,
    listRight,
    commentPreview
  },
  data() {
    return {
      videos:[],
      pageStatrY:0,
      pageEndY:0,
      page:0,
      current_index: 0,
      page_count: 3,  // 配置页面显示视频的条数， 解决ios视频渲染条数过多，导致内存溢出的问题
      showCommnetPopup:false
    };
  },
  //监听传过来的list
  watch:{
    //当list发生改变 就触发这个方法 所以用watch
    list(){
      // this.videos = this.list
    }
  },
  created(){
    this.getThreeElements(this.list,this.page);
  },
  methods:{
    getThreeElements(array, startIndex) {
      const arrayLength = array.length;
      const elements = [];
      for (let i = 0; i < this.page_count; i++) {
        const currentIndex = (startIndex + i) % arrayLength;
        elements.push(array[currentIndex]);
      }
      this.videos = elements;
    },
    closeComment(){
      this.showCommnetPopup=false;
    },
    changeClick(){
      //双击点赞 调用子组件listright的方法
      this.$refs.right[0].change()
    },
    //上下滑动触发事件
    changeplay(res){
      clearTimeout(time)
      this.current_index = res.detail.current
      try{
        for (let i = 0; i < this.$refs.player.length; i++) {
          this.$refs.player[i].pause()
        }
      }catch (e) {
        console.log(e)
      }
      if(this.pageStatrY < this.pageEndY){
        if(this.page<=0){
          this.page = this.list.length-1
        }else{
          this.page = this.page - 1
        }
        if(this.current_index == this.page_count-1){
          this.getThreeElements(this.list,this.page)
        }
        console.log('向上滑动',this.page)
        // #ifdef MP-WEIXIN
        this.$nextTick(()=>{
          setTimeout(()=>{
            this.$refs.player[this.current_index].player()
          }, 200)
        })
        // #endif
        this.pageStatrY=0
        this.pageEndY=0
        this.$emit('previousVideo',this.page);
      }else{
        if(this.page>=this.list.length-1){
          this.page = 0
        }else{
          this.page = this.page + 1
        }
        if(this.current_index ==0){
          this.getThreeElements(this.list,this.page)
        }
        console.log('向下滑动',this.page)
        // #ifdef MP-WEIXIN
        this.$nextTick(()=>{
          setTimeout(()=>{
            this.$refs.player[this.current_index].player()
          }, 200)
        })
        // #endif
        this.pageStatrY=0
        this.pageEndY=0
        this.$emit('nextVideo', this.page);
      }
    },
    //获取向下滑动的值
    touchStart(res){

      this.pageStatrY = res.changedTouches[0].pageY
    },
    //获取向上滑动的值
    touchEnd(res){
      this.pageEndY = res.changedTouches[0].pageY
    },
    toComment(){
      this.showCommnetPopup=true;
    },
    changeLike(){
      this.$emit('changeLike');
    },
    toShare(){
      this.$emit('toShare');
    },
    toLogin(){
      this.$emit('toLogin');
    },
    update_comment_counts(count){
     let tmp= this.videos[this.page];
     tmp.comment_num=count;
    }
  }
}
</script>

<style lang="scss" scoped>
.videoList{
  height: calc(100vh - 126rpx - env(safe-area-inset-bottom));
  width: 100%;
}
.swiper-box{
  height: 100%;
  width: 100%;
}
.swiper{
  height: 100%;
  width: 100%;
}
.swiper-item{
  height: 100%;
  width: 100%;
  z-index:19;
}
.title{
  color: #FFFFFF;
}
/deep/.listleftbox{
  z-index:6;
  transform: translateZ(1000px);
  position: absolute;
  bottom: 0;
  left: 0;
  box-sizing: border-box;
}
/deep/.listrightbox{
  z-index:6;
  position: absolute;
  bottom: 420rpx;
  right: 18rpx;
  color: #FFFFFF;
  transform: translateZ(1000px);
}

.comment-popup{
  width: 100vw;
  height: 100vh;
  background: rgba(0, 0, 0, 0.39);
  position: fixed;
  left: 0;
  top: 0;
  &-content{
    width: 100%;
    height: 70vh;
    position: absolute;
    left: 0;
    bottom: 0;
    overflow-y: auto;
    background-color: white;
    border-radius: 20rpx 20rpx 0px 0px;
    &-close{
      position: absolute;
      right: 24rpx;
      top: 24rpx;
      width: 32rpx;
      height: 32rpx;
    }
  }
}
</style>

