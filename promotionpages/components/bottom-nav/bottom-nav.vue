<template>
  <view>
    <view class="bottom-nav" :class="{'bottom-nav-dark' : theme == 'dark'}">
      <view class="bottom-nav-left" @click="$refs.goodsListPopup.open()" :class="{'bottom-nav-left-dark' : theme == 'dark'}" v-if="dataInfo.goods && dataInfo.goods.length">
        <image :src="$util.img('public/static/youpin/goods-add.png')" class="bottom-nav-left-icon"/>商品({{(dataInfo.goods && dataInfo.goods.length) || 0}})
      </view>
      <view v-else></view>
      <view class="bottom-nav-right">
        <view class="bottom-nav-right-one" @click="changeLike">
          <uni-icons :type="dataInfo.is_like ? 'heart-filled':'heart'" size="20" :color="theme == 'dark' ? dataInfo.is_like ? 'var(--custom-brand-color)' :'#fff' :dataInfo.is_like ? 'var(--custom-brand-color)' :''"/>
          <text class="bottom-nav-right-one-num" :class="{'bottom-nav-right-one-num-dark':theme == 'dark'}">{{dataInfo.like_num || '点赞'}}</text>
        </view>
        <!-- #ifdef MP-WEIXIN -->
        <button class="bottom-nav-right-one" open-type="share" :class="{'bottom-nav-right-one-dark':theme == 'dark'}"><uni-icons type="redo" size="20" :color="theme == 'dark' ? '#fff': ''"/>
          <text class="bottom-nav-right-one-num" :class="{'bottom-nav-right-one-num-dark':theme == 'dark'}">分享</text></button>
        <!-- #endif -->
        <!-- #ifdef H5 -->
        <button class="bottom-nav-right-one" @click="openSharePopup" :class="{'bottom-nav-right-one-dark':theme == 'dark'}"><uni-icons type="redo" size="20" :color="theme == 'dark' ? '#fff': ''"/>
          <text class="bottom-nav-right-one-num" :class="{'bottom-nav-right-one-num-dark':theme == 'dark'}">分享</text></button>
        <!-- #endif -->
      </view>
    </view>
    <uni-popup ref="goodsListPopup" type="bottom" class="goods-list-pop-father" :class="{'goods-list-pop-father-dark' : theme == 'dark'}">
      <view class="goods-list-pop" :class="{'goods-list-pop-dark' : theme == 'dark'}">
        <view class="goods-list-pop-header" :class="{'goods-list-pop-header-dark' : theme == 'dark'}">
          <text class="goods-list-pop-header-left" :class="{'goods-list-pop-header-left-dark' : theme == 'dark'}">商品推荐</text>
          <text class="iconfont icondelete goods-list-pop-header-right" @click="$refs.goodsListPopup.close()"></text>
        </view>
        <view class="goods-list-pop-list">
          <view class="goods-list-pop-list-item" v-for="(item,index) in dataInfo.goods" @click="$util.toProductDetail(item)">
            <view class="goods-list-pop-list-item-left">
              <image :src="item.goods_image" alt="" class="goods-list-pop-list-item-img" @error="errorFun(dataInfo.goods,index)"/>
              <view class="goods-list-pop-list-item-info">
                <view class="goods-list-pop-list-item-info-text overtext-hidden-one">{{item.goods_name}}</view>
                <view class="goods-list-pop-list-item-info-price"><text>￥</text>{{item.retail_price}}</view>
              </view>
            </view>
            <text class="goods-list-pop-list-item-buy">购买</text>
          </view>
        </view>
      </view>
    </uni-popup>
  </view>
</template>

<script>
import UniIcons from "../../../components/uni-icons/uni-icons.vue";
import UniPopup from "../../../components/uni-popup/uni-popup.vue";

export default {
  name: "bottom-nav",
  props:{
     dataInfo:{
       type:Object,
       default:function(){
         return {}
       }
     },
    theme:{
      type:String,
      default: 'white', //white,dark
    }
  },
  data(){
    return{
    }
  },
  components: {UniPopup, UniIcons},
  created(){
    console.log('dataInfo',this.dataInfo)
  },
  methods:{
    openSharePopup(){
      this.$emit('toShare');
    },
    changeLike(is_like){
      this.$emit('changeLike');
    },
    errorFun(dataList,index){
      dataList[index] && (dataList[index].goods_image = this.$util.getDefaultImage().default_goods_img);
      this.$forceUpdate();
    }
  }
}
</script>

<style scoped lang="scss">
.bottom-nav{
  width: 100%;
  height: calc(126rpx + env(safe-area-inset-bottom));
  padding: 0 20rpx;
  padding-bottom: env(safe-area-inset-bottom);
  border-top: 2rpx solid rgba(245, 245, 245, 1);
  box-sizing: border-box;
  position: fixed;
  left: 0;
  bottom: 0;
  background-color: white;
  display: flex;
  justify-content: space-between;
  align-items: center;
  &-dark{
    border-top: 2rpx solid rgba(0,0,0,1);
    background-color: rgba(0,0,0,1);
  }
  &-left{
    width: 194rpx;
    height: 64rpx;
    border-radius: 100rpx;
    background: var(--custom-brand-color-10);
    font-size: 28rpx;
    font-weight: 400;
    line-height: 40rpx;
    color: var(--custom-brand-color);
    display: flex;
    justify-content: center;
    align-items: center;
    &-dark{
      background-color: rgba(255,255,255,0.1);
      color: white;
    }
    &-icon{
      width: 32rpx;
      height: 32rpx;
      margin-right: 12rpx;
    }
  }
  &-right{
    display: flex;
    align-items: center;
    &-one{
      display: flex;
      align-items: center;
      background-color: transparent;
      margin: 0;
      padding: 0;
      box-sizing: border-box;
      &:not(:first-child){
        margin-left: 30rpx;
      }
      &-dark{
        color: white;
      }
      &-num{
        font-size: 28rpx;
        font-weight: 400;
        line-height: 40rpx;
        color: rgba(56, 56, 56, 1);
        margin-left: 6rpx;
        &-dark{
          color: white;
        }
      }
    }
  }
}
/* #ifdef H5 */
.goods-list-pop-father /deep/ .uni-popup__mask{
  bottom: unset;
  height: calc(100vh - 126rpx - env(safe-area-inset-bottom));
}
.goods-list-pop-father /deep/ .uni-popup__wrapper.uni-bottom{
  border-radius: 40rpx 40rpx 0 0;
  bottom: calc(126rpx + env(safe-area-inset-bottom));
}
.goods-list-pop-father-dark /deep/ .uni-popup__wrapper.uni-bottom{
  background: transparent;
}
.goods-list-pop-father /deep/ .uni-popup__wrapper-box{
  border-radius: 40rpx 40rpx 0 0!important;
  background: transparent!important;
}
.goods-list-pop-father /deep/ .bottom{
  padding-bottom: 0!important;
}
/* #endif */
.goods-list-pop{
  padding: 0 20rpx 26rpx 20rpx;
  box-sizing: border-box;
  position: relative;
  &-dark{
    background-color: rgba(0,0,0,0.5);
  }
  &-header{
    display: flex;
    justify-content: space-between;
    align-items: center;
    box-sizing: border-box;
    position: sticky;
    left: 0;
    top: 0;
    height: 98rpx;
    padding-top: 26rpx;
    z-index: 1;
    background-color: white;
    border-radius: 40rpx 40rpx 0 0;
    &-dark{
      background: transparent;
    }
    &-left{
      font-size: 32rpx;
      font-weight: 700;
      line-height: 46.34rpx;
      color: rgba(56, 56, 56, 1);
      &-dark{
        color: white;
      }
    }
    &-right{
      font-size: 40rpx;
      color: rgba(229, 229, 229, 1);
    }
  }
  &-list{
    &-item{
      width: 100%;
      display: flex;
      justify-content: space-between;
      align-items: center;
      height: 144rpx;
      border-radius: 20rpx;
      background: rgba(247, 247, 247, 1);
      position: relative;
      margin-top: 20rpx;
      padding: 0 10rpx;
      box-sizing: border-box;
      &-left{
        display: flex;
        align-items: center;
      }
      &-img{
        width: 120rpx;
        height: 120rpx;
        border-radius: 20rpx;
        display: block;
      }
      &-info{
        height: 100%;
        width: 380rpx;
        margin-left: 20rpx;
        display: flex;
        flex-direction: column;
        justify-content: center;
        &-text{
          font-size: 30rpx;
          font-weight: 400;
          line-height: 43.44rpx;
          color: rgba(34, 34, 34, 1);
        }
        &-price{
          font-size: 32rpx;
          font-weight: 700;
          line-height: 37.5rpx;
          color: var(--custom-brand-color);
          margin-top: 14rpx;
          text{
            font-size: 24rpx;
          }
        }
      }
      &-buy{
        width: 160rpx;
        height: 64rpx;
        border-radius: 100px;
        background: var(--custom-brand-color);
        display: flex;
        justify-content: center;
        align-items: center;
        font-size: 32rpx;
        font-weight: 400;
        line-height: 32rpx;
        color: rgba(255, 255, 255, 1);
      }
      &-card{
        width: 48rpx;
        height: 48rpx;
        &-one{
          position: absolute;
          right: 24rpx;
          top: 50%;
          transform: translateY(-50%);
        }
      }
    }
  }
}
</style>
