<!--videoPlayer.vue //存放视频的组件-->
<template>
  <view class="videoPlayer">
    <video
        :id="`myVideo_${video.id}`"
        class="video"
        :controls="false"
        :src="video.share_resource"
        :loop="true"
        :autoplay="autoplay"
        :poster="video.image"
        @click="click"></video>
  </view>
</template>

<script>
var timer=null
export default {
  props:['video','index'],
  data() {
    return {
      play:false,
      dblClick:false,
      autoplay:false
    };
  },
  mounted(){
    this.videoContext=uni.createVideoContext(`myVideo_${this.video.id}`,this);
    this.atuo();
  },
  methods:{
    click(){
      if(this.play===false){
        this.playThis()
      }else{
        this.pause()
      }
      // clearTimeout(timer)
      // this.dblClick=!this.dblClick
      // timer=setTimeout(()=>{
      //   if(this.dblClick){ //判断是单击 即为true
      //     //单击
      //     if(this.play===false){
      //       this.playThis()
      //     }else{
      //       this.pause()
      //     }
      //   }else{
      //     //双击
      //     this.$emit('changeClick') //向父组件传递一个事件
      //   }
      //   this.dblClick=false //点击后重置状态 重置为false
      // },300)
    },
    player(){
      //从头播放视频
      this.videoContext.play()
      this.play=true
    },
    pause(){
      //暂停视频
      this.videoContext.pause()
      this.play=false
    },
    playThis(){
      //播放当前视频
      this.videoContext.play()
      this.play=true
    },
    //首个视频自动播放
    atuo(){
      //首个视频自动播放
      if(this.index===0){
		  // #ifdef MP-WEIXIN
		  // this.autoplay=true
        this.player();
		  // #endif

      }
    }
  },
  created() {
    // this.atuo()
  },
}

</script>

<style lang="scss" scoped>
.videoPlayer{
  height: 100%;
  width:100%;
  background-color: black;
}
.video{
  height: calc(100% - env(safe-area-inset-bottom));
  width:100%;
}
</style>


