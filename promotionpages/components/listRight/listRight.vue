<!--listRight.vue //页面右组件的内容-->
<template>
  <view class="listright">
    <view class="item">
      <image :src="showData.is_like ? $util.img('public/static/youpin/video-like-red.png'):$util.img('public/static/youpin/video-like.png')" alt="" class="item-img" @click="changeLike"/>
      <text class="item-text">{{showData.like_num ?　showData.like_num : '点赞'}}</text>
    </view>
    <view class="item" @click="toComment">
      <image :src="$util.img('public/static/youpin/video-comment.png')" alt="" class="item-img"/>
      <text class="item-text">{{showData.comment_num ? showData.comment_num : '评论'}}</text>
    </view>
    <!-- #ifdef MP-WEIXIN -->
    <button class="item" @click="toShare">
      <image :src="$util.img('public/static/youpin/video-share.png')" alt="" class="item-img"/>
      <text class="item-text">分享</text>
    </button>
    <!-- #endif -->
    <!-- #ifdef H5 -->
      <view class="item" @click="toShare">
        <image :src="$util.img('public/static/youpin/video-share.png')" alt="" class="item-img"/>
        <text class="item-text">分享</text>
      </view>
    <!-- #endif -->
  </view>
</template>

<script>
export default {
  props:{
    showData:{
      type:Object
    }
  },
  data() {
    return {
      show:true,
    };
  },
  methods:{
    toComment(){
      this.$emit('toComment');
    },
    changeLike(){
      this.$emit('changeLike');
    },
    toShare(){
      this.$emit('toShare');
    }
  }
}
</script>

<style lang="scss" scoped>
.listright{
  width: 112rpx;
  margin: 0 auto;
  .item{
    margin: 0;
    width: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    background-color: transparent;
    &:not(:first-child){
      margin-top: 20rpx;
    }
    &-img{
      width: 112rpx;
      height: 100rpx;
    }
    &-text{
      font-size: 24rpx;
      font-weight: 400;
      line-height: 38rpx;
      color: #FFFFFF;
      text-shadow: 0px 3rpx 6rpx rgba(0, 0, 0, 0.16);
    }
  }
}
</style>


