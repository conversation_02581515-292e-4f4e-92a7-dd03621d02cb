<template>
	<view class="special-offers">
		<image class="red-img" :src="$util.img('public/static/youpin/jubilant.png')" mode=""></image>
		<view class="position-box">
			<view class="content-info">
				<image class="content-img" :src="$util.img('public/static/youpin/discountcard.png')" mode=""></image>
				<view class="content-box">
					<view class="content-discount">
						<view class="num">{{Number(info.discount)}}</view>
						<view class="discount">折</view>
					</view>
					<view class="des">{{info.at_least_cn}}</view>
				</view>
			</view>
			<view class="count-down">
				<view class="count-down-time" v-if="activeStatus">
					距离结束还剩
					<text class="wbgc">{{timeData.day}}</text>天
					<text class="wbgc">{{timeData.hour}}</text>时
					<text class="wbgc">{{timeData.minute}}</text>分
					<text class="wbgc">{{timeData.second}}</text>秒
				</view>
				<view v-else class="active-end">
					{{activityName}}
				</view>
			</view>
			<view class="product-tit">
				<image class="fairworks" :src="$util.img('public/static/youpin/fairworks.png')" mode=""></image>
				<text>以下商品均可参与本优惠活动</text>
				<image class="fairworks" :src="$util.img('public/static/youpin/fairworks.png')" mode=""></image>
			</view>
			<!-- 商品列表 -->
			<view class="product-content" v-if="productList.length != 0">
				<view class="product-box">
					<view class="product-list" v-for="(item, index) in productList" :key="index"
						@click="toProductDetail(item)">
						<view class="img-box">
							<image class="img-one expose_goods_index" :data-expose_goods_sku="item.sku_id" :src="item.goods_image"
								@error="imageError(index)" mode=""></image>
							<image :src="$util.img('public/static/youpin/product-sell-out.png')" class="over"
								v-if="item.goods_stock==0"></image>
						</view>
						<view class="info-box">
							<view class="title">{{item.goods_name}}</view>
							<view class="sold hide-sales">已售{{item.sale_num}}件</view>
							<view class="price">
								<view><text>￥</text>{{item.retail_price}}</view>
								<view>￥{{item.market_price}}</view>
							</view>
						</view>
					</view>
				</view>
			</view>
			<view class="empty" v-else>
				<image :src="$util.img('public/static/youpin/empty_coupon_good.png')"></image>
				<view>商品备货中，请稍候...</view>
			</view>
		</view>
	</view>
</template>

<script>
	import wx_expose_goods from '@/common/mixins/wx_expose_goods.js';
	import system from "@/common/js/system";
	import apiurls from "@/common/js/apiurls";

	export default {
		mixins:[wx_expose_goods],
		data() {
			return {
				info: {},
				productList: [],
				page: 1,
				timer: null,
				timeData: {
					day: "0",
					hour: "0",
					minute: "0",
					second: "0"
				},
				multiple_discount_id:null,
				activeStatus:true,
				activityName:'活动未开始'
			}
		},
		onLoad(options) {
			this.multiple_discount_id = options.multiple_discount_id
		},
		async onShow() {

			await system.wait_staticLogin_success();
			await this.getInfo()

		},
		onHide() {
			this.timer && this.timer.stop()
		},
		onUnload() {
			this.timer && this.timer.stop()
		},
		onReachBottom(){
			this.getProduct()
		},
		methods: {
			getInfo() {
				this.$api.sendRequest({
					url: apiurls.multipleDiscountinfo,
					data: {
						multiple_discount_id:this.multiple_discount_id,
					},
					success:(res)=>{
						if(res.code == 0){
							this.info = res.data

							uni.setNavigationBarTitle({
								title: res.data.multiple_discount_name
							});

							this.getProduct()
							this.runTime()
						}
					}
				})
			},
			// 倒计时
			runTime() {
				if(this.info.start_time*1000 > new Date().getTime()){
					this.activeStatus = false
					return;
				}
				let totaltime = this.info.over_time*1000 - new Date().getTime()

				this.timer = new this.$util.AdjustingInterval(
					() => {
						totaltime -= 1000
						if (totaltime > 0) {
							this.timeData.day = Math.floor(totaltime / (1000 * 60 * 60 * 24))
							this.timeData.hour = Math.floor(totaltime / (1000 * 60 * 60) % 24)
							this.timeData.minute = Math.floor(totaltime / (1000 * 60) % 60)
							this.timeData.second = Math.floor((totaltime / 1000) % 60)
						}
					},
					1000,
					totaltime,
					() => {
						this.activeStatus = false
						this.activityName = '活动已结束'
						// 定时器执行完回调
						this.timeData.day = "0";
						this.timeData.hour = "0";
						this.timeData.minute = "0";
						this.timeData.second = "0";
					}
				)
				this.timer.start()
			},
			async getProduct() {
				try {
					let url = this.info.use_scenario != 1? apiurls.multipleDiscountgoodsList:apiurls.goodsListUrl
					let res = await this.$api.sendRequest({
						url,
						async: false,
						data: {
							multiple_discount_id:this.multiple_discount_id,
							page_size: 10,
							page: this.page,
						},
					});

					if (res.code == 0) {
						let data = res.data.list
						this.page ++;
						if(data.length>0){
							this.productList = this.productList.concat(data)
						}
					}

				} catch {

				}
			},
			toProductDetail(item) {
				let url = item.applet_url;
				if (url) {
					this.$util.redirectTo(url+'&tagShowStatus=false');
				}
			},
			imageError(index) {
				if (this.productList[index]) this.productList[index].goods_image = this.$util.getDefaultImage()
					.default_goods_img;

				this.$forceUpdate();
			},
			getSharePageParams() {
        let share_data=this.$util.unifySharePageParams('/promotionpages/special_offers/special_offers',this.info.multiple_discount_name,
            '',{multiple_discount_id:this.multiple_discount_id},'')
        return share_data;
			},
		},
		// 分享
		onShareAppMessage(res) {
			let {
				title,
				link,
          imageUrl
			} = this.getSharePageParams()

			return this.$buriedPoint.pageShare(link, imageUrl, title);
		},
		// 分享到微信
		onShareTimeline(res) {
			let {
				title,
				imageUrl,
				query
			} = this.getSharePageParams()
			return {
				title,
        imageUrl,
				query,
				success: res => {},
				fail: res => {}
			};
		}
	}
</script>

<style lang="scss" scoped>
	.special-offers {
		width: 100%;
		min-height: 100vh;
		background-color: #EF0129;

		.red-img {
			width: 100%;
			height: 140rpx;
		}

		.position-box {
			position: relative;
			top: -80rpx;
			left: 0;

			.content-info {
				width: 100%;
				box-sizing: border-box;
				padding: 0rpx 48rpx;

				.content-img {
					width: 100%;
					height: 150rpx;
				}

				.content-box {
					width: 100%;
					height: 166rpx;
					position: absolute;
					top: 0rpx;
					left: 0rpx;
					display: flex;
					align-items: center;
					box-sizing: border-box;
					padding: 0rpx 48rpx 0rpx 98rpx;

					.content-discount {
						width: 180rpx;
						display: flex;
						align-items: center;
						.num {
							color: #8B572A;
							font-size: 64rpx;
							font-weight: bold;
						}

						.discount {
							font-size: 32rpx;
							color: #8B572A;
							margin: 15rpx 0rpx 0rpx 4rpx;
						}
					}

					.des {
						width: 390rpx;
						font-size: 32rpx;
						color: #8B572A;
						font-weight: bold;
						margin-left: 35rpx;
						overflow: hidden;
						text-overflow: ellipsis;
						white-space: nowrap;
					}
				}
			}

			.count-down {
				width: calc(100%-48rpx);
				height: 68rpx;
				line-height: 68rpx;
				background: rgba(#000000, 0.1);
				border-radius: 34rpx;
				display: flex;
				align-items: center;
				justify-content: center;
				margin: 26rpx 24rpx 40rpx 24rpx;
				.count-down-time {
					color: white;
					font-size: 24rpx;
					.wbgc {
						min-width: 36rpx;
						height: 36rpx;
						display: inline-block;
						line-height: 36rpx;
						font-size: 24rpx;
						font-weight: bold;
						text-align: center;
						color: #EF0129;
						background-color: white;
						border-radius: 8rpx;
						margin: 0 4rpx;
					}
				}
				.active-end {
					color: white;
					font-size: 24rpx;
				}
			}

			.product-tit {
				width: 100%;
				font-size: 32rpx;
				font-weight: bold;
				color: white;
				text-align: center;
				margin-bottom: 17rpx;

				text {
					margin: 0 20rpx;
				}

				.fairworks {
					width: 64rpx;
					height: 32rpx;
				}
			}

			.product-content {
				width: 100%;
				box-sizing: border-box;
				padding: 0 24rpx;

				.product-box {
					display: flex;
					flex-wrap: wrap;
					margin-bottom: 20rpx;

					.product-list {
						width: 343rpx;
						background-color: #fff;
						border-radius: 10rpx;
						overflow: hidden;
						margin-top: 15rpx;

						&:nth-of-type(2n) {
							margin-left: 16rpx;
						}

						.img-box {
							position: relative;
							.img-one {
								display: inherit;
								width: 100%;
								height: 343rpx;
							}
							.over {
								width: 120rpx;
								height: 120rpx;
								position: absolute;
								top: 50%;
								left: 50%;
								transform: translate(-50%, -50%);
							}
						}

						.info-box {
							padding: 14rpx 16rpx 10rpx;

							.title {
								font-size: 26rpx;
								font-weight: 500;
								line-height: 40rpx;
								text-overflow: ellipsis;
								display: -webkit-box;
								-webkit-box-orient: vertical;
								-webkit-line-clamp: 2;
								overflow: hidden;
								min-height: 80rpx;
							}

							.sold {
								font-size: 24rpx;
								line-height: 36rpx;
								margin-top: 24rpx;
								color: #999;
							}

							.price {
								display: flex;
								align-items: center;
								margin-top: 4rpx;

								&>view:first-child {
									font-size: 36rpx;
									color: #F2270C;
									line-height: 48rpx;

									text {
										font-size: 26rpx;
									}
								}

								&>view:last-child {
									font-size: 24rpx;
									line-height: 48rpx;
									color: #999999;
									text-decoration: line-through;
									margin-left: 8rpx;
								}
							}
						}
					}
				}
			}

			.empty {
				display: flex;
				flex-direction: column;
				align-items: center;
				padding: 100rpx 0 0;

				image {
					width: 402rpx;
					height: 282rpx;
				}

				view {
					font-size: 32rpx;
					line-height: 44rpx;
					color: white;
					margin-top: 42rpx;
				}
			}
		}
	}
</style>
