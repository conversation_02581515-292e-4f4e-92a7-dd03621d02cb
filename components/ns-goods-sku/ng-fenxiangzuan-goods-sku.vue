<template>
	<view class="goods-sku" @touchmove.prevent.stop>
		<uni-popup ref="skuPopup" type="bottom" class="sku-layer" @change="popupChange()">
			<view class="sku-info" :style="{ height: systemInfo.windowHeight * 1 + 'px' }" v-if="goodsDetail">
				<view class="header">
					<view class="img-wrap"><image :src="$util.img(goodsDetail.sku_image)" @error="imageError()" /></view>
					<view class="main">
						<view class="goodsName">{{goodsDetail.goods_name}}</view>
						<view class="price-wrap">
							<view class="price ns-text-color ns-font-size-lg"><text class="prize-icon">￥</text>{{ goodsDetail.show_price || goodsDetail.goods_price }}</view>
							<view class="market_price"><text class="prize-icon">￥</text>{{ goodsDetail.market_price }}</view>
						</view>
					</view>

					<view class="sku-close iconfont iconclose" @click="closeSkuPopup()"></view>
				</view>
				<view class="body-item">
					<scroll-view scroll-y class="wrap">
						<view class="sku-list-wrap" v-for="(item, index) in goodsDetail.goods_spec_format" :key="index">
							<text class="title ns-font-size-base">{{ item.spec_name }}</text>
							<view
								v-for="(item_value, index_value) in item.value"
								:key="index_value"
								:class="{
									selected: item_value['selected'] || skuId == item_value.sku_id,
									disabled: item_value['disabled'] || (!item_value['selected'] && disabled)
								}"
								class="items ns-border-color-gray ns-bg-color-gray ns-font-size-base"
								@click="change(item_value.sku_id, item_value.spec_id)"
							>
								<image v-if="item_value.image" :src="$util.img(item_value.image)" @error="valueImageError(index, index_value)" />
								<text>{{ item_value.spec_value_name }}</text>
							</view>
						</view>
					</scroll-view>
				</view>
				<view class="footer">
						<button @click="confirm()" type="primary" v-if="goodsDetail.goods_stock && goodsDetail.goods_stock != 0">确定</button>
						<button type="primary" class="btn-disabled" v-else>确定</button>
				</view>
			</view>
		</uni-popup>
	</view>
</template>

<script>
import uniPopup from '@/components/uni-popup/uni-popup-sku.vue';
import htmlParser from '@/common/js/html-parser';
import apiurls from '@/common/js/apiurls.js'
// 商品SKU
export default {
	name: 'ng-fenxiangzuan-goods-sku',
	components: {
		uniPopup
	},
	props: {
		goodsDetail: {
			type: Object,
			default: null
		},
		disabled: {
			type: Boolean,
			default: false
		},
		entrance:{
			type:String,
			default:''
		},
	},
	data() {
		return {
			systemInfo: {}, //系统信息
			number: 1,
			token: '',
			btnSwitch: false, //提交按钮防止重复提交
			type: '', //join_cart：加入购物车，buy_now：立即购买
			callback: null, //回调
			skuId: 0,
			//是否开启预览，0：不开启，1：开启
			preview: 0,
			minNumber:0,
			entranceType:this.entrance
		};
	},
	created() {
		this.systemInfo = uni.getSystemInfoSync();
		this.token = uni.getStorageSync('token');
	},
	watch: {
		goodsDetail(newData, oldData) {
			this.skuId = newData.sku_id;
		}
	},
	methods: {
		//分类加入购物车，重置购买数量
		popupChange() {
			// this.number = 1;
		},
		show(type, callback) {
			console.log('type',type)
			if(type == 'choose_spec'){
				this.entranceType = 'choose_spec'
			}
			wx.nextTick(()=>{
				this.$refs.skuPopup.open();
				this.type = type;
				this.callback = callback;
				this.skuId = this.goodsDetail.sku_id;
				this.preview = this.goodsDetail.preview || 0;
			})
		},
		hide() {
			this.$refs.skuPopup.close();
		},
		change(skuId, spec_id) {
			if (this.disabled) return;
			this.btnSwitch = false;
			this.skuId = skuId;
			// 清空选择
			for (var i = 0; i < this.goodsDetail.goods_spec_format.length; i++) {
				var sku = this.goodsDetail.goods_spec_format[i];
				for (var j = 0; j < sku.value.length; j++) {
					// 排除当前点击的规格值
					if (spec_id == this.goodsDetail.goods_spec_format[i].value[j].spec_id) {
						this.goodsDetail.goods_spec_format[i].value[j].selected = false;
					}
				}
			}
			this.getGoodsSkuInfo();
		},
		// 获取普通商品详情
		getGoodsSkuInfo() {
			let params = {
				sku_id: this.skuId
			}
			let res = this.$api.sendRequest({
				url: '/api/goodssku/info',
				data: params,
				success: res => {
					let data = res.data;
					if (data != null) {
						this.goodsSkuDetail = data;
						this.dealData();
						this.btnSwitch = false;
						this.$emit('refresh', this.goodsSkuDetail);
					} else {
						this.$util.redirectTo('/pages/index/index/index', {}, 'reLaunch');
					}
				},
				fail: res => {
					this.btnSwitch = false;
					this.$util.redirectTo('/pages/index/index/index', {}, 'reLaunch');
				}
			});
		},
		dealData() {
			this.goodsSkuDetail.sku_images = this.goodsSkuDetail.sku_images.split(',');

			this.goodsSkuDetail.show_price = this.goodsSkuDetail.price;
			this.goodsSkuDetail.goods_stock = this.goodsSkuDetail.stock
			// 当前商品SKU规格
			if (this.goodsSkuDetail.sku_spec_format) this.goodsSkuDetail.sku_spec_format = JSON.parse(this.goodsSkuDetail.sku_spec_format);

			// 商品SKU格式
			if (this.goodsSkuDetail.goods_spec_format) this.goodsSkuDetail.goods_spec_format = JSON.parse(this.goodsSkuDetail.goods_spec_format);
			this.$forceUpdate();
		},
		
		//提交
		confirm() {
			if (this.preview) {
				this.$util.showToast({
					title: '预览商品无法购买'
				});
				return;
			}
			
			if (this.goodsDetail.goods_stock == 0) {
				this.$util.showToast({
					title: '商品已售罄'
				});
				return;
			}
			if(this.type =='choose_spec'){
				this.$refs.skuPopup.close();
			}else if(this.type == 'buy_now'){
				this.callback()
				this.$refs.skuPopup.close();
			}
		},
		closeSkuPopup() {
			this.$refs.skuPopup.close();
		},
		imageError() {
			this.goodsDetail.sku_image = this.$util.getDefaultImage().default_goods_img;
			this.$forceUpdate();
		},
		valueImageError(index, index_value) {
			this.goodsDetail.goods_spec_format[index].value[index_value].image = this.$util.getDefaultImage().default_goods_img;
			this.$forceUpdate();
		}
	}
};
</script>
<style lang="scss">
.sku-layer .sku-info {
	height: 75vh !important;
	position: relative;
	z-index: 999;
}

.sku-layer .sku-info .header {
	padding: 36rpx 0 36rpx 236rpx;
	/* #ifdef MP-ALIPAY */
	padding: 50rpx 0 50rpx 300rpx;
	/* #endif */
	position: relative;
	// border-bottom: 1px solid rgba(0, 0, 0, 0.1);
}

.sku-layer .sku-info .header .img-wrap {
	width: 180rpx;
	height: 180rpx;
	position: absolute;
	// top: -56rpx;
	/* #ifdef MP-ALIPAY */
	top: 20rpx;
	/* #endif */
	left: 20rpx;
	border-radius: 8rpx;
	overflow: hidden;
	// border: 1px solid rgba(0, 0, 0, 0.1);
	// padding: 2rpx;
	// background-color: #fff;
	line-height: 208rpx;
}

.sku-layer .sku-info .header .img-wrap image {
	width: 100%;
	height: 100%;
}

.sku-layer .sku-info .main {
	font-size: 12px;
	line-height: 40rpx;
	padding-right: 40rpx;
	height: 180rpx;
	position: relative;
}
.sku-layer .sku-info .main .price-wrap{
	position: absolute;
	bottom: 0;
	display: flex;
	align-items: center;
}
.sku-layer .sku-info .main .goodsName{
	display: -webkit-box;
	-webkit-box-orient: vertical;
	-webkit-line-clamp: 2;
	overflow: hidden;
	width: 90%;
}
.sku-layer .sku-info .main .price .prize-icon{
	font-size: 26rpx;
	font-weight: normal;
}
.sku-layer .sku-info .main .price {
	word-wrap: break-word;
	font-weight: bold;
	line-height: 30rpx;
}
.sku-layer .sku-info .main .market_price {
	word-wrap: break-word;
	font-size: 24rpx;
	text-decoration:line-through;
	color: #999;
	margin-left: 8rpx;
}
.sku-layer .sku-info .main .sku-name {
	display: -webkit-box;
	-webkit-line-clamp: 2;
	-webkit-box-orient: vertical;
	height: 90rpx;
	overflow: hidden;
}

.sku-layer .sku-info .main .sku-name text {
	margin-right: 10rpx;
}

.sku-layer .sku-info .sku-close {
	position: absolute;
	top: 24rpx;
	right: 24rpx;
	width: 32rpx;
	height: 32rpx;
	font-size: 20rpx;
	background: #999;
	border-radius: 50%;
	line-height: 34rpx;
	text-align: center;
	color:#fff
}

.sku-layer .body-item {
	padding: 0 30rpx;
	height: calc(100% - 330rpx);
	box-sizing: border-box;
	overflow: scroll;
}

.sku-layer .body-item .wrap {
	height: calc(100% - 116rpx);
}

.sku-layer .body-item .sku-list-wrap {
	// border-bottom: 1px solid rgba(0, 0, 0, 0.1);
	padding-bottom: 20rpx;
}

.sku-layer .body-item .sku-list-wrap .title {
	font-weight: 400;
	padding: 26rpx 0;
	margin: 0;
	display: block;
}

.sku-layer .body-item .sku-list-wrap .items {
	position: relative;
	display: inline-block;
	border: 1px solid;
	padding:0 20rpx;
	border-radius: 27rpx;
	margin: 0 10rpx 10rpx 0;
	font-size: 26rpx;
	line-height: 54rpx;
}

.sku-layer .body-item .sku-list-wrap .items.disabled {
	border: 1px dashed;
}

.sku-layer .body-item .sku-list-wrap .items image {
	height: 48rpx;
	width: 48rpx;
	border-radius: 4rpx;
	margin-right: 10rpx;
	display: inline-block;
	vertical-align: middle;
}

.sku-layer .body-item .number-wrap .number-line {
	padding: 20rpx 0;
	line-height: 72rpx;
	border-bottom: 1px solid rgba(0, 0, 0, 0.1);
}

.sku-layer .body-item .number-wrap .title {
	font-weight: 400;
}

.sku-layer .body-item .number-wrap .limit-txt {
}

.sku-layer .body-item .number-wrap .number {
	height: 72rpx;
	border-radius: 6rpx;
	float: right;
}

.sku-layer .body-item .number-wrap .number button {
	display: inline-block;
	line-height: 64rpx;
	height: 68rpx;
	width: 60rpx;
	font-size: 48rpx;
	box-sizing: content-box;
	border: 1px solid;
	padding: 0;
	margin: 0;
	border-radius: 0;
}

.sku-layer .body-item .number-wrap .number button.decrease {
	border-right: 1px solid #fff !important;
}

.sku-layer .body-item .number-wrap .number button.increase {
	border-left: 1px solid #fff !important;
}

.sku-layer .body-item .number-wrap .number button:after {
	border-radius: 0;
	border: none;
}

.sku-layer .body-item .number-wrap .number input {
	display: inline-block;
	line-height: 64rpx;
	height: 68rpx;
	width: 72rpx;
	text-align: center;
	font-weight: 700;
	border: 1px solid;
	margin: 0;
	padding: 0;
	vertical-align: top;
}

.sku-layer .footer {
	height: 96rpx;
	width: 100%;
	padding: 0 7.5%;
	box-sizing: border-box;
	position: absolute;
	// bottom: 60rpx;
	bottom: 0;
	color: #fff;
	z-index: 1;
	display: flex;
	justify-content: space-between;
	align-items: center;
	button{
		width: 100%;
		padding: 0;
		margin: 0;
	}
	

}
.cart-footer{
	padding-bottom: 0 !important;
}

.position-bottom {
	bottom: 98rpx !important;
}
</style>
