<template>
	<view class="goods-sku" @touchmove.prevent.stop>
		<uni-popup ref="skuPopup" type="bottom" class="sku-layer" @change="popupChange">
			<view class="sku-info" :style="{ height: systemInfo.windowHeight * 1 + 'px' }" v-if="goodsDetail">
				<!-- 头部内容 -->
				<view class="header">
					<view class="img-wrap">
						<image :src="$util.img(goodsDetail.sku_image)" @error="imageError()" />
					</view>
					<view class="main">
						<view class="goodsName">
              <text class="tag-cross-border" v-if="goodsDetail.goods_class == 5">跨境
                <image :src="$util.img(goodsDetail.cbec_origin_icon)" mode="aspectFit" class="tag-cross-border--flag" v-if="goodsDetail.cbec_origin_icon"></image>
                <text class="tag-cross-border--full" v-else>{{goodsDetail.cbec_origin_name[0]}}</text>
              </text>
              {{goodsDetail.sku_name}}
            </view>
						<!-- <view class="price-distributor"
							v-if="type!='pintuan' && type!='seckill' && goodsDetail.is_shop_owner">
							<text>分销商价</text><text>￥{{goodsDetail.vip_price}}</text></view> -->
						<view class="price-wrap">
              <template v-if="entrance=='combo_buy'">
                <view class="price ns-font-size-lg"><text class="prize-icon">￥</text>{{ goodsDetail.combo_price }}</view>
                <view class="cheap_price">已省{{ goodsDetail.cheap_price }}元</view>
              </template>
              <template v-else>
                <view class="price ns-font-size-lg"><text class="prize-icon">￥</text>{{ goodsDetail.show_price }}</view>
                <view class="market_price"><text class="prize-icon">￥</text>{{ goodsDetail.market_price }}</view>
              </template>

						</view>
						<!-- <view class="stock">库存{{ goodsDetail.stock }}{{ goodsDetail.unit }}</view> -->
						<!-- <view class="sku-name ns-font-size-sm">
							<template v-if="goodsDetail.sku_spec_format">
								已选择：
								<text v-for="(item, index) in goodsDetail.sku_spec_format" :key="index">{{ item.spec_value_name }}</text>
							</template>
						</view> -->
					</view>

					<view class="sku-close iconfont iconclose" @click="closeSkuPopup()"></view>
				</view>
				<!-- 优惠券 -->
				<view class="coupon-content" v-if="type!='pintuan' && type!='seckill' && type!='maidou_spec' && type!='combo_buy' && goodsDetail.discount_content && goodsDetail.discount_content.length>0">
					<scroll-view scroll-x="true" class="content-box-scroll">
						<block v-for="(item,index) in goodsDetail.discount_content" :key="index">
							<view class="content-box-items-red" v-if="item.describe != ''">{{item.describe}}</view>
							<view class="content-box-items">{{item.content}}</view>
						</block>
					</scroll-view>
					<!-- <view class="coupon-btn" @click="discountOpen">
						领券 <text class="iconfont iconright"></text>
					</view> -->
				</view>
				<!-- sku -->
				<view class="body-item">
					<scroll-view scroll-y class="wrap">
						<view class="sku-list-wrap" v-for="(item, index) in goodsDetail.goods_spec_format" :key="index">
							<text class="title ns-font-size-base">{{ item.spec_name }}</text>
							<view v-for="(item_value, index_value) in item.value" :key="index_value" :class="{
									selected: item_value['selected'] || skuId == item_value.sku_id,
									disabled: item_value['disabled'] || (!item_value['selected'] && disabled)
								}" class="items ns-border-color-gray ns-bg-color-gray ns-font-size-base"
								@click="change(item_value.sku_id, item_value.spec_id)">
								<image v-if="item_value.image" :src="$util.img(item_value.image)"
									@error="valueImageError(index, index_value)" />
								<text>{{ item_value.spec_value_name }}</text>
							</view>
						</view>

						<view class="number-wrap" v-if="entrance!='cart'">
							<view class="number-line">
								<text class="title ns-font-size-base">购买数量</text>
								<text class="limit-txt ns-font-size-sm"
									v-if="limitNumber > 0">（每人限购{{ limitNumber }}件）</text>
								<text class="limit-txt ns-font-size-sm" v-if="minNumber > 0">（{{ minNumber }}件起购）</text>
								<view class="number">
									<button type="default" class="decrease ns-border-color-gray" :class="{'disabled' : limitBuy}"
										@click="changeNum('-')">-</button>
									<input type="number" class="uni-input ns-border-color-gray ns-font-size-sm"
										@blur="blur" v-model="number" placeholder="0" :disabled="limitBuy"
										@input="keyInput(false)" />
									<button type="default" class="increase ns-border-color-gray" :class="{'disabled' : limitBuy}"
										@click="changeNum('+')">+</button>
								</view>
							</view>
						</view>
					</scroll-view>
				</view>
				<!-- 底部按钮 -->
				<view class="footer" v-if="entranceType=='choose_spec'">
					<view class="footer-btn">
						<button @click="confirm('join_cart')" class="btn-cart"
							v-if="goodsDetail.goods_class !=5 && goodsDetail.stock && goodsDetail.stock != 0 && !skuIsDisabledStatus[goodsDetail.sku_id]">加入购物车</button>
						<button class="btn-disabled" v-else>加入购物车</button>
					</view>
					<view class="footer-btn">
						<button @click="confirm('buy_now')" type="primary" class="btn-confirm"
							v-if="goodsDetail.stock && goodsDetail.stock != 0 && !skuIsDisabledStatus[goodsDetail.sku_id]">确定</button>
						<button type="primary" class="btn-disabled" v-else>确定</button>
					</view>
				</view>
				<view class="footer cart-footer" :style="{bottom:entrance=='cart'?'60rpx':'0rpx'}" v-else
					@click="confirm()">
					<button type="primary" class="btn-confirm" v-if="goodsDetail.stock && goodsDetail.stock != 0 && !skuIsDisabledStatus[goodsDetail.sku_id]">确定</button>
					<button type="primary" class="btn-disabled" v-else>确定</button>
				</view>
			</view>
		</uni-popup>
		<ns-goods-discount ref="discountPopup"/>
	</view>
</template>

<script>
	import uniPopup from '@/components/uni-popup/uni-popup-sku.vue';
	import htmlParser from '@/common/js/html-parser';
	import apiurls from '@/common/js/apiurls.js'
	import nsGoodsDiscount from '@/components/ns-goods-discount/ns-goods-discount.vue'
	// 商品SKU
	export default {
		name: 'ns-goods-sku',
		components: {
			uniPopup,
			nsGoodsDiscount
		},
		props: {
			goodsDetail: {
				type: Object,
				default: null
			},
			pintuanInfo: {
				type: Object,
				default: null
			},
			disabled: {
				type: Boolean,
				default: false
			},
			entrance: {
				type: String,
				default: ''
			},
			periodId: {
				type: String,
				default: ''
			},
			isShowNumChoose: {
				type: Boolean,
				default: true
			},
			limitBuy: { // 是否控制不可以修改数量
				type: Boolean,
				default: false
			},
      comboId:{  //商品组合套餐的id
        type:String,
      }
		},
		data() {
			return {
				systemInfo: {}, //系统信息
				number: 1,
				token: '',
				btnSwitch: false, //提交按钮防止重复提交
				type: '', //join_cart：加入购物车，buy_now：立即购买
				callback: null, //回调
				skuId: 0,
				pintuanId: 0, // 拼团id
				limitNumber: 0, // 限购
				// isShowNumChoose: true, //是否隐藏数量按钮
				//是否开启预览，0：不开启，1：开启
				preview: 0,
				minNumber: 0,
				entranceType: this.entrance,
				IsNotSkip: true, //选择规格后是否跳转页面
				goodsSkuDetail: {},
        skuIsDisabledStatus:{}, //sku禁止情况
        seckill_price: null,  // 把商品详情传入秒杀的销售存起来，应对多sku切换时，显示回这个价格，秒杀价格是跟spu
			};
		},
		created() {
			this.systemInfo = uni.getSystemInfoSync();
			this.token = uni.getStorageSync('token');
			if(this.goodsDetail && this.goodsDetail.goods_spec_format){
			  for (let i = 0; i < this.goodsDetail.goods_spec_format.length; i++) {
				for (let j = 0; j < this.goodsDetail.goods_spec_format[i].value.length; j++) {
				  this.$set(this.skuIsDisabledStatus,this.goodsDetail.goods_spec_format[i].value[j].sku_id,this.goodsDetail.goods_spec_format[i].value[j].disabled);
				}
			  }
			}
		},
		watch: {
			goodsDetail(newData){
				if(newData){
					this.skuId = newData.sku_id;
					if(newData.numControl){
						this.number = 1
					}
				}
			}
		},
		methods: {
			// 弹起活动折扣弹窗
			discountOpen() {
			  this.$refs.discountPopup.open()
			},
			//分类加入购物车，重置购买数量
			popupChange(e) {
				// this.number = 1;
				if (!e.show) {
        		this.$emit("closeModel");
      			}
			},
      // 设置购买限制
      setLimitNumber(){
        if (this.type == 'pintuan' && this.goodsDetail.pintuan_id) {
          this.limitNumber = this.goodsDetail.buy_num;
        } else if (this.type == 'groupbuy' && this.goodsDetail.groupbuy_id) {
          this.number = this.goodsDetail.buy_num;
          this.minNumber = this.goodsDetail.buy_num;
        } else if (this.type == 'seckill') {
          this.limitNumber = this.goodsDetail.buy_num;
        }else if(this.type=='combo_buy'){
          this.limitNumber = this.goodsDetail.buy_nums;
        }
      },
			show(type, callback) {
				if (type == 'choose_spec') {
					this.entranceType = 'choose_spec'
				}
				wx.nextTick(() => {
					this.$refs.skuPopup.open();
					this.$emit("openModel"); // 固定页面禁止上下滚动
					this.type = type;
					this.callback = callback;
					this.skuId = this.goodsDetail.sku_id;
					this.preview = this.goodsDetail.preview || 0;
          this.setLimitNumber()
          // 保存第一次传入的秒杀价格
          if(this.type == 'seckill' && this.seckill_price == null){
            this.seckill_price = this.goodsDetail.show_price
          }
				})
			},
			hide() {
				this.$refs.skuPopup.close();
			},
			// 切换sku
			change(skuId, spec_id) {
				if (this.disabled) return;
        if(skuId == this.skuId) return;
				this.btnSwitch = false;
				this.skuId = skuId;
				// 清空选择
				for (var i = 0; i < this.goodsDetail.goods_spec_format.length; i++) {
					var sku = this.goodsDetail.goods_spec_format[i];
					for (var j = 0; j < sku.value.length; j++) {
						// 排除当前点击的规格值
						if (spec_id == this.goodsDetail.goods_spec_format[i].value[j].spec_id) {
							this.goodsDetail.goods_spec_format[i].value[j].selected = false;
						}
					}
				}

				if (this.goodsDetail.pintuan_id) {
					// 拼团商品信息
					this.getPintuanGoodsSkuInfo();
				} else if (this.goodsDetail.groupbuy_id) {
					// 团购商品信息
					this.getGroupbuyGoodsSkuInfo();
				}if(this.type=='combo_buy'){
          this.getComboSkuInfo();
        } else {
					this.getGoodsSkuInfo();
				}
			},
			// 获取普通商品详情
			getGoodsSkuInfo() {
				let params = {
					sku_id: this.skuId
				}
				// 查看是否是分销客分享页面，检测share_member_id,当用户离开分销客分享详情页，share_member_id会地洞清除
				var share_member_id = this.$store.state.share_member_id
				if (share_member_id) params.share_member_id = share_member_id;
				if (this.number>1) params.nums = this.number;
				let res = this.$api.sendRequest({
					url: '/api/goodssku/info',
					data: params,
					success: res => {
						let data = res.data;
						if (data != null && data instanceof Object) {
							this.goodsSkuDetail = data;
							this.dealData();

							// 限时折扣
							if (this.goodsSkuDetail.promotion_type == 1) {
								this.goodsSkuDetail.discountTimeMachine = this.$util.countDown(this
									.goodsSkuDetail.end_time - res.timestamp);
							}
              // 切换sku或者加减数量时，使用商品详情传入的销售价格
              if(this.type == 'seckill' && this.seckill_price){
                this.goodsSkuDetail.show_price = this.seckill_price
                this.goodsSkuDetail.price = this.seckill_price
              }
							this.btnSwitch = false;
							this.$emit('refresh', this.goodsSkuDetail);
						} else {
							this.$util.redirectTo('/pages/index/index/index', {}, 'reLaunch');
						}
					},
					fail: res => {
						this.btnSwitch = false;
						this.$util.redirectTo('/pages/index/index/index', {}, 'reLaunch');
					}
				});
			},
			// 获取拼团商品详情
			getPintuanGoodsSkuInfo() {
				let res = this.$api.sendRequest({
					url: '/pintuan/api/goods/info',
					data: {
						sku_id: this.skuId,
						pintuan_id: this.goodsDetail.pintuan_id
					},
					success: res => {
						let data = res.data;
						if (data != null) {
							this.goodsSkuDetail = data;
							this.dealData();
							this.goodsSkuDetail.show_price = this.goodsDetail.group_id > 0 ? this
								.goodsSkuDetail.promotion_price : this.goodsSkuDetail.pintuan_price;
							this.goodsSkuDetail.save_price =
								this.goodsSkuDetail.price - this.goodsSkuDetail.show_price > 0 ? (this
									.goodsSkuDetail.price - this.goodsSkuDetail.show_price).toFixed(2) : 0;

							//拼团倒计时
							if (this.goodsSkuDetail.end_time - res.timestamp > 0) {
								this.goodsSkuDetail.timeMachine = this.$util.countDown(this.goodsSkuDetail
									.end_time - res.timestamp);
							} else {
								this.$util.showToast({
									title: '活动已结束'
								});
								setTimeout(() => {
									this.$util.redirectTo(
										'/pages/goods/detail/detail', {
											sku_id: this.goodsSkuDetail.sku_id
										},
										'redirectTo'
									);
								}, 1000);
							}
							this.btnSwitch = false;
							this.$emit('refresh', this.goodsSkuDetail);
						} else {
							this.$util.redirectTo('/pages/index/index/index', {}, 'reLaunch');
						}
					},
					fail: res => {
						this.btnSwitch = false;
						this.$util.redirectTo('/pages/index/index/index', {}, 'reLaunch');
					}
				});
			},
			// 获取团购商品详情
			getGroupbuyGoodsSkuInfo() {
				let res = this.$api.sendRequest({
					url: '/groupbuy/api/goods/info',
					data: {
						sku_id: this.skuId,
						id: this.goodsDetail.groupbuy_id
					},
					success: res => {
						let data = res.data;
						if (data != null) {
							this.goodsSkuDetail = data;
							this.dealData();
							this.goodsSkuDetail.show_price = this.goodsDetail.groupbuy_price;
							this.goodsSkuDetail.save_price =
								this.goodsSkuDetail.price - this.goodsSkuDetail.show_price > 0 ? (this
									.goodsSkuDetail.price - this.goodsSkuDetail.show_price).toFixed(2) : 0;

							//团购倒计时
							if (this.goodsSkuDetail.end_time - res.timestamp > 0) {
								this.goodsSkuDetail.timeMachine = this.$util.countDown(this.goodsSkuDetail
									.end_time - res.timestamp);
							} else {
								this.$util.showToast({
									title: '活动已结束'
								});
								setTimeout(() => {
									this.$util.redirectTo(
										'/pages/goods/detail/detail', {
											sku_id: this.goodsSkuDetail.sku_id
										},
										'redirectTo'
									);
								}, 1000);
							}

							this.btnSwitch = false;
							this.$emit('refresh', this.goodsSkuDetail);
						} else {
							this.$util.redirectTo('/pages/index/index/index', {}, 'reLaunch');
						}
					},
					fail: res => {
						this.btnSwitch = false;
						this.$util.redirectTo('/pages/index/index/index', {}, 'reLaunch');
					}
				});
			},

      //获取商品组合套餐中商品详情
      getComboSkuInfo(){
        let params = {
          sku_id: this.skuId,
          combo_id:this.comboId
        }
        let res = this.$api.sendRequest({
          url: this.$apiUrl.comboSkuInfoUrl,
          data: params,
          success: res => {
            let data = res.data;
            if (data != null && data instanceof Object) {
              this.goodsSkuDetail = data;
              this.dealData();

              this.btnSwitch = false;
              this.$emit('refresh', this.goodsSkuDetail);
            } else {
              this.$util.redirectTo('/pages/index/index/index', {}, 'reLaunch');
            }
          },
          fail: res => {
            this.btnSwitch = false;
            this.$util.redirectTo('/pages/index/index/index', {}, 'reLaunch');
          }
        });
      },
			dealData() {
				this.goodsSkuDetail.sku_images = this.goodsSkuDetail.sku_images ? this.goodsSkuDetail.sku_images.split(
					',') : [];

				this.goodsSkuDetail.show_price = this.goodsSkuDetail.discount_price;

				// 当前商品SKU规格
				if (this.goodsSkuDetail.sku_spec_format) this.goodsSkuDetail.sku_spec_format = JSON.parse(this
					.goodsSkuDetail.sku_spec_format);

				// 商品SKU格式
				if (this.goodsSkuDetail.goods_spec_format) this.goodsSkuDetail.goods_spec_format = JSON.parse(this
					.goodsSkuDetail.goods_spec_format);
				this.$forceUpdate();

				this.keyInput(true);
			},
			// 设置数量
			changeNum(tag) {
				if (!this.isShowNumChoose) return
				if (this.goodsDetail.stock == 0) return;

				var stock = this.goodsDetail.stock;
				var min = 1;

				if (this.type == 'pintuan' && this.goodsDetail.pintuan_id) {
					//限购数量大于库存总数取库存
					if (this.goodsDetail.buy_num > this.goodsDetail.stock) {
						stock = this.goodsDetail.stock;
					} else {
						stock = this.limitNumber;
					}
					// min = stock;
				} else if (this.type == 'groupbuy' && this.goodsDetail.groupbuy_id) {
					//限购数量大于库存总数取库存
					if (this.goodsDetail.buy_num > this.goodsDetail.stock) {
						stock = this.goodsDetail.stock;
					} else {
						stock = this.limitNumber;
					}
					//最低购买数量
					min = this.limitNumber;
				}else if(this.type == 'seckill'){
          //限购数量大于库存总数取库存
          if (this.goodsDetail.buy_num > this.goodsDetail.stock) {
            stock = this.goodsDetail.stock;
          } else {
            stock = this.limitNumber;
          }
        }
				if (tag == '+') {
					// 加
          if(!this.limitBuy){ //限购
            if (this.number < stock) {
              this.number++;
            } else {
              return;
            }
          }else{
            return;
          }
				} else if (tag == '-') {
					// 减
          if(!this.limitBuy) { //限购
            if (this.number > min) {
              this.number -= 1;
            } else {
              return;
            }
          }else{
            return;
          }
				}
        this.getGoodsSkuInfo();
				// if(this.goodsDetail.discount_content && this.goodsDetail.discount_content.length>0){
				// 	let list = JSON.parse(JSON.stringify(this.goodsDetail.discount_content))
				// 	let arr=[];
				// 	list.map((it,index) => {
				// 		if(it.indexOf('分销立减') > -1){
				// 			it = `分销立减${(Number(it.match(/\d+(.\d+)?/g)[0])*this.number).toFixed(2)}元`
				// 		}
				// 		arr.push(it)
				// 	})
				// 	this.goodsDetail.discount_content = arr
				// }
			},
			blur() {
				let newNumber = parseInt(this.number);
				this.number = 0;
				setTimeout(() => {
					this.number = newNumber;
          this.getGoodsSkuInfo();
				}, 0);
			},
			//输入数量
			keyInput(flag, callback) {

				setTimeout(() => {
					var stock = this.goodsDetail.stock;

					// 库存为0
					if (this.goodsDetail.stock == 0) {
						this.number = 0;
						return;
					}

					// 防止空
					if (flag && this.number.length == 0) this.number = 1;

					// 防止输入0和负数、非法输入
					if (flag && (this.number <= 0 || isNaN(this.number))) this.number = 1;

					if (this.type == 'pintuan' && this.goodsDetail.pintuan_id && this.number > this.limitNumber) {
						//限购数量大于库存总数取库存
						this.number = this.limitNumber;
					} else if (this.type == 'groupbuy' && this.goodsDetail.groupbuy_id && this.number < this.limitNumber) {
						//最低购买数量
						this.number = this.limitNumber;
					}else if(this.type == 'seckill' && this.number > this.limitNumber){
            this.number = this.limitNumber;
          } else if(this.type == 'combo_buy' && this.number > this.limitNumber){
            this.number = this.limitNumber;
          }else if (this.number > stock) {
						this.number = stock;
					}

					if (flag) this.number = parseInt(this.number);
					if (callback) callback();
				}, 0);
			},

			//提交
			confirm(type=null,showpop=true) {
				if (this.preview) {
					this.$util.showToast({
						title: '预览商品无法购买'
					});
					return;
				}
				if (this.token == '') {
					this.$util.showToast({
						title: '请登录',
						success: () => {
							// this.$util.redirectTo('/pages/login/login/login');
						}
					});
					return;
				}
				if (type) {
					this.type = type;
				}
        this.setLimitNumber()
        // 保存第一次传入的秒杀价格
        if(this.type == 'seckill' && this.seckill_price == null){
          this.seckill_price = this.goodsDetail.show_price
        }
				//纠正数量
				this.keyInput(true, () => {
					if (this.goodsDetail.stock == 0) {
						this.$util.showToast({
							title: '商品已售罄'
						});
						return;
					}

					if (this.number.length == 0 || this.number == 0) {
						this.$util.showToast({
							title: '购买数量不能为0'
						});
						return;
					}

					if (this.btnSwitch) return;
					this.btnSwitch = true;

					if (this.type == 'join_cart') {
						this.$api.sendRequest({
							url: '/api/cart/add',
							data: {
								site_id: this.goodsDetail.site_id,
								sku_id: this.goodsDetail.sku_id,
								num: this.number
							},
							success: res => {

								var data = res.data;
								let {
									code,
									message
								} = res;
								if (data > 0) {
									if(showpop){
										this.$util.showToast({
											title: '加入购物车成功'
										});
									}
									this.number = 1;
									if (this.callback) this.callback(true);
								} else if (code < 0 && message) {
									// 错误提示
									this.$util.showToast({
										title: message
									});
									if (this.callback) this.callback(false);
								}
								this.$refs.skuPopup.close();
								this.btnSwitch = false;
							},
							fail: res => {
								this.$refs.skuPopup.close();
								this.btnSwitch = false;
							}
						});
					} else if (this.type == 'buy_now' || this.type == 'maidou_spec' || this.type == 'newhand_spec') {
						var data = {
							sku_id: this.goodsDetail.sku_id,
							num: this.number
						};
						// 是否限制数量
						if(this.limitBuy) {
							data['limitType'] = true
						}
						uni.setStorage({
							key: 'orderCreateData',
							data: data,
							success: () => {
								this.$util.redirectTo('/pages/order/payment/payment');
								this.$refs.skuPopup.close();
								this.btnSwitch = false;
							}
						});

					} else if (this.type == 'seckill') {
						// 秒杀
						var data = {
							sku_id: this.goodsDetail.sku_id,
							num: this.number,
							seckill_id: this.goodsDetail.seckill_id
						};

						uni.setStorage({
							key: 'seckillOrderCreateData',
							data: data,
							success: () => {
								this.$util.redirectTo('/promotionpages/new_seckill/payment/payment');
								this.btnSwitch = false;
							}
						});
					} else if (this.type == 'pintuan') {
						// 拼团
						var data = {
							pintuan_goods_id: this.pintuanInfo.pintuan_goods_id,
							pintuan_id: this.pintuanInfo.pintuan_id,
							sku_id: this.goodsDetail.sku_id,
							goods_id: this.goodsDetail.goods_id,
							num: this.number
						};
						uni.setStorage({
							key: 'pintuanOrderCreateData',
							data: data,
							success: () => {
								if (this.IsNotSkip) {
									this.$util.redirectTo('/promotionpages/pintuan/payment/payment');
								}
								this.$refs.skuPopup.close();
								this.btnSwitch = false;
							}
						});
					} else if (this.type == 'topic') {
						// 专题
						var data = {
							topic_goods_id: this.goodsDetail.id,
							num: this.number
						};

						uni.setStorage({
							key: 'topicOrderCreateData',
							data: data,
							success: () => {
								this.$util.redirectTo('/promotionpages/topics/payment/payment');
								this.btnSwitch = false;
							}
						});
					} else if (this.type == 'groupbuy') {
						// 团购
						var data = {
							groupbuy_id: this.goodsDetail.groupbuy_id,
							sku_id: this.skuId,
							num: this.number
						};

						uni.setStorage({
							key: 'groupbuyOrderCreateData',
							data: data,
							success: () => {
								this.$util.redirectTo('/promotionpages/groupbuy/payment/payment');
								this.btnSwitch = false;
							}
						});
					} else if (this.type == 'update_cart') {
						//修改购物车规格
						this.$api.sendRequest({
							url: apiurls.updateCartUrl,
							data: {
								cart_id: this.goodsDetail.cart_id,
								sku_id: this.goodsDetail.sku_id,
								num: this.number
							},
							success: res => {
								if (res.code == 0) {
									if (this.callback) this.callback();
								}
								this.$refs.skuPopup.close();
								this.btnSwitch = false;
							},
							fail: res => {
								this.$refs.skuPopup.close();
								this.btnSwitch = false;
							}
						});
					} else if (this.type == 'periodbuy') { // 周期购
						var data = {
							sku_id: this.skuId,
							num: this.number
						};

						uni.setStorage({
							key: 'orderCreateData',
							data: data,
							success: () => {
								this.$util.redirectTo(
									`/pages/order/cycle_payment/cycle_payment?period_id=${this.periodId}`
									);
								this.$refs.skuPopup.close();
								this.btnSwitch = false;
							}
						});
					} else if(this.type=='combo_buy'){  //组合商品购买
            let tmp_goodsSkuDetail={};
            if(Object.keys(this.goodsSkuDetail).length<1 ){  //没切换过sku
              tmp_goodsSkuDetail=this.goodsDetail;
            }else{  //切换过sku
              if(this.goodsSkuDetail.goods_id==this.goodsDetail.goods_id){  //同一个goods_id切换sku
                tmp_goodsSkuDetail=this.goodsSkuDetail;
              }else{  //不同goods_id切换sku
                tmp_goodsSkuDetail=this.goodsDetail;
              }
            }
            this.$emit('refresh', tmp_goodsSkuDetail);
            this.$refs.skuPopup.close();
            this.btnSwitch = false;
          }
				});
			},
			closeSkuPopup() {
				this.$refs.skuPopup.close();
			},
			imageError() {
				setTimeout(() => {
					this.goodsDetail.sku_image = this.$util.getDefaultImage().default_goods_img;
					this.$forceUpdate();
				}, 100)
			},
			valueImageError(index, index_value) {
				this.goodsDetail.goods_spec_format[index].value[index_value].image = this.$util.getDefaultImage()
					.default_goods_img;
				this.$forceUpdate();
			}
		}
	};
</script>
<style lang="scss">
	.sku-layer .sku-info {
		height: 75vh !important;
		position: relative;
		z-index: 999;

		.coupon-content {
			width: 100%;
			box-sizing: border-box;
			padding: 0rpx 20rpx;
			.content-box-scroll {
				width: 100%;
				white-space: nowrap;
				height: 50rpx;
				view {
					width: fit-content;
					display: inline-block;
					height: 40rpx;
					font-size: 22rpx;
					box-sizing: border-box;
					padding: 4rpx 8rpx;
				}
				.content-box-items-red {
          color: white;
          background-color: var(--custom-brand-color);
          margin-right: 10rpx;
          border-radius: 10rpx;
				}
				.content-box-items {
          color: var(--custom-brand-color);
          background: var(--custom-brand-color-10);
          border-radius: 10rpx;
          margin-right: 10rpx;
          word-break:keep-all;
          white-space:nowrap;
				}
			}
			.coupon-btn {
				color: var(--custom-brand-color);
				font-size: 24rpx;
				.iconright {
				font-size: 24rpx;
				}
			}
		}
	}

	.sku-layer .sku-info .header {
		padding: 36rpx 0 36rpx 236rpx;
		/* #ifdef MP-ALIPAY */
		padding: 50rpx 0 50rpx 300rpx;
		/* #endif */
		position: relative;
		// border-bottom: 1px solid rgba(0, 0, 0, 0.1);
	}

	.sku-layer .sku-info .header .img-wrap {
		width: 180rpx;
		height: 180rpx;
		position: absolute;
		// top: -56rpx;
		/* #ifdef MP-ALIPAY */
		top: 20rpx;
		/* #endif */
		left: 20rpx;
		border-radius: 8rpx;
		overflow: hidden;
		// border: 1px solid rgba(0, 0, 0, 0.1);
		// padding: 2rpx;
		// background-color: #fff;
		line-height: 208rpx;
	}

	.sku-layer .sku-info .header .img-wrap image {
		width: 100%;
		height: 100%;
	}

	.sku-layer .sku-info .main {
		font-size: 12px;
		line-height: 40rpx;
		padding-right: 40rpx;
		height: 180rpx;
		position: relative;
	}

	.sku-layer .sku-info .main .price-distributor {
		position: absolute;
		left: 0;
		bottom: 40rpx;
		display: flex;
		align-items: center;

		text:first-child {
			background: linear-gradient(90deg, var(--custom-brand-color-80) 0%, var(--custom-brand-color) 100%);
			border-radius: 4rpx 0px 0px 4rpx;
			padding: 0rpx 9rpx;
			box-sizing: border-box;
			font-size: 20rpx;
			font-weight: 500;
			color: #FFFFFF;
			text-align: center;
			height: 40rpx;
			line-height: 40rpx;
		}

		text:last-child {
			background: var(--custom-brand-color-10);
			border-radius: 0px 4rpx 4rpx 0px;
			text-align: center;
			font-size: 0.6875rem;
			font-weight: 500;
			color: var(--custom-brand-color);
			padding: 0rpx 9rpx;
			box-sizing: border-box;
			height: 40rpx;
			line-height: 40rpx;
		}
	}

	.sku-layer .sku-info .main .price-wrap {
		position: absolute;
		bottom: 0;
		display: flex;
		align-items: baseline;
	}

	.sku-layer .sku-info .main .goodsName {
		display: -webkit-box;
		-webkit-box-orient: vertical;
		-webkit-line-clamp: 2;
		overflow: hidden;
		width: 90%;
	}

	.sku-layer .sku-info .main .price .prize-icon {
		font-size: 26rpx;
		font-weight: normal;
	}

	.sku-layer .sku-info .main .price {
		word-wrap: break-word;
		font-weight: bold;
		line-height: 30rpx;
    color: var(--custom-brand-color);
	}

	.sku-layer .sku-info .main .market_price {
		word-wrap: break-word;
		font-size: 24rpx;
		text-decoration: line-through;
		color: #999;
		margin-left: 8rpx;
	}
  .sku-layer .sku-info .main .cheap_price{
    font-size: 22rpx;
    font-weight: 400;
    line-height: 32rpx;
    color: #FFFFFF;
    display: inline-block;
    padding: 2rpx 14rpx;
    box-sizing: border-box;
    margin-left: 12rpx;
    background: var(--custom-brand-color);
    border-radius: 32rpx;
  }
	.sku-layer .sku-info .main .sku-name {
		display: -webkit-box;
		-webkit-line-clamp: 2;
		-webkit-box-orient: vertical;
		height: 90rpx;
		overflow: hidden;
	}

	.sku-layer .sku-info .main .sku-name text {
		margin-right: 10rpx;
	}

	.sku-layer .sku-info .sku-close {
		position: absolute;
		top: 24rpx;
		right: 24rpx;
		width: 32rpx;
		height: 32rpx;
		font-size: 20rpx;
		background: #999;
		border-radius: 50%;
		line-height: 34rpx;
		text-align: center;
		color: #fff
	}

	.sku-layer .body-item {
		padding: 0 30rpx;
		height: calc(100% - 330rpx);
		box-sizing: border-box;
		overflow: scroll;
	}

	.sku-layer .body-item .wrap {
		height: calc(100% - 116rpx);
	}

	.sku-layer .body-item .sku-list-wrap {
		// border-bottom: 1px solid rgba(0, 0, 0, 0.1);
		padding-bottom: 20rpx;
	}

	.sku-layer .body-item .sku-list-wrap .title {
		font-weight: 400;
		padding: 26rpx 0;
		margin: 0;
		display: block;
	}

	.sku-layer .body-item .sku-list-wrap .items {
		position: relative;
		display: inline-block;
		border: 1px solid;
		padding: 0 20rpx;
		border-radius: 27rpx;
		margin: 0 10rpx 10rpx 0;
		font-size: 26rpx;
		line-height: 54rpx;
	}

	.sku-layer .body-item .sku-list-wrap .items.disabled {
		border: 1px dashed;
	}

	.sku-layer .body-item .sku-list-wrap .items image {
		height: 48rpx;
		width: 48rpx;
		border-radius: 4rpx;
		margin-right: 10rpx;
		display: inline-block;
		vertical-align: middle;
	}

	.sku-layer .body-item .number-wrap .number-line {
		padding: 20rpx 0;
		line-height: 72rpx;
		border-bottom: 1px solid rgba(0, 0, 0, 0.1);
	}

	.sku-layer .body-item .number-wrap .title {
		font-weight: 400;
	}

	.sku-layer .body-item .number-wrap .limit-txt {}

	.sku-layer .body-item .number-wrap .number {
		height: 72rpx;
		border-radius: 6rpx;
		float: right;
	}

	.sku-layer .body-item .number-wrap .number button {
		display: inline-block;
		line-height: 64rpx;
		height: 68rpx;
		width: 60rpx;
		font-size: 48rpx;
		box-sizing: content-box;
		border: 1px solid;
		padding: 0;
		margin: 0;
		border-radius: 0;
	}

	.sku-layer .body-item .number-wrap .number button.decrease {
		border-right: 1px solid #fff !important;
    border-radius: 10rpx;
	}

	.sku-layer .body-item .number-wrap .number button.increase {
		border-left: 1px solid #fff !important;
    border-radius: 10rpx;
	}
  .sku-layer .body-item .number-wrap .number button.disabled{
    color: rgba(229, 229, 229, 1);
  }

	.sku-layer .body-item .number-wrap .number button:after {
		border-radius: 0;
		border: none;
	}

	.sku-layer .body-item .number-wrap .number input {
		display: inline-block;
		line-height: 64rpx;
		height: 68rpx;
		width: 72rpx;
		text-align: center;
		font-weight: 700;
		border: 1px solid;
		margin: 0;
		padding: 0;
		vertical-align: top;
	}

	.sku-layer .footer {
		height: 96rpx;
		width: 100%;
		padding: 0 7.5%;
		box-sizing: border-box;
		position: absolute;
		// bottom: 60rpx;
		bottom: 0;
		color: #fff;
		z-index: 1;
		display: flex;
		justify-content: space-between;
		align-items: center;

		button {
			width: 100%;
		}

		.footer-btn {
			&:first-child {
				border-radius: 40rpx;

				button {
					color: var(--custom-brand-color);
					background:  var(--custom-brand-color-10);
					border: 2rpx solid var(--custom-brand-color);
				}

				.btn-disabled {
					border-color: #e5e5e5 !important;
				}

			}
      .btn-confirm {
        background-color: var(--custom-brand-color) !important;
      }

			width: 45%;
			flex-shrink: 0;

			button {
				width: 100%;
				margin: 0;
			}
		}

	}

	// .cart_sku .cart-footer{
	// 	bottom: 120rpx !important;
	// }
	.cart-footer {
		padding-bottom: 0 !important;
    .btn-confirm {
      background-color: var(--custom-brand-color) !important;
    }
	}

	.position-bottom {
		bottom: 98rpx !important;
	}
</style>
