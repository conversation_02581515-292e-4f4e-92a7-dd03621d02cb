<template>
	<uni-popup ref="popup">
		<view class="uni-custom">
			<view class="uni-popup__wrapper-box">
				<view class="popup-dialog">
					<view class="popup-dialog-header" v-if="isTitle">{{title}}</view>
					<view class="popup-dialog-body">
						<rich-text :nodes="text"></rich-text>
					</view>
					<view class="popup-dialog-footer">
						<view class="button white" @click="closePopup">{{cancleText}}</view>
						<button class="button red" @click="confirm">{{confirmText}}</button>
					</view>
				</view>
			</view>
		</view>
	</uni-popup>
</template>

<script>
	export default {
		name: 'diy-uni-popup',
		components: {},
		props: {
			isTitle:{
				type:Boolean,
				default:true
			},
			title: {
				default: '提示'
			},
			text: [String],
			cancleText: {
				default: '取消'
			},
			confirmText: {
				default: '确定'
			}
		},
		data() {
			return {}
		},
		methods: {
			open() {
				this.$refs.popup.open()
			},
			closePopup() {
				this.$refs.popup.close()
			},
			cancle(){
				this.closePopup()
				this.$emit('cancle')
			},
			confirm() {
				this.$emit('confirm')
			}
		}
	}
</script>

<style>
	/deep/ .uni-popup__wrapper-box {
		max-width: 540rpx;
		width: 540rpx;
		border-radius: 20rpx;
		background: none;
	}

	.popup-dialog {
		overflow: hidden;
		background: #FFFFFF;
		box-sizing: border-box;
	}

	.popup-dialog .popup-dialog-header {
		height: 106rpx;
		line-height: 106rpx;
		text-align: center;
		font-size: 36rpx;
		color: #333333;
		font-weight: bold;
	}

	.popup-dialog-body {
		color: #656565;
		text-align: center;
		padding: 0 30rpx;
	}

	.popup-dialog-footer {
		margin: 0 32rpx;
		height: 140rpx;
		display: flex;
		align-items: center;
		justify-content: space-around;
	}

	.popup-dialog-footer .button {
		width: 220rpx;
		height: 68rpx;
		line-height: 68rpx;
		text-align: center;
		border-radius: 34rpx;
		box-sizing: border-box;
		margin: 0;
	}

	.popup-dialog-footer .button.white {
		color: var(--custom-brand-color);
		background: #FFFFFF;
		border: 1px solid var(--custom-brand-color);
	}

	.popup-dialog-footer .button.red {
		color: #FFFFFF;
		background: var(--custom-brand-color);

	}
</style>
