<template>
	<view>
		<!-- 分享的页面不是用户绑定的id,提示无法购物弹窗 -->
		<uni-popup ref="popupBan" :maskClick="false">
			<view class="popup-dialog">
				<view class="popup-dialog-header">提示</view>
				<view class="popup-dialog-body">哎哟~系统好像出了点问题，暂时不能支付，请联系客服。</view>
				<view class="popup-dialog-footer">
					<button class="button red" @click="$refs.popupBan.close()">知道了</button>
				</view>
			</view>
		</uni-popup>
	</view>
</template>

<script>
	export default {
		data(){
			return {
				
			}
		},
		methods:{
			open(){
				this.$refs.popupBan.open()
			}
		}
	}
</script>

<style lang="scss" scoped>
	.popup-dialog {
		overflow: hidden;
		background: #FFFFFF;
		box-sizing: border-box;
	
		.popup-dialog-header {
			height: 106rpx;
			line-height: 106rpx;
			text-align: center;
			font-size: 36rpx;
			color: #333333;
			font-weight: bold;
		}
	
		.popup-dialog-body {
			color: #656565;
			text-align: center;
			padding: 0 30rpx;
		}
	
		.popup-dialog-footer {
			margin: 0 32rpx;
			height: 140rpx;
			display: flex;
			align-items: center;
			justify-content: space-around;
	
			.button {
				width: 220rpx;
				height: 68rpx;
				line-height: 68rpx;
				text-align: center;
				border-radius: 34rpx;
				box-sizing: border-box;
				margin: 0;
	
				&.white {
					color: #F2270C;
					background: #FFFFFF;
					border: 1rpx solid #F2270C;
				}
	
				&.red {
					color: #FFFFFF;
					background: #F2270C;
	
				}
			}
		}
	}
</style>
