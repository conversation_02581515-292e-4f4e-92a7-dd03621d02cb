<!--升级vip的按钮，跳转到升级页面-->
<template>
    <div class="yp-vip" :style="{position:positions.position,right: positions.right,bottom: positions.bottom}" v-show="isShow">
      <image :src="$util.img('public/static/youpin/upgrade_vip.png')" @click="toVIP"></image>
    </div>
</template>

<script>
import apiurls from "../../common/js/apiurls";

export default {
  name: "yp-upgrade-vip-button",
  data(){
    return {
      url:""
    }
  },
  props:{
    isShow:{
      type:Boolean,
      defalut:true
    },
    positions:{
      type:Object,
      defalut:{
        position: "fixed",
        right: 0,
        bottom: 0,
      }
    }
  },
  methods:{
    toVIP(){
      let url = this.url;
      if(url){
        if(/^http:.+|https:.+/.test(url)){
          this.$util.redirectTo('/otherpages/web/web', {src:encodeURIComponent(url)}, '');
        }else{
          this.$util.redirectTo(url);
        }
      }
    },
    async getData(){
      let res=await this.$api.sendRequest({
        url: apiurls.getOutLinkUrl,
        async:false,
        data: {
        },
      });
      if(res.code!=0){
        return
      }
      this.url=res.data.link;
    },
  },
  async created(){
    await this.getData();
  }
}
</script>

<style scoped lang="scss">
  .yp-vip{
    position: fixed;
    right: 0;
    bottom: 0;
    width: 137rpx;
    height: 137rpx;
    image{
      width: 100%;
      height: 100%;
    }
  }
</style>
