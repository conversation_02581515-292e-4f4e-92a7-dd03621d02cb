<template>
<view class="countdown">
	<slot name="detail" v-if="useSlot"></slot>
	<view v-else :style="customStyle"> {{countTime}} </view>
</view>
</template>

<script>
export default{
	name:'countdown',
	data(){
		return{
			countTime:'', //显示时间格式值
			milliseconds:this.time, //毫秒数
			timeDate:{}
		}
	},
	props:{
		time:{
			type:Number,
			default:0
		},
		format:{
			type:String,
			default:'dd天 hh:mm:ss'
		},
		autoStart:{
			type:Boolean,
			default:true
		},
		useSlot:{
			type:Boolean,
			default:false
		},
		customStyle:{}
	},
	watch:{
		time(val){
			this.milliseconds = val;
		}
	},
	created(){
		this.reset()
	},
	methods:{
		formatDate(time,fmt){
			let o = {}
			if (!time) {
				o = {
					'd+': 0,
					'h+': 0,
					'm+': 0,
					's+': 0
				}
			}else {
				o = {
					'd+': Math.floor(time/ 1000 / 60 / 60 / 24),
					'h+': Math.floor((time / 1000 / 60 / 60) % 24),
					'm+': Math.floor((time / 1000 / 60) % 60),
					's+': Math.floor((time / 1000) % 60)
				}
			}
			this.timeDate = o;
			if(o['d+']==0){
				fmt = fmt.substring(fmt.indexOf('hh'),fmt.length)
			}
			
			function padLeftZero(str) {
				return ('00' + str).substr(str.length)
			}
			for (let k in o) {
				if (new RegExp(`(${k})`).test(fmt)) {
					let str = o[k] + ''
					fmt = fmt.replace(RegExp.$1, (RegExp.$1.length === 1) ? str : padLeftZero(str))
				}
			}
			return fmt
		},
		// 开启倒计时
		startTimer() {
			if (this.timer) {
				clearInterval(this.timer);
			}
			if(this.milliseconds < 0) {
				 return
			}
			this.timer = setInterval(() => {
				this.milliseconds -= 1000;
				this.countTime = this.formatDate(this.milliseconds,this.format)
				let item = {
					day:this.timeDate['d+'],
					hour:this.timeDate['h+'],
					minute:this.timeDate['m+'],
					second:this.timeDate['s+'],
					value:this.countTime
				}
				if (this.milliseconds <= 0) {
					clearInterval(this.timer);
					this.$emit('finish');
				}else{
					this.$emit('change',item);
				}
				
			}, 1000);
		},
		// 重置倒计时
		reset() {
			this.countTime = this.formatDate(this.milliseconds,this.format);
			if(this.autoStart) {
				this.startTimer()
			}
		},
		change(){
			this.$emit('change',{
				day:this.timeDate['d+'],
				hour:this.timeDate['h+'],
				minute:this.timeDate['m+'],
				second:this.timeDate['s+'],
				value:this.countTime
			})
		}
	}
}
</script>

<style>
</style>
