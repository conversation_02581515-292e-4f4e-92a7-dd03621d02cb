<template>
  <view>
    <view class="yd-popup" v-show="isShow" v-on:click="maskClose">
      <!--授权弹窗 start-->
      <view class="yd-popup--auth" v-if="!isAuth">
        <view class="yd-popup--auth--title">提示</view>
        <view class="yd-popup--auth--content">需要通过授权才能正常使用哦~</view>
        <view class="yd-popup--auth--row">
          <button class="yd-popup--auth--cancel" @click="cancelPopup">取消</button>
          <button v-if="canUseGetUserProfile" class="yd-popup--auth--op" @tap="getUserProfile">确认授权</button>
          <button  v-else class="yd-popup--auth--op" open-type="getUserInfo" @getuserinfo="getuserinfo">确认授权</button>
        </view>
      </view>
      <!--授权弹窗 end-->
      <!--手机授权弹窗 start-->
      <view class="yd-popup--phone" v-else>
        <view class="yd-popup--phone--title">提示</view>
        <view class="yd-popup--phone--content">请使用手机号快捷登录~</view>
        <view class="yd-popup--phone--row">
          <button class="yd-popup--phone--cancel"  @click="cancelPopup" >取消</button>
          <button  v-if="isProtocol" class="yd-popup--phone--op" open-type="getPhoneNumber" @getphonenumber="getphonenumber" >手机号快捷登录</button>
          <button v-else class="yd-popup--phone--op" @click="getphonenumber" >手机号快捷登录</button>
        </view>
        <view class="yd-popup--phone--error" v-if="isShowProtocolError">请先阅读并勾选同意协议!</view>
        <view class="yd-popup--phone--tip" >
          <checkbox-group @change="protocolChange">
            <checkbox :checked="isProtocol" value="1" style="transform:scale(0.7)"/>
          </checkbox-group>
          勾选代表您已同意<text @click="toAgreement">用户协议、</text><text @click="toAgreement">隐私政策</text></view>
      </view>
      <!--手机授权弹窗 end-->
    </view>

    <!-- 手机验证码验证 start -->
    <uni-popup ref="mobileCodePop" type="center" :maskClick="false" :custom="true">
      <view class="code-pop">
        <view class="code-pop-title">安全验证</view>
        <view class="code-pop-tip">发送验证码至 <text class="code-pop-tip-text">+{{mobile}}</text></view>
        <view class="code-pop-input">
          <input type="text" v-model="smsCode" placeholder="请输入短信验证码" placeholder-class="code-pop-input-placeholder">
          <text class="code-pop-input-send" @click="getCode">{{ timing>=0 ? `${timing} S` : '获取验证码'}}</text>
        </view>
        <button class="code-pop-op" @click="confirmLogin">确认</button>
      </view>
    </uni-popup>
    <!-- 手机验证码验证 end -->
  </view>
</template>

<script>
import apiurls from "../../common/js/apiurls";
import statistics from "../../common/mixins/statistics";
import AuthService from "../../common/services/auth";
import util from "../../common/js/util";
import uniPopup from '@/components/uni-popup/uni-popup.vue';

export default {
  name:'yd-auth-popup',
  props: {
    initShow: {
      type: Boolean,
      defalut: false
    }
  },
  components: {
    uniPopup,
  },
  data() {
    return {
      isShow: false,
      isAuth:false,
      userInfo:{},
      encryptedData:"",
      iv:"",
      canUseGetUserProfile:false,
      smsCode:'',
      mobile:'',
      openid_arr:{},
      timing:-1,
      intervalObj:null,
      isProtocol: false,
      isShowProtocolError: false
    };
  },
  methods: {
    //initShow为false调用该函数初始化
    init(callback,showMobileCodePop=false){
      if(callback && typeof callback=="function"){
        this.callback=callback;
      }
      if(!showMobileCodePop){
        this.isShow=true;
      }else{
        this.$refs.mobileCodePop.open();
      }

      // this.getInitUserInfo();
    },
    cancelPopup(){
      this.isShow=false;
    },
    /**
     * 获取用户基础授权信息
     */
    async getUserProfile(){
      let recommend_member_id=uni.getStorageSync('recommend_member_id');
      if(recommend_member_id && (this.$store.state.wechatVerCode.codeType!='login' || (this.$store.state.wechatVerCode.codeType=='login' && !this.$store.state.wechatVerCode.verifyState))){
        await this.$util.toWechatVerificationCode('login')
        return
      }
      try{
        this.$buriedPoint.diyReportAuthorizationInteractionEvent({diy_action_type:'popup_authorization',diy_authorization_type:'wechat_authorization'})
        let [code,res]=await AuthService.getUserProfile();
        this.userInfo=res.userInfo;
        this.isAuth=true;
        this.$buriedPoint.diyReportAuthorizationInteractionEvent({diy_action_type:'confirm_authorization',diy_authorization_type:'wechat_authorization'})
      }catch (e) {
        this.$buriedPoint.diyReportAuthorizationInteractionEvent({diy_action_type:'cancel_authorization',diy_authorization_type:'wechat_authorization'})
        this.$util.showToast({
          title:"获取微信信息失败"
        })
      }
    },
    /**
     * 获取用户基础授权信息
     */
    async getuserinfo(event) {
      let recommend_member_id=uni.getStorageSync('recommend_member_id');
      if(recommend_member_id && (this.$store.state.wechatVerCode.codeType!='login' || (this.$store.state.wechatVerCode.codeType=='login' && !this.$store.state.wechatVerCode.verifyState))){
        await this.$util.toWechatVerificationCode('login')
        return
      }
      try{
        this.$buriedPoint.diyReportAuthorizationInteractionEvent({diy_action_type:'popup_authorization',diy_authorization_type:'wechat_authorization'})
        let [code,res]=await AuthService.getUserInfo(event);
        this.userInfo=res.userInfo;
        this.isAuth=true;
        this.$buriedPoint.diyReportAuthorizationInteractionEvent({diy_action_type:'confirm_authorization',diy_authorization_type:'wechat_authorization'})
      }catch (e) {
        this.$buriedPoint.diyReportAuthorizationInteractionEvent({diy_action_type:'cancel_authorization',diy_authorization_type:'wechat_authorization'})
        this.$util.showToast({
          title:"获取微信信息失败"
        })
      }
    },
    /**
     * 获取用户手机号码
     */
    async getphonenumber(event){
      let recommend_member_id=uni.getStorageSync('recommend_member_id');
      if(recommend_member_id && (this.$store.state.wechatVerCode.codeType!='login' || (this.$store.state.wechatVerCode.codeType=='login' && !this.$store.state.wechatVerCode.verifyState))){
        await this.$util.toWechatVerificationCode('login')
        return
      }
      if(!this.isProtocol){
        this.isShowProtocolError = true
        this.$util.showToast({
          title: '请先阅读并勾选同意协议!'
        })
        return
      }
      this.$buriedPoint.diyReportAuthorizationInteractionEvent({diy_action_type:'popup_authorization',diy_authorization_type:'phone_authorization'})
      try {
        let [code,detail]=await AuthService.getPhoneNumber(event);
        this.encrypteData=detail.encryptedData;
        this.iv=detail.iv;
        this.$buriedPoint.diyReportAuthorizationInteractionEvent({diy_action_type:'confirm_authorization',diy_authorization_type:'phone_authorization'})
        await this.authLogin(code);
      }catch (e) {
        this.$buriedPoint.diyReportAuthorizationInteractionEvent({diy_action_type:'cancel_authorization',diy_authorization_type:'phone_authorization'})
      }

    },
    /**
     * 微信授权登陆 | 手机验证码登陆 统一请求方法
     */
    async wechatAuthLogin(dataObj){
      let result={};
      let recommend_member_id=uni.getStorageSync('recommend_member_id');
      if(recommend_member_id){
        dataObj=Object.assign(dataObj,{recommend_member:recommend_member_id})
      }
      uni.showLoading({
        title: '加载中',
        mask:true
      });
      try{
        let res=await this.$api.sendRequest({
          url:apiurls.wechatAuthUrl,
          async:false,
          data:dataObj
        });

        result=res;
      }catch (e) {

      }
      uni.hideLoading();
      return result;
    },
    /**
     * 微信基本授权和手机授权登陆
     */
    async authLogin(code){
      let data={
        code,
        userinfo:JSON.stringify(this.userInfo),
        encrypteData:this.encrypteData,
        iv:this.iv
      }
      let res=await this.wechatAuthLogin(data);
      if(!Object.keys(res).length){
        this.$util.showToast({
          title:'登录失败'
        })
      }else{
        if(res.code==-10010){
          this.isShow=false;
          uni.showModal({
            title: '提示',
            content: res.message,
            showCancel:false,
          });
          await this.$util.clearUserInfo();
        }else if(res.code==-10011){
          this.openid_arr=res.data.openid_arr;
          this.mobile=res.data.mobile;
          this.isShow=false;
          this.$refs.mobileCodePop.open();
        }else if(res.code!=0){
          this.$util.showToast({
            title:res.message
          })
        }else{
          uni.setStorageSync('is_register', res.data.is_register)
          this.isShow=false;
          let token=res.data.token;
          let shop_id=res.data.shop_id;
          let member_id=res.data.member_id;
          let is_distributor=res.data.is_distributor;
          let shop_name=res.data.site_name;
          let is_shopper = res.data.is_shopper;  //是否为店主，1：是，0：否
          util.setUserInfo({shop_id,member_id,is_distributor,shop_name,is_shopper,token});
          statistics.shopInterview(this);
          if(this.callback && typeof this.callback=="function"){
            this.callback();
          }
        }
      }
    },
    /**
     * 手机验证码登陆
     * @returns {Promise<void>}
     */
    async confirmLogin(){
      if(!this.smsCode){
        this.$util.showToast({
          title:'请输入短信验证码'
        })
        return;
      }
      let data={
        sms_code:this.smsCode,
        mobile:this.mobile,
        openid_arr:JSON.stringify(this.openid_arr),
        userinfo:JSON.stringify(this.userInfo)
      }
      let res=await this.wechatAuthLogin(data);
      if(!Object.keys(res).length){
        this.$util.showToast({
          title:'登录失败'
        })
      }else{
        if(res.code!=0){
          this.$util.showToast({
            title:res.message
          })
        }else{
          uni.setStorageSync('is_register', res.data.is_register)
          let token=res.data.token;
          let shop_id=res.data.shop_id;
          let member_id=res.data.member_id;
          let is_distributor=res.data.is_distributor;
          let shop_name=res.data.site_name;
          let is_shopper = res.data.is_shopper;  //是否为店主，1：是，0：否
          util.setUserInfo({shop_id,member_id,is_distributor,shop_name,is_shopper,token});
          statistics.shopInterview(this);
          this.$refs.mobileCodePop.close();
          if(this.callback && typeof this.callback=="function"){
            this.callback();
          }
        }
      }
    },
    maskClose(event){
      if(event.target.offsetTop==0){
        this.isShow=false;
      }
    },
    protocolChange(event){
      let value = event.detail.value
      if(value.length>0){
        this.isProtocol = true
        this.isShowProtocolError = false
      }else{
        this.isProtocol = false
      }
    },
    toAgreement(){
      this.$util.redirectTo('/pages/agreement/list/list')
    },
    runTiming(){
      if(this.timing>0){
        return
      }
      this.timing=60;
      this.intervalObj=setInterval(()=>{
        if(this.timing<0){
          clearInterval(this.intervalObj);
        }else{
          this.timing--;
        }
      },1000)
    },
    /**
     * 获取手机验证码
     * @returns {Promise<void>}
     */
    async getCode(){
      if(this.timing>=0){
        return
      }
      let data={
        mobile:this.mobile,
        type:8
      };
      uni.showLoading({
        title: '加载中',
        mask:true
      });
      try{
        let res=await this.$api.sendRequest({
          url:apiurls.sendMobileCodeUrl,
          async:false,
          data
        });
        uni.hideLoading();
        if(res.code!=0){
          this.$util.showToast({
            title:res.message
          })
        }else{
          this.$util.showToast({
            title:'验证码已经发送'
          })
          this.runTiming();
        }
      }catch (e) {
        uni.hideLoading();
        this.$util.showToast({
          title:e.message
        })
      }
    }
  },
  created(){
    //判断是否存在getUserProfile方法
    if (uni.getUserProfile) {
      this.canUseGetUserProfile=true;
    }
  }
}
</script>


<style lang="scss">
.yd-popup{
  background: rgba(0, 0, 0, 0.4);
  width: 100%;
  height: 100%;
  z-index: 998;
  position: fixed;
  top: 0;
  left: 0;
  &--auth {
    position: absolute;
    left: 0;
    bottom: 0;
    width: 750rpx;
    height: 523rpx;
    background: #FFFFFF;
    border-radius: 12rpx 12rpx 0rpx 0rpx;
    box-sizing: border-box;
    padding-top: 79rpx;
    &--title {
      font-size: 48rpx;
      font-weight: bold;
      color: #000000;
      text-align: center;
    }
    &--content {
      font-size: 36rpx;
      font-weight: 500;
      color: #666666;
      margin-top: 66rpx;
      text-align: center;
    }
    &--row{
      display: flex;
      justify-content: center;
      align-items: center;
      margin-top: 88rpx;
    }
    &--cancel{
      box-sizing: border-box;
      width: 300rpx;
      height: 81rpx;
      line-height: 81rpx;
      background: #eee;
      border: 2rpx solid #eee;
      border-radius: 8rpx;
      font-size: 34rpx;
      font-weight: bold;
      color: #333;
      margin: 0;
    }
    &--op {
      box-sizing: border-box;
      width: 300rpx;
      height: 81rpx;
      line-height: 81rpx;
      background: #09BB07;
      border: 2rpx solid #029400;
      border-radius: 8rpx;
      font-size: 34rpx;
      font-weight: bold;
      color: #FFFFFF;
      margin: 0;
      margin-left: 50rpx;
    }
  }
  &--phone {
    position: absolute;
    left: 0;
    bottom: 0;
    width: 750rpx;
    height: 523rpx;
    background: #FFFFFF;
    border-radius: 12rpx 12rpx 0rpx 0rpx;
    box-sizing: border-box;
    padding-top: 50rpx;
    &--title {
      font-size: 48rpx;
      font-weight: bold;
      color: #000000;
      text-align: center;
    }
    &--content {
      font-size: 36rpx;
      font-weight: 500;
      color: #666666;
      margin-top: 66rpx;
      text-align: center;
    }
    &--row{
      display: flex;
      justify-content: center;
      align-items: center;
      margin-top: 88rpx;
    }
    &--cancel{
      box-sizing: border-box;
      width: 300rpx;
      height: 81rpx;
      line-height: 81rpx;
      background: #eee;
      border: 2rpx solid #eee;
      border-radius: 8rpx;
      font-size: 34rpx;
      font-weight: bold;
      color: #333;
      margin: 0;
    }
    &--op {
      box-sizing: border-box;
      width: 300rpx;
      height: 81rpx;
      line-height: 81rpx;
      background: #09BB07;
      border: 2rpx solid #029400;
      border-radius: 8rpx;
      font-size: 34rpx;
      font-weight: bold;
      color: #FFFFFF;
      margin: 0;
      margin-left: 50rpx;
    }
    &--error{
      font-size: 24rpx;
      font-weight: 500;
      color: red;
      text-align: center;
      display: flex;
      align-items: center;
      justify-content: center;
      position: absolute;
      left: 50%;
      bottom: 190rpx;
      transform: translateX(-50%);
    }
    &--tip{
      font-size: 24rpx;
      font-weight: 500;
      color: #333333;
      text-align: center;
      margin-top: 30rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      text{
        color: #0B93F2;
      }
    }
  }
}
.code-pop{
  width: 670rpx;
  height: 470rpx;
  background: #FFFFFF;
  border-radius: 20rpx;
  padding-top: 50rpx;
  padding-bottom: 40rpx;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  align-items: center;
  &-title{
    font-size: 32rpx;
    font-weight: bold;
    color: #333333;
    text-align: center;
  }
  &-tip{
    font-size: 30rpx;
    font-weight: 500;
    color: #666666;
    text-align: center;
    &-text{
      font-size: 30rpx;
      font-weight: 500;
      color: var(--custom-brand-color);
      margin-left: 10rpx;
    }
  }
  &-input{
    width: 550rpx;
    height: 88rpx;
    border-radius: 44rpx;
    position: relative;
    margin-top: 46rpx;
    background: #F5F5F5;
    input{
      width: 65%;
      height: 100%;
      //border-radius: 44rpx;
      padding-left: 25rpx;
      box-sizing: border-box;
    }
    &-placeholder{
      font-size: 28rpx;
      font-weight: 500;
      color: #CCCCCC;
    }
    &-send{
      display: inline-block;
      font-size: 28rpx;
      font-weight: 500;
      color: var(--custom-brand-color);
      height: 100%;
      line-height: 3;
      position: absolute;
      right: 25rpx;
      top: 0;
    }
  }
  &-op{
    width: 360rpx;
    height: 80rpx;
    background: linear-gradient(0deg, var(--custom-brand-color) 0%, var(--custom-brand-color-80) 100%);
    border-radius: 40rpx;
    font-size: 30rpx;
    font-weight: 500;
    color: #FFFFFF;
    margin-top: 60rpx;
  }
}
</style>
