<template>
	<view>
		<!-- <slot :time="time" :remain="timeData.remain" :day="timeData.day" :hour="timeData.hour" :minute="timeData.minute" :second="timeData.second" /> -->
		<view class="custom flex-start-center">
			<!-- 小程序暂时不支持在v-slot内部调用方法，后期可能会支持 -->
			<view v-if="timeData.day>=1" class="day">{{timeData.day}}</view>
			<view v-if="timeData.day>=1" class="day-symbol">{{showColon||showDaySymbol?'天':':'}}</view>
			<view class="hour">{{timeData.hour | fillWithZero}}</view>
			<view class="hour-symbol">{{showColon?'时':':'}}</view>
			<view class="minute">{{timeData.minute | fillWithZero}}</view>
			<view class="minute-symbol">{{showColon?'分':':'}}</view>
			<view class="second">{{timeData.second | fillWithZero}}</view>
			<view class="second-symbol">{{showColon?'秒':''}}</view>
		</view>
	</view>
</template>

<script>
	export default {
		props: {
			// 倒计时时长（单位：毫秒）
			time: {
				type: Number,
				default: 0
			},
			// 是否自动
			'autoStart': {
				type: Boolean,
				default: false
			},
			showColon: {
				type: Boolean,
				default: false
			},
      showDaySymbol:{
        type: Boolean,
        default: false
      }
		},
		data() {
			return {
				timer: null,
				timeData: {
					remain: 0,
					day: 0,
					hour: 0,
					minute: 0,
					second: 0
				}
			};
		},
		watch: {
			time() {
				this.reset()
			}
		},
		filters: {
			fillWithZero(num) {
				var len = num.toString().length;
				while (len < 2) {
					num = "0" + num;
					len++;
				}
				return num;
			},
		},
		methods: {
			// 设置timeData
			updateTimeData() {
				let t = this.timeData.remain;
				this.timeData.day = Math.floor(t / 1000 / 60 / 60 / 24);
				this.timeData.hour = Math.floor((t / 1000 / 60 / 60) % 24);
				this.timeData.minute = Math.floor((t / 1000 / 60) % 60);
				this.timeData.second = Math.floor((t / 1000) % 60);
			},
			// 重置倒计时
			reset() {
				if (this.timer) {
					this.timer.stop();
				}
				this.timeData.remain = this.time;
				this.updateTimeData();
				this.timer = new this.$util.AdjustingInterval(() => {
					this.timeData.remain -= 1000;
					if(this.timeData.remain<=0){
						this.timeData.day = "00";
						this.timeData.hour = "00";
						this.timeData.minute = "00";
						this.timeData.second = "00";
					}else{
						this.updateTimeData()
					}
					// this.updateTimeData()
				}, 1000, this.time / 1000, () => {
					this.$emit('finish');
				})
				if (this.autoStart) {
					this.timer.start()
				}
			},
			// 开始倒计时
			start() {
				if (this.timer) {
					return
				}
				this.timer.start();
			}
		},
		mounted() {
			this.reset();
		},
		beforeDestroy() {
			// this.timer.stop()
		}
	};
</script>
