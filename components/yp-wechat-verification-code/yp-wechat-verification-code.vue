<template>
  <view class="yp-wechat-verification-code">
    <!-- app-id：验证码CaptchaAppId, 从腾讯云的验证码控制台中获取, 在验证码控制台页面内【图形验证】>【验证列表】进行查看 -->
	<!-- #ifdef MP-WEIXIN -->
	<t-captcha
	    id="captcha"
	    :app-id="weapp_app_id"
	    @verify="weappHandlerVerify"
	    @ready="weappHandlerReady"
	    @close="weappHandlerClose"
	    @error="weappHandlerError" />
	<!-- #endif -->
  </view>
</template>

<script>
export default {
  name: "yp-wechat-verification-code",
  data(){
    return{
      codeType: '',
      weapp_app_id: '199840371',
      h5_app_id: '198118925',
      isActive: false,
      captchaObj:null,
    }
  },
  mounted(){
    if(this.$util.getPlatform()=='h5'){
      this.$bus.$on('toShowVerCode', (codeType) => {
        this.toShowVerCode(codeType)
      });
    }
  },
  methods:{
    toSuccess(ticket){
      this.$util.successWechatVerificationCode(this.codeType,ticket)
    },
    toShowVerCode: function (codeType) {
      if(!codeType){
        this.$util.showToast({
          title: '参数错误'
        });
        return
      }
      this.codeType = codeType
      // #ifdef MP-WEIXIN
      this.selectComponent('#captcha').show()
      // 进行业务逻辑，若出现错误需重置验证码，执行以下方法
      // if (error) {
      // this.selectComponent('#captcha').refresh()
      // }
      // #endif
      // #ifdef H5
      try {
        // 生成一个验证码对象
        // CaptchaAppId：登录验证码控制台，从【验证管理】页面进行查看。如果未创建过验证，请先新建验证。注意：不可使用客户端类型为小程序的CaptchaAppId，会导致数据统计错误。
        //callback：定义的回调函数
        this.captchaObj = new TencentCaptcha(this.h5_app_id, this.h5Callback, {loading:true});
        // 调用方法，显示验证码
        this.captchaObj.show();
      } catch (error) {
        // 加载异常，调用验证码js加载错误处理函数
        this.loadErrorCallback();
      }
      // #endif
    },
    // 验证码验证结果回调
    weappHandlerVerify: function (ev) {
      // console.log('handlerVerify', ev)
      // 如果使用了 mpvue，ev.detail 需要换成 ev.mp.detail
      if(ev.detail.ret === 0) {
        // 验证成功
        // console.log('ticket:', ev.detail.ticket)
        this.toSuccess(ev.detail.ticket)
      } else {
        // 验证失败
        // 请不要在验证失败中调用refresh，验证码内部会进行相应处理
      }
    },
    // 验证码准备就绪
    weappHandlerReady: function () {
      console.log('验证码准备就绪')
    },
    // 验证码弹框准备关闭
    weappHandlerClose: function (ev) {
      console.log('handlerClose', ev)
      // 如果使用了 mpvue，ev.detail 需要换成 ev.mp.detail,ret为0是验证完成后自动关闭验证码弹窗，ret为2是用户主动点击了关闭按钮关闭验证码弹窗
      if(ev && ev.detail.ret && ev.detail.ret === 2){
        console.log('点击了关闭按钮，验证码弹框准备关闭');
      } else {
        console.log('验证完成，验证码弹框准备关闭');
      }
    },
    // 验证码出错
    weappHandlerError: function (ev) {
      console.log('handlerError', ev.detail.errMsg)
    },
    // 定义验证码js加载错误处理函数
    loadErrorCallback() {
      let appid = this.h5_app_id;
      // 生成容灾票据或自行做其它处理
      let ticket = 'terror_1001_' + appid + '_' + Math.floor(new Date().getTime() / 1000);
      this.h5Callback({
        ret: -1,
        randstr: '@' + Math.random().toString(36).substr(2),
        ticket: ticket,
        errorCode: 1001,
        errorMessage: 'jsload_error'
      });
    },
    // 定义回调函数
    h5Callback(res) {
      // 第一个参数传入回调结果，结果如下：
      // ret         Int       验证结果，0：验证成功。2：用户主动关闭验证码。
      // ticket      String    验证成功的票据，当且仅当 ret = 0 时 ticket 有值。
      // CaptchaAppId       String    验证码应用ID。
      // bizState    Any       自定义透传参数。
      // randstr     String    本次验证的随机串，后续票据校验时需传递该参数。
      // console.log('callback:', res);

      // res（用户主动关闭验证码）= {ret: 2, ticket: null}
      // res（验证成功） = {ret: 0, ticket: "String", randstr: "String"}
      // res（请求验证码发生错误，验证码自动返回terror_前缀的容灾票据） = {ret: 0, ticket: "String", randstr: "String",  errorCode: Number, errorMessage: "String"}
      // 此处代码仅为验证结果的展示示例，真实业务接入，建议基于ticket和errorCode情况做不同的业务处理
      if (res.ret == 0) {
        // 验证成功
        this.toSuccess(res.ticket)
      }else if(res.ret==2){

      }else {
        // 验证失败
        this.$util.showToast({
          title: res.errorMessage
        });
      }
    }
  }
}
</script>

<style scoped lang="scss">
.yp-wechat-verification-code{
}
</style>
