<!--微信隐私协议弹窗-->
<template>
  <uni-popup ref="popup" type="center" :is-mask-click="false">
    <view class="popup-box">
      <view class="weui-half-screen-dialog__hd">
        {{title}}
      </view>
      <view class="weui-half-screen-dialog__bd">
        <text class="weui-half-screen-dialog__tips">{{desc1}}</text>
        <text class="weui-half-screen-dialog__tips color-8BC21F" @click="openPrivacyContract">
          {{urlTitle}}
        </text>
        <text class="weui-half-screen-dialog__tips">{{desc2}}</text>
      </view>
      <view class="weui-half-screen-dialog__ft">
        <button class="weui-btn" @click="handleDisagree">不同意</button>
        <button id="agree-btn" type="default" open-type="agreePrivacyAuthorization" class="weui-btn agree"
                @agreeprivacyauthorization="handleAgree">同意并继续</button>
      </view>
    </view>
  </uni-popup>
</template>

<script>
export default {
  name: "yp-wechat-privacy-popup",
  data() {
    return {
      title: "用户隐私保护提示",
      desc1: "感谢您使用本产品，您使用本产品前应当仔细阅读并同意",
      urlTitle: "《小程序隐私保护指引》",
      desc2: "当您点击同意并开始使用产品服务时，即表示你已理解并同意该条款内容，该条款将对您产生法律约束力。如您拒绝，将无法更好的体验产品。",

      privacyResolves: new Set(),
      closeOtherPagePopUpHooks: new Set()
    }
  },
  mounted(){
    // #ifdef MP-WEIXIN
    this.closeOtherPagePopUpHooks.add(this.closePopUp)
    if (uni.onNeedPrivacyAuthorization) {
      uni.onNeedPrivacyAuthorization((resolve, eventInfo) => {
        // console.log('触发 onNeedPrivacyAuthorization', eventInfo)
        if (typeof this.privacyHandler === 'function') {
          this.privacyHandler(resolve)
        }
      })
    }
    // #endif
  },
  beforeDestroy(){
    // #ifdef MP-WEIXIN
    this.closeOtherPagePopUpHooks.delete(this.closePopUp)
    // #endif
  },
  methods:{
    // #ifdef MP-WEIXIN
    closeOtherPagePopUp(closePopUp){
      this.closeOtherPagePopUpHooks.forEach(hook => {
        if (this.closePopUp !== hook) {
          hook()
        }
      })
    },
    handleAgree(e) {
      this.disPopUp()
      // 这里演示了同时调用多个wx隐私接口时要如何处理：让隐私弹窗保持单例，点击一次同意按钮即可让所有pending中的wx隐私接口继续执行 （看page/index/index中的 wx.getClipboardData 和 wx.startCompass）
      this.privacyResolves.forEach(resolve => {
        resolve({
          event: 'agree',
          buttonId: 'agree-btn'
        })
      })
      this.privacyResolves.clear()
    },
    handleDisagree(e) {
      this.disPopUp()
      this.privacyResolves.forEach(resolve => {
        resolve({
          event: 'disagree',
        })
      })
      this.privacyResolves.clear()
    },
    popUp() {
      this.$refs.popup.open()
    },
    disPopUp() {
      this.$refs.popup.close();
    },
    closePopUp(){
      this.disPopUp()
    },
    privacyHandler(resolve){
      this.privacyResolves.add(resolve)
      this.popUp()
      // 额外逻辑：当前页面的隐私弹窗弹起的时候，关掉其他页面的隐私弹窗
      this.closeOtherPagePopUp(this.closePopUp)
    },
    openPrivacyContract() {
      wx.openPrivacyContract({
        success: res => {
          console.log('openPrivacyContract success')
        },
        fail: res => {
          console.error('openPrivacyContract fail', res)
        }
      })
    }
    // #endif
  }
}
</script>


<style scoped lang="scss">
.popup-box {
  //width: 80vw;
  // height: 40vh;
  overflow: hidden;
  background: #ffffff;
  padding: 30rpx;
  border-radius: 24rpx;

  .weui-half-screen-dialog__hd {
    font-size: 48rpx;
    font-family: Source Han Sans CN-Bold, Source Han Sans CN;
    font-weight: bold;
    color: #000000;
    line-height: 56rpx;
  }

  .weui-half-screen-dialog__bd {
    margin-top: 48rpx;
    text-indent: 2em;
    .weui-half-screen-dialog__tips {
      font-size: 28rpx;
      font-family: Source Han Sans CN-Normal, Source Han Sans CN;
      font-weight: 400;
      color: #000000;
      line-height: 33rpx;
    }
  }

  .weui-half-screen-dialog__ft {
    display: flex;
    justify-content: space-evenly;
    align-items: center;
    margin-top: 48rpx;
    .weui-btn {
      padding: 0 60rpx;
      margin: 0;
      background: none;
      font-size: 32rpx;
      font-family: Source Han Sans CN-Normal, Source Han Sans CN;
      font-weight: 400;
      color: #000000;
      line-height: 80rpx;
      // border: 2rpx solid #8BC21F;
    }

    .agree {
      color: #ffffff;
      background: #09BB07;
    }
  }

  .color-8BC21F {
    color: #0B93F2 !important;
  }
}
</style>
