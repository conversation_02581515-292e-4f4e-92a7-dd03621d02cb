<template>
	<view class="ng-hint-box flex-space-between-center">
		<view class="ng-hint-text">{{text}}</view>
		<uni-icons class="arrow-right" type="arrowright" color="#ffffff" size="16"></uni-icons>
	</view>
</template>

<script>
	export default {
		name: 'ns-hint',
		props: {
			text: {
				type: String,
				default: '请输入提示的内容'
			}
		}
	};
</script>

<style lang='scss'>
.ng-hint-box{
	min-height:40rpx;
	width:640rpx;
	padding:15rpx 30rpx;
	background-color:rgba(0,0,0,0.7);
	border-radius:50rpx;
	color:#ffffff;
	.ng-hint-text{
		color:#ffffff;
		font-size:$ns-font-size-xm;
	}
}
</style>