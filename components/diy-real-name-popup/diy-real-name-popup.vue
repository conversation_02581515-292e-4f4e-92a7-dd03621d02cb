<!--实名验证提示弹窗组件-->
<template>
  <view class="diy-real-name-popup">
    <uni-popup ref="realNamePopupRef" :is-mask-click="false">
      <view class="real-name-popup">
        <view class="real-name-popup-title">暂未实名认证</view>
        <view class="real-name-popup-desc">请先完成实名验证再操作</view>
        <image :src="$util.img('public/static/youpin/questionnaire/real_name.png')" class="real-name-popup-img"/>
        <view class="real-name-popup-op">
          <text class="real-name-popup-op-left" @click="backToHome">暂不实名</text>
          <text class="real-name-popup-op-right" @click="toAuthentication">去认证</text>
        </view>
      </view>
    </uni-popup>
  </view>
</template>


<script>
export default {
  name: "diy-real-name-popup",
  mounted(){
    if(this.$util.getPlatform()=='h5'){
      this.$bus.$on('toShowRealNamePopup', () => {
        this.open()
      });
    }
  },
  methods:{
    open(){
      if(!this.$refs.realNamePopupRef.showPopup){
        this.$refs.realNamePopupRef.open()
      }
    },
    backToHome(){
      this.$refs.realNamePopupRef.close()
      this.$util.redirectTo('/otherpages/shop/home/<USER>', {}, 'reLaunch');
    },
    toAuthentication(){
      this.$refs.realNamePopupRef.close()
      this.$util.redirectTo(`/otherpages/member/real_name_authentication/real_name_authentication`)
    },
  }
}
</script>

<style scoped lang="scss">
.real-name-popup{
  width: 601rpx;
  //height: 734rpx;
  border-radius: 40rpx;
  background: linear-gradient(0deg, rgba(255, 255, 255, 0) 0%, var(--custom-brand-color-10) 100%);
  padding: 40rpx;
  box-sizing: border-box;
  &-title{
    font-size: 40rpx;
    font-weight: 700;
    color: rgba(56, 56, 56, 1);
    margin-top: 46rpx;
    text-align: center;
  }
  &-desc{
    font-size: 28rpx;
    font-weight: 400;
    color: rgba(128, 128, 128, 1);
    text-align: center;
  }
  &-img{
    width: 301rpx;
    height: 249rpx;
    display: block;
    margin: 0 auto;
    margin-top: 68rpx;
  }
  &-op{
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 60rpx;
    &-left{
      width: 248rpx;
      height: 72rpx;
      border-radius: 200rpx;
      background: var(--custom-brand-color-10);
      font-size: 28rpx;
      font-weight: 400;
      color: var(--custom-brand-color);
      display: flex;
      justify-content: center;
      align-items: center;
    }
    &-right{
      width: 248rpx;
      height: 72rpx;
      border-radius: 200rpx;
      background: var(--custom-brand-color);
      font-size: 28rpx;
      font-weight: 400;
      color: rgba(255, 255, 255, 1);
      display: flex;
      justify-content: center;
      align-items: center;
    }
  }
}
</style>
