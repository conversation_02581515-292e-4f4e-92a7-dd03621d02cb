<template>
	<view >
		<view class="share_container" v-if="showShare">
			<view class="close_btn" @tap="closeShare"></view>
			<view class="flex-column-center height100">
				<image class="make_img" :src="canvasImg" mode="widthFix"></image>
				<view class="flex-center">
					<view class="share_btn">
						<view class="share_circle">
							<view class="bg"></view>
						</view>
						<view class="text">分享好友</view>
						<button open-type="share" class="button"></button>
					</view>
					<view class="save_btn"  @tap="saveImg">
						<view class="save_circle">
							<view class="bg"></view>
						</view>
						<view class="text">保存图片</view>
					</view>
				</view>
			</view>
		</view>
		<canvas 
			v-show="showShare"
			canvas-id="firstCanvas">
		</canvas>
		
	</view>
</template>

<script>
	import {mapState} from 'vuex'
	import utils from '@/js/utils/index'
	import baseUrl from '@/js/request/baseUrl.js'
	
	export default{
		props:{
			shareBgImg: {
				type: String,
				default :'/static/imgs/share_posters/share_img.png'
			},
			share_code: {
				type: String,
				default :''
			},
			shareTxt: {
				type: String,
				default :'装修材料移动商城，省时省力省心！'
			},
		},
		data(){
			return{
				utils,
				defaultAvatarUrl:'/static/img/my/default_autor.png',
				canvasImg: '',
				showShare: false,
			}
		},
		computed: {
			...mapState({
				hasLogin: state => state.global.hasLogin,
				userInfo: state => state.global.userInfo,
				userInfoKey: state => state.global.userInfoKey,
				wxMiniCodeImg: state => state.global.wxMiniCodeImg,
				appId: state => state.global.appId,
				storeData: state => state.global.storeData
			}),
		},
		methods: {
			closeShare(){
				this.showShare = false
				this.$store.dispatch('save', {hideTextArea: false})
			},
			openShare(){
				console.log('openShare')
				this.$store.dispatch('save', {hideTextArea: true})
				this.showShare = true
				this.canvasImg = ''
				uni.showToast({
					title: '图片生成中',
					icon: 'loading',
					duration: 2000
				})
				const k = 650/550;

				const code_width=128*k// 小程序码的宽度
				const code_height=128*k// 小程序码的高度

				const bgImg_width=650// 背景图的宽度
				const bgImg_height=1156// 背景图的高度

				const headimg_width=60*k// 头像的宽度
				const headimg_height=60*k// 头像的高度

				const download1 = new Promise((resolve,reject)=>{
					const ctx = wx.createCanvasContext('firstCanvas', this)
					ctx.drawImage(this.shareBgImg, 0, 0, bgImg_width ,bgImg_height)
					ctx.draw(false ,setTimeout(()=>{
						resolve()
					},300));

				})

				const download2=()=>{
					return new Promise((resolve,reject)=>{
						if(!utils.isEmptyObject(this.userInfo) && this.hasLogin && this.userInfoKey.avatarUrl){
							uni.downloadFile({
							  url: this.userInfoKey.avatarUrl,
							  success: (sres) => {
								const ctx = wx.createCanvasContext('firstCanvas', this)
								ctx.save();
								ctx.beginPath(); //开始绘制
								//先画个圆
								ctx.arc(80*k, 792*k, headimg_width/2, 0,  Math.PI * 2, false);
								ctx.strokeStyle="#d5a874";
								ctx.stroke()
								ctx.clip();//画了圆 再剪切  原始画布中剪切任意形状和尺寸。一旦剪切了某个区域，则所有之后的绘图都会被限制在被剪切的区域内
								ctx.drawImage(sres.tempFilePath, 80*k-headimg_width/2, 792*k-headimg_width/2, headimg_width, headimg_height)
								ctx.restore();
								ctx.draw(true,setTimeout(()=>{
									resolve()
								},300));
							  },
							  fail:(fres) => {
								reject()
							  }
							})
						}else{
							const ctx = wx.createCanvasContext('firstCanvas', this)
							ctx.save();
							ctx.beginPath(); //开始绘制
							//先画个圆
							ctx.arc(80*k, 792*k, headimg_width/2, 0,  Math.PI * 2, false);
							ctx.clip();//画了圆 再剪切  原始画布中剪切任意形状和尺寸。一旦剪切了某个区域，则所有之后的绘图都会被限制在被剪切的区域内
							ctx.drawImage(this.defaultAvatarUrl, 80*k-headimg_width/2, 792*k-headimg_width/2, headimg_width, headimg_height)
							ctx.restore();
							ctx.draw(true,setTimeout(()=>{
								resolve()
							},300));
						}
					})
				}

				const download3=()=>{
					return new Promise((resolve,reject)=>{
						uni.downloadFile({
						  url: `${baseUrl.url + this.$api.storeQRCode}?appId=${this.appId}`,
						  success:  (sres) => {
							const ctx = wx.createCanvasContext('firstCanvas', this)
							ctx.save();
							ctx.beginPath(); //开始绘制
							//先画个圆
							ctx.arc(440*k, 830*k, code_width/2, 0,  Math.PI * 2, false);
							ctx.clip();//画了圆 再剪切  原始画布中剪切任意形状和尺寸。一旦剪切了某个区域，则所有之后的绘图都会被限制在被剪切的区域内
							ctx.drawImage(sres.tempFilePath, 440*k-code_width/2, 830*k-code_width/2, code_width, code_height)
							ctx.restore();

							const name = (!utils.isEmptyObject(this.userInfo) && this.hasLogin) ? this.userInfoKey.nickName : ''
							ctx.setFontSize(15*k)
							ctx.fillText(name, 121*k, 799*k)

							ctx.setTextAlign('left')
							ctx.setFontSize(20*k)
							ctx.fillText(this.shareTxt, 55*k, 865*k)

							this.drawRoundedRect(ctx,55*k, 880*k, 165, 30, 10, true, '#f3772f', false)

							ctx.fillStyle="#fff";
							ctx.setFontSize(16*k)
							ctx.fillText('长按识别小程序码', 60*k, 899*k)
							ctx.fillStyle="#000";
							ctx.draw(true,setTimeout(()=>{
								resolve()
							},300));
						  },fail:(fres) =>{
							reject()
						  }
						})

					})
				}

				download1.then(download2).then(download3).then(()=>{
					uni.canvasToTempFilePath({
					  x: 0,
					  y: 0,
					  width: bgImg_width,
					  height: bgImg_height,
					  destWidth: bgImg_width,
					  destHeight: bgImg_height,
					  canvasId: 'firstCanvas',
					  quality: 1,
					  success: (res)=> {
						this.canvasImg = res.tempFilePath
					  },
					  complete: res=>{
						  console.log(res)
					  }
					} , this)
				})
			},
			drawRoundedRect(ctx, x, y, width, height, r, fill, fillStyle, stroke) {
				ctx.save();
				ctx.beginPath(); // draw top and top right corner
				ctx.moveTo(x + r, y);
				ctx.arcTo(x + width, y, x + width, y + r, r); // draw right side and bottom right corner
				ctx.arcTo(x + width, y + height, x + width - r, y + height, r); // draw bottom and bottom left corner
				ctx.arcTo(x, y + height, x, y + height - r, r); // draw left and top left corner
				ctx.arcTo(x, y, x + r, y, r);
				ctx.fillStyle= fillStyle;
				if (fill) { ctx.fill(); }
				if (stroke) { ctx.stroke(); }
				ctx.restore();
			},

			saveImg(){
				uni.saveImageToPhotosAlbum({
					filePath: this.canvasImg,
					success: () => {
						console.log('save success');
						uni.showToast({
							title:'已保存到相册',
							icon: 'none'
						})
					}
				});
			},
		},
	}
</script>

<style lang="scss" scoped>
	canvas{
		width: 650px; height: 1156px;  position: absolute;top: -400000px;
	}
	.share_container{
		position: fixed;
		width: 100%;
		height: 100%;
		top: 0;
		bottom: 0;
		background-color: rgba(0,0,0,0.8);
		// overflow: hidden;
		.share_btn{
			width: 120upx;
			margin: 0 80upx;;
			position: relative;
			.button{
				opacity: 0;
				width: 100%;
				height: 100%;
				position: absolute;
				top: 0;
			}
			.share_circle{
				width: 120upx;
				height: 120upx;
				background:linear-gradient(180deg,rgba(136,221,77,1) 0%,rgba(125,212,61,1) 100%) ;
				border-radius:50%;
				.bg{
					width: 100%;
					height: 100%;
					background: url(~@/static/img/icon_wechat2.png) center center /66upx 66upx no-repeat;
				}
			}
		}
		.save_btn{
			width: 120upx;
			margin: 0 80upx;;
			position: relative;
			.save_circle{
				width: 120upx;
				height: 120upx;
				background:linear-gradient(180deg,rgba(233,206,165,1) 0%,rgba(217,173,122,1) 100%) ;
				border-radius:50%;
				.bg{
					width: 100%;
					height: 100%;
					background: url(~@/static/img/icon_download.png) center center /48upx 51upx no-repeat;
				}
			}
		}
		.text{
			color: #999;
			font-size: 28upx;
			text-align: center;
			margin-top: 5upx;
		}
		
		.close_btn{
			position: absolute;
			width: 100%;
			height: 100%;
			top: 0;;
			left: 0;
		}
		.make_img{
			position: relative;
			width: 73%;
			height: auto; 
			min-height: 970upx;
			margin:0 auto 30upx; 
			display: block;
		}
	}
</style>
