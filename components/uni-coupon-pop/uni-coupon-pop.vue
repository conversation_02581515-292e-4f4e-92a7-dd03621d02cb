<template>
  <div>
    <uni-popup ref="coupon" :custom="true" :mask-click="false">
			<view class="coupon-model" :class="list.length < 4 ? boxClass[list.length-1]:boxClass[3]" :style="{'background-image': `url(${list.length < 4 ? $util.img(img[list.length-1]):$util.img(img[3])})`}" v-if="list.length">
				<view class="coupon-header">
					<view class="title">恭喜您获得以下优惠券</view>
					<view class="tip">马上去使用吧！</view>
				</view>
				<view class="coupon-box">
					<view class="coupon-list" v-for="(item, index) in list" :key="index"  :style="{'background-image': `url(${$util.img('public/static/youpin/coupon_border.png')})`}" @click="toGoodList(item)">
						<view class="left">
							<view class="info">
                <view>¥</view>
                <template v-if="item.money < 100">
                  <view v-if="item.money.split('.')[1] == 0">{{item.money.split('.')[0]}}</view>
                  <view v-if="item.money.split('.')[1] > 0">{{item.money.split('.')[0]}}.<span class="point-class">{{item.money.split('.')[1]}}</span></view>
                </template>
                <view class="money-thousand" v-else>{{item.money.split('.')[0]}}.<span class="point-class">{{item.money.split('.')[1]}}</span></view>
                <!-- <view class="money-thousand" v-else>{{item.money.split('.')[0]}}.<span class="point-class">{{item.money.split('.')[1]}}</span></view> -->
              </view>
						</view>
						<view class="right">
							<view>
								<view class="name">{{item.desc}}</view>
								<!-- #ifdef H5 || APP-PLUS -->
								<view class="time h5-time">有效期至{{item.end_time}}</view>
								<!-- #endif  -->
								<!-- #ifdef MP-WEIXIN -->
								<view class="time">有效期至{{item.end_time}}</view>
			  					<!-- #endif  -->
							</view>
							  <!-- #ifdef H5 || APP-PLUS -->
								<view class="btn"><view class="h5-btn">去使用</view></view>
								<!-- #endif  -->
								<!-- #ifdef MP-WEIXIN -->
								<view class="btn">去使用</view>
								<!-- #endif  -->
						</view>
					</view>
				</view>
				<!-- <image class="coupon_bg" src="/static/coupon_bg.png" alt="" /> -->
				<!-- <image class="coupon_footer" src="/static/coupon_footer.png" alt="" /> -->
			</view>
			<image :src="$util.img('public/static/youpin/icon-close-overlay.png')" class="pop-ad-info-close" @click="$refs.coupon.close()"></image>
		</uni-popup>
  </div>
</template>

<script>
  export default {
    data() {
      return {
        list: [],
        img: ['public/static/youpin/coupon_bg_1.png', 'public/static/youpin/coupon_bg_2.png', 'public/static/youpin/coupon_bg_3.png', 'public/static/youpin/coupon_bg_4.png'],
        boxClass: ['box1', 'box2', 'box3', 'box4'],
      }
    },
    onLoad() {
      this.$util.toShowCouponPopup(this)
    },
    methods: {
			open() {
				// this.$refs.coupon.open();
        this.listInfo()
			},
      async listInfo() {
        try{
          let res = await this.$api.sendRequest({
						url: this.$apiUrl.use_remind,
						async: false,
					});
          if(res.data.length) {
            this.list = res.data
            this.$refs.coupon.open();
          }
        }catch{

        }
      },
      //去使用优惠券
      toGoodList(item) {
        this.$util.redirectTo('/otherpages/goods/coupon_goods_list/coupon_goods_list', {
          goodscoupon_type_id: item.goodscoupon_type_id
        })
      }
    },
  }
</script>

<style lang="scss" scoped>
.coupon-model{
  display: flex;
  flex-direction: column;
  position: relative;
  z-index: 111;
  width: 620rpx;
  background-size: cover;
  background-position: center;
  .coupon-header{
    background-size: cover;
    background-position: center;
    margin-bottom: 117rpx;
  }
  .title{
    font-size: 40rpx;
    line-height: 52rpx;
    background-image: -webkit-linear-gradient(bottom,red,#ff5f60,#f0c41b);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    padding: 124rpx 0 14rpx;
    text-align: center;
    font-weight: bold;
  }
  .tip{
    font-size: 30rpx;
    line-height: 32rpx;
    background-image: -webkit-linear-gradient(0deg, #FC5A50 0%, #FF561A 46.7529296875%, #FF2637 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    text-align: center;
  }
  .coupon-box{
    flex: 1;
    padding: 0 54rpx 0;
    background-size: 100% 100%;
    background-position: center;
    position: relative;
    margin-bottom: 28px;
    overflow-y: auto;
    .coupon-list{
      display: flex;
      background-size: cover;
      background-position: center;
      height: 120rpx;
      margin-bottom: 20rpx;
      position: relative;
      z-index: 11;
      &:last-child{
        margin-bottom: 0;
      }
      .left{
        width: 70px;
        display: flex;
        align-items: center;
        justify-content: center;
        // padding: 12px 0 0 4px;
        .info{
          display: flex;
          align-items: baseline;
          &>view:first-child{
            display: inline-block;
            font-size: 26rpx;
            color: #EB0000;
          }
          &>view:last-child{
            display: inline-block;
            font-size: 48rpx;
            color: #EB0000;
            line-height: 80rpx;
          }
        }
        .point-class{
          font-size: 35rpx;
        }
        .money-thousand{
          font-size: 41rpx !important;
          &>span{
            font-size: 29rpx;
          }
        }
      }
      .right{
        width: 238rpx;
        flex: 1;
        display: flex;
        align-items: center;
        position: relative;
        margin-left: 18rpx;
        &>view:first-child{
          flex: 1;
          overflow: hidden;
          height: 100%;
          .name{
            font-size: 24rpx;
            line-height: 36rpx;
            padding: 24rpx 0 8rpx;
          }
          .time{
            font-size: 18rpx;
            line-height: 30rpx;
            color: #999999;
            white-space: nowrap;
          }
          .h5-time{
            display: flex;
            width: 119px;
            margin-left: -13px;
            transform: scale(0.78);
          }
        }
        .btn{
          display: flex;
          justify-content: center;
          align-items: center;
          width: 94rpx;
          height: 38rpx;
          background: linear-gradient(90deg, #FFAB37 0%, #FFF594 100%);
          border-radius: 19px;
          font-size: 24rpx;
          color: #822D02;
          margin: 0 10rpx 0 0;
        }
        .h5-btn{
          transform: scale(0.8);
        }
      }

    }
  }
  .coupon_bg{
    position: absolute;
    top: 270rpx;
    width: 100%;
    height: 153rpx;
    z-index: -1;
  }
  .coupon_footer{
    // position: absolute;
    // bottom: 0;
    // left: 0;
    width: 100%;
    height: 51rpx;
  }
}
.box1{
  height: 565rpx;
}
.box2{
  height: 660rpx;
}
.box3{
  height: 800rpx;
}
.box4{
  height: 850rpx;
}
.pop-ad-info-close{
  width: 88rpx;
  height: 88rpx;
  display: block;
  margin: 60rpx auto 0;
}
</style>
