<!--商品详情中显示更多好物的组件-->
<template>
  <view class="more-goodies" v-if="list && list.length > 0">
    <view class="more-goodies-header" v-if="title">
      <image :src="$util.img('public/static/youpin/goods/more-goodies.png')" class="more-goodies-header-img"></image>
      <text class="more-goodies-header-title">{{title}}</text>
    </view>
    <view class="more-goodies-list">
      <view class="more-goodies-list-one" v-for="(item,index) in list" :key="index" @click="$util.toProductDetail(item)">
        <image :src="$util.img(item.goods_image)" class="more-goodies-list-one-img" mode='aspectFit' @error="imageError(index,list)"></image>
        <view class="more-goodies-list-one-info">
          <view class="more-goodies-list-one-info-title">{{item.goods_name}}</view>
          <view class="more-goodies-list-one-info-two">
            <text class="more-goodies-list-one-info-two-price"><text class="more-goodies-list-one-info-two-price">￥</text>{{item.retail_price}}</text>
            <text class="more-goodies-list-one-info-two-drawing">￥{{item.market_price}}</text>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import apiurls from "../../common/js/apiurls";

export default {
  name: "diy-goods-detail-more-goodies",
  props:{
    title:{
      type:String,
      default:"更多好物"
    },
    sku_id:{
      type:[Number,String],
      required:true
    }
  },
  data(){
    return {
      list: [],
    }
  },
  watch:{
    sku_id: async function(newValue,oldValue){
      await this.getData()
    }
  },
  async mounted(){
    await this.getData();
  },
  methods:{
    async getData(){
      try{
        let res = await this.$api.sendRequest({
          url: apiurls.moreGoodiesUrl,
          async: false,
          data:{
            sku_id: this.sku_id
          }
        })
        if(res.code == 0){
          this.list = res.data
        }
      }catch (e) {

      }
    },
    imageError(index,dataList) {
      dataList[index] && (dataList[index].goods_image = this.$util.getDefaultImage().default_goods_img);
      this.$forceUpdate();
    },
  }
}
</script>

<style scoped lang="scss">
  .more-goodies{
    width: 100%;
    padding: 0 20rpx;
    padding-top: 20rpx;
    box-sizing: border-box;
    background-color: #fff;
    margin-bottom: 20rpx;
    &-header{
      margin-bottom: 20rpx;
      display: flex;
      align-items: center;
      &-img{
        width: 32rpx;
        height: 32rpx;
      }
      &-title{
        font-size: 30rpx;
        font-weight: 400;
        letter-spacing: 0px;
        line-height: 44rpx;
        color: rgba(56, 56, 56, 1);
        margin-left: 14rpx;
      }
    }
    &-list{
      width: 100%;
      display: flex;
      justify-content: space-between;
      flex-wrap: wrap;
      &-one{
        width: 346rpx;
        border-radius: 20rpx;
        background: rgba(255, 255, 255, 1);
        box-shadow: 0rpx 4rpx 12rpx  rgba(0, 0, 0, 0.05);
        margin-bottom: 20rpx;
        &-img{
          width: 100%;
          height: 346rpx;
          border-radius: 20rpx 20rpx 0 0;
        }
        &-info{
          padding: 10rpx 10rpx 20rpx 10rpx;
          box-sizing: border-box;
          &-title{
            font-size: 28rpx;
            font-weight: 400;
            line-height: 32rpx;
            color: rgba(56, 56, 56, 1);
            word-break: break-all;
            text-overflow: ellipsis;
            display: -webkit-box;
            -webkit-box-orient: vertical;
            -webkit-line-clamp: 2;
            overflow: hidden;
          }
          &-two{
            display: flex;
            align-items: baseline;
            margin-top: 10rpx;
            &-price{
              font-size: 36rpx;
              font-weight: 700;
              line-height: 42.2rpx;
              color: var(--custom-brand-color);
              &-symbal{
                font-size: 32rpx;
              }
            }
            &-drawing{
              font-size: 20rpx;
              font-weight: 400;
              line-height: 20rpx;
              text-decoration-line: line-through;
              color: rgba(166, 166, 166, 1);
              margin-left: 14rpx;
            }
          }
        }
      }
    }
  }
</style>
