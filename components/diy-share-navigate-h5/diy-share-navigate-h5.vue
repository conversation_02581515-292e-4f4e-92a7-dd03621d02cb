<!--编译h5，分享提示导航-->
<template>
  <view class="yd-popup" v-show="isShow" v-on:click="maskClose">
    <image :src="$util.img('public/static/youpin/weixin-share-tip.png')" class="share-tip" v-if="isWeiXin"></image>
    <image :src="$util.img('public/static/youpin/browser-share-tip.png')" class="share-tip" v-else></image>
  </view>
</template>

<script>
// #ifdef H5
import {isOnXianMaiApp, shareMiniProgramSchemeGo} from "../../common/js/h5/appOP";
// #endif

export default {
  name: "diy-share-navigate-h5",
  data(){
    return{
      isShow: false,
      isWeiXin:this.$util.isWeiXin(),
      isOnXianMaiApp:false,
    }
  },
  methods:{
    maskClose(event){
      if(event.target.offsetTop==0){
        this.isShow=false;
      }
    },
    open(share_data={},callback){
      if(this.isOnXianMaiApp){
        let shareJson={
          title:share_data.desc ? this.$util.shareTitleAddNickname(share_data.desc) : '',
          desc:share_data.desc ? share_data.desc : '',
          webpageUrl:share_data.webpageUrl ? share_data.webpageUrl : '',
          thumbImage:share_data.imageUrl ? this.$util.imageCdnResize(share_data.imageUrl,{image_process:'resize,w_300','x-oss-process':'image/resize,w_300'}) : '',
          path:share_data.link,
        };
        shareMiniProgramSchemeGo(shareJson);
        if(callback && typeof callback == 'function'){
          callback()
        }
      }else{
        this.isShow=true;
        if(callback && typeof callback == 'function'){
          callback()
        }
      }

    },
    close(){
      this.isShow=false;
    }
  },
  created(){
    // #ifdef H5
    this.isOnXianMaiApp=isOnXianMaiApp;
    // #endif
  }
}
</script>

<style scoped lang="scss">
.yd-popup{
  background: rgba(0, 0, 0, 0.4);
  width: 100%;
  height: 100%;
  z-index: 998;
  position: fixed;
  top: 0;
  left: 0;
  .share-tip{
    width: 100%;
    height: 447rpx;
    display: block;
  }
}
</style>
