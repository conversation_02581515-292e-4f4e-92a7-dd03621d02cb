<template>
  <!-- 选择弹窗 -->
  <view>
    <view class="content-select">
      <view class="content-select-box">
        <text class="name-text"
          >{{ title }}<text class="requires" v-if="required">*</text></text
        >
        <text class="placeholder" @click="open">
          <text class="name-text" v-if="currentText">{{ currentText }}</text>
          <text v-else
            >请选择<text class="iconfont iconright icon" style="font-size: 24rpx"></text>
          </text>
        </text>
      </view>
    </view>
    <!-- 货物状态下弹窗 -->
    <uni-popup ref="popup-upoff" type="bottom" class="uni-popup-father" @change="change">
      <view class="uni-popup-popup">
        <view class="popup-header">
          <view class="popup-close">
            <text class="iconfont iconguanbi iconclose" @click="closeOp"></text>
          </view>
          <view class="popup-title"
            ><text>{{ title }}</text></view
          >
        </view>
        <view class="popup-content">
          <view class="popup-content-list">
            <!-- min-height: calc(100% + 1px)解决部分ios频繁滚动卡死 -->
            <view style="min-height: calc(100% + 1px)">
              <view
                class="popup-content-list-item"
                v-for="(item, index) in dataList"
                :key="index"
                @click="hanldSelectReason(item)"
              >
                <view class="item-reason">{{ item }}</view>
                <view class="item-select-icon">
                  <text
                    v-if="item == currentText"
                    class="icon iconfont iconyuan_checked"
                  ></text>
                  <text v-else class="icon iconfont iconyuan_checkbox"></text>
                </view>
              </view>
            </view>
          </view>
        </view>
      </view>
    </uni-popup>
  </view>
</template>
<script>
export default {
  props: {
    // 是否禁用
    disabled: {
      type: Boolean,
      default: false,
    },
    // 绑定值
    value: {
      type: String,
      default: "",
    },
    // 标题
    title: {
      type: String,
      default: "",
    },
    // 数据
    dataList: {
      type: Array,
      default: function () {
        return [];
      },
    },
    // 必填标志
    required: {
      type: Boolean,
      default: true,
    },
  },
  data() {
    return {
      // currentId: "",
      currentText: "",
    };
  },
  options: { styleIsolation: 'shared' },
  methods: {
    open() {
      if (!this.disabled) {
        this.$refs["popup-upoff"].open();
      }
      this.$emit('openModel')
    },
    closeOp() {
      this.$refs["popup-upoff"].close();
    },
    change(e) {
      if(!e.show) {
        this.$emit('closeModel')
      }
    },
    hanldSelectReason(data) {
      this.dataList.map((item) => {
        if (data === item) {
          // this.currentId = item.id;
          // this.currentText = item.reason;
          this.currentText = item;
          this.$emit("select", item, this.value);
        }
      });
      this.closeOp();
    },
  },
};
</script>
<style lang="scss" scoped>
// 弹窗
.content-select {
  width: 100%;
  margin-top: 30rpx;
  .content-select-box {
    display: flex;
    align-items: center;
    justify-content: space-between;

    .name-text {
      font-size: 28rpx;
      font-weight: 400;
      color: #333333;
      .requires {
        color: var(--custom-brand-color);
      }
    }
    .placeholder {
      font-size: 28rpx;
      font-weight: 400;
      color: #999999;
    }
    .pirce {
      font-size: 32rpx;
      font-weight: bold;
      color: var(--custom-brand-color);
    }
  }
  .tips {
    font-size: 24rpx;
    font-weight: 400;
    color: #999999;
  }
}
.uni-popup-popup {
  position: relative;
  width: 100%;
  box-sizing: border-box;
  padding: 55rpx 30rpx 10rpx;
  border-radius: 20rpx 20rpx 0 0;
  background-color: #fff;
  .popup-header {
    width: 100%;
    .popup-close {
      position: absolute;
      right: 30rpx;
      top: 27rpx;
      color: #cccccc;
    }
    .popup-title {
      width: 100%;
      text-align: center;
      font-size: 32rpx;
      font-weight: bold;
      color: #333333;
      padding-bottom: 16rpx;
    }
  }
  .popup-content {
    width: 100%;
    .popup-content-list {
      width: 100%;
      height: 60vh;
      overflow: auto;
      -webkit-overflow-scrolling: touch;
      .popup-content-list-item {
        width: 100%;
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 10rpx 0;
        border-bottom: 1px solid #eeeeee;
        .item-reason {
          font-size: 28rpx;
          font-weight: 400;
          color: #333333;
        }
        .item-select-icon {
          font-size: 40rpx;
          color: #999999;
          .iconyuan_checked {
            color: var(--custom-brand-color);
          }
          .icon{
            font-size: 44rpx;
          }
        }
      }
    }
  }
}
/deep/ {
  .uni-popup__wrapper.bottom{
    background: inherit !important;
  }
  .uni-popup__wrapper-box{
    background: inherit !important;
    border-radius: inherit !important;
    overflow-y: inherit !important;
  }
}
</style>
