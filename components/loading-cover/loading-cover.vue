<template>
	<view class="loading-layer" v-show="isShow">
		<view class="loading-anim">
<!--			<view class="box item">-->
<!--				<view class="border out item ns-border-color-top ns-border-color-left"></view>-->
<!--			</view>-->
      <canvas id="loading_animation_id" type="2d" style="width: 120rpx; height: 120rpx;"></canvas>
      <text class="loading-anim-text">正在加载...</text>
		</view>
	</view>
</template>

<script>
// #ifdef MP-WEIXIN
import lottie from 'lottie-miniprogram';
// #endif
// #ifdef H5
import lottie from 'lottie-web';
// #endif
let ani = null; // 必须放在外面，uni里不要挂在this上,否则会触发循环引用的报错
import nsLoading from "@/components/ns-loading/ns-loading.vue"
export default {
	name: 'loading-cover',
	data() {
		return {
			isShow: true,
      animationData: null
		};
	},
	components:{
		nsLoading
	},
  async mounted(){
    if(!this.$store.state.loadingAnimationData){
      let animationData = await this.getAnimationData('/static/youpin/loading_animation.json')
      await this.$store.dispatch('writeLoadingAnimationData',animationData);
    }
    this.animationData = this.$store.state.loadingAnimationData;
    if(this.isShow){
      await this.loadingAnimation();
    }
  },
	methods: {
		show() {
			this.isShow = true;
      this.loadingAnimation();
		},
		hide() {
			this.isShow = false;
		},
    async getAnimationData(animationDataUrl){
      let animationData = {};
      try {
        let res = await this.$api.sendRequest({
          url: animationDataUrl,
          async: false,
          is_xm_url: true
        })
        animationData = res
      }catch (e) {
        console.log('err',e)
      }
      return animationData;
    },
    waitData(){
      return new Promise((resolve ,reject)=>{
        let intervalObj=setInterval(()=>{
          if(this.animationData){
            clearInterval(intervalObj);
            resolve();
          }
        },50)
      })
    },
    async loadingAnimation(){
      if(this.animationData && typeof this.animationData=='object' && Object.keys(this.animationData).length>0){
        await this.waitData();
        // #ifdef MP-WEIXIN
        uni.createSelectorQuery().in(this).select('#loading_animation_id').node(res => {
          // console.log(res); // 节点对应的 Canvas 实例。
          const canvas = res.node;
          const context = canvas.getContext('2d');
          canvas.width = this.animationData.w;
          canvas.height = this.animationData.h;
          lottie.setup(canvas);
          ani = lottie.loadAnimation({
            loop: true,
            autoplay: true,
            animationData: this.animationData,
            rendererSettings: {
              context
            }
          });
        }).exec();
        // #endif
        // #ifdef H5
        const canvas = document.getElementById('loading_animation_id');
        // canvas.width = this.animationData.w;
        // canvas.height = this.animationData.h;
        ani = lottie.loadAnimation({
          loop: true,
          autoplay: true,
          container: canvas,
          renderer: 'canvas',
          animationData: this.animationData,
          // assetsPath: '/mini-h5/static/images/'
        });
        // #endif
      }

    }
	},
};
</script>

<style lang="scss">
@keyframes spin {
	from {
		transform: rotate(0deg);
	}

	to {
		transform: rotate(360deg);
	}
}
.loading-layer {
	width: 100vw;
	height: 100vh;
	position: fixed;
	top: 0;
	left: 0;
	z-index: 997;
	background: #f8f8f8;
}

.loading-anim {
	position: absolute;
	left: 50%;
	top: 50%;
	transform: translate(-50%, -50%);
  &-text{
    font-size: 30rpx;
    font-weight: 400;
    line-height: 43.44px;
    color: rgba(227, 60, 100, 1);
    margin-top: 26rpx;
  }
}

.loading-anim > .item {
	position: relative;
	width: 35px;
	height: 35px;
	perspective: 800px;
	transform-style: preserve-3d;
	transition: all 0.2s ease-out;
}

.loading-anim .border {
	position: absolute;
	border-radius: 50%;
	border: 3px solid;
}

.loading-anim .out {
	top: 15%;
	left: 15%;
	width: 70%;
	height: 70%;
	// border-left-color: red !important;
	border-right-color: rgba($color: #000000, $alpha: 0) !important;
	// border-top-color: rgba($color: #000000, $alpha: 0) !important;
	border-bottom-color: rgba($color: #000000, $alpha: 0) !important;
	animation: spin 0.6s linear normal infinite;
}

.loading-anim .in {
	top: 25%;
	left: 25%;
	width: 50%;
	height: 50%;
	border-top-color: transparent !important;
	border-bottom-color: transparent !important;
	animation: spin 0.8s linear infinite;
}

.loading-anim .mid {
	top: 40%;
	left: 40%;
	width: 20%;
	height: 20%;
	border-left-color: transparent;
	border-right-color: transparent;
	animation: spin 0.6s linear infinite;
}
</style>
