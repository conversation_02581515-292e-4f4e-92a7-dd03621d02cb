<template>
	<view>
		<!-- 分享弹窗 -->
		<view @touchmove.prevent.stop>
			<uni-popup ref="sharePopup" type="bottom" class="share-popup" :bottom-radius="true">
				<block slot="container">
					<canvas v-if="!imagePath" class="canvas canvas1" :style="{width:canvasOptions.width+'px',height:canvasOptions.height+'px',borderRadius:canvasOptions.borderRadius}" canvas-id="myCanvas"></canvas>
					<view class="poster" :style="{'margin-top':isIPhoneX?'80rpx':''}">
						<image v-show="!isShowLoading" class="canvas" v-if="imagePath" :style="{width:canvasOptions.width+'rpx',height:canvasOptions.height+'rpx',borderRadius:canvasOptions.borderRadius}" :src="imagePath" mode=""></image>
					</view>
					<view class="loading-layer" v-show="isShowLoading">
						<view class="loading-anim">
							<view class="box item">
								<view class="border out item ns-border-color-top ns-border-color-left"></view>
							</view>
						</view>
					</view>
				</block>
				<view>
					<view class="share-title">分享到</view>
					<view class="share-content">
						<!-- #ifdef MP -->
						<view class="share-box">
							<button class="share-btn" :plain="true" open-type="share" :data-share-type="shareType">
								<image :src="$util.img('public/static/youpin/goods/wechat-share.png')" mode=""></image>
								<text>链接</text>
							</button>
						</view>
						<!-- #endif -->
						<view class="share-box" @click="saveImage">
							<button class="share-btn" :plain="true">
								<image :src="$util.img('public/static/youpin/goods/save-image.png')" mode=""></image>
								<text>保存图片</text>
							</button>
						</view>
					</view>
					<view class="share-footer" :class="{'share-footer-padding' : bottomPadding<1}" @click="closeSharePopup"><text>取消分享</text></view>
				</view>
			</uni-popup>
		</view>
	</view>
</template>

<script>
	export default {
		props: {
			sharePopupOptions: {
				type:Array,
				default:[]
			},
			canvasOptions:{
				type:Object,
				default:{
					width:620,
					height:917,
					borderRadius:0
				}
			},
      shareType:{
        type: String
      }
		},
		data() {
			return {
				isIPhoneX:false,
				ctx: null, //画布上下文
				counter: -1, //计数器
				drawPathQueue: [], //画图路径队列
				imagePath:'',
				isShowLoading:true,
				height:0,
				windowHeight:0,
				windowWidth:0,
        bottomPadding:0,  //安全区的高度
      };
		},
		computed: {
			myPx() {
				return 1
			},
			drawQueue(){
				return this.sharePopupOptions
			}
		},
		created() {
      this.getSafeArea();
			this.ctx = uni.createCanvasContext('myCanvas', this);
		},
		watch: {
			drawPathQueue(newVal, oldVal) {
				/* 所有元素入队则开始绘制 */
				if (newVal.length === this.drawQueue.length) {
					for (let i = 0; i < this.drawPathQueue.length; i++) {
						for (let j = 0; j < this.drawPathQueue.length; j++) {
							let current = this.drawPathQueue[j]
							/* 按顺序绘制 */
							if (current.index === i) {
								/* 文本绘制 */
								if (current.type === 'text') {
									this.ctx.setFillStyle(current.color || '#000')
									this.ctx.setFontSize(current.size * this.myPx)

									if(current.textBaseline){
									    let textBaselineColor = current.textBaselineColor || '#999999'
									    this.ctx.strokeStyle = textBaselineColor;
									    this.ctx.moveTo(current.x,current.y-5);
									    this.ctx.lineTo(this.ctx.measureText(current.text).width+20,current.y-5);
									    this.ctx.stroke();
									    this.ctx.textBaseline = current.textBaseline;
									}
									if(current.width && current.text){
									    let chr = current.text.split("")
									    let temp = ""
									    let row = []
									    let w = current.width
									    let lineNum = current.lineNum || 1
									    let lineHeight = current.lineHeight || 20;
									    for (let a = 0; a<chr.length;a++){
									      if( this.ctx.measureText(temp).width < w && this.ctx.measureText(temp+(chr[a])).width <= w){
									          temp += chr[a];
									          if(a==chr.length-1){
									            row.push(temp);
									          }
									      }else{
									          row.push(temp);
									          temp = chr[a];
									      }
									    }
									    if(lineNum<=row.length){
									      for(let b=0;b<lineNum;b++){
											if(current.fontWeight){
												this.ctx.fillText(row[b], current.x * this.myPx, current.y * this.myPx + 0.5)
												this.ctx.fillText(row[b], current.x * this.myPx + 0.5, current.y * this.myPx)
											}else{
												this.ctx.fillText(row[b], current.x * this.myPx, current.y * this.myPx)
											}
									       //每行字体y坐标间隔20
									        current.y = current.y + lineHeight
									      }
									    }else{
									      for(let b=0;b<row.length;b++){
									        if(current.fontWeight){
									        	this.ctx.fillText(row[b], current.x * this.myPx, current.y * this.myPx + 0.5)
									        	this.ctx.fillText(row[b], current.x * this.myPx + 0.5, current.y * this.myPx)
									        }else{
									        	this.ctx.fillText(row[b], current.x * this.myPx, current.y * this.myPx)
									        }
											//每行字体y坐标间隔20
									        current.y = current.y + lineHeight
									      }
									    }
									}else{
									   if(current.fontWeight){
											this.ctx.fillText(current.text, current.x * this.myPx, current.y * this.myPx + 0.5)
											this.ctx.fillText(current.text, current.x * this.myPx + 0.5, current.y * this.myPx)
									   }else{
											this.ctx.fillText(current.text, current.x * this.myPx, current.y * this.myPx)
									   }
									}
									this.counter--
								}
								/* 图片绘制 */
								if (current.type === 'image') {
									if(current.path){
										if(!current.radius){ // 绘制普通图片
											this.ctx.drawImage(current.path, current.x * this.myPx, current.y * this.myPx, current.width * this.myPx, current.height * this.myPx)
										}else { // 绘制圆形图片
											let d = 2 * current.radius, cx = current.x + current.radius, cy = current.y + current.radius;
											this.ctx.save()
											this.ctx.beginPath()
											this.ctx.arc(cx, cy, current.radius, 0, 2 * Math.PI);
											this.ctx.clip();
											this.ctx.drawImage(current.path, current.x * this.myPx, current.y * this.myPx, d * this.myPx, d * this.myPx);
											this.ctx.restore()
										}
									}else{
										this.ctx.fillStyle=current.background;
										this.ctx.fillRect(current.x * this.myPx, current.y * this.myPx, current.width * this.myPx, current.height * this.myPx);
									}

									this.counter--
								}
							}
						}
					}
				}
			},
			counter(newVal, oldVal) {
				if (newVal === 0) {
					this.ctx.draw()
					/* draw完不能立刻转存，需要等待一段时间 */
					setTimeout(() => {
						uni.canvasToTempFilePath({
							canvasId: 'myCanvas',
							success: (res) => {
								// 在H5平台下，tempFilePath 为 base64
								// console.log('图片已保存至本地：', res.tempFilePath)
								uni.saveFile({
									tempFilePath: res.tempFilePath,
									success: (res) => {
										this.imagePath = res.savedFilePath
										this.isShowLoading = false
										// console.log('本地保存路径为', res.savedFilePath);
									}
								});
							},
							fail: (err) => {
								console.log('err', err)
							}
						}, this)
					}, 100)
				}
			}
		},
		methods: {
			open() {
				this.isIPhoneX = this.$util.isIPhoneX()
				this.$refs.sharePopup.open();
				if(!this.imagePath){
					this.isShowLoading = true
					this.generateImg()
				}

			},
			closeSharePopup() {
				this.$refs.sharePopup.close()
			},
			generateImg() {
				this.counter = this.drawQueue.length
				this.drawPathQueue = []
				/* 将图片路径取出放入绘图队列 */
				for (let i = 0; i < this.drawQueue.length; i++) {
					let current = this.drawQueue[i]
					current.index = i
					/* 如果是文本直接放入队列 */
					if (current.type === 'text') {
						this.drawPathQueue.push(current)
						continue
					}
					/* 图片需获取本地缓存path放入队列 */
					if(current.path){
						uni.getImageInfo({
							src: current.path,
							success: (res) => {
								current.path = res.path
								this.drawPathQueue.push(current)
							},
							fail:(err)=>{
								console.log('imageErr',err)
							}
						})
					}else{
						this.drawPathQueue.push(current)
					}

				}
			},
			saveImage(){
				//图片保存到本地
				wx.saveImageToPhotosAlbum({
				  filePath: this.imagePath,
				  success: function (data) {
				    wx.hideLoading()
				    wx.showToast({
				      title: '保存成功',
				      icon: 'success',
				      duration: 2000
				    })
				  },
				  fail: function (err) {
				    if (err.errMsg === "saveImageToPhotosAlbum:fail auth deny" || err.errMsg === "saveImageToPhotosAlbum:fail:auth denied") {
				      // console.log("当初用户拒绝，再次发起授权")
				      wx.showModal({
				        title: '提示',
				        content: '需要您授权保存相册',
				        showCancel: false,
				        success: modalSuccess => {
				          wx.openSetting({
				            success(settingdata) {
				              if (settingdata.authSetting['scope.writePhotosAlbum']) {
				                wx.showModal({
				                  title: '提示',
				                  content: '获取权限成功,再次点击图片即可保存',
				                  showCancel: false,
				                })
				              } else {
				                wx.showModal({
				                  title: '提示',
				                  content: '获取权限失败，将无法保存到相册哦~',
				                  showCancel: false,
				                })
				              }
				            },
				            fail(failData) {
				              console.log("failData", failData)
				            },
				            complete(finishData) {
				              console.log("finishData", finishData)
				            }
				          })
				        }
				      })
				    }
				  }
				});
			},

      /* 获取底部安全区域 */
      getSafeArea() {
        const res = uni.getSystemInfoSync();
        this.bottomPadding = res.screenHeight - res.safeArea.bottom;
      }
		}
	}
</script>
<style lang="scss" scoped>
	.share-popup,
	.uni-popup__wrapper-box {
		.share-title {
			line-height: 60rpx;
			font-size: $ns-font-size-lg;
			padding: 15rpx 0;
			text-align: center;
		}

		.share-content {
			display: flex;
			display: -webkit-flex;
			-webkit-flex-wrap: wrap;
			-moz-flex-wrap: wrap;
			-ms-flex-wrap: wrap;
			-o-flex-wrap: wrap;
			flex-wrap: wrap;
			padding: 15rpx;
			//background: #F5F5F5;

      margin-bottom: 22rpx;
			.share-box {
				flex: 1;
				text-align: center;

				.share-btn {
					margin: 0;
					padding: 0;
					border: none;
					line-height: 1;
					height: auto;
					image{
						width: 100rpx;
						height: 100rpx;
					}
					text {
						margin-top: 20rpx;
						font-size: 24rpx;
						display: block;
						color: $ns-text-color-black;
					}
				}

				.iconfont {
					font-size: 80rpx;
					line-height: initial;
				}

				.iconpengyouquan,
				.iconiconfenxianggeihaoyou {
					color: #07c160;
				}
			}
		}

		.share-footer {
      width: 672rpx;
      height: 80rpx;
			line-height: 80rpx;
			text-align: center;
			color: #fff;
      border-radius: 40rpx;
      background: var(--custom-brand-color);
      margin: 0 auto;
      &-padding{
        margin-bottom: 40rpx;
      }
		}
	}

	.canvas {
		width: 620rpx;
		height: 917rpx;
		margin: 0 auto;
		margin-top: 70rpx;
		display: block;
		overflow: hidden;
	}
	.poster{
		display: flex;
		justify-content: center;
	}
	.canvas1{
		top: 100vh;
	}
	@keyframes spin {
		from {
			transform: rotate(0deg);
		}

		to {
			transform: rotate(360deg);
		}
	}
	.loading-layer {
		width: 100vw;
		height: 100vh;
		position: fixed;
		top: 0;
		left: 0;
		z-index: 997;
	}

	.loading-anim {
		position: absolute;
		left: 50%;
		top: 50%;
		transform: translate(-50%, -50%);
	}

	.loading-anim > .item {
		position: relative;
		width: 35px;
		height: 35px;
		perspective: 800px;
		transform-style: preserve-3d;
		transition: all 0.2s ease-out;
	}

	.loading-anim .border {
		position: absolute;
		border-radius: 50%;
		border: 3px solid;
	}

	.loading-anim .out {
		top: 15%;
		left: 15%;
		width: 70%;
		height: 70%;
		// border-left-color: red !important;
		border-right-color: rgba($color: #000000, $alpha: 0) !important;
		// border-top-color: rgba($color: #000000, $alpha: 0) !important;
		border-bottom-color: rgba($color: #000000, $alpha: 0) !important;
		animation: spin 0.6s linear normal infinite;
	}

	.loading-anim .in {
		top: 25%;
		left: 25%;
		width: 50%;
		height: 50%;
		border-top-color: transparent !important;
		border-bottom-color: transparent !important;
		animation: spin 0.8s linear infinite;
	}

	.loading-anim .mid {
		top: 40%;
		left: 40%;
		width: 20%;
		height: 20%;
		border-left-color: transparent;
		border-right-color: transparent;
		animation: spin 0.6s linear infinite;
	}
</style>
