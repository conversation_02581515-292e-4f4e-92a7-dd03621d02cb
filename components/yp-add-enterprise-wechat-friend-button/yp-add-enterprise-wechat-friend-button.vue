<!--加企微好友组件-浮窗-->
<template>
  <view class="enterprise-wechat-friend">
    <image :src="$util.img('https://www.xianmai88.com/static/youpin/enterprise-wechat-customer-service.gif')" class="enterprise-wechat-friend-image bounce-in-right"
           :style="style" @click="toOpen" v-show="isShow"></image>

    <uni-popup ref="enterpriseWechatPop" type="center" class="enterprise-wechat-friend-pop" :mask-click="false">
      <view class="enterprise-wechat-friend-pop-content">
        <image :src="$util.img(cs_info.headpic)" class="enterprise-wechat-friend-pop-content-head"></image>
        <view class="enterprise-wechat-friend-pop-content-name">{{cs_info.name}}</view>
        <view class="enterprise-wechat-friend-pop-content-tip">您好，长按或扫描下方二维码加我哟</view>
        <image :src="$util.img(cs_info.qrcode)" class="enterprise-wechat-friend-pop-content-qrcode" show-menu-by-longpress @longpress="longpress"></image>
        <image :src="$util.img('public/static/youpin/member/signin/sign-close.png')"
               class="enterprise-wechat-friend-pop-content-close" @click="toClose"></image>
      </view>
    </uni-popup>
  </view>
</template>

<script>
import uniPopup from '@/components/uni-popup/uni-popup.vue';
import ypconfig from './config'
import apiurls from "@/common/js/apiurls";
export default {
  name: "yp-add-enterprise-wechat-friend-button",
  components:{
    uniPopup
  },
  props:{
    sleepStart:{  //延迟启动
      type:Number,
      default(){
        // #ifdef MP-WEIXIN
        return 0
        // #endif
        // #ifdef H5
        return 4000
        // #endif
      }
    }
  },
  data(){
    return{
      isShow:false,
      config:{
        bottom: 0, //单位是rpx
        right: 40,
        width:40,
        height:40,
        zIndex: 10,
      },
      cs_info:{}
    }
  },
  computed:{
    style(){
      return `bottom:${this.config.bottom}rpx;right:${this.config.right}rpx;width:${this.config.width}rpx;height:${this.config.height}rpx;z-index:${this.config.zIndex};`
    }
  },
  async created(){
    this.changeParams()
  },
  async mounted(){
    // this.$refs.enterpriseWechatPop.open()
    if(this.sleepStart){
      setTimeout(async ()=>{
        await this.getData()
      },this.sleepStart)
    }else{
      await this.getData()
    }
  },
  methods:{
    toOpen(){
      this.$refs.enterpriseWechatPop.open()
      this.$buriedPoint.diyReportCustomerServiceInteractionEvent({diy_action_type:'click_button'})
    },
    toClose(){
      this.$refs.enterpriseWechatPop.close()
      this.$buriedPoint.diyReportCustomerServiceInteractionEvent({diy_action_type:'close_qrcode'})
    },
    longpress(){
      this.$buriedPoint.diyReportCustomerServiceInteractionEvent({diy_action_type:'scan_qrcode'})
    },
    async getData() {
      try {
        let res = await this.$api.sendRequest({
          url: apiurls.enterpriseWxContactCsUrl,
          async: false,
          data: {}
        })
        if(res.code==0 && res.data.cs_info){
          this.cs_info = res.data.cs_info
          this.isShow = true
        }
      }catch (e) {

      }
    },
    changeParams(){
      let path = ''
      // #ifdef MP-WEIXIN
      let curPage = getCurrentPages();
      path = curPage[curPage.length - 1].route; //获取当前页面的路由
      // #endif
      // #ifdef H5
      path=window.location.pathname
      // #endif
      let matchData = null;
      for (let i = 0; i < ypconfig.custom.length; i++) {
        if(path.indexOf(ypconfig.custom[i].path)!=-1){
          matchData = ypconfig.custom[i].params
          break
        }
      }
      this.config = matchData || ypconfig.default
    },
  }
}
</script>

<style scoped lang="scss">
@-webkit-keyframes bounce-in-right {
  0% {
    -webkit-transform: translateX(500rpx);
    transform: translateX(500rpx);
    -webkit-animation-timing-function: ease-in;
    animation-timing-function: ease-in;
    opacity: 0;
  }
  38% {
    -webkit-transform: translateX(0);
    transform: translateX(0);
    -webkit-animation-timing-function: ease-out;
    animation-timing-function: ease-out;
    opacity: 1;
  }
  55% {
    -webkit-transform: translateX(68rpx);
    transform: translateX(68rpx);
    -webkit-animation-timing-function: ease-in;
    animation-timing-function: ease-in;
  }
  72% {
    -webkit-transform: translateX(0);
    transform: translateX(0);
    -webkit-animation-timing-function: ease-out;
    animation-timing-function: ease-out;
  }
  81% {
    -webkit-transform: translateX(32rpx);
    transform: translateX(32rpx);
    -webkit-animation-timing-function: ease-in;
    animation-timing-function: ease-in;
  }
  90% {
    -webkit-transform: translateX(0);
    transform: translateX(0);
    -webkit-animation-timing-function: ease-out;
    animation-timing-function: ease-out;
  }
  95% {
    -webkit-transform: translateX(8rpx);
    transform: translateX(8rpx);
    -webkit-animation-timing-function: ease-in;
    animation-timing-function: ease-in;
  }
  100% {
    -webkit-transform: translateX(0);
    transform: translateX(0);
    -webkit-animation-timing-function: ease-out;
    animation-timing-function: ease-out;
  }
}
@keyframes bounce-in-right {
  0% {
    -webkit-transform: translateX(500rpx);
    transform: translateX(500rpx);
    -webkit-animation-timing-function: ease-in;
    animation-timing-function: ease-in;
    opacity: 0;
  }
  38% {
    -webkit-transform: translateX(0);
    transform: translateX(0);
    -webkit-animation-timing-function: ease-out;
    animation-timing-function: ease-out;
    opacity: 1;
  }
  55% {
    -webkit-transform: translateX(68rpx);
    transform: translateX(68rpx);
    -webkit-animation-timing-function: ease-in;
    animation-timing-function: ease-in;
  }
  72% {
    -webkit-transform: translateX(0);
    transform: translateX(0);
    -webkit-animation-timing-function: ease-out;
    animation-timing-function: ease-out;
  }
  81% {
    -webkit-transform: translateX(32rpx);
    transform: translateX(32rpx);
    -webkit-animation-timing-function: ease-in;
    animation-timing-function: ease-in;
  }
  90% {
    -webkit-transform: translateX(0);
    transform: translateX(0);
    -webkit-animation-timing-function: ease-out;
    animation-timing-function: ease-out;
  }
  95% {
    -webkit-transform: translateX(8rpx);
    transform: translateX(8rpx);
    -webkit-animation-timing-function: ease-in;
    animation-timing-function: ease-in;
  }
  100% {
    -webkit-transform: translateX(0);
    transform: translateX(0);
    -webkit-animation-timing-function: ease-out;
    animation-timing-function: ease-out;
  }
}
.bounce-in-right {
  -webkit-animation: bounce-in-right 1s 0.5s both;
  animation: bounce-in-right 1s 0.5s both;
}
  .enterprise-wechat-friend{
    &-image{
      position: fixed;
    }
    &-pop{
      /deep/ .uni-popup__wrapper-box{
        overflow-y: unset!important;
      }
      &-content{
        width: 560rpx;
        height: 760rpx;
        border-radius: 40rpx;
        background: rgba(255, 255, 255, 1);
        padding-top: 62rpx;
        box-sizing: border-box;
        display: flex;
        flex-direction: column;
        align-items: center;
        position: relative;
        &-head{
          width: 160rpx;
          height: 160rpx;
          border-radius: 50%;
        }
        &-name{
          font-size: 32rpx;
          font-weight: 700;
          line-height: 37.5rpx;
          color: rgba(56, 56, 56, 1);
          margin-top: 26rpx;
        }
        &-tip{
          font-size: 28rpx;
          font-weight: 400;
          line-height: 32.82rpx;
          color: rgba(166, 166, 166, 1);
          margin-top: 14rpx;
        }
        &-qrcode{
          width: 320rpx;
          height: 320rpx;
          margin-top: 40rpx;
        }
        &-close{
          width: 48rpx;
          height: 48rpx;
          position: absolute;
          left: 50%;
          bottom: -100rpx;
          transform: translateX(-50%);
        }
      }
    }
  }
</style>
