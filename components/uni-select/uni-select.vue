<template>
  <!-- 选择框 -->
  <view class="view">
    <view class="content" @click="open">
      <text class="value" v-if="company_name">{{ company_name }}</text>
      <text class="placeholder" v-else>请选择物流公司</text>
      <text class="iconfont iconiconangledown icon"></text>
    </view>
    <view class="popup">
      <uni-popup
        ref="popup-select-upoff"
        type="bottom"
        class="uni-popup-father"
        @change="change"
      >
        <view class="uni-popup-popup">
          <view class="popup-header">
            <view class="popup-close">
              <text
                class="iconfont iconguanbi iconclose"
                @click="closeOp"
              ></text>
            </view>
            <view class="popup-title"><text>请选择物流公司</text></view>
          </view>
          <view class="popup-content">
            <view class="popup-content-list">
              <!-- <view
                @click="select(item)"
                class="popup-content-item"
                v-for="item in express_company_list"
                :key="item.company_id"
              >
                {{ item.company_name }}
              </view> -->
              <!-- min-height: calc(100% + 1px)解决部分ios频繁滚动卡死 -->
              <view class="main-inner" style="min-height: calc(100% + 1px)">
                <view
                  class="popup-content-item"
                  v-for="(item, index) in express_company_list"
                  :key="index"
                  @click="select(item)"
                >
                  <view class="item-reason">{{ item.company_name }}</view>
                  <view class="item-select-icon">
                    <text
                      v-if="item.company_id == currentId"
                      class="icon iconfont iconyuan_checked"
                    ></text>
                    <text v-else class="icon iconfont iconyuan_checkbox"></text>
                  </view>
                </view>
              </view>
            </view>
          </view>
        </view>
      </uni-popup>
    </view>
  </view>
</template>
<script>
export default {
  props: {
    refund_delivery_company_id: {
      type: [String, Number],
      default: "",
    },
    express_company_list: {
      type: Array,
      default: function () {
        return [];
      },
    },
  },
  watch: {
    refund_delivery_company_id: {
      immediate: true,
      handler(val) {
        if (val) {
          this.express_company_list.map((item) => {
            if (val == item.company_id) {
              this.company_name = item.company_name;
              this.$emit("companySelect", item);
            }
          });
        }
      },
    },
  },
  data() {
    return {
      currentId: "",
      company_name: "",
    };
  },
  methods: {
    open() {
      this.$refs["popup-select-upoff"].open();
      this.$emit("openModel");
    },
    closeOp() {
      this.$refs["popup-select-upoff"].close();
    },
    change(e) {
      if (!e.show) {
        this.$emit("closeModel");
      }
    },
    select(data) {
      this.company_name = data.company_name;
      this.currentId = data.company_id;
      this.$emit("companySelect", data);
      this.$refs["popup-select-upoff"].close();
    },
  },
};
</script>
<style lang="scss" scoped>
// 弹窗
.uni-popup-father /deep/ .uni-popup__wrapper {
  background: transparent !important;
  .uni-popup__wrapper-box {
    border-radius: 20rpx 20rpx 0 0 !important;
  }
}
.view {
  width: 100%;
  height: 68rpx;
  background: #ffffff;
  border: 1px solid #cccccc;
  border-radius: 4px;
  .content {
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: space-between;
    box-sizing: border-box;
    padding: 0 19rpx;
    font-size: 28rpx;
    font-weight: 400;
    color: #666666;
    .placeholder {
      font-size: 28rpx;
      font-weight: 400;
      color: #cccccc;
    }
  }
  .uni-popup-popup {
    width: 100%;
    box-sizing: border-box;
    padding: 28rpx 30rpx;
    .popup-header {
      width: 100%;
      .popup-close {
        width: 100%;
        text-align: end;
        color: #cccccc;
      }
      .popup-title {
        width: 100%;
        text-align: center;
        font-size: 32rpx;
        font-weight: bold;
        color: #333333;
      }
    }
    .popup-content {
      width: 100%;
      height: 60vh;
      overflow: auto;
      -webkit-overflow-scrolling: touch;
      .popup-content-list {
        width: 100%;
        .popup-content-item {
          width: 100%;
          display: flex;
          align-items: center;
          justify-content: space-between;
          padding: 10rpx 0;
          border-bottom: 1px solid #eeeeee;
          .item-reason {
            font-size: 28rpx;
            font-weight: 400;
            color: #333333;
          }
          .item-select-icon {
            font-size: 40rpx;
            color: #999999;
            .iconyuan_checked {
              color: var(--custom-brand-color);
            }
            .icon {
              font-size: 44rpx;
            }
          }
        }
      }
    }
  }
}
</style>
