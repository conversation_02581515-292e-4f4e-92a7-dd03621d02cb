<!--页面手势组件 例如左右滑动-->
<template>
	<view class="diy-page-gesture" @touchstart="gestureTouchStart" @touchcancel="gestureTouchCancel" @touchend="gestureTouchEnd">
    <slot></slot>
	</view>
</template>
<script>

  export default {
		name: 'diy-page-gesture',
		props: {
      leftSlide:{
        type: Function
      },
      rightSlide:{
        type: Function
      },
    },
    data(){
      return{
        gestureMinOffset: 50, //最小偏移量，低于这个值不响应滑动处理
        gestureMinTime: 60, // 最小时间，单位：毫秒，低于这个值不响应滑动处理
        gestureStartX: 0, //开始时的X坐标
        gestureStartY: 0, //开始时的Y坐标
        gestureStartTime: 0, //开始时的毫秒数
        gestureAnimationData: {},
      }
    },
		created() {},
    methods:{
      gestureTouchStart(e) {
        this.gestureStartX = e.touches[0].pageX; // 获取触摸时的x坐标
        this.gestureStartY = e.touches[0].pageY; // 获取触摸时的x坐标
        this.gestureStartTime = new Date().getTime(); //获取毫秒数
      },
      gestureTouchCancel: function (e) {
        this.gestureStartX = 0; //开始时的X坐标
        this.gestureStartY = 0; //开始时的Y坐标
        this.gestureStartTime = 0; //开始时的毫秒数
      },
      gestureTouchEnd: function (e) {
        var that = this;
        var endX = e.changedTouches[0].pageX;
        var endY = e.changedTouches[0].pageY;
        var touchTime = new Date().getTime() - this.gestureStartTime; //计算滑动时间
        //1.判断时间是否符合
        if (touchTime >= this.gestureMinTime) {
          //2.判断偏移量：分X、Y
          var xOffset = endX - this.gestureStartX;
          var yOffset = endY - this.gestureStartY;
          //①条件1（偏移量x或者y要大于最小偏移量）
          //②条件2（可以判断出是左右滑动还是上下滑动）
          if (Math.abs(xOffset) >= Math.abs(yOffset) && Math.abs(xOffset) >= this.gestureMinOffset) {
            //左右滑动
            //③条件3（判断偏移量的正负）
            if (xOffset < 0) {
              // console.log('向左滑动')
              if(this.leftSlide && typeof this.leftSlide == 'function'){
                this.leftSlide()
              }
            } else {
              // console.log('向右滑动')
              if(this.rightSlide && typeof this.rightSlide == 'function'){
                this.rightSlide()
              }
            }
          } else if (Math.abs(xOffset) < Math.abs(yOffset) && Math.abs(yOffset) >= this.gestureMinOffset) {
            //上下滑动
            //③条件3（判断偏移量的正负）
            if (yOffset < 0) {
              // console.log('向上滑动')
            } else {
              // console.log('向下滑动')
            }
          }
        } else {
          // console.log('滑动时间过短', touchTime)
        }
      }
    }
	};
</script>

<style lang="scss">
	.diy-page-gesture{
    width: 100vw;
    height: 100vh;
  }
</style>
