<!--使用json 显示动画弹窗-->
<template>
  <view class="revenue_popup" v-if="isShowRevenueAnimation" @click="closeRevenueAnimation" :style="bgColor">
    <div class="revenue_content" style="width: 750rpx; height: 1334rpx;">
      <canvas id="revenue_id" type="2d" style="width: 100%; height: 100%;" @click.stop="canvasClick">
      </canvas>
      <slot></slot>
    </div>
  </view>
</template>


<script>
// #ifdef MP-WEIXIN
import lottie from 'lottie-miniprogram';
// #endif
// #ifdef H5
import lottie from 'lottie-web';
// #endif
let ani = null; // 必须放在外面，uni里不要挂在this上,否则会触发循环引用的报错
export default {
  name: "diy-animation-popup",
  props:{
    animationData:{  //动画json数据
      type: Object,
      default(){
        return {}
      }
    },
    audioUrls:{  //音频文件路径，可以多个
      type:Array,
      default() {
        return [];
      }
    },
    audioPlayTime:{  //音频播放的时间点,单位是毫秒
      type:Array,
      default() {
        return [];
      }
    }
  },
  data(){
    return{
      isShowRevenueAnimation:false,
      innerAudioContextList:[],
      playTimeList:[],
      showBgColor: false
    }
  },
  computed:{
    bgColor(){
      return this.showBgColor ? `background-color: rgba(0,0,0,0.5);` : ''
    }
  },
  methods:{
    // 给父组件调用
    async showAnimation(){
      await this.revenueAnimation()
    },
    canplay(innerAudioContext){
      return new Promise((resolve, reject)=>{
        // #ifdef MP-WEIXIN
        innerAudioContext.onCanplay(()=>{
          resolve()
        })
        // #endif
        // #ifdef H5
        resolve()
        // #endif
      })
    },
    async audioOperation(){
      if(this.audioUrls.length){
        for (let i = 0; i < this.audioUrls.length; i++) {
          const playTime = i < this.audioPlayTime.length && this.audioPlayTime[i] ? this.audioPlayTime[i] : 0
          if(playTime){
            const innerAudioContext = uni.createInnerAudioContext();
            innerAudioContext.src = this.audioUrls[i]
            await this.canplay(innerAudioContext)
            this.innerAudioContextList.push(innerAudioContext)
            this.playTimeList.push(playTime)
          }
        }
      }
    },
    // 播放音频文件
    audioListPlay(){
      for (let i = 0; i < this.innerAudioContextList.length; i++) {
        setTimeout(()=>{
          this.innerAudioContextList[i].play()
        },this.playTimeList[i])
      }
    },
    // 等待节点加载完成
    waitNodeExist(){
      if(this.$util.getPlatform()=='weapp'){
        return new Promise((resolve, reject)=>{
          const query = uni.createSelectorQuery().in(this)
          query.select('#revenue_id').boundingClientRect((data)=>{
            if(data){
              resolve()
            }
          }).exec()
        })
      }else if(this.$util.getPlatform()=='h5'){
        return new Promise((resolve, reject) => {
          const canvas = document.getElementById('revenue_id');
          if(canvas){
            resolve()
          }
        })
      }
    },
    async revenueAnimation(){
      this.isShowRevenueAnimation=true
      setTimeout(async ()=>{
        await this.waitNodeExist();
        await this.audioOperation()
        // #ifdef MP-WEIXIN
        uni.createSelectorQuery().in(this).select('#revenue_id').node(res => {
          // console.log(res); // 节点对应的 Canvas 实例。
          const canvas = res.node;
          const context = canvas.getContext('2d');
          canvas.width = this.animationData.w;
          canvas.height = this.animationData.h;
          lottie.setup(canvas);
          ani = lottie.loadAnimation({
            loop: false,
            autoplay: true,
            animationData: this.animationData,
            rendererSettings: {
              context
            }
          });
        }).exec();
        // #endif
        // #ifdef H5
        const canvas = document.getElementById('revenue_id');
        canvas.width = this.animationData.w;
        canvas.height = this.animationData.h;
        ani = lottie.loadAnimation({
          loop: false,
          autoplay: true,
          container: canvas,
          renderer: 'canvas',
          animationData: this.animationData,
          // assetsPath: '/mini-h5/static/images/'
        });
        // #endif
        this.showBgColor = true
        this.audioListPlay()
        this.$emit('startPlay')
      },500)

    },
    closeRevenueAnimation(){
      try{
        ani.destroy()
      }catch (e) {

      }
      for (let i = 0; i < this.innerAudioContextList.length; i++) {
        this.innerAudioContextList[i].destroy()
      }
      this.isShowRevenueAnimation=false
    },
    canvasClick(){
      this.closeRevenueAnimation()
      this.$emit('click')
    }
  }
}
</script>

<style scoped lang="scss">
.revenue_popup{
  z-index: 999;
  position: fixed;
  left: 0;
  top: 0;
  width: 100vw;
  height: 100vh;
  display: flex;
  justify-content: center;
  align-items: center;
}
.revenue_content{
  position: relative;
}
</style>
