<!--浮动滚动信息组件-->
<template>
	<view class="rolling-order" :style="style" :animation="animationData" v-if="activeData.message">
    <image class="rolling-order-head"
           :src="activeData.headimg ? $util.img(activeData.headimg) : $util.getDefaultImage().default_headimg"
           mode="aspectFill"
           @error="activeData.headimg = $util.getDefaultImage().default_headimg"></image>
    <text class="rolling-order-text">{{activeData.message}}</text>
  </view>
</template>

<script>
	export default {
		name: 'diy-floating-rolling-order',
		props: {
      top:{
        type:[String],
        default(){
          return '280rpx'
        }
      },
      left:{
        type:[String],
        default(){
          return '24rpx'
        }
      },
      zIndex:{
        type: Number,
        default(){
          return 888
        }
      },
      intervalsTime:{   //两个消息显示的间隔时间
        type:Number,
        default(){
          return 3000
        }
      },
      positionType:{
        type: String,
        default(){
          return 'index_banner'   //index_banner首页banner，goods_detail商品详情页， pintuan_detail拼团详情页  sign_detail 签到页面
        }
      },
      sleepStart:{  //延迟启动
        type:Number,
        default(){
        // #ifdef MP-WEIXIN
        return 0
        // #endif
        // #ifdef H5
        return 4000
        // #endif
        }
      }
		},
		data() {
			return {
        ordersList:[],
        activeIndex: 0,
        activeData:{},
        animationData:{},
        animation:null,
        duration: 1000,
        timeOutOne:null,
        timeOutTwo:null
      }
		},
    computed:{
      style(){
        return `top: ${this.top};left: ${this.left};z-index:${this.zIndex};`
      }
    },
    created(){

    },
    mounted(){
      if(this.sleepStart){
        setTimeout(()=>{
          this.switchMessage()
        },this.sleepStart)
      }else{
        this.switchMessage()
      }
    },
    beforeDestroy(){
      clearTimeout(this.timeOutOne)
      clearTimeout(this.timeOutTwo)
    },
		methods: {
      contentAnimation(){
        this.activeData = this.ordersList[this.activeIndex]
        this.animation = uni.createAnimation({
          duration:this.duration,
          timingFunction:'linear',
        })
        this.animation.opacity(1).step()
        this.animationData = this.animation.export()
        this.timeOutOne = setTimeout(async ()=>{
          this.animation.opacity(0).step()
          this.animationData = this.animation.export()
          this.activeIndex+=1
          if(this.activeIndex>=this.ordersList.length){
            await this.getData()
          }
          this.timeOutTwo = setTimeout(()=>{
            this.contentAnimation()
          },this.duration+this.intervalsTime)
        },this.duration+this.intervalsTime)
      },
      async switchMessage(){
        await this.getData()
        if(this.ordersList.length<1){
          return;
        }
        this.contentAnimation()
      },
      async getData(){
        this.activeIndex = 0
        try{
          let res = await this.$api.sendRequest({
            url: this.positionType=='sign_detail' ? this.$apiUrl.getSignDriftMessageUrl : this.$apiUrl.driftMessageUrl,
            async:false,
            data:{
              position: this.positionType
            }
          })
          if(res.code == 0){
            this.ordersList = res.data
          }
        }catch (e) {

        }
      }
		}
	}
</script>

<style scoped lang="scss">
.rolling-order{
  position: fixed;
  display: flex;
  align-items: center;
  background-color: rgba(0, 0, 0, 0.6);
  padding: 0 20rpx 0 6rpx;
  height: 60rpx;
  line-height: 60rpx;
  box-sizing: border-box;
  border-radius: 40rpx;
  opacity: 0;
  &-head{
    width: 50rpx;
    height: 50rpx;
    border-radius: 50%;
  }
  &-text{
    font-size: 24rpx;
    line-height: 1.5;
    color: rgb(255, 255, 255);
    margin-left: 10rpx;
  }
}
</style>
