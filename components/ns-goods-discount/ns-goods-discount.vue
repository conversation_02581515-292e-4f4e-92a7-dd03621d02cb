<template>
  <view class="goods-discount" @touchmove.prevent.stop>
    <uni-popup
      ref="discountPopup"
      type="bottom"
      :bottomRadius="true"
      @change="change"
      class="discountPopup"
    >
      <view class="discount-popup">
        <view class="discount-popup-title">
          <text></text>
          <uni-icons type="clear" size="24" color="rgba(217, 217, 217, 1)" @click="hide"></uni-icons>
        </view>
        <scroll-view class="coupon-all" style="height: 1000rpx; box-sizing: border-box; padding-bottom: 40rpx; border-radius: 40rpx 40rpx 0 0;" :scroll-y="true" :show-scrollbar="false" :refresher-enabled="false" @refresherrefresh="refresherrefresh">
        <view class="discount-warp">
          <view class="warp-type_01"  v-if="promotion_discount && promotion_discount.length">
            <view class="warp-title"
              ><view class="title-text">分销商优惠</view></view
            >
            <template>
              <view
                class="warp-discount"
                v-for="(item, index) in promotion_discount"
                :key="index"
              >
                <view class="textbox">
                  <text class="money"
                    ><text class="icon">￥</text
                    ><text class="big">{{
                      filterNum(item.shop_owner_brokerage, 0)
                    }}</text
                    >{{
                      filterNum(item.shop_owner_brokerage, 1)
                        ? "." + filterNum(item.shop_owner_brokerage, 1)
                        : ""
                    }}</text
                  >
                </view>
                <view class="textbox" style="margin-left: 48rpx"
                  ><text class="text">分销商每件立减优惠</text></view
                >
              </view>
            </template>
            <!-- <view v-else class="discount-null">-暂无分销商优惠-</view> -->
          </view>
          <view class="warp-type_02" v-if="multiple_discounts && multiple_discounts.length">
            <view class="warp-title"
              ><view class="title-text">折扣活动</view
              ><view class="title-tips">部分活动不能与券叠加使用</view></view
            >
            <view class="warp-discount">
              <view class="warp-discount-list">
                <view class="warp-discount-item" v-for="(item,index) in multiple_discounts" :key="index">
                  <view class="item-left">
                    <view class="content">
                      <view class="content-text">{{item.type == 1 ? '单商品':'多商品'}}满{{ item.at_least }}件享{{ Number(item.discount) }}折</view>
                      <view class="content-date">有效期至{{ $util.timeStampTurnTime(item.over_time) }}</view>
                    </view>
                  </view>
                  <view class="item-right">
                    <view class="discount"><text class="num">{{ Number(item.discount) }}</text>折</view>
                    <view
                        class="check"
                        @click="
                        $util.redirectTo(`/promotionpages/special_offers/special_offers?multiple_discount_id=${item.multiple_discount_id}`)
                      "
                    >查看</view
                    >
                  </view>
                  <view class="tagType">多件折扣</view>
                </view>
              </view>
            </view>
           <!-- <view v-else class="discount-null">-暂无折扣-</view> -->
          </view>
          <view class="warp-type_02"  v-if="goodscoupons && goodscoupons.length">
            <view class="warp-title"
              ><view class="title-text">优惠券</view>
              <!-- <view class="title-tips">部分活动不能与券叠加使用</view> -->
            </view>
            <view class="warp-discount">
              <view class="warp-discount-list">
                <view class="warp-discount-item" v-for="(item, index) in goodscoupons" :key="index"
                      @click="item.is_receive == 1 ?　$util.redirectTo('/otherpages/goods/coupon_goods_list/coupon_goods_list', {goodscoupon_type_id: item.goodscoupon_type_id})　: null"
                      :class="{'warp-discount-item-use' :item.is_receive === 1}"
                >
                  <view class="item-left">
                    <view class="content">
                      <!-- <view class="content-text">全场商品满{{ Number(item.at_least) }}元可用</view> -->
                      <view class="content-text">{{ item.content }}</view>
                      <view class="content-date" v-if="item.validity_type === 1">领取后{{ item.fixed_term }}天内有效</view>
                      <view class="content-date" v-if="item.validity_type === 0">有效期至{{ $util.timeStampTurnTime(item.end_time) }}</view>
                    </view>
                  </view>
                  <view class="item-right">
                    <text class="money"
                      ><text class="icon">￥</text
                      ><text class="big">{{ filterNum(item.money, 0) }}</text
                      >{{
                        filterNum(item.money, 1) ? "." + filterNum(item.money, 1) : ""
                      }}</text
                    >
                    <view class="check" v-if="item.is_receive == 1">使用</view>
                    <view class="check" v-else @click="getCoipon(item)">领券</view>
                  </view>
                  <view class="tagType">满减券</view>
<!--                  <view class="getLogo"  v-if="item.is_receive === 1">-->
<!--                    <view class="logo-child">已领</view>-->
<!--                  </view>-->
                </view>
              </view>
            </view>
            <!-- <view v-else class="discount-null">-暂无优惠券-</view> -->
          </view>
          </view>
          </scroll-view>
<!--        <view class="bottom-btn">-->
<!--          <view class="btn" @click="hide">关闭</view>-->
<!--        </view>-->
      </view>
    </uni-popup>
    <!-- 授权登录弹窗 -->
    <yd-auth-popup ref="ydauth"></yd-auth-popup>
    <ns-login ref="login"></ns-login>
  </view>
</template>

<script>
import uniPopup from "@/components/uni-popup/uni-popup.vue";
import UniIcons from "../uni-icons/uni-icons.vue";
export default {
  name: "ns-goods-discount",
  components: {
    UniIcons,
    uniPopup,
  },
  props: {
    sku_id: {
      type: [Number,String],
      default: ''
    }
  },
  // filters:{
  //   discountFilter: function (value) {
  //      const newVal = String(Number(value)).split('.')
  //      if(newVal.length > 0 && newVal.length == 1) {
  //        return newVal[0]
  //      } else if(newVal.length > 0 && newVal.length == 2) {
  //        return newVal[0] + '.' + newVal[1]
  //      }
  //   }
  // },
 data() {
    return {
      promotion_discount: [],
      multiple_discounts: [],
      goodscoupons: []
    };
  },
  created() {
  },
  mounted() {},
  methods: {
    // 获取商品详情
		async getGoodsSkuDetail(skuId) {
			this.skuId = skuId || this.skuId;
			let datas = {
				sku_id: this.skuId
			}
			let res = await this.$api.sendRequest({
				url: '/api/goodssku/detail',
				async: false,
				data: datas
			});
			let data = res.data;
			if (data.discount_info != null) {
				this.promotion_discount = data.discount_info.promotion_discount
				this.multiple_discounts = data.discount_info.multiple_discounts
				this.goodscoupons = data.discount_info.goodscoupons
			}
		},
   async getCoipon(item) {
      if (!uni.getStorageSync('token')) {
          // &是跳转方法写死不能用？拼
          this.$util.toShowLoginPopup(this,null,`/pages/goods/detail/detail?sku_id=${this.sku_id}`);
          return false
        }
         await this.$util.subscribeMessage({
					source:'goodscoupon',
					source_id:'',
					scene_type:'goodscoupon_group'
        })
         uni.showLoading({
          title: '加载中',
          mask: true
        });
        this.$api.sendRequest({
          url:this.$apiUrl.goodsCouponReceive,
          data: {
            token: uni.getStorageSync('token'),
            goodscoupon_type_id: item.goodscoupon_type_id
          },
          success: res => {
            uni.hideLoading();
            setTimeout(() => {
              this.$util.showToast({
                title: res.code == 0 ? '领取成功' : res.message,
                mask: true,
                duration: 2000,
                success: res => {
                  this.getGoodsSkuDetail(this.sku_id)
                  // this.goodsCouponReceivedNum()
                }
              });
            }, 100)
          },
          fail: res => {
            uni.hideLoading();
          }
        })
    },
    filterNum(val, index) {
      const numArr = String(Number(val)).split(".");
      if (numArr[index]) {
        return numArr[index];
      } else {
        return false;
      }
    },
    change(e) {
      if (!e.show) {
        this.$emit("closeModel");
      }
    },
    open() {
      this.$refs.discountPopup.open();
      this.$emit("openModel");
    },
    hide() {
      this.$refs.discountPopup.close();
    },
    refresherrefresh() {
      return false
    }
  },
};
</script>

<style lang="scss" scoped>
/deep/.uni-popup__wrapper.bottom-radius{
  border-radius: 40rpx 40rpx 0 0;
}
.goods-discount{
  height: auto;
}
.discount-popup {
  width: 100%;
  box-sizing: border-box;
  padding: 0 24rpx;
  border-radius: 40rpx 40rpx 0 0;
  .discount-popup-title {
    width: 100%;
    font-size: 36rpx;
    font-weight: bold;
    line-height: 43.2rpx;
    color: rgba(56, 56, 56, 1);
    padding: 18rpx 0;
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
  .discount-warp {
    width: 100%;
    height: 100%;
    // overflow-y: auto;
    // margin-top: 30rpx;
    // box-sizing: border-box;
    // padding-bottom: 120rpx;
    .warp-title {
      width: 100%;
      display: flex;
      justify-content: space-between;
	  align-items: center;
      .title-text {
        font-size: 32rpx;
        font-family: PingFang SC;
        font-weight: 600;
        line-height: 44rpx;
        color: #333333;
      }
      .title-tips {
        font-size: 24rpx;
        font-family: PingFang SC;
        font-weight: 300;
        line-height: 34rpx;
        color: #666666;
      }
    }
    .warp-type_01 {
      width: 100%;
      margin-bottom: 30rpx;
      .warp-discount {
        width: 100%;
        height: 148rpx;
        margin-top: 24rpx;
        background: var(--custom-brand-color);
        border-radius: 8rpx;
        color: #ffffff;
        box-sizing: border-box;
        padding: 48rpx;
        display: flex;
        .textbox {
          height: 100%;
          display: flex;
          align-items: center;
        }
        .money {
          font-size: 32rpx;
          font-weight: 600;
          color: #ffffff;
          .icon {
            font-size: 24rpx;
          }
          .big {
            font-size: 48rpx;
          }
        }
        .text {
          font-size: 30rpx;
          font-weight: 500;
          color: #ffffff;
          line-height: 148rpx;
        }
      }
    }
    .warp-type_02 {
      width: 100%;
      .warp-discount-list {
        width: 100%;
        margin-top: 24rpx;
        .warp-discount-item {
          display: flex;
          width: 100%;
          height: 210rpx;
          margin-bottom: 24rpx;
          border-radius: 8rpx;
          position: relative;
          overflow: hidden;
          background: linear-gradient(135deg, var(--custom-brand-color-80) 0%, var(--custom-brand-color) 100%);
          .item-left {
            flex: 1;
            height: 100%;
            box-sizing: border-box;
            padding: 20rpx;
            display: flex;
            justify-content: space-between;
            align-items: center;
            position: relative;
            &::after {
              content: "";
              display: block;
              width: 9px;
              height: 9px;
              position: absolute;
              top: -7px;
              right: -6px;
              background: #ffffff;
              border-radius: 50%;
              border: 1px solid var(--custom-brand-color-10);
              clip-path: polygon(50% 0%, 100% 0%, 100% 3600%, 50% 50%);
              -webkit-clip-path: polygon(50% 0%, 100% 0%, 100% 3600%, 50% 50%);
              transform: rotate(90deg);
              z-index: 10;
            }
            &::before {
              content: "";
              display: block;
              width: 9px;
              height: 9px;
              position: absolute;
              bottom: -7px;
              right: -6px;
              background: #ffffff;
              border-radius: 50%;
              border: 1px solid var(--custom-brand-color-10);
              clip-path: polygon(50% 0%, 100% 0%, 100% 3600%, 50% 50%);
              -webkit-clip-path: polygon(50% 0%, 100% 0%, 100% 3600%, 50% 50%);
              transform: rotate(-90deg);
              z-index: 10;
            }
            .content-text {
              font-size: 32rpx;
              line-height: 44rpx;
              font-weight: 700;
              color: #fff;
            }
            .content-date {
              font-size: 24rpx;
              line-height: 36rpx;
              font-weight: 400;
              color: #fff;
              margin-top: 6rpx;
            }
          }
          .item-right {
            width: 248rpx;
            height: 100%;
            position: relative;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            border-left: 2rpx dashed rgba(255, 255, 255, 1);
            .discount {
              font-size: 28rpx;
              font-weight: 600;
              color: #fff;
              .num{
                font-size: 72rpx;
                line-height: 84rpx;
              }
            }
            .money {
              font-size: 32rpx;
              font-weight: 600;
              color: #fff;
              .icon {
                font-size: 28rpx;
              }
              .big {
                font-size: 72rpx;
                line-height: 84rpx;
              }
            }
            .check {
              font-size: 32rpx;
              font-weight: 400;
              color: var(--custom-brand-color);
              position: relative;
              z-index: 99;
              width: 169.85rpx;
              height: 60rpx;
              border-radius: 40rpx;
              background: rgba(255, 255, 255, 1);
              display: flex;
              justify-content: center;
              align-items: center;
            }
          }
          .tagType {
            //width: 104rpx;
            padding: 0 10rpx;
            height: 40rpx;
            border-radius: 8rpx 0 20rpx 0;
            background: linear-gradient(135deg, var(--custom-brand-color-80) 0%, var(--custom-brand-color) 100%);
            position: absolute;
            top: 0;
            left: 0;
            font-size: 24rpx;
            font-weight: 400;
            color: #ffffff;
            text-align: center;
            line-height: 36rpx;
          }
          .getLogo {
            width: 124rpx;
            height: 104rpx;
            border: 3px solid var(--custom-brand-color-50);
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 50%;
            position: absolute;
            bottom: -44rpx;
            right: -40rpx;
            transform: rotate(-32deg);
            .logo-child {
              width: 112rpx;
              height: 92rpx;
              background: var(--custom-brand-color-50);
              border-radius: 50%;
              text-align: center;
              line-height: 60rpx;
              font-size: 32rpx;
              font-family: PingFang SC;
              font-weight: 600;
              color: #ffffff;
            }
          }
          &-use{
            background: var(--custom-brand-color-10);
            .item-left{
              .content-text{
                color: var(--custom-brand-color);
              }
              .content-date{
                color: var(--custom-brand-color);
              }
            }
            .item-right{
              border-color: var(--custom-brand-color);
              .money{
                color: var(--custom-brand-color);
              }
              .check{
                background: linear-gradient(135deg, var(--custom-brand-color-80) 0%, var(--custom-brand-color) 100%);
                color: #fff;
              }
            }
          }
        }
      }
    }
  }
  .bottom-btn {
    width: 100%;
    height: 120rpx;
    background: #ffffff;
    position: fixed;
    bottom: 0;
    left: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 100;
    .btn {
      width: 642rpx;
      height: 82rpx;
      border-radius: 40rpx;
      background: var(--custom-brand-color);
      text-align: center;
      line-height: 82rpx;
      color: #ffffff;
      font-size: 30rpx;
    }
  }
  .discount-null {
    width: 100%;
    text-align: center;
    padding: 10rpx 0;
  }
}
</style>
