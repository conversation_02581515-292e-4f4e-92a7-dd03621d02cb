<template>
	<view>
		<view @touchmove.prevent.stop>
			<uni-popup ref="auth" :custom="true" :mask-click="false">
				<view class="uni-tip">
					<view class="uni-tip-title">您还未登录</view>
					<view class="uni-tip-content">请先登录之后再进行操作</view>
					<view class="uni-tip-group-button">
						<button type="default" class="uni-tip-button uni-tip-button-not" @click="close">暂不登录</button>
						<!-- #ifdef MP-WEIXIN || MP-QQ || MP-BAIDU -->
						<button type="primary" open-type="getUserInfo" @getuserinfo="bindgetuserinfo" class="uni-tip-button">立即登录</button>
						<!-- #endif  -->
						<!-- #ifdef MP-ALIPAY -->
						<button type="primary" open-type="getAuthorize" scope="userInfo" @getAuthorize="bindgetuserinfo" class="uni-tip-button ns-bg-color">立即登录</button>
						<!-- #endif  -->
						<!-- #ifdef H5 || APP-PLUS -->
						<button type="primary" class="uni-tip-button ns-bg-color" @click="login">立即登录</button>
						<!-- #endif  -->
					</view>
				</view>
			</uni-popup>
		</view>
	</view>
</template>

<script>
	import uniPopup from '../uni-popup/uni-popup.vue';
	import Config from 'common/js/config.js';
  // #ifdef H5
  import {isOnXianMaiApp, schemeGo} from "../../common/js/h5/appOP";
  import {appLoginUrl} from "../../common/js/h5/appSchemeUrl";
  // #endif

	export default {
		name: 'ns-login',
		components: {
			uniPopup
		},
		data() {
			return {
				url: ''
			};
		},
		created() {},
		onShow() {},
		methods: {
			open(url) {
				if (url) this.url = url;
				this.$refs.auth.open();
			},
			close() {
				this.$refs.auth.close();
			},
			bindgetuserinfo() {
				this.$refs.auth.close();
				if (this.url) this.$util.redirectTo('/pages/login/login/login', {
					back: this.url
				});
				else this.$util.redirectTo('/pages/login/login/login');
			},
			login() {
				this.$refs.auth.close();
				if (this.$util.isWeiXin()) {
					let redirect_url = '';
					if (this.url) redirect_url = this.$router.options.base+'pages/login/login/login?back=' + encodeURIComponent(this.url);
					else redirect_url = this.$router.options.base+'pages/login/login/login';

					this.$api.sendRequest({
						url: '/wechat/api/wechat/authcode',
						data: {
							redirect_url
						},
						success: res => {
							if (res.code >= 0) {
								location.href = res.data;
							}
						}
					})
				} else {
          // #ifdef H5
				  if(isOnXianMaiApp){
            schemeGo(appLoginUrl);
				   return;
          }
          // #endif
					if (this.url) this.$util.redirectTo('/pages/login/login/login', {
						back: this.url
					});
					else this.$util.redirectTo('/pages/login/login/login');
				}
			}
		}
	};
</script>

<style lang="scss">
	.uni-tip {
    width: 552rpx;
    background: #FFFFFF;
    border-radius: 20rpx;
    padding-top: 35rpx;
    padding-bottom: 36rpx;
    box-sizing: border-box;
	}
  .uni-tip-title{
    font-size: 36rpx;
    font-weight: bold;
    color: #333333;
    text-align: center;
  }
  .uni-tip-content{
    font-size: 28rpx;
    font-weight: 500;
    color: #666666;
    text-align: center;
  }
  .uni-tip-group-button{
    display: flex;
    justify-content: space-around;
    align-items: center;
    margin-top: 56rpx;
  }
  .uni-tip-button{
    width: 220rpx;
    height: 68rpx;
    line-height: 68rpx;
    background: #FFFFFF;
    border: 2rpx solid var(--custom-brand-color);
    border-radius: 34rpx;
    margin: 0;
    &-not{
      color: var(--custom-brand-color);
    }
  }
</style>
