<template>
	<view class="empty" :class="{ fixed: fixed }">
		<view v-if="isEmptyImg">
			<!-- 购物车 -->
			<view class="empty_img1" v-if="entrance=='cart'"><image :src="$util.img('/public/static/youpin/cart_empty.png')" mode="aspectFit"></image></view>
			<!-- 我的收藏 -->
			<view class="empty_img1" v-else-if="entrance=='collection'"><image :src="$util.img('/public/static/youpin/collection_empty.png')" mode="aspectFit"></image></view>
			<!-- 银行卡 -->
			<view class="empty_img1" v-else-if="entrance=='bank'"><image :src="$util.img('/public/static/youpin/bank_empty.png')" mode="aspectFit"></image></view>
			<!-- 直播 -->
			<view class="empty_img" v-else-if="entrance=='live'"><image :src="$util.img('public/static/youpin/empty_wondeful.png')" mode="aspectFit"></image></view>
      <!--	签到		-->
      <view class="empty_img singIn_img" v-else-if="entrance=='signIn'"><image :src="$util.img('public/static/youpin/member/signin/sign-in-record-empty.png')" mode="aspectFit"></image></view>
      <!--   足迹   -->
      <view class="empty_img1 footprint_img" v-else-if="entrance=='footprint'"><image :src="$util.img('/public/static/youpin/footprint_empty.png')" mode="aspectFit"></image></view>
      <!--   订单列表   -->
      <view class="empty_img1 footprint_img" v-else-if="entrance=='orderList'"><image :src="$util.img('/public/static/youpin/order/order-empty.png')" mode="aspectFit"></image></view>
      <!-- 其他 -->
			<view class="empty_img" v-else><image :src="$util.img('upload/uniapp/common-empty.png')" mode="aspectFit"></image></view>
		</view>
		<text class="ns-text-color-gray ns-margin-top ns-margin-bottom" :class="{isEmptyImg:!isEmptyImg}">{{ text }}</text>
		<button type="primary" size="mini" class="button " @click="goIndex" v-if="isIndex && emptyBtn.text != '去登陆'">{{ emptyBtn.text }}</button>
		<button type="primary" size="mini" class="button " open-type="getUserInfo" @getuserinfo="bindgetuserinfo" @click="goIndex" v-if="isIndex && emptyBtn.text == '去登陆'">
			{{ emptyBtn.text }}
		</button>
	</view>
</template>

<script>
export default {
	name: 'ns-empty',
	data() {
		return {
			currentRoute: ''
		};
	},
	props: {
		text: {
			type: String,
			default: '暂时没找到相关数据哦！'
		},
		isIndex: {
			type: Boolean,
			default: true
		},
		// 判断是否有图标
		isEmptyImg: {
			type: Boolean,
			default: true
		},
		emptyBtn: {
			type: Object,
			default: () => {
				return { text: '去逛逛',mode:'redirectTo' };
			}
		},
		fixed: {
			type: Boolean,
			default: true
		},
		entrance:{
			type:String,
			default: ''
		},
		params:{
			type: Object,
			default: () => {
				return {
					isMemberAuth:1, // 是否实名
				};
			}
		},
	},
	created() {
		let currentPage = getCurrentPages()[getCurrentPages().length - 1];
		this.currentRoute = '/' + currentPage.route;
	},
	methods: {
		goIndex() {
			if(!this.params.isMemberAuth){ //未实名执行父组件绑定的方法
				this.$emit('bindEvent')
				return
			}
			if (this.emptyBtn.url) {
				this.$util.redirectTo(this.emptyBtn.url, {}, this.emptyBtn.mode);
			} else {
				this.$util.redirectTo('/otherpages/shop/home/<USER>', {}, 'redirectTo');
			}
		},
		bindgetuserinfo() {
			if (this.$util.isWeiXin()) {
				let redirect_url = '';
				if (this.url) redirect_url = Config.h5Domain + '/pages/login/login/login?back=' + encodeURIComponent(this.url);
				else redirect_url = Config.h5Domain + '/pages/login/login/login';

				this.$api.sendRequest({
					url: '/wechat/api/wechat/authcode',
					data: {
						redirect_url
					},
					success: res => {
						if (res.code >= 0) {
							location.href = res.data;
						}
					}
				});
			} else {
				if (this.url)
					this.$util.redirectTo('/pages/login/login/login', {
						back: this.url
					});
				else this.$util.redirectTo('/pages/login/login/login');
			}
			this.$util.redirectTo('/pages/login/login/login', {
				back: this.currentRoute
			});
		}
	}
};
</script>

<style lang="scss">
.empty {
	width: 100%;
	display: flex;
	flex-direction: column;
	align-items: center;
	padding: $ns-padding;
	box-sizing: border-box;
	.empty_img {
		width: 206rpx;
		height: 206rpx;

		image {
			width: 100%;
			height: 100%;
			padding-bottom: $ns-margin;
		}
	}
  .singIn_img{
    width: 360rpx;
    height: 360rpx;
  }
  .footprint_img{
    width: 240rpx!important;
    height: 240rpx!important;
  }
	.empty_img1{
		width: 400rpx;
		height: 280rpx;
		margin-bottom: 26rpx;
		image {
			width: 100%;
			height: 100%;
			padding-bottom: $ns-margin;
		}
	}
	.iconfont {
		font-size: 190rpx;
		color: $ns-text-color-gray;
		line-height: 1.2;
	}
	button {
		min-width: 300rpx;
		line-height: 2.9;
		margin-top: 100rpx;
	}
}
.isEmptyImg{
	padding: 120rpx 0 60rpx 0;
}
.fixed {
	position: fixed;
	left: 0;
	top: 20vh;
}
.ns-text-color-gray{
	text-align: center;
}
</style>
