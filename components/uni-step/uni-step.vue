<template>
  <!-- 进度 当前进度条 -->
  <view class="step__content">
    <view class="step__progress">
      <view class="step__progress-box">
        <view
          v-for="(spots, index) in dataList"
          :key="index"
          class="step__progress-spot"
          :class="
            spots.status == 'white'
              ? ''
              : spots.status == 'black'
              ? 'isCurrent-spot'
              : 'noArrived-spot'
          "
          :style="'left: ' + margin * index + 'rpx'"
        ></view>
        <view
          v-for="(lines, index) in DATALIST_SPLICE"
          :key="getKey(index)"
          class="step__progress-line"
          :class="lines.status != 'gray' ? 'isArrived-line' : ''"
          :style="'left: ' + margin * (index + 1) + 'rpx'"
        ></view>
      </view>
    </view>
    <view class="step__text">
      <view
        class="step__text-name"
        v-for="(item, index) in dataList"
        :key="index"
        >{{ item.status_text }}</view
      >
    </view>
  </view>
</template>
<script>
export default {
  props: {
    dataList: {
      type: Array,
      default: function () {
        return {};
      },
    },
  },
  computed: {
    DATALIST_SPLICE() {
      let newArr = [...this.dataList];
      newArr.splice(0, 1);
      return newArr;
    },
  },
  data() {
    return {
      margin: 122,
    };
  },
  methods: {
    getKey(index){
      return index + Math.random()
    }
  },
};
</script>
<style lang="scss" scoped>
.step__content {
  width: 100%;
  box-sizing: border-box;
  padding-top: 10rpx;
  .step__progress {
    width: 100%;
    display: flex;
    // padding: 10rpx 0;
    // align-items: center;
    // justify-content: center;
    box-sizing: border-box;
    padding: 0 56rpx;

    .step__progress-box {
      padding: 10rpx 0;
      display: flex;
      justify-content: center;
      position: relative;
      .step__progress-spot {
        width: 14rpx;
        height: 14rpx;
        background: #ffffff;
        border-radius: 50%;
        border: 4rpx solid var(--custom-brand-color);
        position: absolute;
        top: 0;
      }
      .isCurrent-spot {
        background: var(--custom-brand-color);
      }
      .noArrived-spot {
        width: 14rpx;
        height: 14rpx;
        background: #cccccc;
        border: 4rpx solid #cccccc;
      }
      .step__progress-line {
        width: 124rpx;
        height: 3rpx;
        background: #cccccc;
        border-radius: 2px;
      }
      .isArrived-line {
        background: var(--custom-brand-color);
      }
    }
  }
  .step__text {
    width: 100%;
    display: flex;
    justify-content: space-around;
    margin-top: 10rpx;
    .step__text-name {
      font-size: 24rpx !important;
      font-weight: 400;
      color: #333333;
    }
  }
}
</style>
