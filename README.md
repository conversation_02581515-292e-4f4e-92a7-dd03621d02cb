# youpin_shop

#### 介绍
柚品多商户系统，面向用户端-微信小程序

### 前端框架
```
使用的是vue.js技术栈,uniapp前端框架
```

### 目录
```
│
├─common: css文件及js工具类等（包含minxin文件）
│
├─components: 存放所有组件 (包含公用组件、特定组件等)
│
├─lang: 中英文语言包
│
├─node_modules: 所有依赖
│
├─otherpages: 其他页面
│
├─promotionpages: 促销活动页面
│
├─static： 静态文件
│
├─store: vuex状态管理器
│
├─unpackage: 用HbuildX打包运行的文件目录
│
├─App.vue: 入口文件
│
├─main.js: 全局插件配置文件
│
├─manifest.json: 开发全局配置文件,如权限地图配置等
│
├─pages.json: 路由及小程序或H5外框样式控制等
│
├─README.md 整个项目说明文档；
│
├─uni.scss: 重置uniapp-UI框架的全局样式
│
├─.gitignore 过滤git上传文件等用途；
│

```


### 目录说明「 v100版本无用到的,后面会添加 /**/ 作为标识」

#### 一、common
```

│
├─js
│  ├──map
│	 │   ├─map文件存放打开地图的共用方法，里面包含通用的、IOS、安卓三种方法，根据需求调用；
│	 │   └─openMap.js为开启地图方法，transformCoordinate.js文件为各地图平台坐标转换方法；
│  ├──request
│	 │   └─http.js 简单的的一个请求封装；
│  │
│  ├─adaPay.js 为调起支付封装方法；
│  ├─apiurls.js 为现版本story_v100新增的Api路径；
│  ├─buriedPoint.js we分析埋点统一简单封装请求；
│  ├─config.js 项目的请求的配置路径和密钥存放文件；
│  ├─golbalConfig.js 存放获取请求里面的一些全局设置的[mixins]文件，如主题等；
│  ├─html-parser.js 富文本解析方法；
│  ├─http.js 简单的的一个请求封装；
│  ├─lang.js 封装的一个语言管理包，传对应的参数（对应的变量名），返回对应的语言属性值；
│  ├─map-wx-jssdk.js 封装好的微信地图jssdk，项目里面共有三处用到，用于原生微信小程序定位地图显示；
│  ├─socket.js 即时通讯方法，暂时无用；
│  ├─util.js 存放着一些工具类方法以及请求方法等；
│  ├─validate.js 封装好的一些表单验证方法，使用例子可以查看otherpages/login/find；
│  ├─wx-jssdk.js 封装着微信的原生方法，如初始化注入全新啊验证配置，调用支付、获取收货地址、分享微信；
│  ├─system.js 存放全局验证等方法（部分带有回调处理）；
│
├─mixins
│  ├─applnlineH5.js app内嵌h5返回页面处理；
│  ├─auth.js 为获取用户登录code、openId、用户第三方信息等，现阶段App.vue、login.vue、regiser.vue文件引用；
│  ├─fenxiao-words.js 主用于根目录otherpages/fenxiao的分销模块的[mixins]文件（story_v100无用到）；
│  ├─scroll-view.js 公用返回顶部的滚动[mixins]文件；
│  ├─socketTest.js 即时通讯[mixins]文件，用于other/chat/room；
│  ├─statistics.js 统计访问次数[mixins]文件；
│  └─wx_expose_goods.js 滚动监听商品曝光处理；
│
```

#### 二、components
```
│
├─city-select 城市选择组件，用法可参考otherpages/index/city
├─diy-bargain 砍价组件，用于营销活动/**/
├─diy-bottom-nav 自定义的底部tab模块
├─diy-coupon 优惠券领取组件/**/
├─diy-discount 限时折扣组件/**/
├─diy-fenxiao-goods-list 展示分销的商品列表组件/**/
├─diy-goods-level-category 商品分类组件
├─diy-goods-list 商品列表
├─diy-graphic-nav 图文导航/**/
├─diy-groupbuy 团购组件/**/
├─diy-horz-blank 辅助空白组件，一般是用来隔开，可配置高度和背景颜色
├─diy-horz-line 辅助线组件
├─diy-img-ads 轮播图组件，首页应用
├─diy-live 小程序直播广告组件、此组件只能是在微信小程序里面使用，正常使用需要安装live-player-plugin插件才可以/**/
├─diy-notice 文字公告 /**/
├─diy-pintuan 拼团组件 /**/
├─diy-rich-text 富文本回显组件 /**/
├─diy-rubik-cube 魔方、橱窗组件 /**/
├─diy-search 共用商品搜索组件 /**/ ，此组件项目有需求，但没有引用到，建议在此组件上引用
├─diy-seckill 秒杀专区组件 /**/
├─diy-shop-info 店铺信息组件 /**/
├─diy-shop-rank-list 店铺排行榜组件/**/
├─diy-shop-search 店内商品搜索组件/**/
├─diy-shop-store 门店列表组件 /**/
├─diy-text 文本组件 /**/
├─diy-text-nav 文本导航组件 /**/
├─diy-title 标题组件 /**/
├─diy-uni-popup 弹窗组件
├─diy-video 视频播放组件 /**/
├─diy-wholesale 批发组件模块 /**/
├─drag-button 悬浮拖放按钮组件 /**/
├─l-time 时间转换文字描述，例如：XXX，刚刚砍掉10元，刚刚为时间描述 /**/
├─loading-cover 页面加载组件，全局使用
├─mescroll/my-list-mescroll 上拉加载,下拉刷新组件,很多地方引用
├─myp-one 用于模拟数字密码输入组件，内部模拟支付效果输入可以调用
├─ns-adv 广告组件 /**/
├─ns-chat
│  └─ns-chat-goods 发送商品组件；  ns-chat-order 发送订单组件，此组件是用于客服即时聊天系统的otherpages/chat/room /**/
├─ns-copyright 底部版权信息展示组件 /**/
├─ns-datepicker 时间日期选择器，具体功能请看具体组件参数 /**/
├─ns-empty 空数据时展示组件 ,很多地方引用
├─ns-fenxiao-goods-detail 分销商品模块展示组件 /**/
├─ns-goods-action 商品底部导航外层框组件
├─ns-goods-action-button 商品底部导航按钮组件
├─ns-goods-action-icon 商品底部导航icon按钮组件
├─ns-goods-item
│  └─ns-goods-item-col 商品信息竖列表展示，配合团购、砍价、批发使用; ns-goods-item-row 商品信息行列表展示，配合拼团组件使用；/**/
├─ns-goods-promotion 当前商品参与的营销活动入口组件 /**/
├─ns-goods-recommend 推荐商品页面，一般用于一些营销模块浏览完后，底部放置推荐模块，或者购物车下方推荐模块；/**/
├─ns-goods-sku
│  └─ns-goods-sku-new 商品SKU模版选择； ns-goods-sku-wholesale 商品批发SKU模版选择；ns-goods-sku 普通商品SKU模版选择，配合├─diy-goods-level-category组件使用
├─ns-hint 提示窗口组件，主应用于商品详情页
├─ns-loading 加载中组件
├─ns-login 登录弹窗组件
├─ns-navbar 顶部导航栏组件 /**/
├─ns-progress 进度条组件 /**/
├─ns-search 商品搜索组件，配合diy-goods-level-category组件使用
├─ns-switch 开关组件 /**/
├─payment-app app选择支付方式组件 /**/
├─payment-h5 h5选择支付方式组件 /**/
├─payment-mp-wx 使用uni.requestPayment封装好的方法选择支付方式
├─pick-regions uni-app基于各平台选择器封装好的多列选择器
├─sx-rate 星星评价选择器，用于订单评价 /**/
├─toTop 返回顶部，运用于购物车pages/goods/cart/cart.vue，批发购物车列表promotionpages/wholesale/cartList/cartList.vue
├─uni-badge 数字角标组件 /**/
├─uni-calendar 日历选择器，用于签到otherpages/member/signin/signin.vue /**/
├─uni-count-down 倒计时组件，用于订单倒计时
├─uni-drawer 抽屉弹窗，商品分类、筛选等可用
├─uni-grid 与 ni-grid-item Grid宫格组件，提供移动端常见的宫格布局，用于otherpages/goods/brand/brand.vue
├─uni-icons 基于uni-app的图标组件
├─uni-list 基于uni-app列表组件,用于pages/agreement/list/list.vue
├─uni-list-chat 聊天列表组件  /**/
├─uni-nav-bar 基于uni-app自定义导航栏  /**/
├─uni-number-box 计数器
├─uni-popup 弹窗父盒子
├─uni-status-bar 状态栏组件，用于uni-nav-bar组件
├─uni-swipe-action 滑动操作，可用于列表左滑动显示操作栏,现用于聊天列表左滑动删除等操作
├─uni-tag 基于uni-app的Tag标签组件
├─yd-auth-popup 登录授权获取微信信息组件
│
```

```
已删除的组件
│
├─uni-section 标题栏组件，没啥卵用 /**/
├─uni-list-ad 不知道有什么卵用 /**/
├─uni-load-more 基于uni-app加载更多组件  /**/
│
```

#### 三、lang
```
│
├─en-us文件包含英文语言包；
├─zh-cn文件包含中文语言包；
│
提示：通过lang.js进行传参获取,已挂载到vue全局；
```

#### 四、node_modules
```
项目所需要的依赖，一般安装插件的基本信息会在package.json里面
```

#### 五、otherpages
```
│
├─chat(客服聊天模块) /**/
│  ├─list(聊天列表)
│  └─room(聊天窗口)
│
├─diy(微页面组件集合模块，包含了components中diy-开头的所有组件展示) /**/
│
├─fenxiao(分销) /**/
│  ├─apply(分销商申请)/**/
│  ├─bill(账单) /**/
│  ├─follow(我的关注) /**/
│  ├─goods_list(分销商品列表) /**/
│  ├─index(分销中心) /**/
│  ├─level(分销商等级) /**/
│  ├─order(分销订单) /**/
│  ├─order_detail(订单详情) /**/
│  ├─promote_code(推广码) /**/
│  ├─team(我的团队) /**/
│  ├─withdraw_apply(提现申请) /**/
│  └─withdraw_list(提现记录) /**/
│
├─goods(商品)
│  ├─brand(品牌专区)/**/
│  ├─consult(商品咨询) /**/
│  ├─consult_edit(我要咨询) /**/
│  ├─coupon(优惠券领取) /**/
│  ├─coupon_receive(领取优惠券) /**/
│  ├─evaluate(商品评价)
│  └─search(搜索-公用)
│
├─help(帮助中心) /**/
│
├─index/city(城市选择，具体没什么用处) /**/
│
├─live/list(生活直播列表模块) /**/
│
├─member(会员模块)
│  ├─account(账户) /**/
│  ├─account_edit(账户编辑-提现) /**/
│  ├─address(收货地址)
│  ├─address_edit(地址编辑)
│  ├─apply_withdrawal(申请提现)/**/
│  ├─balance(我的余额)/**/
│  ├─balance_detail(余额明细)/**/
│  ├─bank_card_detail(银行卡详情)
│  ├─bank_card_list(银行卡管理列表)
│  ├─bank_list(银行列表选择)
│  ├─collection(我的收藏)
│  ├─coupon(我的优惠券) /**/
│  ├─footprint(我的足迹)/**/
│  ├─gift(我的礼品)/**/
│  ├─gift_detail(礼品订单)/**/
│  ├─info(个人资料)/**/
│  ├─level(会员等级)/**/
│  ├─message(我的消息)/**/
│  ├─modify_face(修改头像)/**/
│  ├─open_shopkeeper(VIP开通掌柜)
│  ├─pay_password(支付密码)/**/
│  ├─point(我的积分)/**/
│  ├─real_name_authentication(实名认证)
│  ├─signin(签到有礼)/**/
│  ├─withdrawal(提现记录)/**/
│  └─withdrawal_detail(提现详情)/**/
│
├─notice(公告) /**/
│
├─order(订单列表) /**/
│  ├─evaluate(我要评价) /**/
│  ├─refund(申请退款) /**/
│  └─refund_detail(退款详情)/**/
│
├─recharge(充值) /**/
│  ├─list(充值列表) /**/
│  ├─detaila(充值详情) /**/
│  └─order_list(充值记录)/**/
│
├─seckill(秒杀) /**/
│  ├─list(秒杀专区) /**/
│  ├─detaila(秒杀商品详情) /**/
│  └─order_list(秒杀代付款列表)/**/
│
├─show(店铺)
│  ├─category(店内分类)
│  ├─home(店铺首页)
│  ├─introduce(店铺介绍) /**/
│  ├─list(商品分类->分类的全部商品列表)
│  ├─message(我的信息) /**/空文件
│  ├─search(店铺搜索) /**/
│  ├─store_detail(门店信息) /**/
│  └─street(店铺街)/**/
│
├─store/store_empty(店铺打烊页面)
│
├─verification(核销审核模块) /**/
│
├─web(浏览器窗口，传入路径进行访问，不过需要在微信小程序后台添加安全域名) /**/
│
├─assist(分销助力)
│  └─detail(详情)
│
└
```

#### 六、pages
```
│
├─agreement(协议)
│
├─goods(商品)
│  ├─category(商品分类) /**/
│  ├─detail(商品详情)
│  ├─list(商品列表) /**/
│  ├─periodbuy-detail (周期购商品详情页)
│  ├─index/index(微页面组件集合) /**/
│  ├─search(店铺搜索) /**/
│  ├─store_detail(门店信息) /**/
│  └─street(店铺街)/**/
│
├─login(登录注册模块)/**/
│
├─member(我的)
│
├─order(订单中心)
│  ├─activist(退款/售后)
│  ├─complain(平台维权) /**/
│  ├─cycle_payment(周期购确认订单)
│  ├─detail(普通订单详情-快递方式)
│  ├─detail_local_delivery(订单详情-实时配送功能（如外卖）) /**/
│  ├─detail_pickup(订单详情-增加自提功能) /**/
│  ├─detail_virtual(订单详情-增加核销功能) /**/
│  ├─list(订单列表)
│  ├─list_cycle(周期购列表)
│  ├─logistics(物流信息) /**/
│  ├─manage_cycle(周期购管理)
│  ├─payment(确认订单) /**/
│  └─upgrade_payment(0元店铺升级订单支付) /**/
│
├─pay(支付)
│  ├─index(支付方式)
│  └─payment(支付结果)
│
```

#### 七、promotionpages
```
提示：存放促销活动的全部页面
│
├─articlemessage(分享体验)/**/
├─bargain(砍价专区)/**/
├─combo(组合套餐)/**/
├─game(游戏专区)/**/
├─groupbuy(团购专区)/**/
├─pintuan(拼团专区)/**/
├─point(积分商城)/**/
├─seckill(秒杀专区)/**/
├─topics(专题活动)/**/
├─wholesale(批发专区)/**/
├─assist(分销助力)
├─special_offers(多折扣优惠)
│
```

#### 八、pluginspages
```
提示：存放插件引用的分包
│
├─logisticsPlugin(物流查询)/**/
│
```

#### 九、static
```
存放静态文件，一般放css和img文件等等；
│
├─imgs 静态图片
│
├─css/theme
│  ├─gradient.scss 分销组件的样式表
│  └─theme-***.scss 都是主题样式，根据需求引进，但theme-default存放一些样式变量
```

#### 十、store
```
vuex状态管理器，index.js文件存放着系统的临时信息；
```

#### 十一、unpackage
```
HBuilderX编译好的项目包，针对编译的需求有微信小程序、H5等相对应的包；
```



### 2020.09.10 story_v100版本可以优化内容
```
1、“首页、购物车、我的”切换方式从业务逻辑及用户体验建议，建议使用uni.switchTab({url: '***'})方法,同时可以去除首页按钮，不用需要调用uni.hideHomeButton方法；--------结合业务需求，维持原有的方法；
2、根目录common/css建议放到静态目录static/css里面； --------已经优化
3、发现全局样式在App.vue和uni.scss都引用混乱，建议全局样式挂到App.vue, 常用样式变量则引用到uni.cscc里面，而项目公用的css则全部迁移到common/css/main.css里面；--------已经优化；
【uni.scss中所写的一切内容，都会注入到每个声明了scss的文件中，这意味着，如果您的uni.scss如果有几百行，大小10k左右，那么这个10k都会被注入所有的 其他scss文件(页面)中，如果您的应用有50个页面，那么有可能因此导致整体的包体积多了50 * 10 = 500k的大小，这可能会导致小程序包太大而无法预览和发布， 所以，我们建议您只将scss变量相关的内容放到uni.scss中。】
4、api路径建议在在根目录创建一个api文件，存放以不同模块命名的api的文件并挂载到全局，这样后期模块清晰，便于维护；-------优化调整为（路径不变，但文件内部分注释模块，并挂载到全局）
5、公用的mixins文件，建议在common创建一个mixins文件夹单独存放，对于只针对特有模块的，如分销、营销等，建议放置分包里面pubic/js里面；--------已优化
6、common/js/util.js文件存放的工具类和方法混合在一起，建议剥离开来，单独存放；
7、建议store状态管理器，按系统、订单、购物车等模块进行文件拆解，不建议统一放在一起；
8、针对YPcomponents组件，不建议放在根目录，建议迁移到根目录的components里面； --------已优化
9、components/diy-bottom-nav建议有需要的页面才引用，不要挂载到vue全局上面（其他挂载全局组件也一样）；--------考虑到开发工具问题，暂时放挂载
10、小图片建议用base64转处理，减小体积，或放服务端进行请求；；--------已优化
11、建议请求方法进行改造，添加拦截请求、响应请求，方便后期做全局请求处理、权限处理等；--------已优化
12、

```


### 建议的项目结构如下：
```
│
│
├─js
│  ├─common(放共用的系统方法，比如封装好的权限方法、支付方法、重置token方法，配置文件、mixins文件夹等)
│  ├─utils(放置工具类方法，针对处理数据的方法，验证等，后期可以直接拎出来维护存放的)
│  ├─request(封装好的一个请求方法，单独管理)
│  └...
│
├─components（存放所有的组件，建议组件内部的请求方法的，添加一个前缀，如：req-city-select, 无请求的方法的按正常分段命名即可）
│ 【此目录下创建组件，遵循“ components/组件名称/组件名称.vue ”目录结构，HBuilderX 2.5.5版本以上不用安装-》引用-》注册使用，easycom下直接在页面写标签即可使用】
│
├─lang（语言包，看是否去除该文件）
│
├─pages（主包）
│
├─static（静态资源文件）
│  ├─css(静态全局css文件，iconfont等文件)
│  └─img(静态图片，建议使用png图片，并发布前在线压缩)
│
├─store（状态管理器）
│  ├─index.js(集合所有状态管理器，然后进行统一挂载)
│  └─modules(语义化命名每个模块的store，比较大模块的需要分开来，后期分销、营销模块也许单独建一个.js文件管理)
│       ├─stytem(系统，包含个人中心、配置等)
│       ├─order(订单、退款等)
│       └...
│
│

```



### 安装教程
1. 小程序发布教程
开发相关介绍：https://uniapp.dcloud.io/

2. H5发布教程
开发相关开发介绍：https://uniapp.dcloud.io/




