/*盒子模型*/
.display-flex{
  display: flex;
}
.flex-wrap{
  flex-wrap:wrap;
}
.flex-space-between{
display: flex;
justify-content:space-between;
}
.flex-space-between-center{
display: flex;
justify-content:space-between;
align-items: center;
}
.flex-center {
  display: flex;
  justify-content: center;
  align-items: center;
}

.flex-column{
  display: flex;
  flex-direction: column;
}

.flex-row{
  display: flex;
  flex-direction: row;
}

.align-items-center{
  align-items: center;
}
.flex-start{
  display: flex;
  justify-content: flex-start;
}
.flex-column-start{
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: flex-start;
}
.flex-column-end{
  display: flex;
  flex-direction: column;
  justify-content: flex-end;
  align-items: flex-end;
}
.flex-start-center{
  display: flex;
  justify-content: flex-start;
  align-items: center;
}
.flex-start-end{
  display: flex;
  justify-content: flex-start;
  align-items:flex-end;
}
.flex-end{
  display: flex;
  justify-content: flex-end;
}
.flex-end-center{
  display: flex;
  justify-content: flex-end;
  align-items: center;
}
.flex1-end-center{
  display: flex;
  flex: 1;
  justify-content: flex-end;
  align-items: center;
}

.flex-column-center {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}
.flex1-column{
  display: flex;
  flex-direction: column;
  flex: 1;
}
.flex1-f{
  display: flex;
  flex: 1;
}
.flex2-f{
  display: flex;
  flex: 2;
}
.flex3-f{
  display: flex;
  flex: 3;
}
.flex1{
  flex: 1;
}
.flex2{
  flex: 2;
}
.flex3{
  flex: 3;
}
.flex1-center{
  display: flex;
  flex: 1;
  justify-content: center;
  align-items: center;
}
.flex1-start{
  display: flex;
  flex: 1;
  justify-content: flex-start;
}
.flex2-center{
  display: flex;
  flex: 2;
  justify-content: center;
  align-items: center;
}
.flex3-center{
  display: flex;
  flex: 3;
  justify-content: center;
  align-items: center;
}

.flex2-start-center{
  display: flex;
  flex: 2;
  justify-content: flex-start;
  align-items: center;
}
.flex1-start-center{
  display: flex;
  flex: 1;
  justify-content: flex-start;
  align-items: center;
}
.flex1-column-center{
  display: flex;
  flex: 1;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}
.flex1-column-start-center{
  display: flex;
  flex: 1;
  flex-direction: column;
  justify-content: center;
  align-items: flex-start;
}
.flex1-end{
  display: flex;
  flex: 1;
  justify-content: flex-end;
}

.display-box{
 display: -webkit-box;
}
.box-vertical{
  -moz-box-orient:block-axis;
  -webkit-box-orient: block-axis;
}
.box1-center{
  display: -webkit-box;
  -webkit-box-flex: 1;
  -webkit-box-align: center;
  -webkit-box-pack: center;
}
.align-self-center{
align-self: center;
}
.align-self-start{
align-self: flex-start;
}