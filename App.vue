<script>
import Vue from 'vue'
	import auth from 'common/mixins/auth.js';
  import statistics from "./common/mixins/statistics.js";
	import apiurls from "./common/js/apiurls.js";
	import system from './common/js/system.js';
  // #ifdef H5
  import {isOnXianMaiApp, setAppData} from "./common/js/h5/appOP";
  // #endif
  import ypAddEnterpriseWechatFriendButton from "@/components/yp-add-enterprise-wechat-friend-button/yp-add-enterprise-wechat-friend-button.vue"
  const ypAddEnterpriseWechatFriendButtonComponet = Vue.component('yp-add-enterprise-wechat-friend-button', ypAddEnterpriseWechatFriendButton);

  import ypWechatVerificationCode from "@/components/yp-wechat-verification-code/yp-wechat-verification-code.vue"
  const ypWechatVerificationCodeComponet = Vue.component('yp-wechat-verification-code', ypWechatVerificationCode);

  import diyRealNamePopup from "@/components/diy-real-name-popup/diy-real-name-popup.vue";
  const diyRealNamePopupComponet = Vue.component('diy-real-name-popup', diyRealNamePopup);


	export default {
		mixins: [auth],
		onLaunch: function(data) {
      let that=this;
      uni.addInterceptor('reLaunch',{
        success(e) {
          that.watchRouter();
        }
      })
      uni.addInterceptor('navigateTo', {//监听跳转
        success(e) {
          that.watchRouter();
        }
      })
      uni.addInterceptor('redirectTo', {//监听关闭本页面跳转
        success(e) {
          that.watchRouter();
        }
      })
      uni.addInterceptor('switchTab', {//监听tabBar跳转
        success(e) {
          that.watchRouter();
        }
      })
      uni.addInterceptor('navigateBack', {//监听返回
        success(e) {
          that.watchRouter();
        }
      })
      this.watchRouter()  //启动h5时，主动触发一次
			// #ifdef MP
			const updateManager = uni.getUpdateManager();
			updateManager.onCheckForUpdate(function(res) {
				// 请求完新版本信息的回调
			});

			updateManager.onUpdateReady(function(res) {
				uni.showModal({
					title: '更新提示',
					content: '新版本已经准备好，是否重启应用？',
					success(res) {
						if (res.confirm) {
							// 新的版本已经下载好，调用 applyUpdate 应用新版本并重启
							updateManager.applyUpdate();
						}
					}
				});
			});

			updateManager.onUpdateFailed(function(res) {
				// 新的版本下载失败
			});
			// #endif

			// uni.getLocation({
			// 	type: 'gcj02',
			// 	success: (res) => {
			// 		uni.setStorage({
			// 			key: 'location',
			// 			data: {
			// 				latitude: res.latitude,
			// 				longitude: res.longitude
			// 			}
			// 		})
			// 	}
			// })

			//判断是否支持 获取本地位置
			// #ifdef H5
			// if (navigator.geolocation) {
			// 	var n = navigator.geolocation.getCurrentPosition(function(res) {
			// 		console.log(res); // 需要的坐标地址就在res中
			// 	});
			// } else {
			// 	console.log('该浏览器不支持定位');
			// }
			// #endif

			// #ifdef H5
      uni.setStorageSync('appOrigin', window.location.origin+this.$router.options.base)
			if (uni.getSystemInfoSync().platform == 'ios') {
				uni.setStorageSync('initUrl', location.href);
			}
			if(isOnXianMaiApp){
        setAppData({hide:true,bounces:true,nativeNav:true});
      }else{
        let style = document.createElement('style');
        style.type = 'text/css';
        style.innerHTML="uni-page-head{ display: none }";
        document.getElementsByTagName("HEAD").item(0).appendChild(style);
      }
			// #endif
		},
    onShow: async function(data) {
      console.log("data",data)
		  await system.AppVueInit(data,this,true);
		},
    //定义404跳转
    onPageNotFound:function (){
      uni.reLaunch({
        url:`/pages/index/index/index?shop_id=110`
      })
    },
		onHide: function() {},
    methods:{
      watchRouter(){
        // console.log('路由跳转');
        // #ifdef H5
        setTimeout(()=>{
          // h5的模式下全局引入组件
          document.getElementsByTagName('uni-page-body')[0].appendChild(new ypAddEnterpriseWechatFriendButtonComponet().$mount().$el)
          document.getElementsByTagName('uni-page-body')[0].appendChild(new ypWechatVerificationCodeComponet().$mount().$el)
          document.getElementsByTagName('uni-page-body')[0].appendChild(new diyRealNamePopupComponet().$mount().$el)
        })
        // #endif
      }
    }
	};
</script>

<style lang="scss">
	/*每个页面公共css */
	/* #ifndef APP-PLUS-NVUE */
	@import url('/static/css/iconfont.css');
	@import url('/static/css/main.css');
	/* #endif */

	@import  './static/css/flex.scss';
	@import  './static/css/theme/theme-default.scss';
	// @import './static/css/theme/theme-blue.scss';
	// @import './static/css/theme/theme-green.scss';
	@import './static/css/theme/theme-common.scss';
	@import './static/css/theme/gradient.scss';


	/* #ifdef H5 */
	uni-page[data-page="otherpages/index/city/city"] {
		overflow: hidden !important;
	}
	/* #endif */
</style>
