/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */

/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
// $base-color: #FF0036;
// $base-color: #FF335C;
$base-color: #F2280C;
//主色调 rgba  可用opacify函数改变alpha值  实例：opacify($base-color-rgba, 0.8);
$base-color-rgba: rgba(255, 0, 54, 0.1);
$base-help-color: #FF9800; //辅助颜色
/* 文字基本颜色 */
$ns-text-color-black: #333333; //基本色
$ns-vice-color-black: #666666; //基本色
$ns-text-color-gray: rgba(166, 166, 166, 1); //辅助灰色，如加载更多的提示信息
$ns-btn-color-gray: #cccccc; //置灰不可用灰色
$ns-border-color-gray: #e7e7e7;
$ns-bg-color-gray: #e5e5e5;
/* 文字尺寸 */
$ns-font-size-sm: 24rpx; //12
$ns-font-size-xm: 26rpx; //13
$ns-font-size-base: 28rpx; //14
$ns-font-size-lg: 32rpx; //16
//内边距
$ns-padding: 20rpx;
//外边距
$ns-margin: 20rpx;
//圆角
$ns-border-radius: 4px;
// 按钮禁用背景色
$btn-disabled-color: #b7b7b7;

.empty-list-text{
  width: 100%;
  text-align: center;
}
