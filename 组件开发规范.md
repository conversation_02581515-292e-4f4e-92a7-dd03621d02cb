
## 开发组件要求功能
* 1.支持数据双向绑定，可改变父组件的值（可选）
* 2.要支持事件回调（可选）

## 组件命名规则
xm-XXX-XXX 如：xm-nav-item

## 回调方法命名规则
使用驼峰命名，如： onChange onSubmit onSubmitSuccess

## 例子参考

#### vue
```vue

<template>
  <view>
    <view v-for="(item,index) in list" :key="index" :v-model="currentValue" @click="onTagChange">{{item.name}}</view>
  </view>
</template>

```

#### js
```js
export default {
    props: {
      value : {     //v-model传递进来的参数名默认是value
        type : [String,Number],
        default : ''
      }
    },
    name: "xm-nav-item",
    data() {
      return {
        currentValue: this.value,
        list: [
          {
            id: 1,
            name: "商品1"
          },
          {
            id: 2,
            name: "商品2"
          },
        ]
      };
    },
    components: {
    },
    computed: {
    },
    beforeMount() {
    },
    mounted() {
    },
    methods: {
      setValue(val){
        if (this.currentValue === val) {
          return;
        }
        this.currentValue = val;
      },
      onTagChange(value) {
        //执行input方法（数据双向绑定，改变父组件的值）
        this.$emit('input', value);
        // 父组件引用时，绑定<xm-nav-item :TagValue.sync='TagValue'></xm-nav-item>，即可改变父组件值，仅编译成微信小程序支持，其他端有问题
        this.$emit('update:TagValue', value.id)
        //执行父组件传递进来的回调函数
        this.$emit('onChange', value.id);
      }
    },
    watch : {
      value(newVal,oldVal){    //监听父组件传递进来的v-model改变
        this.setValue(newVal)
      }
    }
  }
```