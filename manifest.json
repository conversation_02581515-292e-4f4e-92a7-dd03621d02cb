{
    "name" : "youpin_shop",
    "appid" : "__UNI__5B80CF8",
    "description" : "",
    "versionName" : "1.0.0",
    "versionCode" : "100",
    "transformPx" : false,
    "sassImplementationName" : "node-sass",
    /* 5+App特有相关 */
    "app-plus" : {
        "usingComponents" : true,
        "nvueCompiler" : "uni-app",
        "splashscreen" : {
            "alwaysShowBeforeRender" : true,
            "waiting" : true,
            "autoclose" : true,
            "delay" : 0
        },
        /* 模块配置 */
        "modules" : {
            "OAuth" : {},
            "Payment" : {},
            "Share" : {},
            "VideoPlayer" : {},
            "Webview-x5" : {},
            "LivePusher" : {},
            "Maps" : {}
        },
        /* 应用发布信息 */
        "distribute" : {
            /* android打包配置 */
            "android" : {
                "permissions" : [
                    "<uses-feature android:name=\"android.hardware.camera\"/>",
                    "<uses-feature android:name=\"android.hardware.camera.autofocus\"/>",
                    "<uses-permission android:name=\"android.permission.ACCESS_COARSE_LOCATION\"/>",
                    "<uses-permission android:name=\"android.permission.ACCESS_FINE_LOCATION\"/>",
                    "<uses-permission android:name=\"android.permission.ACCESS_NETWORK_STATE\"/>",
                    "<uses-permission android:name=\"android.permission.ACCESS_WIFI_STATE\"/>",
                    "<uses-permission android:name=\"android.permission.CALL_PHONE\"/>",
                    "<uses-permission android:name=\"android.permission.CAMERA\"/>",
                    "<uses-permission android:name=\"android.permission.CHANGE_NETWORK_STATE\"/>",
                    "<uses-permission android:name=\"android.permission.CHANGE_WIFI_STATE\"/>",
                    "<uses-permission android:name=\"android.permission.FLASHLIGHT\"/>",
                    "<uses-permission android:name=\"android.permission.GET_ACCOUNTS\"/>",
                    "<uses-permission android:name=\"android.permission.INTERNET\"/>",
                    "<uses-permission android:name=\"android.permission.MODIFY_AUDIO_SETTINGS\"/>",
                    "<uses-permission android:name=\"android.permission.MOUNT_UNMOUNT_FILESYSTEMS\"/>",
                    "<uses-permission android:name=\"android.permission.READ_CONTACTS\"/>",
                    "<uses-permission android:name=\"android.permission.READ_LOGS\"/>",
                    "<uses-permission android:name=\"android.permission.READ_PHONE_STATE\"/>",
                    "<uses-permission android:name=\"android.permission.RECORD_AUDIO\"/>",
                    "<uses-permission android:name=\"android.permission.VIBRATE\"/>",
                    "<uses-permission android:name=\"android.permission.WAKE_LOCK\"/>",
                    "<uses-permission android:name=\"android.permission.WRITE_CONTACTS\"/>",
                    "<uses-permission android:name=\"android.permission.WRITE_EXTERNAL_STORAGE\"/>",
                    "<uses-permission android:name=\"android.permission.WRITE_SETTINGS\"/>"
                ]
            },
            /* ios打包配置 */
            "ios" : {},
            /* SDK配置 */
            "sdkConfigs" : {
                "maps" : {
                    "amap" : {
                        "appkey_ios" : "",
                        "appkey_android" : "6aada8bfb1a98e3a287267dd3dcaf7e2"
                    }
                },
                "ad" : {}
            }
        },
        "safearea" : {
            "bottom" : {
                "offset" : "none"
            }
        }
    },
    /* 快应用特有相关 */
    "quickapp" : {},
    /* 小程序特有相关 */
    "mp-weixin" : {
        "appid" : "wx30baa146131ed417",
        "setting" : {
            "urlCheck" : false,
            "postcss" : true,
            "es6" : true,
            "minified" : true
        },
        "usingComponents" : true,
        "permission" : {
            "scope.userLocation" : {
                "desc" : "为了更好地为您提供服务"
            }
        },
        "plugins" : {
            "live-player-plugin" : {
                "version" : "1.3.5",
                "provider" : "wx2b03c6e691cd7370"
            },
            "shoppingGuarantee" : {
                "version" : "latest",
                "provider" : "wxd65104595293601e"
            },
            "captcha" : {
                "version" : "2.1.2",
                "provider" : "wx1fe8d9a3cb067a75"
            }
        },
        "uniStatistics" : {
            "enable" : false
        },
        "requiredPrivateInfos" : [ "chooseAddress", "chooseLocation" ],
        "__usePrivacyCheck__" : true
    },
    // "live-player-plugin" : {
    //     "version" : "1.0.14",
    //     "provider" : "wx2b03c6e691cd7370"
    // }
    "mp-alipay" : {
        "usingComponents" : true
    },
    "mp-baidu" : {
        "usingComponents" : true
    },
    "mp-toutiao" : {
        "usingComponents" : true
    },
    "h5" : {
        "sdkConfigs" : {
            "maps" : {
                "qqmap" : {
                    "key" : "6ZDBZ-CLSLX-66747-7MVM4-HLK47-XMBXU"
                }
            }
        },
        "router" : {
            "mode" : "history",
            "base" : "/mini-h5/"
        },
        "title" : "先迈商城",
        "devServer" : {
            "port" : 8950,
            "proxy" : {
                "/apis" : {
                    "target" : "http://www.youpin-local.com",
                    "changeOrigin" : true,
                    "pathRewrite" : {
                        "^/apis" : ""
                    }
                },
                "/xm_apis" : {
                    "target" : "https://dev.xianmai88.com:4443",
                    "changeOrigin" : true,
                    "pathRewrite" : {
                        "^/xm_apis" : ""
                    }
                }
            }
        }
    }
}
