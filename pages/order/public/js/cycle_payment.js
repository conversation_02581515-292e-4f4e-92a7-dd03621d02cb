import AdaPay from 'common/js/adaPay.js'
import store from '../../../../store';
export default {
	data() {
		return {
			isSub: false,
			suk_id: '',
			orderPaymentData: {}

		}
	},
	methods: {
		/**
		 * 选择收货地址
		 */
		selectAddress() {
			this.$util.redirectTo('/otherpages/member/address/address', {
				'back': '/pages/order/cycle_payment/cycle_payment'
			});
		},
		/**
		 * 获取订单初始化数据
		 */
		getOrderPaymentData() {
			this.orderCreateData = uni.getStorageSync('orderCreateData');
			if (!this.orderCreateData) {
				this.$util.showToast({
					title: '未获取到创建订单所需数据!！',
					success: () => {
						setTimeout(() => {
							console.log('!this.orderCreateData')
							console.log(this.orderCreateData)
							this.$util.redirectTo('/pages/index/index/index', {}, 'reLaunch');
						}, 1500)
					}
				});
				return;
			}
			this.$api.sendRequest({
				url: '/api/periodOrder/payment',
				data: {sku_id:this.orderCreateData.sku_id,period_id:this.period_id},
				success: res => {
					if (res.code >= 0) {
						this.orderPaymentData = res.data;
						var member_address = uni.getStorageSync('member_address');
						if(member_address) {
							this.orderPaymentData.member_address = member_address
						} else {
							uni.setStorageSync('member_address',this.orderPaymentData.member_address)
						}
						if (this.$refs.loadingCover) this.$refs.loadingCover.hide();
					} else {
						this.$util.showToast({
							title: res.message,
							success: () => {
								setTimeout(() => {
									this.$util.redirectTo('/pages/index/index/index', {}, 'reLaunch');
								}, 1500)
							}
						});
					}
				},
				fail: res => {
					if (this.$refs.loadingCover) this.$refs.loadingCover.hide();
				}
			})
		},
		/**
		 * 订单创建
		 */
		orderCreate() {
			if (this.verify()) {
				if (this.isSub) return;
				this.isSub = true;
				uni.showLoading({
					mask: true,
					title: '加载中'
				});
				// 获取收货地址选择后存储的地址
				var member_address = uni.getStorageSync('member_address')
				var data = {
					sku_id: this.orderCreateData.sku_id,
					member_address: JSON.stringify(member_address.id),
					period_id:this.period_id
				}
				this.$api.sendRequest({
					url: '/api/periodOrder/submitOrder',
					data,
					success: res => {
						if (res.code >= 0) {

							// 当支付金额为0元时，调用微信支付接口，直接支付成功跳转到周期购管理页
							if(res.data.is_free == 1){
								// 跳转周期购管理页
								this.$util.redirectTo('/pages/order/manage_cycle/manage_cycle', {
									code: res.data.out_trade_no
								}, 'redirectTo');
								uni.removeStorage({
									key: 'orderCreateData',
									success: () => {}
								});
							}else{
								this.orderPayPay(res.data.out_trade_no,res.data.order_ids)
							}
							uni.removeStorageSync('member_address')
						} else {
							this.isSub = false;
							uni.hideLoading();
							if (res.data.error_code == 10 || res.data.error_code == 12) {
								uni.showModal({
									title: '订单未创建',
									content: res.message,
									confirmText: '去设置',
									success: res => {
										if (res.confirm) {
											this.selectAddress();
										}
									}
								})
							} else {
								this.$util.showToast({
									title: res.message
								});
							}
						}
					},
					fail: res => {
						uni.hideLoading();
						this.isSub = false;
					}
				})
			}
		},
		/**
		 * 订单支付
		 */
		orderPayPay(out_trade_no,order_ids) {
			var that = this;
				this.$api.sendRequest({
					url: '/api/pay/pay',
					data: {
						out_trade_no: out_trade_no,
						pay_type: 'adapay'
					},
					success: res => {
						uni.hideLoading();
						if (res.code >= 0) {
							this.$util.wechatPay(res.data.pay_type,res.data.pay_type=='adapay'? res.data.payment : res.data.pay_info, (res)=>{
								uni.hideLoading();
								console.log('succeeded')
								// 跳转支付成功页面
								this.$util.redirectTo('/pages/order/manage_cycle/manage_cycle', {
									code: out_trade_no
								}, 'redirectTo');
								uni.removeStorage({
									key: 'orderCreateData',
									success: () => {}
								});
							},(err)=>{
								uni.hideLoading();
								// 跳转支付失败页面
								this.$util.redirectTo('/pages/order/manage_cycle/manage_cycle', {
									code: out_trade_no
								}, 'redirectTo');
								uni.removeStorage({
									key: 'orderCreateData',
									success: () => {}
								});
							},(err)=>{
								setTimeout(() => {
									this.$util.redirectTo("/pages/order/list/list", {}, "redirectTo")
								}, 2000)
							})
						} else {
							console.log(res)
							// this.isSub = false;
							if(res.message) {
								this.$util.showToast({
									title: res.message
								});
							} else {
								uni.hideLoading();
							}
							if(this.$refs.popupToList) this.$refs.popupToList.open();
						}
					},
					fail: res => {
						this.$util.showToast({
							title: 'request:fail'
						});
					}
				})
		},
		/**
		 * 订单验证
		 */
		verify() {
			if (this.orderPaymentData.is_virtual == 1) {
				if (!this.orderCreateData.member_address.mobile.length) {
					this.$util.showToast({
						title: '请输入您的手机号码'
					});
					return false;
				}
				var reg = /^[1](([3][0-9])|([4][5-9])|([5][0-3,5-9])|([6][5,6])|([7][0-8])|([8][0-9])|([9][1,8,9]))[0-9]{8}$/;
				if (!reg.test(this.orderCreateData.member_address.mobile)) {
					this.$util.showToast({
						title: '请输入正确的手机号码'
					});
					return false;
				}
			}
			if (this.orderPaymentData.is_virtual == 0) {
				if (!this.orderPaymentData.member_address) {
					this.$util.showToast({
						title: '请先选择您的收货地址'
					});
					return false;
				}
			}
			return true;
		},
		init(){
			// 刷新多语言
			this.$langConfig.refresh();
			var member_address = uni.getStorageSync('member_address')
			// 判断登录
			if (!uni.getStorageSync('token')) {
				this.$util.redirectTo('/pages/login/login/login');
			} else {
				this.orderPaymentData= {}
				this.orderCreateData= {}
				if(member_address) {
					this.orderCreateData.member_address=member_address.id
				}
				if (!this.isSub) {
					this.getOrderPaymentData();
				};
			}
		}
	},

	onLoad() {
		uni.removeStorageSync('member_address')
	},
	onShow() {
		if(this.ischoiceWechatAdder){
			let wechatAdderInterval=setInterval(()=>{
				if(this.postWechatAdder){
					console.log("this.choiceWechatAdderError",this.choiceWechatAdderError)
					if(this.choiceWechatAdderError){
						//微信收获地址提交到服务器报错时，需要跳转到地址管理页面
						if(this.choiceWechatAdderError){
							if (this.$refs.loadingCover) this.$refs.loadingCover.hide();
							this.$util.showToast({
								title: '获取微信地址失败，请手动添加地址',
								success: () => {
									setTimeout(() => {
										this.selectAddress()
									}, 1500)
								}
							});
						}
					}
					this.ischoiceWechatAdder=false;
					this.postWechatAdder=false;
					this.choiceWechatAdderError=false;
					clearInterval(wechatAdderInterval);
					this.init();
				}
			},100)
		}else{
			this.init()
		}
	},
	onHide() {
		if (this.$refs.loadingCover) this.$refs.loadingCover.show();
	},
	filters: {
		/**
		 * 金额格式化输出
		 * @param {Object} money
		 */
		moneyFormat(money) {
			return parseFloat(money).toFixed(2);
		},
	}
}
