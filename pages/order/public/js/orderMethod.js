import AdaPay from 'common/js/adaPay.js'
export default {
	methods: {
		/**
		 * 订单支付
		 * @param {Object} out_trade_no
		 */
		async orderPay(orderData, callback) {
			await this.$util.subscribeMessage({
				source:'order',
				source_id:orderData.order_id,
				scene_type:'order_pay_before'
			})
			uni.showLoading({
				mask: true,
				title:'加载中'
			});
			if (orderData.adjust_money == 0) {
				this.$api.sendRequest({
					url: '/api/order/pay',
					data: {
						order_ids: orderData.order_id
					},
					success: res => {
						if (res.code >= 0) {

							this.createBuriedPoint('out_trade_no',res.data,3)

							this.orderPayPay(res.data,orderData, (data) => {
								typeof callback == 'function' && callback(data);
							})
						} else {
							if(res.code == -11) {
								uni.hideLoading()
								typeof callback == 'function' && callback(res);
								return false
							}
							this.$util.showToast({
								title: res.message
							});
							uni.hideLoading()
						}
					}
				})
			} else {
				uni.showModal({
					title: '提示',
					content: '商家已将支付金额调整为' + orderData.pay_money + '元，是否继续支付？',
					success: res => {
						if (res.confirm) {
							this.$api.sendRequest({
								url: '/api/order/pay',
								data: {
									order_ids: orderData.order_id
								},
								success: res => {
									if (res.code >= 0) {
										this.createBuriedPoint('out_trade_no',res.data,3)

										this.orderPayPay(res.data,orderData, (data) => {
											typeof callback == 'function' && callback(data);
										})
									} else {
										if(res.code == -11) {
											uni.hideLoading()
											typeof callback == 'function' && callback(res);
											return false
										}
										this.$util.showToast({
											title: res.message
										});
										uni.hideLoading()
									}
								}
							})
						}
					}
				})
			}
		},

		/**
		 * 关闭订单
		 * @param {Object} order_id
		 */
		orderClose(order_id, callback) {
			uni.showModal({
				title: '提示',
				content: '确定要取消该订单吗？',
				cancelText:"我再想想",
				confirmText:"确定",
				success: res => {
					if (res.confirm) {
						this.$api.sendRequest({
							url: '/api/order/close',
							data: {
								order_id
							},
							success: res => {
								if(res.code>=0){
									this.createBuriedPoint('order_id',order_id,250)

									this.$util.showToast({
										'title':'取消订单成功！',
										success: () => {
											// setTimeout(() => {
												typeof callback == 'function' && callback();
											// }, 3000);
										}

									})
								}else{
									if(res.code == -11) {
										this.$util.showToast({
											'title':res.message
										})
										setTimeout(() => {
											typeof callback == 'function' && callback();
										}, 1500)
										return false
									}
									this.$util.showToast({
										'title':res.message
									})
								}
							}
						})
					}
				}
			})
		},
		/**
		 * 订单收货
		 * @param {Object} order_id
		 */
		orderDelivery(order_id, callback) {
			uni.showModal({
				title: '提示',
				content: '你已经收到货物了吗？',
				confirmText:'已收到',
				cancelText:'未收到',
				success: res => {
					if (res.confirm) {
						this.$api.sendRequest({
							url: '/api/order/takedelivery',
							data: {
								order_id
							},
							success: res => {
								typeof callback == 'function' && callback();
							}
						})
					}
				},
			})
		},
		orderPayPay(out_trade_no,orderData, callback) {
			var order_ids = [orderData.order_id]
				this.$api.sendRequest({
					url: '/api/pay/pay',
					data: {
						out_trade_no: out_trade_no,
						pay_type: 'adapay'
					},
					success: res => {
						if (res.code >= 0) {

							uni.hideLoading();
							this.$util.wechatPay(res.data.pay_type,res.data.pay_type=='adapay' ? res.data.payment : res.data.pay_info, (res)=>{
								this.createBuriedPoint('out_trade_no',out_trade_no,11)

								// 跳转支付成功页面
								// #ifdef MP-WEIXIN
								this.$util.redirectTo('/pages/pay/result/result?order_ids='+order_ids, {
									code: out_trade_no
								}, '', 'redirectTo');
								// #endif
								// #ifdef H5
								this.$util.redirectTo('/pages/order/list/list?status=all', {}, 'redirectTo');
								// #endif
							},(err)=>{
								this.createBuriedPoint('out_trade_no',out_trade_no,9001)

								uni.hideLoading();
							},(err)=>{
								uni.hideLoading();
							},this)
						} else {
							console.log(res)
							if(res.message) {
								this.$util.showToast({
									title: res.message
								});
							} else {
								uni.hideLoading();
							}
						}
						typeof callback == 'function' && callback(res);
					},
					fail: res => {
						this.$util.showToast({
							title: 'request:fail'
						});
					}
				})
		},
		// 订单埋点
		createBuriedPoint(idName,out_trade_no,status){
			this.$buriedPoint.orderStatus({ [idName]:out_trade_no, status })
		},
	}
}
