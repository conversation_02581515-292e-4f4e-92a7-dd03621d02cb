.activist-container{
	width: 100vw;
	height: 100vh;
}

.order-item{
	margin: 20rpx 24rpx;
	padding: 0 24rpx;
	padding-bottom: 36rpx;
	border-radius: 20rpx;
	background: #fff;
	position: relative;


	.order-header{
		display: flex;
		align-items:center;
		justify-content: space-between;
		height: 98rpx;
		.icondianpu{
			display: inline-block;
			line-height: 1;
			margin-right: 12rpx;
			font-size: 30rpx;
		}
		.site-name-box {
			display: flex;
			align-items: center;

		}
		.site-name {
			width: 400rpx;
			display: -webkit-box;
			-webkit-box-orient: vertical;
			-webkit-line-clamp: 1;
			overflow: hidden;
			font-weight: bold;
			font-size: 28rpx;
		}
		.status-name{
			color: var(--custom-brand-color);
			font-size: 28rpx;
			text-align: right;
		}
	}

	.order-body {
		.goods-wrap {
			margin-bottom: 20rpx;
			display: flex;
			position: relative;

			&:last-of-type{
				margin-bottom: 0;
			}

			.goods-img {
				width: 180rpx;
				height: 180rpx;
				margin-right: 20rpx;

				image {
					width: 100%;
					height: 100%;
					border-radius: $ns-border-radius;
				}
			}

			.goods-info {
				flex: 1;
				position: relative;
				padding: 5rpx 0 0 0;
				max-width: calc(100% - 200rpx);

				.goods-name {
					display: -webkit-box;
					-webkit-box-orient: vertical;
					-webkit-line-clamp: 2;
					overflow: hidden;
					line-height: 1.5;
					font-size: 28rpx;
				}
				.spec_name {
					color: #999999;
					font-size: 24rpx;
				}
				.goods-sub-section {
					padding-top: 4rpx;
					width: 100%;
					line-height: 1.3;
					display: flex;

					.refund-price {
						font-size: 28rpx;
						.high-text-color {
							color: var(--custom-brand-color);
						}
					}

					.unit {
						font-weight: normal;
						margin-right: 2rpx;
					}
				}

				.goods-operation{
					text-align: right;
					padding-top: 20rpx;

					.operation-btn{
						line-height: 1;
						padding: 14rpx 20rpx;
						color: #333;
						display: inline-block;
						border-radius: 28rpx;
						background: #fff;
						border: 0.5px solid #999;
						font-size: 24rpx;
						margin-left: 10rpx;
					}
				}
			}
		}
	}

	.order-footer{
		.status{
			padding: 20rpx;
			background: #f5f5f5;
			margin-top: 20rpx;
			border-radius: $ns-border-radius;
			display: flex;

			view {
				line-height: 1.3;

				&:first-of-type{
					text-align: right;
					padding-right: 20rpx;
					width: 160rpx;
					font-weight: 700;
				}

				&:last-of-type{
					flex: 1;
				}
			}
		}

		.order-operation{
			text-align: right;
			padding-top: 20rpx;

			.operation-btn{
				line-height: 1;
				padding: 18rpx 26rpx;
				color: #333;
				display: inline-block;
				border-radius: 32rpx;
				background: #fff;
				border: 0.5px solid #999;
				font-size: 24rpx;
				margin-left: 10rpx;
			}
		}
	}
}

.empty {
	padding-top: 200rpx;
	text-align: center;

	.empty-image {
		width: 180rpx;
		height: 180rpx;
	}
}
