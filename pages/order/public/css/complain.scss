.align-right {
	text-align: right;
}

.goods-wrap {
	margin: $ns-margin;
	padding: $ns-padding;
	border-radius: $ns-border-radius;
	background: #fff;
	display: flex;
	position: relative;

	.goods-img {
		width: 170rpx;
		height: 170rpx;
		margin-right: 20rpx;

		image {
			width: 100%;
			height: 100%;
		}
	}

	.goods-info {
		flex: 1;
		position: relative;
		max-width: calc(100% - 200rpx);

		.goods-name {
			display: -webkit-box;
			-webkit-box-orient: vertical;
			-webkit-line-clamp: 2;
			overflow: hidden;
			line-height: 1.5;
			font-size: 28rpx;
		}
	}
}

.popup {
	width: 100vw;
	background: #fff;
	border-top-left-radius: 24rpx;
	border-top-right-radius: 24rpx;

	.popup-header {
		height: 90rpx;
		display: flex;
		align-items: center;
		padding: 0 30rpx;

		& > view {
			flex: 1;
			line-height: 1;
		}

		.tit {
			font-size: 32rpx;
			font-weight: 600;
		}

		.vice-tit {
			margin-right: 20rpx;
		}
	}

	.popup-body {
		height: calc(100% - 210rpx);
		height: calc(100% - 210rpx - constant(safe-area-inset-bottom));
		height: calc(100% - 210rpx - env(safe-area-inset-bottom));
	}

	.popup-footer {
		height: 120rpx;
		padding-bottom: 0 !important;  
		padding-bottom: constant(safe-area-inset-bottom) !important;  
		padding-bottom: env(safe-area-inset-bottom) !important;  

		.confirm-btn {
			height: 80rpx;
			line-height: 80rpx;
			color: #fff;
			text-align: center;
			margin: 20rpx;
			border-radius: 40rpx;
		}
	}
}

.refund-form {
	margin: $ns-margin;
	padding: $ns-padding;
	border-radius: $ns-border-radius;
	background: #fff;

	.item-wrap {
		display: flex;
		position: relative;
		line-height: 60rpx;

		.label {
			width: 120rpx;
			padding-right: 20rpx;
			line-height: 60rpx;
		}

		.cont {
			flex: 1;
			line-height: 60rpx;

			.refund-desc {
				font-size: 28rpx;
				width: 100%;
				padding: 14rpx 0;
				line-height: 1.3;
			}
		}

		.iconright {
			position: absolute;
			top: 50%;
			transform: translateY(-50%);
			color: #ddd;
			right: 0;
		}
	}
}

.sub-btn {
	position: fixed;
	width: 100%;
	height: 100rpx;
	display: flex;
	justify-content: center;
	align-items: center;
	background: #ffffff;
	bottom: 0;
	button {
		width: 100%;
	}
	&.safe-area {
		padding-bottom: 0 !important;
		padding-bottom: constant(safe-area-inset-bottom) !important;
		padding-bottom: env(safe-area-inset-bottom) !important;
	}
}

.refund-reason-popup {
	height: 65vh;

	.popup-body {
		.item {
			display: flex;
			padding: 0 30rpx;
			position: relative;
			height: 70rpx;
			line-height: 70rpx;

			.reason {
				flex: 1;
				height: 70rpx;
				line-height: 70rpx;
			}

			& > .iconfont {
				font-size: 40rpx;
				position: absolute;
				top: 50%;
				right: 30rpx;
				transform: translateY(-50%);
			}
			& > .iconyuan_checkbox {
				color: $ns-text-color-gray;
			}
		}
	}
}

.status-wrap {
	padding: 20rpx;
	background: #fff;
	border-top: 1px solid #f5f5f5;
	margin: 20rpx;
	border-radius: $ns-border-radius;

	.status-name {
		display: block;
		font-size: 32rpx;
		line-height: 70rpx;
		height: 70rpx;
	}

	.refund-explain {
		border-top: 1px dashed #eee;
		padding-top: 20rpx;
	}
}

.history-wrap {
	margin: 20rpx;
	background: #fff;
	padding: 20rpx;
	display: flex;
	position: relative;
	border-radius: $ns-border-radius;

	view {
		flex: 1;
	}

	.iconright {
		position: absolute;
		top: 50%;
		transform: translateY(-50%);
		color: #ddd;
		right: 20rpx;
	}
}

.refund-info {
	margin: 20rpx;
	background: #fff;
	border-radius: $ns-border-radius;

	.header {
		height: 90rpx;
		line-height: 90rpx;
		padding: 0 20rpx;
	}

	.body {
		padding-bottom: 20rpx;

		.goods-wrap {
			display: flex;
			position: relative;
			padding: 20rpx;
			background: #f5f5f5;

			&:last-of-type {
				margin-bottom: 0;
			}

			.goods-img {
				width: 180rpx;
				height: 180rpx;
				margin-right: 20rpx;

				image {
					width: 100%;
					height: 100%;
				}
			}

			.goods-info {
				flex: 1;
				position: relative;
				max-width: calc(100% - 200rpx);

				.goods-name {
					display: -webkit-box;
					-webkit-box-orient: vertical;
					-webkit-line-clamp: 2;
					overflow: hidden;
					line-height: 1.5;
					font-size: 28rpx;
				}

				.goods-sub-section {
					padding-top: 20rpx;
					width: 100%;
					line-height: 1.3;
					display: flex;

					.refund-price {
						font-size: 28rpx;
					}

					.unit {
						font-weight: normal;
						font-size: 24rpx;
						margin-right: 2rpx;
					}
				}
			}
		}

		.info {
			margin-top: 20rpx;

			.cell {
				height: 50rpx;
				line-height: 50rpx;
				padding: 0 20rpx;
				font-size: 24rpx;
				color: $ns-text-color-gray;
			}
		}
	}
}

.footer-operation {
	position: fixed;
	z-index: 5;
	left: 0;
	bottom: 0;
	width: 100vw;
	height: 100rpx;
	background: #fff;
	box-shadow: 0 0px 10px rgba(0, 0, 0, 0.1);
	text-align: right;

	&.bottom-safe-area {
		 padding-bottom: 0 !important;  
		  padding-bottom: constant(safe-area-inset-bottom) !important;  
		  padding-bottom: env(safe-area-inset-bottom) !important;  
	}

	.operation-btn {
		height: 70rpx;
		line-height: 70rpx;
		color: #fff;
		padding: 0 40rpx;
		display: inline-block;
		text-align: center;
		margin: 16rpx 20rpx 16rpx 0;
		border-radius: 40rpx;

		&.white {
			height: 68rpx;
			line-height: 68rpx;
			color: #333;
			border: 0.5px solid #999;
			background: #fff !important;
		}
	}
}

.record-wrap {
	padding: 0 20rpx;

	.record-item {
		display: flex;

		.cont {
			flex: 1;
			margin-top: 40rpx;
			box-shadow: 0 1.5px 3px 0 rgba(0, 0, 0, 0.06), 0 1.5px 3px 0 rgba(0, 0, 0, 0.08);
			border-radius: 20rpx;
			padding: 20rpx;

			.head {
				line-height: 1;

				.time {
					margin-left: 40rpx;
					color: #999;
					font-size: 24rpx;
					float: right;
				}
			}

			.body {
				padding-top: 20rpx;
				color: #999;
			}
		}

		&.buyer {
			.cont {
				margin-left: 10%;
				background: #fff;
			}
		}

		&.seller {
			.cont {
				margin-right: 10%;
				background: #ffe48c;
			}
		}

		&.platform {
			.cont {
				margin-right: 10%;
				background: #bfd7ff;
			}
		}
	}

	.empty-box {
		height: 168rpx;
	}
}

.history-bottom {
	position: fixed;
	z-index: 5;
	left: 0;
	bottom: 0;
	width: 100vw;
	height: 100rpx;
	background: #fff;
	box-shadow: 0 0px 10px rgba(0, 0, 0, 0.1);
	text-align: right;
	display: flex;

	&.bottom-safe-area {
		padding-bottom: 0 !important;  
		padding-bottom: constant(safe-area-inset-bottom) !important;  
		padding-bottom: env(safe-area-inset-bottom) !important;  
	}

	view {
		flex: 1;
		text-align: center;
		line-height: 100rpx;

		&:first-child {
			border-right: 1px solid #eee;
		}
		.iconfont {
			font-weight: bold;
			margin-right: 10rpx;
			font-size: 28rpx;
			line-height: 1;
		}
	}
	button {
		width: 50%;
		height: 100%;
		position: absolute;
		border: none;
		z-index: 1;
		padding: 0;
		margin: 0;
		background: none;

		&::after {
			border: none !important;
		}
	}
}

.newText {
	width: 100%;
	min-height: 130px;
	border-radius: $ns-border-radius;
	border: 1rpx solid $ns-border-color-gray;
	padding: 10rpx;
	box-sizing: border-box;
	margin-top: 10rpx;
}
