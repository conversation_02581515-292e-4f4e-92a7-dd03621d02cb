.order-container {
	width: 100vw;
	height: 100vh;
}

.order-nav {
	width: calc(100vw - 120rpx);
	height: 100rpx;
	flex-direction: row;
	white-space: nowrap;
	background: #fff;
	//padding: 15rpx 0;
	position: fixed;
	left: 0;
	z-index: 998;
	overflow-x: auto;
	border-bottom: 2rpx solid rgba(245, 245, 245, 1);
	box-sizing: border-box;
	.uni-tab-item {
		display: inline-flex;
		align-items: center;
		flex-wrap: nowrap;
		padding-left: 17rpx;
		padding-right: 17rpx;
		height: 100%;

	}

	.uni-tab-item-title {
		color: #555;
		font-size: 30rpx;
		display: block;
		height: 64rpx;
		line-height: 64rpx;
		// border-bottom: 2px solid #fff;
		padding: 0 10rpx;
		flex-wrap: nowrap;
		white-space: nowrap;
		.line {
			width: 36rpx;
			height: 6rpx;
			background: transparent;
			border-radius: 3rpx;
			margin: 0 auto;
		}
	}

	.uni-tab-item-title-active {
		display: block;
		height: 64rpx;
		// border-bottom: 2px solid #ffffff;
		padding: 0 10rpx;
		position: relative;
		.line {
			width: 24rpx;
			height: 24rpx;
			overflow: hidden;
			background: transparent;
			border-bottom-left-radius: 28rpx;
			border-bottom-right-radius: 0rpx;
			border-left: 6rpx solid var(--custom-brand-color);
			border-bottom: 6rpx solid var(--custom-brand-color);
			position: absolute;
			left: 50%;
			bottom: -10rpx;
			transform: translateX(-50%) rotate(-45deg);
		}
	}

	::-webkit-scrollbar {
		width: 0;
		height: 0;
		color: transparent;
	}
}
.uni-tab-filter{
	position: fixed;
	top: 0;
	right: 0;
	display: flex;
	align-items: center;
	box-sizing: border-box;
	background-color: transparent;
	z-index: 9999;
	color: #555;
	font-size: 30rpx;
	// border-bottom: 2px solid #fff;
	flex-wrap: nowrap;
	white-space: nowrap;
	.line {
		width: 36rpx;
		height: 6rpx;
		background: transparent;
		border-radius: 3rpx;
		margin: 0 auto;
	}

	&-fill{
		display: inline-block;
		height: 98rpx;
		width: 30rpx;
		background: linear-gradient(90deg, rgba(204, 204, 204, 0) 0%, rgba(255, 255, 255, 1) 60%);
	}
	&-info{
		background-color: white;
		box-sizing: border-box;
		display: inline-flex!important;
		align-items: center;
		height: 98rpx;
		padding-left: 0!important;
		padding-bottom: 4rpx;
		padding-right: 24rpx;
		padding-top: 6rpx;
		&-title{
			display: flex!important;
			align-items: center;
			padding: 0!important;
			font-size: 30rpx;
			&-icon{
				width: 32rpx;
				height: 32rpx;
				margin-bottom: 2rpx;
			}
			&-filter{
				color: var(--custom-brand-color)!important;
			}
		}
	}
}

.order-item {
	margin: 20rpx 20rpx;
	padding: 20rpx 20rpx;
	border-radius: 20rpx;
	background: #fff;
	position: relative;

	.order-header {
		display: flex;
		align-items: center;
		position: relative;
		justify-content: space-between;
		&.waitpay {
			.iconyuan_checked,
			.iconyuan_checkbox {
				font-size: 36rpx;
				position: absolute;
				top: 50%;
				left: 0;
				transform: translateY(-50%);
			}
			.iconyuan_checkbox {
				color: $ns-text-color-gray;
			}
		}

		.icondianpu {
			display: inline-block;
			line-height: 1;
			margin-right: 12rpx;
			font-size: 30rpx;
			color: #333333;
		}

		.status-name {
			text-align: right;
		}
	}

	.order-body {
		margin-top: 20rpx;
		.goods-wrap {
			margin-bottom: 20rpx;
			display: flex;
			position: relative;

			&:last-of-type {
				margin-bottom: 0;
			}

			.goods-img {
				width: 200rpx;
				height: 200rpx;
				margin-right: 20rpx;

				image {
					width: 100%;
					height: 100%;
					border-radius: 20rpx;
				}
			}

			.goods-info {
				flex: 1;
				position: relative;
				padding: 28rpx 0 0 0;
				max-width: calc(100% - 200rpx);
				&-bottom{
					display: flex;
					align-items: center;
					justify-content: space-between;
					margin-top: 10rpx;
				}
				.goods-name {
					display: -webkit-box;
					-webkit-box-orient: vertical;
					-webkit-line-clamp: 2;
					overflow: hidden;
					font-size: 28rpx;
					font-weight: 400;
					line-height: 40rpx;
					color: rgba(56, 56, 56, 1);
					min-height: 85rpx;
				}

				.goods-sub-section {
					font-size: 28rpx;
					font-weight: 400;
					line-height: 40rpx;
					color: rgba(56, 56, 56, 1);
					display: flex;
					justify-content: space-between;
					view{
						&:last-child{
							margin-left: 10rpx;
						}
					}
				}
				.goods-price {
					color: rgba(56, 56, 56, 1);
					font-size: 32rpx;
					text-align: right;
				}
				.unit {
					font-weight: normal;
					font-size: 26rpx;
					margin-right: 2rpx;
				}
			}
		}
	}

	.order-footer {
		.order-base-info {
			display: flex;

			.total {
				text-align: right;
				padding-top: 20rpx;
				flex: 1;

				& > text {
					line-height: 1;
					margin-left: 10rpx;
				}
				text {
					font-size: 26rpx;
					color: #343434;
					text:last-of-type {
						margin-left: 0;
					}
					&.strong {
						font-weight: bold;
						font-size: 32rpx;
					}
				}

			}
		}

		.order-operation {
			display: flex;
			justify-content: space-between;
			align-items: center;
			text-align: right;
			padding-top: 20rpx;
			position: relative;
			&-left{
				display: flex;
				align-items: center;
				font-size: 26rpx;
				font-weight: 400;
				height: 38rpx;
				line-height: 38rpx;
			}
			&-right{
				display: flex;
				align-items: center;
			}
			.operation-btn {
				line-height: 1;
				padding: 20rpx 26rpx;
				color: #333;
				display: inline-block;
				border-radius: 32rpx;
				background: #fff;
				border: 0.5px solid #999;
				font-size: 24rpx;
				margin-left: 10rpx;
			}
		}
	}
}

.empty {
	padding-top: 200rpx;
	text-align: center;

	.empty-image {
		width: 180rpx;
		height: 180rpx;
	}
}

.order-batch-operation {
	position: fixed;
	z-index: 5;
	left: 0;
	bottom: 0;
	width: 100vw;
	height: 100rpx;
	background: #fff;
	box-shadow: 0 0px 10px rgba(0, 0, 0, 0.1);
	text-align: right;

	&.bottom-safe-area {
		padding-bottom: 0;
		padding-bottom: constant(safe-area-inset-bottom);
		padding-bottom: env(safe-area-inset-bottom);
	}

	.operation-btn {
		height: 68rpx;
		line-height: 68rpx;
		background: #fff;
		padding: 0 40rpx;
		display: inline-block;
		text-align: center;
		margin: 16rpx 20rpx 16rpx 0;
		border-radius: 40rpx;
		border: 0.5px solid #ffffff;

		&.white {
			height: 68rpx;
			line-height: 68rpx;
			color: #333;
			border: 0.5px solid #999;
			background: #fff;
		}
	}
}
.high-text-color {
	color: var(--custom-brand-color)!important;
}
.order-box-btn {
	min-width: 160rpx;
	padding: 0 16rpx;
	box-sizing: border-box;
	display: flex;
	height: 64rpx;
	line-height: 64rpx;
	align-items: center;
	justify-content: center;
	border-color: #CCCCCC;
	color: #666666;
	font-size: 26rpx;
	&.order-pay {
		background: var(--custom-brand-color)!important;
		border-color: var(--custom-brand-color);
		color: #fff;
	}
}
.site-name-box {
	display: flex;
	align-items: center;
	.site-name {
		//width: 400rpx;
		font-size: 26rpx;
		font-weight: 400;
		color: rgba(56, 56, 56, 1);
		display: flex;
		align-items: center;
		&-copy{
			width: 32rpx;
			height: 32rpx;
			margin-left: 16rpx;
		}
	}
}

.order-filter-pop{
	/deep/ .uni-popup__wrapper-box{
		border-radius: 0!important;
	}
	&-form{
		padding: 0 40rpx;
		padding-bottom: 52rpx;
		box-sizing: border-box;
		&-row{
			border-bottom: 2rpx solid rgba(245, 245, 245, 1);
			display: flex;
			align-items: center;
			height: 108rpx;
			&-label{
				font-size: 30rpx;
				font-weight: 400;
				line-height: 34.92rpx;
				color: rgba(56, 56, 56, 1);
				margin-right: 38rpx;
				min-width: 120rpx;
			}
			&-date{
				display: flex;
				align-items: center;
				&-select{
					line-height: 34.92rpx;
				}
				&-one{
					width: 200rpx;
					display: inline-block;
					font-size: 30rpx;
					font-weight: 400;
					line-height: 34.92rpx;
					&-placeholder{
						color: rgba(229, 229, 229, 1);
					}
				}
				&-separator{
					font-size: 32rpx;
					font-weight: 400;
					line-height: 37.26rpx;
					color: rgba(56, 56, 56, 1);
					padding: 0 32rpx;
					box-sizing: border-box;
				}
			}
			&-value{
				&-placeholder{
					font-size: 30rpx;
					font-weight: 400;
					line-height: 34.92rpx;
					color: rgba(229, 229, 229, 1);
				}
			}
		}
		&-op{
			display: flex;
			justify-content: center;
			align-items: center;
			margin-top: 42rpx;
			&-reset{
				width: 220rpx;
				height: 68rpx;
				border-radius: 100rpx;
				background: rgba(255, 255, 255, 1);
				border: 2rpx solid rgba(229, 229, 229, 1);
				font-size: 30rpx;
				font-weight: 400;
				line-height: 34.92rpx;
				color: rgba(56, 56, 56, 1);
				margin-right: 50rpx;
				display: flex;
				justify-content: center;
				align-items: center;
			}
			&-search{
				width: 220rpx;
				height: 68rpx;
				border-radius: 100rpx;
				background: var(--custom-brand-color);
				font-size: 30rpx;
				font-weight: 400;
				line-height: 34.92rpx;
				color: rgba(255, 255, 255, 1);
				display: flex;
				justify-content: center;
				align-items: center;
			}
		}
	}
}
