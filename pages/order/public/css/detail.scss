@mixin flex-row-center {
	display: flex;
	justify-content: center;
	align-items: center;
}

@mixin wrap {
	margin: $ns-margin;
	padding: $ns-padding;
	border-radius: $ns-border-radius;
	background: #fff;
	position: relative;
}

.align-right {
	text-align: right;
}

.detail-container {
	min-height: 100vh;
	box-sizing: border-box;
	padding-bottom: calc(60rpx + env(safe-area-inset-bottom));

	.height-box {
		display: block;
		padding-bottom: 100rpx;
		padding-bottom: calc(100rpx + constant(safe-area-inset-bottom));
		padding-bottom: calc(100rpx + env(safe-area-inset-bottom));
	}
}

.status-wrap {
	height: 220rpx;
	box-sizing: border-box;
	background-color: var(--custom-brand-color);
	display: flex;
	flex-direction: column;
	justify-content: flex-end;
	padding-bottom: 136rpx;
	view {
		text-align: center;
		color: #fff;
	}
	.status-box {
		display: flex;
		align-items: center;
		justify-content: space-between;
		padding: 0 20rpx;
		box-sizing: border-box;
	}
	.status-name-box {
		display: flex;
		align-items: center;
		justify-content: center;
	}
	.status-name {
		font-size: 48rpx;
		font-weight: 700;
		line-height: 52rpx;
		color: rgba(255, 255, 255, 1);
	}

	.desc {
		margin-left: 20rpx;
		font-size: 28rpx;
	}
	.strong {
		font-weight: bold;
		font-size: 36rpx;
	}

	.operation-group {
		text-align: center;
		padding-top: 20rpx;

		.operation-btn {
			line-height: 1;
			padding: 16rpx 50rpx;
			display: inline-block;
			border-radius: 32rpx;
			background: #fff;
			box-shadow: 0 0 14rpx rgba(158, 158, 158, 0.6);
		}
	}
}
.address-wrap.active {
	display: flex;
	align-items: center;
}
.address-wrap {
	margin: 0 20rpx;
	margin-bottom: 20rpx;
	margin-top: -84rpx;
	min-height: 168rpx;
	border-radius: 20rpx;
	background-color: #FFFFFF;
	padding: 0 30rpx;
	.icon {
		display: flex;
		align-items: center;
		justify-content: center;
		image {
			width: 48rpx;
			height: 48rpx;
		}
	}

	.address-info {
		flex-grow: 1;
		// padding-left: 40rpx;
		// box-sizing: border-box;

		.info {
			display: flex;
			justify-content: space-between;
			align-items: center;
			font-size: 32rpx;
			font-weight: 400;
			margin-bottom: 20rpx;
			.info-left{
				display: flex;
				justify-content: center;
				align-items: center;
			}
			.info-name {
				max-width: 380rpx;
				margin-right: 32rpx;
				overflow: hidden;
				display: -webkit-box;
				-webkit-line-clamp: 1;
				-webkit-box-orient: vertical;
				word-break: break-all;
				font-weight: bold;
				font-size: 32rpx;
			}
			.info-mobile{
				font-size: 32rpx;
			}
		}

		.detail {
			font-size: 26rpx;
			font-weight: 400;
			line-height: 32rpx;
			color: rgba(128, 128, 128, 1);
			width: 508rpx;
		}
	}

	.cell-more {
		position: absolute;
		top: 50%;
		transform: translateY(-50%);
		right: 10rpx;

		.iconfont {
			color: #999;
		}
	}
}

.pickup-info {
	@include wrap;
	padding: 20rpx 30rpx;

	.pickup-point-info {
		.name {
			display: flex;
			height: 50rpx;
			align-items: flex-end;
			margin-bottom: 10px;

			text {
				line-height: 1;
				&.mark {
					font-size: 20rpx;
					padding: 1px 10rpx;
					border: 0.5px solid #ffffff;
					border-radius: 4rpx;
					margin-left: 10rpx;
				}
			}
		}

		.address,
		.time,
		.contact {
			font-size: 26rpx;
			line-height: 1;
			margin-top: 16rpx;

			.iconfont {
				color: #999;
				font-size: 26rpx;
				line-height: 1;
				margin-right: 10rpx;
			}
		}
	}
	.hr {
		border-top: 1px dashed #e5e5e5;
		margin: 20rpx 0;
	}

	.pickup-code-info {
		.info {
			text-align: center;
		}

		.code {
			margin: 0 auto;
			width: 160rpx;
			height: 160rpx;

			image {
				width: 100%;
				height: 100%;
			}
		}
	}
}

.verify-code-wrap {
	@include wrap;

	.wrap {
		line-height: 40rpx;

		.copy {
			font-size: 20rpx;
			display: inline-block;
			color: #666;
			background: #f7f7f7;
			line-height: 1;
			padding: 6rpx 10rpx;
			margin-left: 10rpx;
			border-radius: 18rpx;
		}
	}

	.hr {
		border-top: 1px dashed #e5e5e5;
		margin: 20rpx 0;
	}

	.code {
		margin: 0 auto;
		width: 200rpx;
		height: 200rpx;

		image {
			width: 100%;
			height: 100%;
		}
	}
}

.site-wrap {
	margin: 0 20rpx;
	padding: 0 20rpx;
	border-radius: 20rpx 20rpx 0 0;
	background: #fff;
	position: relative;

	.site-header {
		display: flex;
		align-items: center;
		height: 78rpx;
		.icondianpu {
			display: inline-block;
			line-height: 1;
			margin-right: 12rpx;
			font-size: 30rpx;
		}
	}

	.site-body {
		.goods-wrap {
			margin-bottom: 20rpx;
			display: flex;
			position: relative;

			&:last-of-type {
				margin-bottom: 0;
			}

			.goods-img {
				width: 160rpx;
				height: 160rpx;
				margin-right: 20rpx;

				image {
					width: 100%;
					height: 100%;
					border-radius: 20rpx;
				}
			}

			.goods-info {
				flex: 1;
				position: relative;
				max-width: calc(100% - 200rpx);

				.goods-name {
					padding-top: 5rpx;
					display: -webkit-box;
					-webkit-box-orient: vertical;
					-webkit-line-clamp: 2;
					overflow: hidden;
					font-size: 28rpx;
					font-weight: 400;
					line-height: 40rpx;
					color: rgba(56, 56, 56, 1);
					min-height: 84rpx;
				}
				&-row{
					display: flex;
					justify-content: space-between;
					align-items: center;
					margin-top: 30rpx;
				}
				.goods-sub-section {
					display: flex;
					align-items: center;
					view {
						font-size: 28rpx;
						font-weight: 400;
						line-height: 40rpx;
						color: rgba(56, 56, 56, 1);
						&:last-child{
							margin-left: 10rpx;
						}
					}

				}
				.goods-price {
					font-size: 32rpx;
					font-weight: 400;
					line-height: 37.5rpx;
					color: rgba(56, 56, 56, 1);
					.unit {
						font-size: 24rpx;
						font-weight: 400;
						line-height: 44rpx;
						color: rgba(56, 56, 56, 1);
					}

				}
			}
		}

		.goods-operation {
			text-align: right;
			padding: 20rpx 0;

			.disinline {
				display: inline-block;
			}

			.operation-btn {
				line-height: 1;
				padding: 14rpx 20rpx;
				color: #333;
				display: inline-block;
				border-radius: 28rpx;
				background: #fff;
				border: 0.5px solid #999;
				font-size: 24rpx;
				margin-left: 10rpx;
			}
		}
	}
}

.order-cell {
	display: flex;
	margin: 35rpx 0;
	// align-items: center;
	background: #fff;
	line-height: 40rpx;

	.tit {
		text-align: left;
	}

	.box {
		flex: 1;
		padding: 0 20rpx;
		line-height: inherit;

		.textarea {
			height: 40rpx;
		}
	}

	.iconfont {
		color: #bbb;
		font-size: 28rpx;
	}

	.order-pay {
		padding: 0;

		text {
			display: inline-block;
			margin-left: 6rpx;
		}
	}
}

.order-summary {
	margin: 20rpx 20rpx;
	padding: 1rpx 20rpx;
	margin-bottom: 54rpx;
	border-radius: 20rpx;
	background-color: #FFFFFF;
	.order-cell {
		font-size: 26rpx;
		.tit {
			width: 170rpx;
		}

		.box {
			display: flex;
			align-items: center;
		}

		.copy {
			font-size: 26rpx;
			display: inline-block;
			color: var(--custom-brand-color);
			line-height: 1;
			margin-left: 20rpx;
		}
	}

	.hr {
		width: calc(100% - 190rpx);
		margin-left: 190rpx;
		height: 1px;
		background: #f7f7f7;
	}
}

.order-money {
	margin: 0 20rpx;
	padding: 1rpx 20rpx;
	background-color: #FFFFFF;
	.tit{
		font-size: 30rpx;
		font-weight: 400;
		line-height: 64rpx;
		color: rgba(128, 128, 128, 1);
	}
	.order-cell {
		font-size: 26rpx;
		.order-cell-right {
			font-size: 30rpx;
			font-weight: 700;
			line-height: 64rpx;
			color: rgba(56, 56, 56, 1);
			.pay-money {
				color: var(--custom-brand-color);
			}
			&-tip{
				font-size: 30rpx;
				font-weight: 400;
				line-height: 64rpx;
				color: rgba(56, 56, 56, 1);
			}
		}
		.box {
			padding: 0;

			.operator {
				font-size: 24rpx;
				margin-right: 6rpx;
			}
		}
	}
}

.kefu {
	@include wrap;

	& > view {
		@include flex-row-center;

		.iconfont {
			font-weight: bold;
			margin-right: 10rpx;
			font-size: 28rpx;
			line-height: 1;
		}
	}

	button {
		width: 100%;
		height: 100%;
		position: absolute;
		border: none;
		z-index: 1;
		padding: 0;
		margin: 0;
		background: none;

		&::after {
			border: none !important;
		}
	}
}

.order-refund{
	display: flex;
	flex-direction: row-reverse;
}

.order-operation {
	position: fixed;
	left: 0;
	bottom: 0;
	width: 100vw;
	height: 126rpx;
	display: flex;
	justify-content: flex-end;
	align-items: center;
	background: #fff;

	.order-box-btn {
		margin-right: $ns-margin;
		margin-left: 0;
	}
}
.desc-box,.desc-box .desc {
	display: flex;
	align-items: center;
	justify-content: center;
	font-size: 32rpx;
	font-weight: 400;
	line-height: 37.5rpx;
	color: rgba(255, 255, 255, 1);
}
.icon-pay-clock {
	font-size: 38rpx;
	line-height: 44rpx;
	margin-right: 15rpx;
	margin-top: -50rpx;
}
.site-name {
	font-size: 28rpx;
	font-weight: bold;
	border-bottom: 2rpx solid rgba(245, 245, 245, 1);
	width: 100%;
}

.order-box-btn {
	min-width: 160rpx;
	padding: 0 16rpx;
	box-sizing: border-box;
	display: flex;
	height: 64rpx;
	line-height: 64rpx!important;
	text-align: center;
	align-items: center;
	justify-content: center;
	border-color: #CCCCCC!important;
	color: #666666!important;
	font-size: 26rpx;
	&.order-pay {
		background: var(--custom-brand-color)!important;
		border-color: var(--custom-brand-color)!important;
		color: #fff!important;
	}
}
.order-box-btn-wrap {
	display: inline-block;
}
