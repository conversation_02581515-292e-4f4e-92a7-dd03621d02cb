<template>
	<view class="order-container" :class="themeStyle">
		<view class="custom" :style="{paddingTop:statusBarHeight+'px',height:navHeight+'px'}">
			<view class="iconfont iconback_light" @click="navigateBack"></view>
			<view class="custom-navbar">
				<view class="navbar-item" @click="navigateBack">订单列表</view>
				<view class="navbar-item active" >周期购</view>
			</view>
		</view>
		<mescroll-uni ref="mescroll" @getData="getListData" :top="(navHeight+statusBarHeight)*2 + 'rpx'">
			<block slot="list">
				<view class="order-list" v-if="orderList.length > 0">
					<view class="order-item" v-for="(orderItem, orderIndex) in orderList" :key="orderIndex">
						<view class="order-header">
							<view class="site-name-box">
<!--								<view class="iconfont icondianpu"></view>-->
<!--								<view class="site-name">-->
<!--									{{ orderItem.supp_name ? orderItem.supp_name : '暂无供应商名称' }}-->
<!--								</view>-->
								<text class="cycle-title">周期购总{{orderItem.period_count}}期</text>

							</view>
							<text class="status-name ns-text-color">{{ orderItem.order_status_name }}</text>
						</view>
						<view class="order-body" @click="orderCycleList(orderItem)">
							<view class="goods-wrap">
								<view class="goods-img">
									<image :src="$util.img(orderItem.order_goods.sku_image)" @error="imageError(orderIndex)" mode="aspectFill"
									 :lazy-load="true"></image>
								</view>
								<view class="goods-info">
									<view class="goods-name">{{ orderItem.order_goods.goods_name }}</view>
									<view class="goods-sub-section">
										<view>{{ orderItem.order_goods.spec_name }}</view>
										<!-- <view>x{{ goodsItem.num }}</view> -->
									</view>
									<view class="goods-price">
										<text class="unit">￥</text>
										<text>{{ orderItem.buy_price }}</text>
									</view>
								</view>
							</view>
						</view>
						<view class="order-footer">
							<view class="order-base-info">
								<view class="total">
									<!-- <text>共{{ orderItem.goods_num }}件商品</text> -->
									<text>
										总计：
										<text class="ns-font-size-sm">￥</text>
										<text class="strong">{{ orderItem.buy_price }}</text>
									</text>
								</view>
							</view>
						</view>
					</view>
				</view>
				<view v-else>
					<ns-empty :isIndex="!1" :text="text"></ns-empty>
				</view>
			</block>
		</mescroll-uni>
		<loading-cover ref="loadingCover"></loading-cover>
	</view>
</template>

<script>

	export default {
		components: {},
		data() {
			return {
				statusList: [],
				orderList: [],
				text:'您还暂无相关周期购订单',
				statusBarHeight:0,
				navHeight:0
			};
		},
		onLoad() {
			uni.getSystemInfo({
			  success: res => {
			    //导航高度
			    this.navHeight = res.statusBarHeight + 46 - wx.getSystemInfoSync()['statusBarHeight'];
			  },
			  fail(err) {
			    console.log(err);
			  }
			})
		},
		onShow() {
			this.statusBarHeight = wx.getSystemInfoSync()['statusBarHeight']
			if (this.$refs.mescroll) this.$refs.mescroll.refresh();
		},
		methods: {
			getListData(mescroll) {
				this.$api.sendRequest({
					url: this.$apiUrl.orderCycleManage,
					data: {
						page: mescroll.num,
						page_size: mescroll.size,
					},
					success: res => {
						let newArr = []
						let msg = res.message;
						if (res.code == 0 && res.data) {
							newArr = res.data.list;
						} else {
							this.$util.showToast({
								title: msg
							})
						}
						mescroll.endSuccess(newArr.length);
						//设置列表数据
						if (mescroll.num == 1) this.orderList = []; //如果是第一页需手动制空列表
						this.orderList = this.orderList.concat(newArr); //追加新数据
						if (this.$refs.loadingCover) this.$refs.loadingCover.hide();

					},
					fail: res => {
						mescroll.endErr();
						if (this.$refs.loadingCover) this.$refs.loadingCover.hide();
					}
				});
			},
			orderCycleList(data) {
				// 周期购订单列表
					this.$util.redirectTo('/pages/order/list_cycle/list_cycle', {id: data.id});
			},
			imageError(orderIndex) {
				this.orderList[orderIndex].order_goods.sku_image = this.$util.getDefaultImage().default_goods_img;
				this.$forceUpdate();
			},
			navigateBack() {
				this.$util.redirectTo('/pages/order/list/list');
			}
		},
		computed: {
			//vueX页面主题
			themeStyle() {
				return 'theme-' + this.$store.state.themeStyle
			}
		},

	};
</script>

<style lang="scss">
	.order-container {
		width: 100vw;
		height: 100vh;
	}

	/* 标题栏 */
	.custom{
		background: #fff;
		display: flex;
		align-items: center;
		justify-content: center;
		position: fixed;
		top: 0;
		left: 0;
		width: 100%;
		.iconfont{
			font-size:$ns-font-size-base + 12;
			color:#333;
			font-weight: bold;
			position: absolute;
			left: 20rpx;
		}
		.custom-navbar{
			display: flex;
			border-radius: 30rpx;
			background: #FFF4F4;
			width: 360rpx;
			align-items: center;
			.navbar-item{
				height: 60rpx;
				line-height: 60rpx;
				width: 50%;
				text-align: center;
				color: $base-color;
				font-size: $ns-font-size-base + 2;
				&.active{
					background:$base-color;
					color: #FFFFFF;
					border-radius: 30rpx;
				}
			}
		}
	}

	.order-item {
		margin: 20rpx 24rpx;
		padding: 28rpx 24rpx;
		border-radius: 20rpx;
		background: #fff;
		position: relative;

		.order-header {
			display: flex;
			align-items: center;
			position: relative;
			justify-content: space-between;


			.icondianpu {
				display: inline-block;
				line-height: 1;
				margin-right: 12rpx;
				font-size: 30rpx;
				color: #333333;
			}

			.status-name {
				text-align: right;
			}
		}

		.order-body {
			.goods-wrap {
				margin-bottom: 20rpx;
				display: flex;
				position: relative;

				&:last-of-type {
					margin-bottom: 0;
				}

				.goods-img {
					width: 180rpx;
					height: 180rpx;
					padding: 28rpx 0 0 0;
					margin-right: 20rpx;

					image {
						width: 100%;
						height: 100%;
						border-radius: 20rpx;
					}
				}

				.goods-info {
					flex: 1;
					position: relative;
					padding: 28rpx 0 0 0;
					max-width: calc(100% - 200rpx);

					.goods-name {
						display: -webkit-box;
						-webkit-box-orient: vertical;
						-webkit-line-clamp: 2;
						overflow: hidden;
						line-height: 1.5;
						font-size: 28rpx;
					}

					.goods-sub-section {
						width: 100%;
						line-height: 1.3;
						display: flex;
						justify-content: space-between;
						view {
							color: #9A9A9A;
							font-size: 24rpx;
						}
					}
					.goods-price {
						color: #343434;
						font-size: 28rpx;
						text-align: right;
					}
					.unit {
						font-weight: normal;
						font-size: 24rpx;
						margin-right: 2rpx;
					}
				}
			}
		}

		.order-footer {
			.order-base-info {
				display: flex;

				.total {
					text-align: right;
					padding-top: 20rpx;
					flex: 1;

					& > text {
						line-height: 1;
						margin-left: 10rpx;
					}
					text {
						font-size: 26rpx;
						color: #343434;
						text:last-of-type {
							margin-left: 0;
						}
						&.strong {
							font-weight: bold;
							font-size: 32rpx;
						}
					}

				}
			}

			.order-operation {
				display: flex;
				justify-content: flex-end;
				text-align: right;
				padding-top: 20rpx;

				.operation-btn {
					line-height: 1;
					padding: 20rpx 26rpx;
					color: #333;
					display: inline-block;
					border-radius: 32rpx;
					background: #fff;
					border: 0.5px solid #999;
					font-size: 24rpx;
					margin-left: 10rpx;
				}
			}
		}
	}

	.empty {
		padding-top: 200rpx;
		text-align: center;

		.empty-image {
			width: 180rpx;
			height: 180rpx;
		}
	}

	.site-name-box {
		display: flex;
		align-items: center;
		.site-name {
			max-width: 250rpx;
			display: -webkit-box;
			-webkit-box-orient: vertical;
			-webkit-line-clamp: 1;
			overflow: hidden;
			font-weight: bold;
		}
		.cycle-title{
			font-size:$ns-font-size-sm;
			padding:6rpx 12rpx;
			line-height:24rpx;
			font-weight: normal;
			color:#fff;
			background-color:$base-color;
			border-radius:8rpx;
			margin-left:10rpx;
		}
	}
</style>
<style scoped>
	/deep/ .uni-page {
		overflow: hidden;
	}

	/deep/ .mescroll-upwarp {
		padding-bottom: 100rpx;
	}

</style>
