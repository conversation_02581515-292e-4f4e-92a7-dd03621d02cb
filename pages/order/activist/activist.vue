<template>
	<view class="activist-container" :class="themeStyle" :style="[themeColorVar]">
		<mescroll-uni ref="mescroll" @getData="getListData" :top="navHeight">
			<block slot="list">
				<view class="container">
					<block v-if="refundList.length">
						<view class="order-item" v-for="(item, index) in refundList" :key="index">
							<view class="order-header">
								<view class="site-name-box">
<!--									<view class="iconfont icondianpu"></view>-->
									<text class="site-name">{{ item.order_no }}</text>
								</view>
								<text class="status-name">{{ item.refund_name }}</text>
							</view>
							<view class="order-body">
								<view class="goods-wrap">
									<view class="goods-img" @click="refundDetail(item)">
										<image :src="$util.img(item.sku_image)" @error="imageError(index)" mode="aspectFill"
										 :lazy-load="true"></image>
									</view>
									<view class="goods-info" @click="refundDetail(item)">
										<view class="goods-name">{{ item.goods_name }}</view>
										<view class="spec_name">{{ item.spec_name }}</view>
										<view class="goods-sub-section" v-if="item.refund_type != 3">
											<view class="refund-price">
												<text>退款：</text>
												<text class="unit high-text-color" v-if="item.is_maidou_pay!=1">{{ $lang('common.currencySymbol') }}</text>
												<text class="high-text-color">{{ item.refund_apply_money }}</text>
												<text class="high-text-color" v-if="item.is_maidou_pay==1">迈豆</text>
											</view>
										</view>
									</view>
								</view>
							</view>
						</view>
					</block>
					<block v-else>
						<view>
							<ns-empty :isIndex="!1" :text="$lang('emptyTips')"></ns-empty>
						</view>
					</block>
				</view>
			</block>
		</mescroll-uni>
	</view>
</template>

<script>
	import refundMethod from '../public/js/refundMethod.js';
  // #ifdef H5
  import {isOnXianMaiApp} from "../../../common/js/h5/appOP";
  // #endif
  import globalConfig from "../../../common/mixins/golbalConfig";
	export default {
		mixins: [refundMethod,globalConfig],
		data() {
			return {
				refundList: [],
        navHeight:0
			};
		},
		onLoad() {
      // #ifdef H5
      if(isOnXianMaiApp){
        this.navHeight=88;
      }
      // #endif
    },
		onShow() {
			// 刷新多语言
			this.$langConfig.refresh();

			if (!uni.getStorageSync('token')) {
				this.$util.redirectTo('/pages/order/login/login', {
					back: '/pages/order/activist/activist'
				});
			}
		},
		computed: {
			themeStyle(){
				return 'theme-'+this.$store.state.themeStyle
			}
		},
		methods: {
			getListData(mescroll) {
				this.$api.sendRequest({
					url: '/api/orderrefund/lists',
					data: {
						page: mescroll.num,
						page_size: mescroll.size
					},
					success: res => {
						let newArr = [];
						let msg = res.message;
						if (res.code == 0 && res.data) {
							newArr = res.data.list;
						} else {
							this.$util.showToast({
								title: msg
							});
						}
						mescroll.endSuccess(newArr.length);
						//设置列表数据
						if (mescroll.num == 1) this.refundList = []; //如果是第一页需手动制空列表
						this.refundList = this.refundList.concat(newArr);
					},
					fail: res => {
						mescroll.endErr();
					}
				});
			},
			//退款详情
			refundDetail(info) {
				if(info.refund_type == 1) {
					this.$util.redirectTo('/otherpages/order/refund_detail/refund_detail', {
						order_goods_id: info.order_goods_id
					});
				}else{
					this.$util.redirectTo('/otherpages/order/return_and_exchange/refund_progress', {
						order_goods_id: info.order_goods_id
					});
				}
			},

			refundAction(event, data) {
				switch (event) {
					case 'orderRefundCancel': // 撤销维权
						this.cancleRefund(data.order_goods_id, res => {
							if (res.code >= 0) {
								this.$util.showToast({
									title: '撤销成功'
								});
								this.$refs.mescroll.refresh();
							}
						});
						break;
					case 'orderRefundDelivery': // 退款发货
						this.$util.redirectTo('/otherpages/order/refund_detail/refund_detail', {
							order_goods_id: data.order_goods_id,
							action: 'returngoods'
						});
						break;
					case 'orderRefundAsk':
						this.$util.redirectTo('/otherpages/order/refund/refund', {
							order_goods_id: data.order_goods_id
						});
						break;
				}
			},
			imageError(index) {
				this.refundList[index].sku_image = this.$util.getDefaultImage().default_goods_img;
				this.$forceUpdate();
			},
			toShopDetail(e){
				this.$util.redirectTo('/otherpages/shop/index/index', { site_id: e});
			},
      /**
       *分享参数组装
       */
      getSharePageParams() {
        return this.$util.unifySharePageParams('/otherpages/shop/home/<USER>', this.shareTitle || '先迈商城',
            '',{}, this.$util.img(this.shareImg) || this.$util.img('public/static/youpin/home_share.jpg'))
      }
		},
    /**
     * 自定义分享内容
     * @param {Object} res
     */
    onShareAppMessage(res) {
      let { title, link, imageUrl, query } = this.getSharePageParams()
      return this.$buriedPoint.pageShare(link , imageUrl, title);
    },
	};
</script>

<style lang="scss">
	@import '../public/css/activist.scss';
</style>
<style scoped>
	/deep/.mescroll-upwarp{
		padding: 0 !important;
		margin-bottom: 0;
		min-height: 0;
		line-height: 0;
	}
</style>
