<template>
	<view class="page">
		<view class="title"> {{orderPaymentData.desc||'--'}} </view>
		<view class="price"> 实付金额：￥<text>{{orderPaymentData.price|| 0}}</text></view>
		<view class="submit-btn" @click="orderPayPay(orderPaymentData.out_trade_no || '')"><button type="primary" size="mini">微信支付</button></view>
		<loading-cover ref="loadingCover"></loading-cover>

		<uni-popup ref="tips_popup" :maskClick="false">
		    <view class="popup-dialog desc-dialog">
				<view class="popup-dialog-body">
					<image :src="$util.img('public/static/youpin/order/paymentSuccess.png')" mode="widthFix" class="result-image"></image>
					支付成功
				</view>
				<view class="popup-dialog-footer">
					<button class="button red" open-type='launchApp'>返回APP</button>
				</view>
			</view>
		</uni-popup>

		<uni-popup ref="popupToList" class="my-popup-dialog">
		    <view class="popup-dialog">
				<view class="popup-dialog-header">提示</view>
				<view class="popup-dialog-body">支付失败！！</view>
				<view class="popup-dialog-footer">
					<button class="button red" @click="this.$refs.popupToList.close()">我知道了</button>
				</view>
			</view>
		</uni-popup>

		<!-- 授权登录弹窗 -->
		<yd-auth-popup ref="ydauth"></yd-auth-popup>
    <ns-login ref="login"></ns-login>
    <uni-coupon-pop ref="couponPop"></uni-coupon-pop>
	</view>
</template>

<script>
import AdaPay from 'common/js/adaPay.js'
export default{
	name:"app_payment",
	data(){
		return{
			orderPaymentData:null,
			shopId:0,
			disabled:true
		}	
	},
	onLoad(options){
		this.shopId = options.payShopId || 0
		this.createOrder()
	},
	onShow(){
		if (uni.getStorageSync('is_register')) {
			this.$util.toShowCouponPopup(this)
			uni.removeStorageSync('is_register');
		}
	},
	methods:{
		/**
		 * 创建订单
		 */
		createOrder(){
			this.$api.sendRequest({
				url: '/shopapi/upgrade/createOrder',
				data: {
					shop_id:this.shopId
				},
				success: res => {
					if (this.$refs.loadingCover) this.$refs.loadingCover.hide();
					if (res.code >= 0) {
						this.disabled = false
						this.orderPaymentData = res.data
					} else {
						this.$util.showToast({
							title: res.message,
						});
					}
				},
				fail: res => {
					if (this.$refs.loadingCover) this.$refs.loadingCover.hide();
				}
			})
		},

		/**
		 * 订单支付
		 */
		orderPayPay(out_trade_no) {
			if(!uni.getStorageSync('token')){
        this.$util.toShowLoginPopup(this,null,'/pages/order/upgrade_payment/upgrade_payment?payShopId=' + this.shopId);
				return
			}
			if(this.disabled) return
			if(!out_trade_no){
				this.$util.showToast({
					title: '支付失败!!'
				});
				return
			}
			var that = this;
			uni.showLoading({
				title:'提交中'
			})
			this.$api.sendRequest({
				url: '/api/pay/pay',
				data: {
					out_trade_no: out_trade_no,
					pay_type: 'adapay',
					shop_id:this.shopId
				},
				success: res => {
					uni.hideLoading();
					if (res.code >= 0) {
						var payData = res.data.data;
						AdaPay.doPay(res.data.payment, (result)=>{
							console.log("result.result_status....." + result.result_status);
							if(result.result_status == 'succeeded') {
								uni.hideLoading();
								// 跳转支付成功页面
								this.$refs.tips_popup.open()
							} else if (result.result_status == 'failed' || result.result_status == 'cancel') {
								uni.hideLoading();
								this.$refs.popupToList.open()
							} else {
								this.$util.showToast({
									title: '支付失败!!'
								});
							}
						});
					} else {
						if(res.message) {
							this.$util.showToast({
								title: res.message
							});
						} else {
							uni.hideLoading();
						}
					}
				},
				fail: res => {
					this.$util.showToast({
						title: 'request:fail'
					});
				}
			})
		},
	}
}
</script>

<style lang="scss" scoped>
.page{
	padding: 30rpx;
}
.title{
	padding-top: 50rpx;
	font-size: $ns-font-size-base + 6rpx;
	color: #333;
	text-align: center;
}
.price{
	font-size: $ns-font-size-xm;
	color: $base-color;
	text-align: center;
	text{
		font-size: $ns-font-size-lg + 10rpx;
		font-weight: bold;
	}
}
.submit-btn {
	height: 80rpx;
	margin-top: 50rpx;
	margin-left:calc(50% - 300rpx);
	width: 600rpx;
	button {
		padding: 0;
		width: 100%;
		background-color: #09BB07!important;
		height: 80rpx;
		line-height: 80rpx;
		font-size: 28rpx;
		border-radius: 40rpx;
	}
}

.popup-dialog {
	overflow: hidden;
	background: #FFFFFF;
	box-sizing: border-box;
	.popup-dialog-header {
		height: 106rpx;
		line-height: 106rpx;
		text-align: center;
		font-size: 36rpx;
		color: #333333;
		font-weight: bold;
	}
	.popup-dialog-body {
		color: #656565;
		text-align: center;
		padding: 0 30rpx;
		padding-top: 30rpx;
		max-height: 400rpx;
		overflow-y: scroll;
		.result-image{
			width: 150rpx;
			padding: 50rpx 100rpx;
			display: block;
		}
	}
	.popup-dialog-footer {
		margin: 0 32rpx;
		height: 140rpx;
		display: flex;
		align-items: center;
		justify-content: space-around;
		.button {
			width: 220rpx;
			height: 68rpx;
			line-height: 68rpx;
			text-align: center;
			border-radius: 34rpx;
			box-sizing: border-box;
			margin: 0;
			&.white {
				color: #F2270C;
				background: #FFFFFF;
				border: 1rpx solid #F2270C;
			}
			&.red {
				color: #FFFFFF;
				background: #F2270C;

			}
		}
	}
}
</style>
