<template>
	<view class="order-container" :class="themeStyle">
		<view class='nav bg-white' :style="{ height: navHeight + 'px' }">
			<view class='nav-title'>
				<image  :src="$util.img('public/static/youpin/order/back.png')" mode='aspectFit' class='back' @click='openPopup()'></image>
				<text>确认订单</text>
			</view>
		</view>
		<view class="wrapper" :style="{ marginTop: navHeight + 'px'}">
			<!-- 收货地址 -->
			<view class="address-wrap">
				<view class="icon">
					<image :src="$util.img('public/static/youpin/order/icon-no-pay-address.png')" mode=""></image>
				</view>
				<!-- <view class="address-info" @click="selectAddress"> -->
				<view class="address-info" @click="getChooseAddress">
					<block v-if="orderPaymentData.member_address">
						<view class="info">
							<text>{{ orderPaymentData.member_address.name }}</text>
							<text>{{ orderPaymentData.member_address.mobile }}</text>
						</view>
						<view class="detail">
							<text>{{ orderPaymentData.member_address.full_address }} {{ orderPaymentData.member_address.address }}</text>
						</view>
					</block>
					<block v-else>
						<view class="address-empty">
							<text>选择收货地址</text>
						</view>
					</block>
					<view class="cell-more"><view class="iconfont iconright"></view></view>
				</view>
			</view>

			<!-- 店铺 -->
			<view class="site-wrap" v-if="orderPaymentData.goodsInfo">
				<view class="site-header" style="visibility: hidden;">
					<view class="iconfont icondianpu"></view>
					<text class="site-name">{{ orderPaymentData.goodsInfo.site_name }}</text>
				</view>
				<view class="site-body">
					<view class="goods-wrap">
						<navigator hover-class="none" class="goods-img" :url="'/pages/goods/detail/detail?sku_id=' + goodsItem.sku_id">
							<image :src="$util.img(orderPaymentData.goodsInfo.sku_image)" @error="imageError" mode="aspectFill"></image>
						</navigator>
						<view class="goods-info">
							<navigator hover-class="none" :url="'/pages/goods/detail/detail?sku_id=' + orderPaymentData.goodsInfo.sku_id" class="goods-name">{{ orderPaymentData.goodsInfo.goods_name }}</navigator>
							<view class="goods-sub-section">
								<view>
									<text>
										{{ orderPaymentData.goodsInfo.spec_name }}
									</text>
								</view>
								<view>
									<text>x1</text>
								</view>
							</view>
							<view class="goods-price">
								<text>
									<text class="unit">{{ $lang('common.currencySymbol') }}</text>
									<text class="price">{{ orderPaymentData.goodsInfo.price }}</text>
								</text>
							</view>
						</view>
					</view>
				</view>
				<!-- 订单金额 -->
<!-- 				<view class="order-money">
					<view class="order-cell">
						<text class="tit">运费</text>
						<view class="box align-right">
							<text class="">
								<text class="ns-font-size-sm">{{ $lang('common.currencySymbol') }}</text>
								<text>{{ siteItem.delivery_money }}</text>
							</text>
						</view>
					</view>
				</view> -->
				<view class="site-footer">
<!-- 					<view class="order-cell">
						<text class="tit">买家留言</text>
						<view class="box">
							<input
								type="text"
								value=""
								placeholder="请填对本次交易的说明"
								class="ns-font-size-base"
								placeholder-style="{color:#CCCCCC}"
								v-model="orderCreateData.buyer_message[siteItem.site_id]"
								maxlength='50'
							/>
						</view>
					</view> -->
					<view class="order-cell">
						<view class="box align-right order-pay">
							共1件商品
							<text>
								小计：{{ $lang('common.currencySymbol') }}<text class="pay-money"> {{ orderPaymentData.periodBuy.buy_price | moneyFormat }}</text>
							</text>
						</view>
					</view>
				</view>
			</view>
			<view class="order-submit bottom-safe-area">
				<view class="order-settlement-info">
					<text>实付金额：</text>
					<text class="text-color">
						{{ $lang('common.currencySymbol') }}
						<text class="money">{{ orderPaymentData.periodBuy.buy_price }}</text>
					</text>
				</view>
				<view class="submit-btn"><button type="primary" size="mini" @click="orderCreate">微信支付</button></view>
			</view>
		</view>

		<!-- 未支付返回上一页提示弹窗 -->
		<uni-popup ref="popup" class="my-popup-dialog">
		    <view class="popup-dialog">
				<view class="popup-dialog-header">提示</view>
				<view class="popup-dialog-body">是否放弃本次付款？</view>
				<view class="popup-dialog-footer">
					<view class="button white" @click="toBack()">放弃</view>
					<button class="button red" @click="closePopup()">继续付款</button>
				</view>
			</view>
		</uni-popup>

		<loading-cover ref="loadingCover"></loading-cover>
	</view>
</template>

<script>
import payment from '../public/js/cycle_payment.js';
import uniPopup from '@/components/uni-popup/uni-popup.vue';
import mypOne from '@/components/myp-one/myp-one.vue';

export default {
	components: {
		uniPopup,
		mypOne,
	},
	data() {
		return {
			timeTip: '选择配送时间',
			time:null,
			navHeight: 0,
      period_id:null,
      choiceWechatAdderError:false,  //直接选择微信地址失败
      ischoiceWechatAdder:false,  //是否直接选择微信地址
      postWechatAdder:false,  //微信地址添加到后台请求完成
		};
	},
	computed: {
		themeStyle() {
			return 'theme-' + this.$store.state.themeStyle;
		}
	},
	onLoad(options) {
		if (options.sku_id) this.sku_id = options.sku_id;
		this.period_id = options.period_id;
		uni.getSystemInfo({
		  success: res => {
			//导航高度
			let navHeight = res.statusBarHeight + 46;
			this.navHeight = navHeight;
		  },
		  fail(err) {
			console.log(err);
		  }
		})
	},
	onShow() {},
	mixins: [payment],
	methods: {
		openPopup(){
			// 未支付返回上一页提示弹窗
			this.$refs.popup.open()
		},
		closePopup(){
			this.$refs.popup.close();
		},

		toBack() {
			this.closePopup()
			uni.navigateBack()
		},
		toWaitPayList() {
			this.$refs.popupToList.close();
			this.$util.redirectTo("/pages/order/list/list?status=waitpay", {}, "redirectTo")
			uni.removeStorage({
				key: 'orderCreateData',
				success: () => {}
			});
		},
		/**
		 * 一键获取地址
		 */
		getChooseAddress() {
			var that = this;
			if(!this.orderPaymentData.member_address) {
        this.ischoiceWechatAdder=true;
				uni.chooseAddress({
					success: res => {
						console.log('success.res')
						console.log(res)
						if (res.errMsg == 'chooseAddress:ok') {
							this.saveAddress({
								name: res.userName, // 收货人姓名,
								mobile: res.telNumber, // 手机号
								province: res.provinceName, // 省
								city: res.cityName, // 市
								district: res.countyName, // 县
								address: res.detailInfo, // 详细地址
								full_address: res.provinceName + ' ' + res.cityName + ' ' + res.countyName
							});
						} else {
							this.$util.showToast({
								title: res.errMsg
							});
						}
					},
					fail: res => {
						console.log('fail.res')
						console.log(res)
						this.$util.showToast({
							title: '获取微信地址失败',
							success: () => {
								setTimeout(() => {
									that.selectAddress()
								}, 1500)
							}
						});
            this.postWechatAdder=true;
					}
				});
			} else {
				this.selectAddress()
			}
		},
		/**
		 * 保存微信地址
		 * @param {Object} params
		 */
		saveAddress(params) {
			this.$api.sendRequest({
				url: '/api/memberaddress/addthreeparties',
				data: params,
				success: res => {
					if (res.code >= 0) {
					} else {
						this.$util.showToast({
							title: res.message
						});
            this.choiceWechatAdderError=true;
					}
				},
        complete: ()=>{
          this.postWechatAdder=true;
        }
			});
		},
    imageError() {
      this.orderPaymentData.goodsInfo.sku_image = this.$util.getDefaultImage().default_goods_img;
      this.$forceUpdate();
    },

	}
};
</script>

<style lang="scss" scoped>

	/deep/ .uni-navbar--border {
		border-bottom-width: 0;
	}
</style>
<style lang="scss" scoped>
	/deep/ .my-popup-dialog .uni-popup__wrapper-box {
		max-width: 540rpx;
		width: 540rpx;
		border-radius: 20rpx;
	}
	/deep/ .coupon-instructions .uni-popup__wrapper-box {
		max-width: 620rpx;
		width: 620rpx;
		border-radius: 20rpx;
	}
	.popup-dialog {
		overflow: hidden;
		background: #FFFFFF;
		box-sizing: border-box;
		.popup-dialog-header {
			height: 106rpx;
			line-height: 106rpx;
			text-align: center;
			font-size: 36rpx;
			color: #333333;
			font-weight: bold;
		}
		.popup-dialog-body {
			color: #656565;
			text-align: center;
			padding: 0 30rpx;
		}
		.popup-dialog-footer {
			margin: 0 32rpx;
			height: 140rpx;
			display: flex;
			align-items: center;
			justify-content: space-around;
			.button {
				width: 220rpx;
				height: 68rpx;
				line-height: 68rpx;
				text-align: center;
				border-radius: 34rpx;
				box-sizing: border-box;
				margin: 0;
				&.white {
					color: #F2270C;
					background: #FFFFFF;
					border: 1rpx solid #F2270C;
				}
				&.red {
					color: #FFFFFF;
					background: #F2270C;

				}
			}
		}
	}
	.coupon-instructions-popup-body {
		width: 560rpx;
		height: 540rpx;
		margin: 0 auto;
	}
	.coupon-instructions {
		.popup-dialog {
			.popup-dialog-body {
				padding: 0;
			}
			.popup-dialog-footer {
				.button {
					width: 480rpx;
				}
			}
		}
	}
</style>


<style lang="scss">
@import '../public/css/payment.scss';
</style>
