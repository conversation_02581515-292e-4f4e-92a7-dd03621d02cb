<template>
	<view class="order-container" :class="themeStyle" :style="[themeColorVar]">
		<!-- #ifdef MP-WEIXIN -->
		<view class="custom" :style="{paddingTop:statusBarHeight+'px',height:navHeight+'px'}">
			<view class="iconfont iconback_light" @click="navigateBack"></view>
			<view class="custom-navbar">
				<view class="navbar-item">订单列表</view>
				<!-- <view class="navbar-item" @click="navigateCycle">周期购</view> -->
			</view>
		</view>
		<!-- #endif -->
		<!-- #ifdef H5 -->
		<uni-nav-bar left-icon="back" :border="false" @clickLeft="appGoBack" v-if="isOnXianMaiApp">
		  <template>
		    <view class="page-title">我的订单</view>
		  </template>
		</uni-nav-bar>
		<!-- #endif -->
		<!-- #ifdef H5 -->
		<!-- <view class="custom" :style="{paddingTop:statusBarHeight+'px',height:navHeight+'px'}" v-if="isOnXianMaiApp">
		  <view class="iconfont iconback_light" @click="navigateBack"></view>
		  <view class="custom-navbar">
			<view class="navbar-item">订单列表</view>
		  </view>
		</view> -->
		<!-- #endif -->
		<scroll-view id="tab-bar" :style="{top:statusBarHeight+navHeight+'px'}" class="order-nav" :scroll-x="true"
		 :show-scrollbar="false" :scroll-into-view="scrollInto">
			<view v-for="(statusItem, statusIndex) in statusList" :key="statusIndex" class="uni-tab-item" :id="statusItem.id"
			 :data-current="statusIndex" @click="ontabtap">
				<view class="uni-tab-item-title" :class="statusItem.status == orderStatus ? 'uni-tab-item-title-active high-text-color' : ''">{{ statusItem.name }}
					<view class="line"></view>
				</view>
			</view>
		</scroll-view>
    <view class="uni-tab-filter" :style="{top:statusBarHeight+navHeight+'px'}">
      <view class="uni-tab-filter-fill"></view>
      <view class="uni-tab-item uni-tab-filter-info" @click="toShowFilter">
        <view class="uni-tab-item-title uni-tab-filter-info-title" :class="{'uni-tab-filter-info-title-filter':isClickFilter}">
          <image :src="orderFilterBg" class="uni-tab-filter-info-title-icon"></image>
          筛选</view>
      </view>
    </view>

		<mescroll-uni ref="mescroll" @getData="getListData" :top="100 + (navHeight+statusBarHeight)*2 + 'rpx'" :class="{'empty-content' : orderList.length<1}">
			<block slot="list">
				<view class="order-list" v-if="orderList.length > 0">
					<view class="order-item" v-for="(orderItem, orderIndex) in orderList" :key="orderIndex">
						<view class="order-header" :class="{ waitpay: (orderStatus == 'waitpay' && orderItem.order_status == 0) }">
							<view class="site-name-box">
								<!--								<view class="iconfont icondianpu"></view>-->
								<!--								<view class="site-name">{{ orderItem.supply_shop_name }}</view>-->
								<view class="site-name">订单号：{{ orderItem.order_no }}<image :src="$util.img('/public/static/youpin/order/copy-two.png')" class="site-name-copy" @click="to_copy_order_no(orderItem.order_no)"></image></view>
							</view>
							<text class="status-name high-text-color">{{ orderItem.order_status_name }}</text>
						</view>
						<view class="order-body" @click="orderDetail(orderItem)">
							<view class="goods-wrap" v-for="(goodsItem, goodsIndex) in orderItem.order_goods" :key="goodsIndex">
								<view class="goods-img">
									<image :src="$util.img(goodsItem.sku_image)" @error="imageError(orderIndex,goodsIndex)" mode='aspectFit'
									 :lazy-load="true"></image>
								</view>
								<view class="goods-info">
									<view class="goods-name">{{ goodsItem.goods_name }}</view>
                  <view class="goods-info-bottom">
                    <view class="goods-info-bottom-left">
                      <view class="goods-price" v-if="orderItem.is_maidou_pay == 1">
                        <text>{{ goodsItem.price}}迈豆</text>
                      </view>
                      <view class="goods-price" v-else>
                        <text class="unit">{{ $lang('common.currencySymbol') }}</text>
                        <text>{{ goodsItem.price }}</text>
                      </view>
                    </view>
                    <view class="goods-sub-section">
                      <view>{{ goodsItem.spec_name }}</view>
                      <view>x{{ goodsItem.num }}</view>
                    </view>
                  </view>
								</view>
							</view>
						</view>
						<view class="order-footer">
							<view class="order-base-info">
								<view class="total">
									<text>共{{ orderItem.goods_num }}件商品</text>
									<text>
										总计：
										<block v-if="orderItem.is_maidou_pay == 1">
											<text class="strong">{{ orderItem.pay_money}}迈豆</text>
										</block>
										<block v-else>
											<text class="ns-font-size-sm">{{ $lang('common.currencySymbol') }}</text>
											<text class="strong">{{ orderItem.pay_type == 'BALANCE'?orderItem.balance_money:orderItem.pay_money }}</text>
										</block>
									</text>
								</view>
							</view>
							<view class="order-operation" v-if="orderItem.action.length > 0">
                <view v-if="orderItem.action && orderItem.action.filter(operationItem=>operationItem.action == 'orderPay').length" class="order-operation-left">
                  <view class="countdown">
                    <view class="clockrun">
                      <uni-count-down :day="orderItem.discountTimeMachine.d" :hour="orderItem.discountTimeMachine.h" :minute="orderItem.discountTimeMachine.i"
                                      :second="orderItem.discountTimeMachine.s" color="var(--custom-brand-color)" splitorColor="var(--custom-brand-color)" background-color="transparent" />
                    </view>
                  </view>
                  <text>后取消订单</text>
                </view>
                <view v-else></view>
                <view class="order-operation-right">
                  <view class="order-box-btn" :class="{ 'order-pay' : operationItem.action == 'orderPay'||operationItem.action == 'memberTakeDelivery' }"
                        v-for="(operationItem, operationIndex) in orderItem.action" :key="operationIndex" @click="operation(operationItem, orderItem)">
                    {{ operationItem.title }}
                  </view>
                </view>
							</view>
						</view>
					</view>
				</view>
				<view v-else>
					<ns-empty :isIndex="!1" :text="text" entrance="orderList" :fixed="false"></ns-empty>
				</view>
			</block>
		</mescroll-uni>
    <!--  订单筛选搜索  -->
    <uni-popup ref="orderFilter" type="top" class="order-filter-pop" :top="100 + (navHeight+statusBarHeight)*2 + 'rpx'">
      <view class="order-filter-pop-form">
        <view class="order-filter-pop-form-row">
          <text class="order-filter-pop-form-row-label">商品名字</text>
          <input type="text" class="order-filter-pop-form-row-value" placeholder-class="order-filter-pop-form-row-value-placeholder" placeholder="请输入商品名称" v-model="goods_name">
        </view>
        <view class="order-filter-pop-form-row">
          <text class="order-filter-pop-form-row-label">订单号</text>
          <input type="text" class="order-filter-pop-form-row-value" placeholder-class="order-filter-pop-form-row-value-placeholder" placeholder="请输入订单号" v-model="order_no">
        </view>
        <view class="order-filter-pop-form-row">
          <text class="order-filter-pop-form-row-label">日期</text>
          <view class="order-filter-pop-form-row-date">
            <picker mode="date" :value="date_start" class="order-filter-pop-form-row-date-select" @change="startDateChange">
<!--              <input type="text" class="order-filter-pop-form-row-value" placeholder-class="order-filter-pop-form-row-value-placeholder" placeholder="开始日期" disabled="disabled">-->
              <text class="order-filter-pop-form-row-date-one" :class="{'order-filter-pop-form-row-date-one-placeholder':!date_start}">{{date_start ? date_start : '开始日期'}}</text>
            </picker>
            <text class="order-filter-pop-form-row-date-separator">-</text>
            <picker mode="date" :value="date_end" class="order-filter-pop-form-row-date-select" @change="endDateChange">
<!--              <input type="text" class="order-filter-pop-form-row-value" placeholder-class="order-filter-pop-form-row-value-placeholder" placeholder="结束日期" disabled="disabled">-->
              <text class="order-filter-pop-form-row-date-one" :class="{'order-filter-pop-form-row-date-one-placeholder':!date_end}">{{date_end ? date_end : '结束日期'}}</text>
            </picker>
          </view>
        </view>
        <view class="order-filter-pop-form-op">
          <text class="order-filter-pop-form-op-reset" @click="toResetOrder">重置</text>
          <text class="order-filter-pop-form-op-search" @click="toFilterOrder">搜索</text>
        </view>
      </view>
    </uni-popup>
		<loading-cover ref="loadingCover"></loading-cover>
		<yd-auth-popup ref="ydauth"></yd-auth-popup>
    <ns-login ref="login"></ns-login>
    <uni-coupon-pop ref="couponPop"></uni-coupon-pop>
	</view>
</template>

<script>
	import apiurls from "../../../common/js/apiurls";
	import uniCountDown from '@/components/uni-count-down/uni-count-down.vue';
	import orderMethod from '../public/js/orderMethod.js';
	import system from "@/common/js/system.js";
	import appInlineH5 from "../../../common/mixins/appInlineH5";
  import golbalConfig from "../../../common/mixins/golbalConfig";
	// #ifdef H5
	import {isOnXianMaiApp} from "../../../common/js/h5/appOP";
	// #endif

	export default {
		components: {
			uniCountDown
		},
		data() {
			return {
				scrollInto: '',
				orderStatus: 'all',
				statusList: [],
				orderList: [],
				contentText: {},
				mergePayOrder: [],
				text: '暂无订单信息',
				statusBarHeight: 0,
				navHeight: 0,
				// #ifdef H5
				isOnXianMaiApp:isOnXianMaiApp,
				// #endif
        isFirstLoad: true, //第一次打开页面
        isClickFilter:false,
        order_no: '',
        goods_name:'',
        date_start:'',
        date_end:'',
        orderFilterBg:''
			};
		},
		mixins: [orderMethod,appInlineH5,golbalConfig],
		onLoad(option) {
			if (option.status) this.orderStatus = option.status;
			uni.getSystemInfo({
				success: res => {
					//导航高度
					this.navHeight = res.statusBarHeight + 46 - wx.getSystemInfoSync()['statusBarHeight'];
					// #ifdef H5
					if(!this.isOnXianMaiApp){
						this.navHeight=0;
					}
					// #endif
				},
				fail(err) {
					console.log(err);
				}
			})
      let color = this.$util.colorToHex(this.$store.state.themeColorVar['--custom-brand-color']).slice(1);
      this.orderFilterBg = encodeURI(this.$util.img( `api/website/svgChangeFillColor?svg_name=order-filter&color=${color}`))
		},
		async onShow() {
			this.statusBarHeight = wx.getSystemInfoSync()['statusBarHeight']
			// 刷新多语言
			this.$langConfig.refresh();
			await system.wait_staticLogin_success();

			this.getOrderStatus();

			if (uni.getStorageSync('token')) {
				if (this.$refs.mescroll && !this.isFirstLoad) this.$refs.mescroll.refresh();
			} else {
			  let path=`/pages/order/list/list?status=${this.orderStatus}`
				this.$util.toShowLoginPopup(this,null,path);
				// this.$util.redirectTo('/pages/login/login/login', {
				// 	back: '/pages/order/list/list?status=' + this.orderStatus
				// });
			}
			if (uni.getStorageSync('is_register')) {
				this.$util.toShowCouponPopup(this)
				uni.removeStorageSync('is_register');
			}
		},
		methods: {
			ontabtap(e) {
				let index = e.target.dataset.current || e.currentTarget.dataset.current;
				this.orderStatus = this.statusList[index].status;
				if (this.orderStatus == 'all') {
					this.text = '您还暂无相关订单'
				} else if (this.orderStatus == 'waitpay') {
					this.text = '您还暂无待付款订单'
				} else if (this.orderStatus == 'waitsend') {
					this.text = '您还暂无待发货订单'
				} else if (this.orderStatus == 'waitconfirm') {
					this.text = '您还暂无待收货订单'
				} else {
					this.text = '您还暂无已完成订单'
				}
				if (this.orderStatus == '') this.mergePayOrder = [];
				// this.$refs.loadingCover.show();
				this.$refs.mescroll.refresh();
			},
			async getListData(mescroll) {
        await system.wait_staticLogin_success();
        let params = {
          page: mescroll.num,
          page_size: mescroll.size,
          order_status: this.orderStatus,
        }
        if(this.isClickFilter){
          Object.assign(params,{
            order_no:this.order_no,
            date_start:this.date_start,
            date_end:this.date_end,
            goods_name:this.goods_name})
        }
				this.$api.sendRequest({
					url: '/api/order/lists',
					data: params,
					success: res => {
						let newArr = []
						let msg = res.message;
						if (res.code == 0 && res.data) {
							newArr = res.data.list;
						} else {
							if (!uni.getStorageSync('token') && res.code == -10009) {

							} else {
								this.$util.showToast({
									title: msg
								})
							}
						}
						mescroll.endSuccess(newArr.length);
						//设置列表数据
						if (mescroll.num == 1) this.orderList = []; //如果是第一页需手动制空列表
						this.orderList = this.orderList.concat(newArr); //追加新数据
						for (let i = 0; i < this.orderList.length; i++) {
							if (typeof this.orderList[i].rest_pay_time == 'number' || this.orderList[i].rest_pay_time) {
								this.orderList[i].discountTimeMachine = this.$util.countDown(this.orderList[i].rest_pay_time);
							}
						}
            this.isFirstLoad = false
            if (this.$refs.loadingCover) this.$refs.loadingCover.hide();
					},
					fail: res => {
						mescroll.endErr();
						if (this.$refs.loadingCover) this.$refs.loadingCover.hide();
					}
				});
			},
			/**
			 * 获取订单状态
			 */
			getOrderStatus() {
				this.statusList = [{
						status: 'all',
						name: this.$lang('all'),
						id: 'status_0'
					},
					{
						status: 'waitpay',
						name: this.$lang('waitPay'),
						id: 'status_1'
					},
					{
						status: 'waitsend',
						name: this.$lang('readyDelivery'),
						id: 'status_2'
					},
					{
						status: 'waitconfirm',
						name: this.$lang('waitDelivery'),
						id: 'status_3'
					},
					{
						status: 'waitrate',
						name: this.$lang('waitEvaluate'),
						id: 'status_4'
					}
				];
			},
			operation(operationItem, orderData) {
				let action = operationItem.action
				let index = this.status;
				switch (action) {
					case 'orderPay': // 支付
						if(operationItem.disabled) {
							this.$util.showToast({
								title: operationItem.disabled_tips
							})
						}else{
							this.orderPay(orderData, (res) => {
								if(res.code == -11) {
									this.$util.showToast({
										'title': res.message
									})
									let that = this
									that.orderStatus = 'all'
									setTimeout(() => {
										that.$refs.mescroll.refresh();
									}, 1500)
								}
							});
						}
						break;
					case 'orderClose': //关闭
						if(operationItem.disabled) {
							this.$util.showToast({
								title: operationItem.disabled_tips
							})
						}else{
							this.orderClose(orderData.order_id, () => {
								this.orderStatus = 'all'
								this.$refs.mescroll.refresh();
							});
						}
						break;
					case 'memberTakeDelivery': //收货
						this.orderDelivery(orderData.order_id, () => {
							this.$refs.mescroll.refresh();
						});
						break;
					case 'trace': //查看物流

						// #ifdef MP-WEIXIN
						if(orderData.package_list && orderData.package_list.length == 1 && orderData.package_list[0].out_delivery_type == 'wx_mini'){
							this.$util.redirectTo(
								'/pluginspages/logisticsPlugin/logisticsPlugin',
								{ waybillToken:orderData.package_list[0].out_delivery_no },
							);
						} else if(orderData.package_list && orderData.package_list.length > 1) {
							this.$util.redirectTo('/otherpages/order/parcel/parcel', {
						        order_id: orderData.order_id
			        		});
						} else {
							this.$util.redirectTo('/pages/order/logistics/logistics', {
								order_id: orderData.order_id
							});
						}
						// #endif
						// #ifdef H5
							if(orderData.package_list && orderData.package_list.length == 1) {
								this.$util.redirectTo('/pages/order/logistics/logistics', {
								order_id: orderData.order_id
							});
							} else if(orderData.package_list && orderData.package_list.length > 1) {
								this.$util.redirectTo('/otherpages/order/parcel/parcel', {
						        order_id: orderData.order_id
			        		});
							}

						// #endif
						break;
					case 'memberOrderEvaluation': //评价
						this.$util.redirectTo('/otherpages/order/evaluate/evaluate', {
							order_id: orderData.order_id
						});
						break;
					case 'orderBuy': //再次购买
						this.orderBuy(orderData)
						// this.$util.redirectTo('/otherpages/order/evaluate/evaluate', {
						// 	order_id: orderData.order_id
						// });
						break;
				}
			},
			async orderBuy(orderData) {

				const skus = [];
				orderData.order_goods.forEach(item => skus.push(item.sku_id));

				if (orderData.order_create_type == 8) {
					this.$util.redirectTo('/promotionpages/pintuan/detail/detail', {
						id: orderData.pintuan_goods_id
					});
					return
				} else {
					this.$util.redirectTo('/pages/goods/detail/detail', {
						sku_id: skus[0]
					});
					return
				}
				// if(orderData.order_create_type==6){
				// 	this.$util.redirectTo('/pages/goods/detail/detail', {
				// 		sku_id: skus[0]
				// 	});
				// 	return
				// }
				// let res=await this.$api.sendRequest({
				//   url: apiurls.orderBuyUrl,
				//   async:false,
				//   data: {
				//     skus:skus,
				//   },
				// });
				// console.log(skus)
				// if(res.code!=0){
				//   uni.showToast({
				//     title: res.message,
				//     mask:true,
				//     icon:"none",
				//     duration: 3000
				//   });

				// };
				// this.$util.redirectTo('/pages/goods/cart/cart', {}, 'reLaunch');
			},
      to_copy_order_no(order_no){
        this.$util.copy(order_no);
      },
			orderDetail(data) {
				switch (data.order_type ? parseInt(data.order_type) : 1) {
					case 2:
						// 自提订单
						this.$util.redirectTo('/pages/order/detail_pickup/detail_pickup', {
							order_id: data.order_id
						});
						break;
					case 3:
						// 本地配送订单
						this.$util.redirectTo('/pages/order/detail_local_delivery/detail_local_delivery', {
							order_id: data.order_id
						});
						break;
					case 4:
						// 虚拟订单
						this.$util.redirectTo('/pages/order/detail_virtual/detail_virtual', {
							order_id: data.order_id
						});
						break;
					default:
						this.$util.redirectTo('/pages/order/detail/detail', {
							order_id: data.order_id
						});
						break;
				}
			},
			/**
			 * 选择订单
			 * @param {Object} orderId
			 */
			selectOrder(orderId) {
				if (this.$util.inArray(orderId, this.mergePayOrder) != -1) {
					this.mergePayOrder.splice(this.$util.inArray(orderId, this.mergePayOrder), 1);
				} else {
					this.mergePayOrder.push(orderId);
				}
			},
			/**
			 * 合并支付
			 */
			mergePay() {
				if (this.mergePayOrder.length) {
					this.$api.sendRequest({
						url: '/api/order/pay',
						data: {
							order_ids: this.mergePayOrder.toString()
						},
						success: res => {
							if (res.code >= 0) {
								this.$util.redirectTo('/pages/pay/index/index', {
									code: res.data
								});
							} else {
								this.$util.showToast({
									title: res.message
								});
							}
						}
					});
				}
			},
			imageError(orderIndex, goodsIndex) {
        if(this.orderList[orderIndex] && this.orderList[orderIndex].order_goods){
          this.orderList[orderIndex].order_goods[goodsIndex].sku_image = this.$util.getDefaultImage().default_goods_img;
          this.$forceUpdate();
        }
			},
			toShopDetail(e) {
				this.$util.redirectTo('/otherpages/shop/index/index', {
					site_id: e
				});
			},
			navigateBack() {
				// #ifdef MP-WEIXIN
				// this.$util.redirectTo('/pages/member/index/index', '', 'redirectTo');
        uni.navigateBack();
				// #endif
				// #ifdef H5
				uni.navigateBack();
				// #endif
			},
			navigateCycle() {
				this.$util.redirectTo('/pages/order/manage_cycle/manage_cycle');
			},
      /**
       *分享参数组装
       */
      getSharePageParams() {
        return this.$util.unifySharePageParams('/otherpages/shop/home/<USER>', this.shareTitle || '先迈商城',
            '',{}, this.$util.img(this.shareImg) || this.$util.img('public/static/youpin/home_share.jpg'))
      },
      toShowFilter(){
        this.$refs.orderFilter.open()
      },
      startDateChange(e){
        this.date_start = e.detail.value
      },
      endDateChange(e){
        this.date_end = e.detail.value
      },
      toResetOrder(){
        this.date_start=''
        this.date_end=''
        this.order_no=''
        this.goods_name=''
        this.isClickFilter = false
        this.$refs.orderFilter.close();
        this.$refs.mescroll.refresh();
      },
      toFilterOrder(){
        this.isClickFilter = true
        this.$refs.orderFilter.close();
        this.$refs.mescroll.refresh();
      }
		},
    /**
     * 自定义分享内容
     * @param {Object} res
     */
    onShareAppMessage(res) {
      let { title, link, imageUrl, query } = this.getSharePageParams()
      return this.$buriedPoint.pageShare(link , imageUrl, title);
    },
		computed: {
			mpOrderList() {
				if (!this.orderList[this.status]) return;
				return this.orderList[this.status].list || [];
			},
			//vueX页面主题
			themeStyle() {
				return 'theme-' + this.$store.state.themeStyle
			}
		},
	};
</script>

<style lang="scss">
	@import '../public/css/list.scss';

	/* 标题栏 */
	.custom {
		background: #fff;
		display: flex;
		align-items: center;
		justify-content: center;
		position: fixed;
		top: 0;
		left: 0;
		width: 100%;

		.iconfont {
			font-size: $ns-font-size-base + 12;
			color: #333;
			font-weight: bold;
			position: absolute;
			left: 20rpx;
		}

		.custom-navbar {
			display: flex;
			// border-radius: 30rpx;
			// background: #FFF4F4;
			width: 360rpx;
			align-items: center;

			.navbar-item {
				height: 60rpx;
				line-height: 60rpx;
				width: 100%;
				text-align: center;
				color: #333333;
				font-size: $ns-font-size-base + 2;
				// &.active{
				// 	background:$base-color;
				// 	color: #FFFFFF;
				// 	border-radius: 30rpx;
				// }
			}
		}
	}
	.page-title{
	  width: 360rpx;
	  overflow:hidden;
	  white-space: nowrap;
	  text-overflow: ellipsis;
	  -o-text-overflow:ellipsis;
	  text-align: center;
	}
</style>
<style scoped>
	/deep/ .uni-page {
		overflow: hidden;
	}

   /deep/ .mescroll-upwarp {
		padding-bottom: 100rpx;
	}
  .empty-content /deep/ .mescroll-uni{
    background-color: white;
  }

	.countdown .clockrun>>>.uni-countdown {
		display: flex;
		justify-content: center;
		align-items: center;
		padding: 0;
    height: 38rpx;
    line-height: 38rpx;
	}

	.countdown .clockrun>>>.uni-countdown__number {
		background: #000;
		/* // #690b08 */
		padding: 0;
		margin: 0;
		border: none;
    font-size: 26rpx;
    height: 38rpx;
    line-height: 38rpx;
	}

	.countdown .clockrun>>>.uni-countdown__splitor {
		padding: 0;
		font-size: 26rpx;
    height: 38rpx;
    line-height: 38rpx;
	}

	.countdown .clockrun>>>.uni-countdown__splitor.day {
		width: initial;
    font-size: 26rpx;
    height: 38rpx;
    line-height: 38rpx;
	}

	/deep/.mescroll-upwarp {
		padding: 0 !important;
		margin-bottom: 0;
		min-height: 0;
		line-height: 0;
	}
  /deep/.mescroll-uni{
    height: auto!important;
  }
</style>
