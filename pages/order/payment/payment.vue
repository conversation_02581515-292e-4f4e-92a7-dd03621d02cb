<template>
	<view class="order-container" :class="themeStyle" :style="[themeColorVar]">
		<!-- #ifdef MP-WEIXIN -->
		<view class='nav bg-white' :style="{ height: navHeight + 'px' }">
			<view class='nav-title'>
				<image  :src="$util.img('public/static/youpin/order/back.png')" mode='aspectFit' class='back' @click='openPopup()'></image>
				<text>确认订单</text>
			</view>
		</view>
		<!-- #endif -->
    <!-- #ifdef H5 -->
    <view class='nav bg-white' :style="{ height: navHeight + 'px' }" v-if="isOnXianMaiApp">
      <view class='nav-title'>
        <image  :src="$util.img('public/static/youpin/order/back.png')" mode='aspectFit' class='back' @click='openPopup()'></image>
        <text>确认订单</text>
      </view>
    </view>
    <!-- #endif -->
		<view class="wrapper" :style="{ marginTop: navHeight + 'px'}">
			<!-- 收货地址 -->
			<view class="address-wrap">
				<view class="icon">
					<image :src="$util.img('public/static/youpin/order/icon-no-pay-address.png')" mode=""></image>
				</view>
				<!-- <view class="address-info" @click="selectAddress"> -->
				<view class="address-info" @click="getChooseAddress">
					<block v-if="orderPaymentData.member_address">
						<view class="info">
							<text>{{ orderPaymentData.member_address.name }}</text>
							<text>{{ orderPaymentData.member_address.mobile }}</text>
						</view>
						<view class="detail">
							<text>{{ orderPaymentData.member_address.full_address }} {{ orderPaymentData.member_address.address }}</text>
						</view>
					</block>
					<block v-else>
						<view class="address-empty">
							<text>选择收货地址</text>
						</view>
					</block>
					<view class="cell-more"><view class="iconfont iconright"></view></view>
				</view>
			</view>

      <!--  跨境实名信息    -->
      <view class="cross-border-wrap">
        <view class="cross-border-title">
          <uni-icons type="person" size="22" color="#000" class="cross-border-title-icon"></uni-icons>
          <view class="cross-border-title-text">跨境商品需实名验证，购买时请确保收货人与身份证信息一致</view>
        </view>
        <view class="cross-border-btn">
          <view class="cross-border-btn-text" v-if="true" @click="$util.redirectTo('/otherpages/member/cross_border_real_name_authentication/cross_border_real_name_authentication')">添加购买人身份信息</view>
          <view class="cross-border-info" v-else>
            <view class="cross-border-info-name">何生产</view>
            <view class="cross-border-info-id">4466715245787215</view>
          </view>
        </view>
      </view>

			<!-- 店铺 -->
			<template v-if="orderPaymentData.shop_goods_list" >
			<view class="site-wrap" v-for="(siteItem, siteIndex) in orderPaymentData.shop_goods_list" :key="siteIndex">
				<view class="site-header" style="visibility: hidden;">
					<view class="iconfont icondianpu"></view>
					<text class="site-name">{{ siteItem.site_name }}</text>
				</view>
				<view class="site-body">
					<view class="goods-wrap" v-for="(goodsItem, goodsIndex) in siteItem.goods_list" :key="goodsIndex">
						<navigator hover-class="none" class="goods-img" :url="'/pages/goods/detail/detail?sku_id=' + goodsItem.sku_id">
							<image :src="$util.img(goodsItem.sku_image)" @error="imageError(siteIndex, goodsIndex)" mode="aspectFill"></image>
						</navigator>
						<view class="goods-info">
							<navigator hover-class="none" :url="'/pages/goods/detail/detail?sku_id=' + goodsItem.sku_id" class="goods-name">{{ goodsItem.sku_name }}</navigator>
							<view class="goods-sub-section">
								<view>
									<text>
										{{ goodsItem.spec_name }}
									</text>
								</view>
								<!-- <view>
									<text>x{{ goodsItem.num }}</text>
								</view> -->
							</view>
							<view class="goods-price-box">
								<view class="box-left">
										<view class="share-discount" v-if="parseFloat(goodsItem.multiple_discount_money) > 0">享多件折扣</view>
							  <view class="goods-price">
								<text>
									<text class="unit">{{ $lang('common.currencySymbol') }}</text>
									<text class="price">{{ goodsItem.price }}</text>
								</text>
						      </view>
								</view>
								<view class="box-right">
										<uni-number-box v-if="!limitType" :min="1" type="cart" :max="goodsItem.stock" :value="goodsItem.num"
												size="small" :modifyFlag="modifyFlag"
												@change="cartNumChange($event, { siteIndex, goodsIndex })" />
										<view class="goods-number" v-else>x{{orderCreateData.combo_id ? goodsItem.num :'1'}}</view>
								</view>
							</view>

						</view>
					</view>
				</view>
				<!-- 运费金额 -->
				<view class="order-money">
					<view class="order-cell">
						<text class="tit">运费</text>
						<view class="box align-right">
							<text class="" v-if="parseFloat(siteItem.delivery_money)>0">
								<text class="ns-font-size-sm">{{ $lang('common.currencySymbol') }}</text>
								<text>{{ siteItem.delivery_money | moneyFormat }}</text>
							</text>
              <text v-else>包邮</text>
						</view>
					</view>
				</view>
				<!-- 优惠金额 -->
				<!-- <view class="order-money">
					<view class="order-cell">
						<text class="tit">运费</text>
						<view class="box align-right">
							<text class="">
								<text class="ns-font-size-sm">{{ $lang('common.currencySymbol') }}</text>
								<text>{{ siteItem.delivery_money | moneyFormat }}</text>
							</text>
						</view>
					</view>
				</view> -->
				<view class="site-footer">
					<view class="order-cell">
						<text class="tit">订单备注</text>
						<view class="box">
							<input
								type="text"
								value=""
								placeholder="请填对本次交易的说明"
								class="ns-font-size-base"
								placeholder-style="{color:#CCCCCC}"
								v-model="orderCreateData.buyer_message[siteItem.site_id]"
								maxlength='50'
							/>
						</view>
					</view>
          <view class="order-cell" v-if="siteItem.promotion_money && parseFloat(siteItem.promotion_money)">
            <text class="tit">分销商优惠</text>
            <view class="box align-right">
              <text class="" style="color: var(--custom-brand-color);">
                <text class="ns-font-size-sm">-{{ $lang('common.currencySymbol') }}</text>
                <text>{{ siteItem.promotion_money }}</text>
              </text>
            </view>
          </view>
          <view class="order-cell order-discount" v-if="maidou.maidou_tag != 1 && parseFloat(siteItem.multiple_discount_money)">
            <view class="tit">多件折扣
							<!-- <text> 满2件享8折</text> -->
						</view>
            <view class="box align-right">
              <text class="" style="color: var(--custom-brand-color);">
                <text class="ns-font-size-sm">-¥</text>
                <text>{{ siteItem.multiple_discount_money }}</text>
              </text>
            </view>
          </view>
          <view class="order-cell" v-if="orderCreateData.combo_id && siteItem.combo_cheap_price && parseFloat(siteItem.combo_cheap_price)">
            <text class="tit">组合优惠</text>
            <view class="box align-right">
              <text class="" style="color: var(--custom-brand-color);">
                <text class="ns-font-size-sm">-{{ $lang('common.currencySymbol') }}</text>
                <text>{{ siteItem.combo_cheap_price }}</text>
              </text>
            </view>
          </view>
					<view class="order-cell">
						<view class="box align-right order-pay">
							共{{ siteItem.goods_num }}件商品
							<text>
								<!-- 小计：{{ $lang('common.currencySymbol') }}<text class="pay-money"> {{ siteItem.pay_money | moneyFormat }}</text> -->
								小计：{{ $lang('common.currencySymbol') }}<text class="pay-money"> {{ parseFloat(orderPaymentData.pay_money) > 0  ? parseFloat(siteItem.pay_money):siteItem.balance_money }}</text>
							</text>
						</view>
					</view>
				</view>
			</view>
			</template>
			<view class="site-wrap" v-if="maidou.maidou_tag != 1 && couponShow">
				<view class="site-footer" style="padding-bottom: 0;">
					<view class="order-cell my-coupon">
						<text class="tit">优惠券</text>
						<view class="box text-overflow" @click="openSiteCoupon()">
							<text v-if="orderPaymentData.goodscoupon_id != 0" class="ns-text-color-gray">
								<text class="inline">满减优惠</text>
								<text class="ns-text-color inline">
									{{ orderPaymentData.goodscoupon_money }} 元
								</text>
							</text>
							<text class="ns-text-color-gray" v-else-if="useCoupon">有可用优惠券</text>
							<text class="ns-text-color-gray" v-else>无可用优惠券</text>
						</view>
						<text class="iconfont iconright"></text>
					</view>
				</view>
			</view>
      <!--  免税额度信息    -->
      <view class="site-wrap tax-exemption" v-if="false">
        <view class="site-header">免税额度信息</view>
        <view class="site-body">
          <view class="order-cell">
            <input type="number" placeholder="请输入今年已使用免税额度（元）" class="tax-exemption-input"></input>
            <view class="box align-right">
              <text class="tax-exemption-query" @click="$refs.popupTaxExemptionChannel.open()">如何查询</text>
            </view>
          </view>
        </view>
        <view class="tax-exemption-tip">根据海关规定，个人年度跨境免税额度为26000元，请如实填写已使用额度，<text class="tax-exemption-tip-text">否则可能因无法清关导致下单失败。</text></view>
      </view>
			<view class="site-wrap payment-methods" v-if="maidou.maidou_tag==1">
				<view class="item" :class="{'disable':method.disable}" v-for="method in paymentMethods" @click="changePayment(method)">
					<image class="icon" :src="method.icon"></image>
					<view class="title">
						<text class="text">{{method.name}}</text>
						<text class="desc">{{method.desc}}</text>
					</view>
<!--					<image class="checkbox" v-show="paymentMethod==method.key" :src="$util.img('public/static/youpin/maidou/get.png')"></image>-->
<!--					<image class="checkbox" v-show="paymentMethod!=method.key" :src="$util.img('public/static/youpin/maidou/noneget.png')"></image>-->
          <uni-icons class="checkbox" type="checkbox-filled" :color="paymentMethod==method.key ? 'var(--custom-brand-color)' : '#ccc'" size="18"></uni-icons>
				</view>
			</view>
			<view class="site-wrap payment-methods" v-else>
				<view class="item" :class="{'disable':method.disable}" v-for="method in otherPaymentMethods" @click="changePayment(method)">
					<image class="icon" :src="method.icon"></image>
					<view class="title">
						<text class="text">{{method.name}}</text>
						<text class="desc">{{method.desc}}</text>
					</view>
<!--					<image class="checkbox" v-show="paymentMethod==method.key" :src="$util.img('public/static/youpin/maidou/get.png')"></image>-->
<!--					<image class="checkbox" v-show="paymentMethod!=method.key" :src="$util.img('public/static/youpin/maidou/noneget.png')"></image>-->
          <uni-icons class="checkbox" type="checkbox-filled" :color="paymentMethod==method.key ? 'var(--custom-brand-color)' : '#ccc'" size="18"></uni-icons>
				</view>
			</view>
			<view class="order-submit bottom-safe-area">
				<view class="order-settlement-info">
					<view class="money-info">
						<!-- <text class="ns-text-color-gray">共{{ orderPaymentData.goods_num }}件，</text> -->
						<text>实付金额：</text>
						<text class="text-color" v-if="paymentMethod=='WECHAT' || paymentMethod=='BALANCE'">
							{{ $lang('common.currencySymbol') }}
							<text class="money pay_money">{{ Number(orderPaymentData.pay_money) || Number(orderPaymentData.balance_money) }}</text>
							<!-- <text class="money pay_money">{{ orderPaymentData.pay_money | moneyFormat }}</text> -->
						</text>
						<text class="text-color" v-else>
							<text class="money pay_money">{{ maidou.maidou_pay_num }}迈豆</text>
						</text>
					</view>
					<view class="total-discount" v-if="maidou.maidou_tag != 1 && parseFloat(orderPaymentData.total_account_money) > 0">合计已优惠 ¥{{orderPaymentData.total_account_money}}</view>
				</view>
				<view class="submit-btn"><button type="primary" size="mini" @click="showGoWeixin">确认支付</button></view>
			</view>
		</view>
		<!-- 优惠券弹窗 -->
		<uni-popup ref="couponPopup" type="bottom" class="coupon-popup-father">
			<view class="coupon-popup popup">
				<view class="popup-header">
					<view><text class="tit">优惠券</text></view>
					<view class="coupon-instructions-close">
						<view class="align-right"><text class="iconfont iconguanbi coupon-close" @click="closePopupCoupon('couponPopup')"></text></view>
					</view>
				</view>
				<view class="flex-center ns-padding-bottom u-padding-30">
					<view v-for="(item, index) in couponTitData" :key="index" class="uni-tab-item" :data-current="index" >
						<view class="uni-tab-item-title" :class="item.status == currentTab ? 'uni-tab-item-title-active high-text-color' : ''" @click="ontabtapCoupon(item)">{{ item.name }}
							<view class="line"></view>
						</view>
					</view>
				</view>
				<scroll-view scroll-y="true" class="popup-body">
					<view class="empty-box" v-if="myCoupon.length == 0">
						<image :src="$util.img('public/static/youpin/empty_coupon.png')" mode="" />
						<view class="empty-info">暂无可用优惠券</view>
					</view>
					<template>
						<view class="coupon-box">
							<view class="coupon-list" v-for="(item, index) in myCoupon" :key="index" :style="{'background-image':`url(${currentTab ? $util.img('public/static/youpin/coupon_ysy.png'):$util.img('public/static/youpin/coupon_no.png')})`}" @click="selectCoupon(item)">
								<view class="coupon-left">
									<view>
										<text :style="{color: currentTab ? 'var(--custom-brand-color)':'#fff'}">￥</text>
										<text :style="{color: currentTab ? 'var(--custom-brand-color)':'#fff'}">{{ parseFloat(item.money) }}</text>
									</view>
								</view>
								<view class="coupon-right">
									<view class="coupon-name">{{ item.goodscoupon_title }}</view>
									<view class="coupon-time">有效期：{{ item.end_time }}</view>
									<image :src="selectCouponId == item.goodscoupon_id ? $util.img('public/static/youpin/get.png'):$util.img('public/static/youpin/select.png')" mode="" v-if="currentTab" />
								</view>
							</view>
						</view>
					</template>
				</scroll-view>
				<view class="popup-footer"><view class="confirm-btn" :class="currentTab == 0 ? 'disabled':''" @click="popupConfirm('couponPopup')">确定</view></view>
			</view>
		</uni-popup>

    <!--  免税额度查询渠道弹窗  -->
    <uni-popup ref="popupTaxExemptionChannel" class="tax-exemption-channel-popup">
      <view class="tax-exemption-channel-popup-box">
        <view class="tax-exemption-channel-popup-header">
          <view class="tax-exemption-channel-popup-header-title">免税额度查询渠道</view>
          <text class="tax-exemption-channel-popup-header-close iconfont iconclose" @click="$refs.popupTaxExemptionChannel.close()"></text>
        </view>
        <view class="tax-exemption-channel-popup-body">
          <view class="tax-exemption-channel-popup-body-step">
            <view class="tax-exemption-channel-popup-body-step-title">查询步骤：</view>
            <view class="tax-exemption-channel-popup-body-step-content">1.打开微信，搜索并关注”掌上海关“公众号</view>
            <view class="tax-exemption-channel-popup-body-step-content">2.进入公众号后，点击底部菜单”服务“</view>
            <view class="tax-exemption-channel-popup-body-step-content">3.在服务菜单中选择”跨境个人消费“</view>
            <view class="tax-exemption-channel-popup-body-step-content">4.输入身份证号和姓名进行实名认证</view>
            <view class="tax-exemption-channel-popup-body-step-content">5.认证通过后即可查看个人年度免税额度使用情况</view>
          </view>
          <view class="tax-exemption-channel-popup-body-tip">
            <view class="tax-exemption-channel-popup-body-tip-title">温馨提示：</view>
            <view class="tax-exemption-channel-popup-body-tip-content">根据海关总署公告2019年第90号，跨境电子商务零售进口商品的单次交易限值为5000元，年度交易限值为26000元。</view>
          </view>
        </view>
        <view class="tax-exemption-channel-popup-footer">
          <view class="tax-exemption-channel-popup-footer-btn" @click="$refs.popupTaxExemptionChannel.close()">我知道了</view>
        </view>
      </view>
    </uni-popup>

		<!-- 未支付返回上一页提示弹窗 -->
		<uni-popup ref="popupCoupon" class="my-popup-dialog">
		    <view class="popup-dialog">
				<view class="popup-dialog-header">提示</view>
				<view class="popup-dialog-body">选择优惠券部分商品将无法参与“多件折扣”，是否使用优惠券？</view>
				<view class="popup-dialog-footer">
					<view class="button white" @click="comfirmCoupon">确定</view>
					<button class="button red" @click="comfirmCouponCancel">取消</button>
				</view>
			</view>
		</uni-popup>

		<!-- 未支付返回上一页提示弹窗 -->
		<uni-popup ref="popup" class="my-popup-dialog">
		    <view class="popup-dialog">
				<view class="popup-dialog-header">提示</view>
				<view class="popup-dialog-body">是否放弃本次付款？</view>
				<view class="popup-dialog-footer">
					<view class="button white" @click="toBack()">放弃</view>
					<button class="button red" @click="closePopup()">继续付款</button>
				</view>
			</view>
		</uni-popup>
		<!-- 取消支付弹窗 -->
		<uni-popup ref="popupToList" class="my-popup-dialog" :mask-click="false">
		    <view class="popup-dialog">
				<view class="popup-dialog-header">提示</view>
				<view class="popup-dialog-body">{{limit_pay_tips}}</view>
				<view class="popup-dialog-footer">
					<button class="button red" @click="toWaitPayList()">我知道了</button>
				</view>
			</view>
		</uni-popup>
		<!-- 优惠券使用说明弹窗 -->
		<uni-popup ref="couponInstructions" class="coupon-instructions">
		    <view class="popup-dialog">
				<view class="popup-dialog-header">优惠券使用说明</view>
				<view class="popup-dialog-body">
					<scroll-view scroll-y="true" class="coupon-instructions-popup-body">
						<rich-text :nodes="couponInstructionsContent"></rich-text>
					</scroll-view>
				</view>
				<view class="popup-dialog-footer">
					<button class="button red" @click="closeCouponInstructionsPopup()">我知道了</button>
				</view>
			</view>
		</uni-popup>

		<!-- 迈豆支付密码弹窗 -->
		<uni-popup ref="payPassword" :custom="true">
			<view class="pay-password">
				<block v-if="orderPaymentData.member_account.is_pay_password == 0">
					<view class="title">为了您的账户安全,请先设置您的支付密码</view>
					<view class="tips">可到"个人中心-设置-支付密码设置"中设置</view>
					<view class="btn ns-bg-color ns-border-color" @click="setPayPassword">立即设置</view>
					<view class="btn white ns-border-color ns-text-color" @click="noSet">暂不设置</view>
				</block>
				<block v-else>
					<view class="popup-title">
						<image class="cha_close" :src="$util.img('public/static/youpin/maidou/cha.png')" mode="" @click="close_pay()"></image>
						<view class="title">请输入支付密码</view>
					</view>
					<view class="money-box" v-if="maidou.maidou_tag!=1 || paymentMethod == 'BALANCE'">
						<view class="total-fee">总金额￥{{ Number(orderPaymentData.pay_money) || Number(orderPaymentData.balance_money) }}</view>
						<view class="balance">(当前余额￥{{orderPaymentData.member_account.balance_money}})</view>
					</view>
          <view class="money-box" v-if="paymentMethod == 'MAIDOU'">
            <view class="total-fee">总金额{{maidou.maidou_pay_num}}迈豆</view>
            <view class="balance">(当前余额{{maidou.canuse_maidou}}迈豆)</view>
          </view>
					<view class="password-wrap">
						<myp-one :maxlength="6" :is-pwd="true" @input="input" ref="input" :auto-focus="isFocus" type="box"></myp-one>
						<view class=" ns-text-color ns-font-size-sm forget-password error-tips" v-if="errMsg">{{errMsg}}</view>
						<view class="align-right"><text class="ns-text-color ns-font-size-sm forget-password" @click="setPayPassword">忘记密码</text></view>
					</view>
				</block>
			</view>
		</uni-popup>

    <!-- 在app中需要提示用户跳转微信小程序支付 -->
    <uni-popup ref="popupGoWeixin" class="my-popup-dialog">
      <view class="popup-dialog">
        <view class="popup-dialog-header">提示</view>
        <view class="popup-dialog-body">需要跳转到微信APP，使用先迈小程序支付订单金额</view>
        <view class="popup-dialog-footer">
          <button class="button white" @click="$refs.popupGoWeixin.close()">取消</button>
          <view class="button red" @click="comfirmGoWeixin">去支付</view>
        </view>
      </view>
    </uni-popup>

		<loading-cover ref="loadingCover"></loading-cover>
	</view>
</template>

<script>
import payment from '../public/js/payment.js';
import uniPopup from '@/components/uni-popup/uni-popup.vue';
import mypOne from '@/components/myp-one/myp-one.vue';
import golbalConfig from "../../../common/mixins/golbalConfig";
// #ifdef H5
import {isOnXianMaiApp} from "../../../common/js/h5/appOP";
// #endif
import UniIcons from "../../../components/uni-icons/uni-icons.vue";

export default {
	components: {
    UniIcons,
		uniPopup,
		mypOne,
	},
	data() {
		return {
			timeTip: '选择配送时间',
			time:null,
			navHeight: 0,
			couponTitData:[
				{
					name:'可用优惠券',
					status:1
				},
				{
					name:'不可使用优惠券',
					status:0
				}
			],
			currentTab:1,
			couponInstructionsContent: '',
			choiceWechatAdderError:false,  //直接选择微信地址失败
			ischoiceWechatAdder:false,  //是否直接选择微信地址
			postWechatAdder:false,  //微信地址添加到后台请求完成
			maidou: {},
			paymentMethod: 'WECHAT',
      // #ifdef H5
      isOnXianMaiApp:isOnXianMaiApp,
      // #endif
		};
	},
	computed: {
		themeStyle() {
			return 'theme-' + this.$store.state.themeStyle;
		},
		paymentMethods() {
			return [{
				icon: this.$util.img('public/static/youpin/paySuccess-icon.png'),
				name: '微信支付',
				desc: '',
				key: 'WECHAT',
				disable: false
			}, {
				icon: this.$util.img('public/static/youpin/balance.png'),
				name: '余额支付',
				desc: `(当前余额￥${this.orderPaymentData.member_account.balance_money}）`,
				key: 'BALANCE',
				disable: this.orderCreateData.is_balance == 1 ? Number(this.orderPaymentData.member_account.balance_money) < Number(this.orderPaymentData.balance_money) : Number(this.orderPaymentData.member_account.balance_money) < Number(this.orderPaymentData.pay_money)
			},{
				icon: this.$util.img('public/static/youpin/maidou.png'),
				name: '迈豆支付',
				desc: `(当前迈豆${this.maidou.canuse_maidou}）`,
				key: 'MAIDOU',
				disable: this.maidou.maidou_pay != 1
			}]
		},
		otherPaymentMethods(){
			return [{
				icon: this.$util.img('public/static/youpin/paySuccess-icon.png'),
				name: '微信支付',
				desc: '',
				key: 'WECHAT',
				disable: false
			},{
				icon: this.$util.img('public/static/youpin/balance.png'),
				name: '余额支付',
				desc: `(当前余额￥${this.orderPaymentData.member_account.balance_money}）`,
				key: 'BALANCE',
				disable: this.orderCreateData.is_balance == 1 ? Number(this.orderPaymentData.member_account.balance_money) < Number(this.orderPaymentData.balance_money) : Number(this.orderPaymentData.member_account.balance_money) < Number(this.orderPaymentData.pay_money)
			}]
		}
	},
	onLoad() {
		uni.getSystemInfo({
		  success: res => {
			//导航高度
			let navHeight = res.statusBarHeight + 46;
			this.navHeight = navHeight;
			// #ifdef H5
			if(!this.isOnXianMaiApp){
        this.navHeight=0;
      }
			// #endif
		  },
		  fail(err) {
			console.log(err);
		  }
		})
	},
	onShow() {},
	mixins: [payment,golbalConfig],
	methods: {
		/**
			 * 变更商品数量
			 * @param {Object} params
			 */
			cartNumChange(num, params) {
				uni.showLoading({
					title: '加载中',
					mask: true
				});
				this.modifyFlag = true;
				if(this.orderPaymentData.cart_ids ) {
					// 入口：购物车
					let item = this.orderPaymentData.shop_goods_list[params.siteIndex].goods_list[params.goodsIndex]
				   if (num < 1) num = 1;
				this.$api.sendRequest({
					url: '/api/cart/edit',
					data: {
						num,
						cart_id: item.cart_id
					},
					success: res => {
						if (res.code >= 0) {
							let preNum = item.num
							this.orderPaymentData.shop_goods_list[params.siteIndex].goods_list[params.goodsIndex].num = parseInt(num);
							this.getOrderPaymentData(1) // 重置订单数据
							this.$buriedPoint.purchaseGoods({
								id: item.sku_id,
								action_type: preNum < num ? 0 : 1,
								action_num: [preNum < num ? num - preNum : preNum - num],
								is_goods_page: 0
							})

						} else {
							this.$util.showToast({
								title: res.message
							});
						}
					}
				});
				} else {
					// 入口：详情页
          this.orderCreateData.num=num;
					this.getOrderPaymentData(1)
				}

			},
		bindTimeChange(e) {
			this.timeTip=""+e.target.value;
			this.time = e.target.value;
			this.orderCreateData.delivery[e.currentTarget.dataset.siteid].buyer_ask_delivery_time=this.time;
		},
		toShopDetail(e){
			this.$util.redirectTo('/otherpages/shop/index/index', { site_id: e});
		},
		openPopup(){
			// 未支付返回上一页提示弹窗
			this.$refs.popup.open()
		},
		closePopup(){
			this.$refs.popup.close();
		},
		openCouponInstructionsPopup(){
			// 未支付返回上一页提示弹窗
			this.$refs.couponInstructions.open()
		},
		closeCouponInstructionsPopup(){
			this.$refs.couponInstructions.close();
		},
		close_pay(){
			this.errMsg = ''
			this.$refs.payPassword.close();
		},
		/**
		 * 关闭弹出层
		 * @param {Object} ref
		 */
		closePopupCoupon(ref) {
			if (this.tempData) {
				Object.assign(this.orderCreateData, this.tempData);
				Object.assign(this.orderPaymentData, this.tempData);
				this.tempData = null;
				this.$forceUpdate();
			}
			this.$refs[ref].close();
		},

		toBack() {
			this.closePopup()
			uni.navigateBack()
		},
		toWaitPayList() {
			this.$refs.popupToList.close();
			this.$util.redirectTo("/pages/order/list/list?status=waitpay", {}, "redirectTo")
			uni.removeStorage({
				key: 'orderCreateData',
				success: () => {}
			});
		},
		/**
		 * 一键获取地址
		 */
		getChooseAddress() {
			var that = this;
			if(!this.orderPaymentData.member_address) {
        // #ifdef MP-WEIXIN
			  this.ischoiceWechatAdder=true;
				uni.chooseAddress({
					success: res => {
						console.log('success.res')
						console.log(res)
						if (res.errMsg == 'chooseAddress:ok') {
							this.saveAddress({
								name: res.userName, // 收货人姓名,
								mobile: res.telNumber, // 手机号
								province: res.provinceName, // 省
								city: res.cityName, // 市
								district: res.countyName, // 县
								address: res.detailInfo, // 详细地址
								full_address: res.provinceName + ' ' + res.cityName + ' ' + res.countyName
							});
						} else {
							this.$util.showToast({
								title: res.errMsg
							});
						}
					},
					fail: res => {
						console.log('fail.res')
						console.log(res)
						this.$util.showToast({
							title: '获取微信地址失败',
							success: () => {
								setTimeout(() => {
									that.selectAddress()
								}, 1500)
							}
						});
            this.postWechatAdder=true;
					}
				});
        // #endif
        // #ifdef H5
        this.selectAddress()
        // #endif
			} else {
				this.selectAddress()
			}
		},
		/**
		 * 保存微信地址
		 * @param {Object} params
		 */
		saveAddress(params) {
			this.$api.sendRequest({
				url: '/api/memberaddress/addthreeparties',
				data: params,
				success: res => {
					if (res.code >= 0) {
					} else {
						this.$util.showToast({
							title: res.message
						});
						this.choiceWechatAdderError=true;
					}
				},
        complete: ()=>{
				  this.postWechatAdder=true;
        }
			});
		}

	}
};
</script>

<style lang="scss" scoped>

	/deep/ .uni-navbar--border {
		border-bottom-width: 0;
	}
</style>
<style lang="scss" scoped>
	/deep/ .my-popup-dialog .uni-popup__wrapper-box {
		max-width: 540rpx;
		width: 540rpx;
		border-radius: 20rpx;
	}
	/deep/ .coupon-instructions .uni-popup__wrapper-box {
		max-width: 620rpx;
		width: 620rpx;
		border-radius: 20rpx;
	}
	.popup-dialog {
		overflow: hidden;
		background: #FFFFFF;
		box-sizing: border-box;
		.popup-dialog-header {
			height: 106rpx;
			line-height: 106rpx;
			text-align: center;
			font-size: 36rpx;
			color: #333333;
			font-weight: bold;
		}
		.popup-dialog-body {
			color: #656565;
			text-align: center;
			padding: 0 30rpx;
		}
		.popup-dialog-footer {
			margin: 0 32rpx;
			height: 140rpx;
			display: flex;
			align-items: center;
			justify-content: space-around;
			.button {
				width: 220rpx;
				height: 68rpx;
				line-height: 68rpx;
				text-align: center;
				border-radius: 34rpx;
				box-sizing: border-box;
				margin: 0;
				&.white {
					color: var(--custom-brand-color);
					background: #FFFFFF;
					border: 1rpx solid var(--custom-brand-color);
				}
				&.red {
					color: #FFFFFF;
					background: var(--custom-brand-color);

				}
			}
		}
	}
	.coupon-instructions-popup-body {
		width: 560rpx;
		height: 540rpx;
		margin: 0 auto;
	}
	.coupon-instructions {
		.popup-dialog {
			.popup-dialog-body {
				padding: 0;
			}
			.popup-dialog-footer {
				.button {
					width: 480rpx;
				}
			}
		}
	}
	.cha_close{
		width: 30rpx;
		height: 30rpx;
	}
	.pay_money{
		font-size: 36rpx !important;
	}
</style>


<style lang="scss">
@import '../public/css/payment.scss';
</style>
