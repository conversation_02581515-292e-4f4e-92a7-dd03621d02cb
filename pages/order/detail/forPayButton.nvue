<template>
<view class="agreement">
	<view class="order-operation">
		<view class="btn" @click="getRegisterAggrement()">
			支付
		</view>
	</view>
	<loading-cover ref="loadingCover"></loading-cover>
</view>
</template>

<script>
	import apiurls from '@/common/js/apiurls.js'
	export default {
		components: {
		},
		data() {
			return {
			};
		},
		mounted(){
			if(this.$refs.loadingCover) this.$refs.loadingCover.hide()
		},

		methods: {
			getRegisterAggrement(){
				this.$api.sendRequest({
					url:apiurls.registerAggrementUrl,
					success:res=>{
						if(this.$refs.loadingCover) this.$refs.loadingCover.hide()
						console.log(res)
					},
					fail:err=>{
						if(this.$refs.loadingCover) this.$refs.loadingCover.hide()
					}
				})
			},
		},
		computed: {
		},
		filters: {
		}
	};
</script>
<style lang="scss">
	.btn {
		position: fixed;
		bottom: 5px;
		left: 0;
		right: 0;
		margin: 0 auto;
		width: 327px;
		height: 40px;
		line-height: 40px;
		text-align: center;
		color: #ffffff;
		font-size: 16px;
		background: #F2270C;
		border-radius: 50px;
	}
</style>