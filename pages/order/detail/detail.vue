<template>
	<view class="detail-container" :class="{ themeStyle, 'safe-area': isIphoneX, 'heightvh': myPopupIsShow}" :style="style">
    <!-- #ifdef MP-WEIXIN -->
    <uni-nav-bar left-icon="back" :border="false" @clickLeft="goBack" :statusBar="true" :fixed="true" background-color="var(--custom-brand-color)" color="white">
      <template>
        <view class="page-title">我的订单</view>
      </template>
    </uni-nav-bar>
    <!-- #endif -->
    <!-- #ifdef H5 -->
    <uni-nav-bar left-icon="back" :border="false" @clickLeft="appGoBack" :fixed="true" v-if="isOnXianMaiApp" background-color="var(--custom-brand-color)" color="white">
      <template>
        <view class="page-title">我的订单</view>
      </template>
    </uni-nav-bar>
    <!-- #endif -->
		<!-- 订单状态 -->
		<view class="status-wrap">
			<view class="status-box">
				<view class="status-name-box">
<!--					<image v-if="orderData.order_status_name!='已完成'" class="icon-pay-clock" :src="$util.img('public/static/youpin/order/icon-pay-clock.png')"/>-->
<!--					<image v-if="orderData.order_status_name==='已完成'" class="icon-pay-clock" :src="$util.img('public/static/youpin/order/icon-finished.png')"/>-->
					<text class="status-name">{{ orderData.order_status_name }}</text>
				</view>
				<view v-if="orderData.order_status == 1">订单已支付，请等待发货</view>
				<view v-if="orderData.order_status == 3">订单已发货，请等待收货</view>
				<block v-if="orderData.order_status == 0">
					<view class="desc-box">
<!--						<text class="desc">-->
<!--							需支付：-->
<!--							<text class="ns-font-size-sm">{{ $lang('common.currencySymbol') }}</text>-->
<!--							<text class="ns-font-size-lg strong">{{ orderData.pay_money }}</text>-->
<!--						</text>-->
						<view class="desc">
							<view class="countdown">
								<view class="clockrun">
									<uni-count-down
										:day="orderData.discountTimeMachine.d"
										:hour="orderData.discountTimeMachine.h"
										:minute="orderData.discountTimeMachine.i"
										:second="orderData.discountTimeMachine.s"
										color="#FFFFFF"
										splitorColor="#FFFFFF"
										background-color="transparent"
									/>
								</view>
							</view>
              <text>后取消订单</text>
						</view>
					</view>
					<!-- <view class="operation-group"><view class="operation-btn ns-text-color" @click="operation('orderPay')">去支付</view></view> -->
				</block>
			</view>
		</view>

		<!-- 地址信息 -->
		<view class="address-wrap active">
			<view class="icon">
<!--				<image class="icon-pay-clock" :src="$util.img('public/static/youpin/order/icon-no-pay-address.png')"/>-->
        <text class="icon-pay-clock iconfont icondizhi"></text>
			</view>
			<view class="address-info">
				<view class="info">
          <view class="info-left">
            <view class="info-name">{{ orderData.name }}</view>
            <view class="info-mobile">{{ orderData.mobile || orderData.telephone }}</view>
          </view>
          <uni-icons type="arrowright" size="12" color="rgba(166, 166, 166, 1)" class="info-more"></uni-icons>
				</view>
				<view class="detail">
					<text>{{ orderData.full_address }} {{ orderData.address }}</text>
				</view>
			</view>
		</view>

		<view class="refund-apply-money" v-if="orderData.order_create_type==2">
			<view class="name">总周期数：</view>
			<view class="money">{{ orderData.periodInfo.period_count }}</view>
			<view class="name">当前周期数：</view>
			<view class="money">{{ orderData.periodInfo.period }}</view>
		</view>
		<!-- 店铺 -->
		<view class="site-wrap">
			<view class="site-header">
<!--				<view class="iconfont icondianpu"></view>-->
<!--				<text class="site-name">{{ orderData.order_no }}</text>-->
				<text class="site-name">商品信息</text>
			</view>
			<view class="site-body">

				<view v-for="(goodsItem, goodsIndex) in orderData.order_goods" :key="goodsIndex">
					<view class="goods-wrap">
						<navigator hover-class="none" class="goods-img" :url="'/promotionpages/pintuan/detail/detail?id=' + orderData.pintuan_goods_id" v-if="orderData.order_create_type==8">
							<image :src="$util.img(goodsItem.sku_image)" @error="imageError(goodsIndex)" mode="aspectFill"></image>
						</navigator>
						<navigator hover-class="none" class="goods-img" :url="'/pages/goods/detail/detail?sku_id=' + goodsItem.sku_id" v-else>
							<image :src="$util.img(goodsItem.sku_image)" @error="imageError(goodsIndex)" mode="aspectFill"></image>
						</navigator>
						<view class="goods-info">
							<navigator hover-class="none" v-if="orderData.order_create_type==8" :url="'/promotionpages/pintuan/detail/detail?id=' + orderData.pintuan_goods_id" class="goods-name">{{ goodsItem.sku_name }}</navigator>
							<navigator hover-class="none" v-else :url="'/pages/goods/detail/detail?sku_id=' + goodsItem.sku_id" class="goods-name">{{ goodsItem.sku_name }}</navigator>
							<view class="goods-info-row">
                <view class="goods-info-row-left">
                  <view class="goods-price" v-if="orderData.is_maidou_pay != 1">
                    <text class="unit">{{ $lang('common.currencySymbol') }}</text>
                    <text v-if="!orderData.periodInfo">{{ goodsItem.price }}</text>
                    <text v-if="orderData.periodInfo">{{ orderData.periodInfo.buy_price }}</text>
                  </view>
                  <view class="goods-price" v-if="orderData.is_maidou_pay == 1">
                    <text>{{ goodsItem.price }}迈豆</text>
                  </view>
                </view>
                <view class="goods-sub-section">
                  <view>{{ goodsItem.spec_name }}</view>
                  <view>x{{ goodsItem.num }}</view>
                </view>
              </view>
						</view>
					</view>
					<view class="goods-operation" v-if="!orderData.periodInfo && orderData.order_create_type!=8">
						<block v-if="orderData.is_enable_refund">
							<view class="disinline" v-if="goodsItem.refund_status == 0">
								<view @click.stop="openPopup(goodsItem.order_goods_id,goodsIndex,0)" class="order-box-btn">申请退款</view>
							</view>
							<navigator class="order-box-btn-wrap" v-if="goodsItem.refund_status == 1" hover-class="none" :url="'/otherpages/order/refund_detail/refund_detail?order_goods_id=' + goodsItem.order_goods_id">
								<view class="order-box-btsdn">查看退款</view>
							</navigator>
						</block>
					</view>
					<view class="order-refund">
						<navigator class="order-box-btn-wrap" v-if="goodsItem.aftersale_block.is_enable_aftersale && orderData.order_status == 4" hover-class="none" :url="goodsItem.aftersale_block.status == 1 || goodsItem.aftersale_block.status == 2 ? `/otherpages/order/return_and_exchange/refund_progress?order_goods_id=${goodsItem.order_goods_id}` : `/otherpages/order/return_and_exchange/select_service?order_goods_id=${goodsItem.order_goods_id}&num=${goodsItem.num}`">
							<view class="order-box-btn">{{goodsItem.aftersale_block.status == 1 ? '售后中':'退换'}}</view>
						</navigator>
<!--						<view class="order-box-btn" v-if="orderData.order_status == 4" @click="findFun(goodsItem)">评论</view>-->
					</view>
				</view>
			</view>
		</view>
		<!-- 订单金额 -->
		<view class="order-money">
			<view class="order-cell">
				<text class="tit">共{{ orderData.goods_num }}件商品</text>
				<view class="box align-right">

					<text class="order-cell-right" v-if="orderData.is_maidou_pay == 1">
						{{ orderData.goods_money }} 迈豆
					</text>
					<text class="order-cell-right" v-if="!orderData.periodInfo && orderData.is_maidou_pay != 1">
						<text class="ns-font-size-sm">{{ $lang('common.currencySymbol') }}</text>
						{{ orderData.goods_money }}
					</text>
					<text class="order-cell-right" v-if="orderData.periodInfo">
						<text class="ns-font-size-sm">{{ $lang('common.currencySymbol') }}</text>
						{{ orderData.periodInfo.total_price }}
					</text>
				</view>
			</view>
			<view class="order-cell" v-if="!orderData.periodInfo">
				<text class="tit">运费</text>
				<view class="box align-right">
					<text class="order-cell-right">
						<text class="ns-font-size-sm">{{ $lang('common.currencySymbol') }}</text>
						<text>{{ orderData.delivery_money }}</text>
					</text>
				</view>
			</view>
			<!-- <view class="order-cell" v-if="!orderData.periodInfo && orderData.order_create_type!==5 && orderData.order_create_type!==6">
				<text class="tit">优惠券</text>
				<view class="box align-right">
					<text class="order-cell-right">
						<text class="ns-font-size-sm">优惠 {{ $lang('common.currencySymbol') }}</text>
						<text>{{ orderData.coupon_money }}</text>
					</text>
				</view>
			</view> -->
			<view class="order-cell" v-if="orderData.order_create_type==5">
				<text class="tit">优惠</text>
				<view class="box align-right">
					<text class="order-cell-right">
						<text class="ns-font-size-sm">砍价优惠 {{ $lang('common.currencySymbol') }}</text>
						<text>{{ orderData.bargain_promotion | moneyFormat }}</text>
					</text>
				</view>
			</view>
			<view class="order-cell" v-if="orderData.goodscoupon_id">
				<text class="tit">优惠券</text>
				<view class="box align-right">
					<text class="order-cell-right">
						<text class="pay-money">-￥{{ orderData.goodscoupon_money }}</text>
					</text>
				</view>
			</view>
      <view class="order-cell" v-if="orderData.promotion_money && parseFloat(orderData.promotion_money)">
        <text class="tit">分销商优惠</text>
        <view class="box align-right">
          <text class="order-cell-right">
            <text class="ns-font-size-sm pay-money">-{{ $lang('common.currencySymbol') }}</text>
            <text class="pay-money">{{ orderData.promotion_money }}</text>
          </text>
        </view>
      </view>
      <view class="order-cell" v-if="parseFloat(orderData.multiple_discount_money) > 0">
        <text class="tit">多件折扣</text>
        <view class="box align-right">
          <text class="order-cell-right">
            <text class="ns-font-size-sm pay-money">-{{ $lang('common.currencySymbol') }}</text>
            <text class="pay-money">{{ orderData.multiple_discount_money }}</text>
          </text>
        </view>
      </view>
      <view class="order-cell" v-if="parseFloat(orderData.combo_cheap_price) > 0">
        <text class="tit">组合优惠</text>
        <view class="box align-right">
          <text class="order-cell-right">
            <text class="ns-font-size-sm pay-money">-{{ $lang('common.currencySymbol') }}</text>
            <text class="pay-money">{{ orderData.combo_cheap_price }}</text>
          </text>
        </view>
      </view>
      <view class="order-cell" v-if="parseFloat(orderData.seckill_discount_money) > 0">
        <text class="tit">秒杀优惠</text>
        <view class="box align-right">
          <text class="order-cell-right">
            <text class="ns-font-size-sm pay-money">-{{ $lang('common.currencySymbol') }}</text>
            <text class="pay-money">{{ orderData.seckill_discount_money }}</text>
          </text>
        </view>
      </view>
			<view class="order-cell">
				<text class="tit">{{orderData.order_status != 0 ? '实付金额' : ''}}</text>
				<view class="box align-right">
					<text class="order-cell-right" v-if="orderData.is_maidou_pay == 1">
            <text class="order-cell-right-tip" v-if="orderData.order_status == 0">需付款：</text>
						<text class="pay-money">{{ orderData.pay_money}}迈豆</text>
					</text>
					<text class="order-cell-right" v-if="orderData.is_maidou_pay != 1">
            <text class="order-cell-right-tip" v-if="orderData.order_status == 0">需付款：</text>
						<text class="pay-money">{{ $lang('common.currencySymbol') }}</text>
						<text v-if="!orderData.periodInfo" class="pay-money">{{ orderData.pay_type == 'BALANCE'?orderData.balance_money:orderData.pay_money }}</text>
						<text v-if="orderData.periodInfo" class="pay-money">{{ orderData.periodInfo.total_price }}</text>
					</text>
				</view>
			</view>
		</view>
		<!-- 订单操作 -->
		<view>
			<view class="order-operation">
        <view class="order-box-btn" @click="$util.getCustomerService()">
          客服
        </view>
        <template v-if="orderData.action.length > 0">
          <!-- 已支付待发货状态详情不显示再次购买按钮 -->
          <view v-if="operationItem.action != 'orderBuy'" class="order-box-btn" :class="{ 'order-pay' : operationItem.action == 'orderPay'|| operationItem.action == 'memberTakeDelivery' }" v-for="(operationItem, operationIndex) in orderData.action" :key="operationIndex" @click="operation(operationItem)">
            {{ operationItem.title }}
          </view>
        </template>
        <!-- 已完成状态添加售后按钮 -->
<!--        <view class="order-box-btn" @click="finishFindFun()" v-if="orderData.order_status_name=='已完成'">评论</view>-->
        <view class="order-box-btn" v-if="orderData.order_status_name=='已完成' && !orderData.periodInfo && orderData.order_create_type!=8" @click="$util.getCustomerService()">
          售后
        </view>
        <!--  整单申请退款 整单退款标识  true显示  false隐藏   -->
        <view class="order-box-btn" @click="openPopup(orderData.order_id,null,1)" v-if="orderData.is_order_refund">
          整单退款
        </view>
			</view>
		</view>

		<!-- 订单概况 -->
		<view class="order-summary">
			<view class="order-cell">
				<text class="tit">订单号：</text>
				<view class="box">
					<text class="ns-text-color-black">{{ orderData.order_no }}</text>
					<view class="copy" @click="to_copy_order_no">复制</view>
				</view>
			</view>
			<view class="order-cell">
				<text class="tit">交易号：</text>
				<view class="box">
					<text class="ns-text-color-black">{{ orderData.out_trade_no }}</text>
				</view>
			</view>
			<view class="order-cell">
				<text class="tit">下单时间：</text>
				<view class="box">
					<text class="ns-text-color-black">{{ $util.timeStampTurnTime(orderData.create_time) }}</text>
				</view>
			</view>
			<block v-if="orderData.pay_status > 0">
				<view class="order-cell">
					<text class="tit">支付时间：</text>
					<view class="box">
						<text class="ns-text-color-black">{{ $util.timeStampTurnTime(orderData.pay_time) }}</text>
					</view>
				</view>
				<view class="order-cell" v-if="orderData.app_type_name">
					<text class="tit">支付方式：</text>
					<view class="box">
						<text class="ns-text-color-black">{{ orderData.app_type_name }}</text>
					</view>
				</view>
			</block>
			<view class="order-cell" v-if="orderData.delivery_time > 0">
				<text class="tit">发货时间：</text>
				<view class="box">
					<text class="ns-text-color-black">{{ $util.timeStampTurnTime(orderData.delivery_time) }}</text>
				</view>
			</view>
			<view class="order-cell"  v-if="orderData.package_list.length > 1">
				<text class="tit">物流信息：</text>
				<view class="box">
					<text class="ns-text-color-black">{{ orderData.package_list.length }}个包裹</text>
					<view class="copy" @click="$util.redirectTo('/otherpages/order/parcel/parcel?order_id=' + orderData.order_id)">查看</view>
				</view>
			</view>
			<view class="order-cell" v-if="orderData.package_list.length == 1 && orderData.package_list[0].express_company_name">
				<text class="tit">快递类型：</text>
				<view class="box">
					<text class="ns-text-color-black">{{ orderData.package_list.length>0 &&  orderData.package_list[0].express_company_name }}</text>
				</view>
			</view>
			<view class="order-cell" v-if="orderData.package_list.length == 1 &&  orderData.package_list[0].delivery_no">
				<text class="tit">快递单号：</text>
				<view class="box">
					<text class="ns-text-color-black">{{ orderData.package_list.length>0 &&  orderData.package_list[0].delivery_no }}</text>
					<view class="copy" @click="to_copy_code">复制</view>
				</view>
			</view>
      <view class="order-cell" v-if="orderData.logistics_remark">
        <text class="tit">物流备注：</text>
        <view class="box">
          <text class="ns-text-color-black">{{ orderData.logistics_remark }}</text>
        </view>
      </view>
			<view class="order-cell" v-if="orderData.sign_time > 0">
				<text class="tit">确认收货：</text>
				<view class="box">
					<text class="ns-text-color-black">{{ $util.timeStampTurnTime(orderData.sign_time) }}</text>
				</view>
			</view>
			<view class="order-cell" v-if="orderData.finish_time > 0">
				<text class="tit">完成时间：</text>
				<view class="box">
					<text class="ns-text-color-black">{{ $util.timeStampTurnTime(orderData.finish_time) }}</text>
				</view>
			</view>
			<view class="order-cell">
				<text class="tit">订单备注：</text>
				<view class="box">
					<text class="ns-text-color-black">{{orderData.buyer_message}}</text>
				</view>
			</view>
      <view class="order-cell" v-if="orderData.remark">
        <text class="tit">商家备注：</text>
        <view class="box" style="margin-top: -4rpx;">
          <view class="ns-text-color-black" style="word-break: break-word;">{{orderData.remark}}<view class="copy" @click="$util.copy(orderData.remark)">复制</view></view>
        </view>
      </view>
		</view>
		<loading-cover ref="loadingCover"></loading-cover>
		<!-- 退款弹窗 -->
		<view class="my-popup" v-if="myPopupIsShow">
			<view class="popup-mask"></view>
			<view class="popup-box">
				<view class="popup-dialog" :style="{ marginBottom: keyHeight + 'px'}">
					<form>
						<view class="popup-dialog-header">申请退款</view>
						<view class="popup-dialog-body">
							<textarea
							placeholder="请输入退款理由，最多100字"
							placeholder-style="{color:#CCCCCC,font-size:28rpx}"
							adjust-position="true"
							name="refundRemark"
							v-model="refundRemark"
							@focus="textareaFocus"
							@blur="textareaBlur"
							></textarea>
              <view class="popup-dialog-body-tip" v-if="!complete_order_refund">注意：部分商品退款不退运费，如需退运费请将该订单全部商品申请退款，取消订单。</view>
						</view>
						<view class="popup-dialog-footer">
							<view class="button white" @click="closePopup">取消</view>
							<button class="button red" @click="orderGoodRefund">提交</button>
						</view>
					</form>
				</view>
			</view>
		</view>
		<uni-popup ref="selectPopup" type="bottom" class="select-popup">
      <div class="select-box">
        <view @click="addFun(3)">
          <image :src="$util.img('public/static/youpin/photo-icon.png')" mode="" />
          <view>发图片</view>
        </view>
        <view @click="addFun(4)">
          <image :src="$util.img('public/static/youpin/video-icon.png')" mode="" />
          <view>发视频</view>
        </view>
      </div>
      <image class="close" :src="$util.img('public/static/youpin/seed-close.png')" mode="" @click="closeFun()" />
    </uni-popup>
	</view>
</template>

<script>
import uniCountDown from '@/components/uni-count-down/uni-count-down.vue';
import orderMethod from '../public/js/orderMethod.js';
import globalConfig from 'common/mixins/golbalConfig.js'
import system from "../../../common/js/system";
import golbalConfig from "../../../common/mixins/golbalConfig";
import appInlineH5 from "../../../common/mixins/appInlineH5";
import UniIcons from "../../../components/uni-icons/uni-icons.vue";
// #ifdef H5
import {isOnXianMaiApp} from "../../../common/js/h5/appOP";
// #endif
export default {
	components: {
    UniIcons,
		uniCountDown,
	},
	data() {
		return {
			isIphoneX: false,
			orderId: 0,
			orderData: {
        package_list:[],
				action: []
			},
			refundRemark:"",
			keyHeight: 0,
			myPopupIsShow: false,
			orderIndex:0,
			goods_id: '',
			findGoodArr: [],
      complete_order_refund: false, //用户选择是否整单退款
      // #ifdef H5
      isOnXianMaiApp:isOnXianMaiApp,
      // #endif
      navHeight: 44,
		};
	},
	onLoad(option) {
		if (option.order_id) this.orderId = option.order_id;
    if(this.$util.getPlatform()=='h5' && !this.isOnXianMaiApp){
      this.navHeight = 0
    }
		// #ifdef MP-WEIXIN
    this.navHeight += uni.getSystemInfoSync().statusBarHeight
		// #endif
	},
	async onShow() {
		// 刷新多语言
		this.$langConfig.refresh();
    await system.wait_staticLogin_success();
		this.isIphoneX = this.$util.uniappIsIPhoneX();

		if (uni.getStorageSync('token')) {
			this.getOrderData();
		} else {
			this.$util.redirectTo('/otherpages/shop/home/<USER>');
		}

		uni.removeStorageSync('selectGood');
		uni.removeStorageSync('selectGoodType');
	},
	mixins:[globalConfig,orderMethod,appInlineH5],
  computed:{
    style(){
      let text = `padding-top: ${this.navHeight}px;`;
      for (const argumentsKey in this.themeColorVar) {
        text += `${argumentsKey}:${this.themeColorVar[argumentsKey]};`;
      }
      return text;
    }
  },
	methods: {
    goBack(){
      this.$util.goBack()
    },
		openPopup(order_goods_id,index,type=0){
      this.complete_order_refund = type==1?true:false
			this.orderIndex = index || 0
			this.myPopupIsShow = true
			uni.setStorageSync('order_goods_id',order_goods_id)
		},
		closePopup(){
			this.myPopupIsShow = false
			this.refundRemark = ""
		},
		textareaFocus(e) {
		   //点击输入框时，获取键盘高度，设置距离底部的高度为键盘的高度

			this.keyHeight = e.detail.height
		},
		textareaBlur(e){

			this.keyHeight = 0
			// this.inputClassParent = 'bottom: 0px;';
		},
		/**
		 * 订单退款
		 * @param {Object} order_goods_id
		 */
		async orderGoodRefund(e) {

			const order_goods_id = uni.getStorageSync('order_goods_id')
			if(order_goods_id) {
				if(this.refundRemark=='') {
					this.$util.showToast({
						title: '请输入申请理由',
					});
				} else {
				  await this.$util.subscribeMessage({
					source: this.complete_order_refund ? 'order' : 'order_goods',
					source_id:order_goods_id,
					scene_type:'order_refund_before'
				  })
          uni.showLoading({
            title: '提交中...',
            mask:true
          });
          let params = {
            refund_type: 1,
            refund_remark: this.refundRemark
          }
          if(this.complete_order_refund){
            params.order_id = order_goods_id
          }else{
            params.order_goods_id = order_goods_id
          }
				  this.$api.sendRequest({
					url: '/api/orderrefund/refund',
					data: params,
					success: res => {
            uni.hideLoading()
					  this.$util.showToast({ title: res.message });
					  if (res.code == 0) {
						if(this.orderData.app_type_name != '迈豆支付') {
              if(this.complete_order_refund){
                this.orderData.order_goods.map(item=>{
                  this.$buriedPoint.refundAll({
                    order_no:item.order_no,
                    sku_id:item.sku_id
                  })
                })
              }else{
                this.$buriedPoint.refundAll({
                  order_no:this.orderData.order_goods[this.orderIndex].order_no,
                  sku_id:this.orderData.order_goods[this.orderIndex].sku_id
                })
              }
						}
						this.$util.showToast({
						  title: '申请退款已提交',
						  success: () => {
							setTimeout(() => {
							  this.myPopupIsShow = false
							  this.refundRemark = ""
							  this.$util.redirectTo('/pages/order/activist/activist');
							}, 1500);
						  }
						});
					  } else {
						this.$util.showToast({
						  title: res.message
						});
					  }
					},
					fail: res => {
            uni.hideLoading()
					  this.$util.showToast({
						title: res.message
					  });
					}
				  });
				}
			}
		},
		getOrderData() {
			this.$api.sendRequest({
				url: '/api/order/detail',
				data: {
					order_id: this.orderId
				},
				success: res => {
					uni.stopPullDownRefresh();
					if (res.code >= 0) {
						if (this.$refs.loadingCover) this.$refs.loadingCover.hide();
						this.orderData = res.data;
						if (typeof this.orderData.reset_pay_time == 'number') {
              this.orderData.discountTimeMachine = this.$util.countDown(this.orderData.reset_pay_time);
						}
					} else {
						this.$util.showToast({
							title: '未获取到订单信息!！',
							success: () => {
								// setTimeout(() => {
								// 	this.getOrderData();
								// }, 1500);
							}
						});
					}
				},
				fail: res => {
					uni.stopPullDownRefresh();
					if (this.$refs.loadingCover) this.$refs.loadingCover.hide();
				}
			});
		},
		/**
		 * 下拉刷新
		 */
		onPullDownRefresh() {
			this.getOrderData();
		},
		operation(operationItem) {
			let action = operationItem.action
			switch (action) {
				case 'orderPay': // 支付
					if(operationItem.disabled) {
						this.$util.showToast({
							title: operationItem.disabled_tips
						})
					}else{
						this.orderPay(this.orderData, (res) => {
							if(res.code == -11) {
								this.$util.showToast({
									'title': res.message
								})
								let that = this
								setTimeout(() => {
									that.getOrderData();
								}, 1500)
							}
						});
					}
					break;
				case 'orderClose': //关闭
					if(operationItem.disabled) {
						this.$util.showToast({
							title: operationItem.disabled_tips
						})
					}else{
						this.orderClose(this.orderData.order_id, () => {
							this.getOrderData();
						});
					}
					break;
				case 'memberTakeDelivery': //收货
					this.orderDelivery(this.orderData.order_id, () => {
						this.getOrderData();
					});
					break;
				case 'trace': //查看物流

					// #ifdef MP-WEIXIN
					if(this.orderData.package_list && this.orderData.package_list.length == 1 && this.orderData.package_list[0].out_delivery_type == 'wx_mini'){
						this.$util.redirectTo(
							'/pluginspages/logisticsPlugin/logisticsPlugin',
							{ waybillToken:this.orderData.package_list[0].out_delivery_no },
						);
					} else if(this.orderData.package_list && this.orderData.package_list.length > 1 ) {
						this.$util.redirectTo('/otherpages/order/parcel/parcel', {
					    	order_id: this.orderData.order_id
					    });
					} else {
						this.$util.redirectTo('/pages/order/logistics/logistics', {
							order_id: this.orderData.order_id
						});
					}
					// #endif
					// #ifdef H5
					if(this.orderData.package_list && this.orderData.package_list.length == 1) {
						this.$util.redirectTo('/pages/order/logistics/logistics', {
						order_id: this.orderData.order_id
					});
					} else if(this.orderData.package_list && this.orderData.package_list.length > 1) { // 多包裹
						this.$util.redirectTo('/otherpages/order/parcel/parcel', {
						order_id: this.orderData.order_id
					});
					}

					// #endif

					break;
				case 'memberOrderEvaluation': //评价
					this.$util.redirectTo('/otherpages/order/evaluate/evaluate', {
						order_id: this.orderData.order_id
					});
					break;
			}
		},
		imageError(index) {
			this.orderData.order_goods[index].sku_image = this.$util.getDefaultImage().default_goods_img;
			this.$forceUpdate();
		},
		toShopDetail(e) {
			this.$util.redirectTo('/otherpages/shop/index/index', { site_id: e });
		},
    to_copy_order_no(){
		  this.$util.copy(this.orderData.order_no);
    },
    to_copy_code(){
		  this.$util.copy(this.orderData.package_list.length>0 && this.orderData.package_list[0].delivery_no);
    },
		findFun(info) {
      this.$refs.selectPopup.open()
			this.findGoodArr = [{
				goods_id: info.goods_id,
				goods_image: this.$util.img(info.sku_image)
			}]
    },
		finishFindFun() {
			let arr = []
			this.orderData.order_goods.forEach(v => {
				arr.push({
					goods_id: v.goods_id,
					goods_image: this.$util.img(v.sku_image)
				})
			})
			this.findGoodArr = arr
			this.$refs.selectPopup.open()
		},
    closeFun() {
      this.$refs.selectPopup.close()
    },
    addFun(type) {
			uni.setStorageSync('selectGood', JSON.stringify(this.findGoodArr))
			uni.setStorageSync('selectGoodType', 1)
      this.$util.redirectTo(`/promotionpages/seeding/seeding-add/seeding-add?type=${type}&goods_id=${this.goods_id}`);
      this.$refs.selectPopup.close()
    },
    /**
     *分享参数组装
     */
    getSharePageParams() {
      return this.$util.unifySharePageParams('/otherpages/shop/home/<USER>', this.shareTitle || '先迈商城',
          '',{}, this.$util.img(this.shareImg) || this.$util.img('public/static/youpin/home_share.jpg'))
    }
	},
  /**
   * 自定义分享内容
   * @param {Object} res
   */
  onShareAppMessage(res) {
    let { title, link, imageUrl, query } = this.getSharePageParams()
    return this.$buriedPoint.pageShare(link , imageUrl, title);
  },
	filters: {
		abs(value) {
			return Math.abs(parseFloat(value)).toFixed(2);
		},
		// 转化时间字符串
		timeStr(val) {
			var h = parseInt(val / 3600).toString();
			var m = parseInt((val % 3600) / 60).toString();
			if (m.length == 1) {
				m = '0' + m;
			}
			if (h.length == 1) {
				h = '0' + h;
			}
			return h + ':' + m;
		},
		/**
		 * 金额格式化输出
		 * @param {Object} money
		 */
		moneyFormat(money) {
			return parseFloat(money).toFixed(2);
		}
	}
};
</script>

<style lang="scss">
@import '../public/css/detail.scss';
</style>
<style scoped>
/deep/ .uni-page {
	overflow: hidden;
}
/deep/ .uni-navbar{
  position: fixed;
  left: 0;
  top: 0;
  z-index: 1;
}
.countdown{
  margin-right: 6rpx;
}
.countdown .clockrun >>> .uni-countdown  {
	display: flex;
	justify-content: center;
	align-items: center;
	height: 64rpx;
	padding: 0;
  font-size: 32rpx;
}
.countdown .clockrun >>> .uni-countdown__number {
	background: #000;
	/* // #690b08 */
	padding: 0;
	margin: 0;
	border: none;
	font-weight: bold;
  font-size: 32rpx;
}

.countdown .clockrun >>> .uni-countdown__splitor {
	padding: 0;
	color: #000;
	font-weight: bold;
  font-size: 32rpx;
}

.countdown .clockrun >>> .uni-countdown__splitor.day {
	width: initial;
	font-weight: bold;
  font-size: 32rpx;
}

/deep/ .uni-popup__wrapper-box {
	max-width: 620rpx;
	width: 620rpx;
}
</style>
<style lang="scss" scoped>
	.my-popup {
		position: fixed;
		top: 0;
		left: 0;
		width: 100%;
		height: 100vh;
		z-index: 2;
		display: flex;
		align-items: center;
		justify-content: center;
	}
	.popup-mask {
		position: absolute;
		top: 0;
		left: 0;
		width: 100%;
		height: 100vh;
		background-color: rgba($color: #000000, $alpha: 0.75);
	}
	.popup-box {
		width: 620rpx;
		z-index: 3;
	}
	.popup-dialog {
		overflow: hidden;
		//height: 580rpx;
		background: #FFFFFF;
		border-radius: 20rpx;
		box-sizing: border-box;
		.popup-dialog-header {
			height: 106rpx;
			line-height: 106rpx;
			text-align: center;
			font-size: 36rpx;
			color: #333333;
			font-weight: bold;
		}
		.popup-dialog-body {
			textarea {
				width: 556rpx;
				padding: 15rpx;
				box-sizing: border-box;
				margin: 0 auto;
				border: 1px solid #CCCCCC;
				height: 260rpx;
				border-radius: 8rpx;
			}
      &-tip{
        color: var(--custom-brand-color);
        font-size: 24rpx;
        line-height: 1.5;
        padding: 0 32rpx;
        box-sizing: border-box;
        margin-top: 10rpx;
      }
		}
		.popup-dialog-footer {
			margin: 0 32rpx;
			height: 140rpx;
			display: flex;
			align-items: center;
			justify-content: space-around;
			.button {
				width: 220rpx;
				height: 68rpx;
				line-height: 68rpx;
				text-align: center;
				border-radius: 34rpx;
				box-sizing: border-box;
				margin: 0;
				&.white {
					color: var(--custom-brand-color);
					background: #FFFFFF;
					border: 1rpx solid var(--custom-brand-color);
				}
				&.red {
					color: #FFFFFF;
					background: var(--custom-brand-color);

				}
			}
		}
	}

	.heightvh {
		overflow-y: hidden;
	}
	.refund-apply-money {
		display: flex;
		align-items: center;
		justify-content: space-between;
		padding: 0 20rpx;
		margin: 0 20rpx;
		margin-bottom: 20rpx;
		height: 100rpx;
		border-radius: 20rpx;
		background-color: #FFFFFF;
		.name {
			font-weight: bold;
			color: #333333;
			font-size: 28rpx;
		}
		.money {
			.text {
				font-weight: normal;
				font-size: 26rpx;
			}
			font-weight: bold;
			color: var(--custom-brand-color);
			font-size: 36rpx;
		}
	}
	.select-popup{
		/deep/ .uni-popup__wrapper-box {
			max-width: initial !important;
			width: initial !important;
		}
	}
	.select-box{
    position: relative;
    height: 580rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    &>view:first-child{
      flex: 1;
      display: flex;
      flex-direction: column;
      margin-left: 110rpx;
      image{
        width: 200rpx;
        height: 200rpx;
      }
      view{
        width: 200rpx;
        text-align: center;
      }
    }
    &>view:last-child{
      flex: 1;
      display: flex;
      flex-direction: column;
      image{
        width: 200rpx;
        height: 200rpx;
      }
      view{
        width: 200rpx;
        text-align: center;
      }
    }
  }
  .close{
    position: absolute;
    width: 32rpx;
    height: 32rpx;
    right: 36rpx;
    top: 28rpx;
  }
  .page-title{
    width: 360rpx;
    overflow:hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    -o-text-overflow:ellipsis;
    text-align: center;
    color: white;
  }
</style>
