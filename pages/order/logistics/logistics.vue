<template>
 	<view :class="themeStyle" :style="[themeColorVar]">
<!-- 		<scroll-view class="order-nav" :scroll-x="true" :show-scrollbar="false">-->
<!-- 			<view v-for="(packageItem, packageIndex) in packageList" :key="packageIndex" class="uni-tab-item" @click="ontabtap(packageIndex)">-->
<!-- 				<text class="uni-tab-item-title" :class="packageIndex == currIndex ? 'uni-tab-item-title-active ns-border-color  ns-text-color' : ''"> {{ packageItem.package_name }}</text>-->
<!-- 			</view>-->
<!-- 		</scroll-view>-->
 			<view v-for="(packageItem, packageIndex) in packageList" :key="packageIndex" class="swiper-item">
 				<view class="container">
<!-- 					<view class="goods-wrap">-->
<!-- 						<view class="body">-->
<!-- 							<view class="goods" v-for="(goodsItem, goodsIndex) in packageItem.goods_list" :key="goodsIndex">-->
<!-- 								<view class="goods-img" @click="toGoodsDetail(goodsItem.sku_id)">-->
<!-- 									<image :src="$util.img(goodsItem.sku_image)" @error="imageError(packageIndex, goodsIndex)" mode="aspectFill"></image>-->
<!-- 								</view>-->
<!-- 								<view class="goods-info">-->
<!-- 									<view @click="toGoodsDetail(goodsItem.sku_id)" class="goods-name">{{ goodsItem.sku_name }}</view>-->
<!-- 									<view class="goods-sub-section">-->
<!-- 										<view>-->
<!-- 											<text>-->
<!-- 												<text class="iconfont iconclose"></text>-->
<!-- 												{{ goodsItem.num }}-->
<!-- 											</text>-->
<!-- 										</view>-->
<!-- 									</view>-->
<!-- 								</view>-->
<!-- 							</view>-->
<!-- 						</view>-->
<!-- 					</view>-->

 					<view class="express-company-wrap">
 						<view class="company-logo"><image :src="$util.img(packageItem.express_company_image)" mode=""></image></view>
 						<view class="info">
              <view class="no">
                <text>
                  快递编号：
                  <text>{{ packageItem.delivery_no }}</text>
                </text>
								<view class="copy-no" @click="$util.copy(packageItem.delivery_no)">复制</view>
<!--                <text class="iconfont iconfuzhi" @click="$util.copy(packageItem.delivery_no)"></text>-->
              </view>
              <view class="company">
                快递公司：
 								<text>{{ packageItem.express_company_name }}</text>
 							</view>
 						</view>
 					</view>

 					<view class="track-wrap" :class="{'track-wrap-empty': !packageItem.trace.success || packageItem.trace.list.length<1}">
 						<block v-if="packageItem.trace.success && packageItem.trace.list.length>0">
 							<view class="track-item" v-for="(traceItem, traceIndex) in packageItem.trace.list" :data-theme="themeStyle" :class="traceIndex==0?'active':''" :key="traceIndex">
 								<view class="dot" :class="traceIndex==0?'dot-color':''"></view>
 								<view class="msg">
 									<view class="text" :class="traceIndex==0?'text-color':''">{{ traceItem.remark }}</view>
 									<view class="time">{{ traceItem.datetime }}</view>
 								</view>
 							</view>
 						</block>
 						<block v-else>
              <view class="track-empty">
                <image :src="$util.img('/public/static/youpin/order/empty-logistics.png')" class="track-empty-img"></image>
                <view class="fail-wrap ns-font-size-sm">{{ packageItem.trace.reason }}</view>
              </view>
 						</block>
 					</view>
 				</view>
 			</view>
 		<loading-cover ref="loadingCover"></loading-cover>
 	</view>
 </template>

 <script>
 import golbalConfig from "../../../common/mixins/golbalConfig";

 export default {
   mixins:[golbalConfig],
 	data() {
 		return {
 			orderId: '',
 			packageList: [],
 			currIndex:0,
 			status:0,
			refund: 0,
			delivery_no: '',
			company_id: ''
 		};
 	},
 	onLoad(option) {
		if (option.order_id) {
			this.orderId = option.order_id
			this.delivery_no = option.delivery_no || ''  //多包裹传
		}
 		if (option.refund) {
			this.refund = option.refund;
			this.delivery_no = option.delivery_no;
			this.company_id = option.company_id;
		}
 	},
 	onShow() {
 		// 刷新多语言
 		this.$langConfig.refresh();
 		// 判断登录
		if(this.refund) {
			this.getRefundInfo();
		}else{
			this.getPackageInfo();
		}
 	},
	computed: {
		themeStyle(){
			return 'theme-'+this.$store.state.themeStyle
		}
	},
 	methods: {
 		ontabtap(e) {
 			this.currIndex = e
 			// let index = e.target.dataset.current || e.currentTarget.dataset.current;
 			// this.orderStatus = this.statusList[index].status;
 			// if (this.orderStatus == '') this.mergePayOrder = [];
 			// this.$refs.loadingCover.show();
 			// this.$refs.mescroll.refresh();
 		},
 		getPackageInfo() {
 			this.$api.sendRequest({
 				url: '/api/order/package',
 				data: {
 					order_id: this.orderId,
					delivery_no: this.delivery_no
 				},
 				success: res => {
 					if (res.code >= 0) {
 						this.packageList = res.data;
 						this.packageList.forEach(item => {
 							item.status = this.status++
 						})
 						if (this.$refs.loadingCover) this.$refs.loadingCover.hide();
 					} else {
 						this.$util.showToast({
 							title: '未获取到订单包裹信息!！',
 							success: () => {
 								setTimeout(() => {
 									this.$util.redirectTo('/pages/order/list/list', {}, 'redirectTo');
 								}, 1500);
 							}
 						});
 					}
 				},
 				fail: res => {
 					if (this.$refs.loadingCover) this.$refs.loadingCover.hide();
 				}
 			});
 		},
		getRefundInfo() {
			let that = this
			this.$api.sendRequest({
				url: this.$apiUrl.logistics,
				data: {
					delivery_no: this.delivery_no,
					company_id: this.company_id
				},
				success: (res) => {
					let info = {}
					let data = res.data
					info.delivery_no = data.delivery_no
					info.express_company_name = data.company.company_name
					info.express_company_image = data.company.logo
					info.trace = data.trace
					that.packageList.push(info)
					if (this.$refs.loadingCover) this.$refs.loadingCover.hide();
				},
 				fail: res => {
 					if (this.$refs.loadingCover) this.$refs.loadingCover.hide();
 				}
			})
		},
 		toGoodsDetail(e){
 			this.$util.redirectTo('/pages/goods/detail/detail',{sku_id:e})
 		},
 		imageError(packageIndex, goodsIndex) {
 			this.packageList[packageIndex].goods_list[goodsIndex].sku_image = this.$util.getDefaultImage().default_goods_img;
 			this.$forceUpdate();
 		},
    /**
     *分享参数组装
     */
    getSharePageParams() {
      return this.$util.unifySharePageParams('/otherpages/shop/home/<USER>', this.shareTitle || '先迈商城',
          '',{}, this.$util.img(this.shareImg) || this.$util.img('public/static/youpin/home_share.jpg'))
    }
 	},
   /**
    * 自定义分享内容
    * @param {Object} res
    */
   onShareAppMessage(res) {
     let { title, link, imageUrl, query } = this.getSharePageParams()
     return this.$buriedPoint.pageShare(link , imageUrl, title);
   },
 };
 </script>

 <style lang="scss">
 @import '../public/css/logistics.scss';
 /deep/.uni-scroll-view ::-webkit-scrollbar {
 	 /* 隐藏滚动条，但依旧具备可以滚动的功能 */
 	 display: none;
 	 width: 0;
 	 height: 0;
 	 color: transparent;
 	 background: transparent;
 }
 /deep/::-webkit-scrollbar {
 	 display: none;
 	 width: 0;
 	 height: 0;
 	 color: transparent;
 	 background: transparent;
 }
 </style>
