<template>
	<view :class="[themeStyle, FixedHeight]" :style="[themeColorVar]">
	<!-- #ifdef H5 -->
    <uni-nav-bar left-icon="back" :border="false" @clickLeft="appGoBack" v-if="isOnXianMaiApp">
      <template>
        <view class="page-title">{{goodsSkuDetail.goods_name}}</view>
      </template>
    </uni-nav-bar>
	<!-- #endif -->
		<view class="goods-detail">

			<view class="goods-container">
				<!-- 商品媒体信息 -->
				<view class="goods-media">
					<!-- 商品图片 -->
					<view class="goods-img" :class="{ show: switchMedia == 'img' }">
						<swiper class="swiper" @change="swiperChange" :interval="swiperInterval" :autoplay="swiperAutoplay" :circular="true">
							<swiper-item v-for="(item, index) in goodsSkuDetail.sku_images" :key="index">
								<view class="item" @click="previewMedia(index)">
									<image :src="$util.img(item)" @error="swiperImageError(index)" mode="aspectFit" />
								</view>
							</swiper-item>
						</swiper>
						<view class="img-indicator-dots">
							<text>{{ swiperCurrent }}</text>
							<text v-if="goodsSkuDetail.sku_images">/{{ goodsSkuDetail.sku_images.length }}</text>
						</view>
					</view>
					<!-- 商品视频 -->
					<view class="goods-video" :class="{ show: switchMedia == 'video' }">
						<!-- #ifndef H5 -->
						<video :src="$util.img(goodsSkuDetail.video_url)" :poster="$util.img(goodsSkuDetail.sku_image)" objectFit="contain"></video>
						<!-- #endif -->
						<!-- #ifdef H5 -->
						<view class="video-img">
							<image :src="$util.img(goodsSkuDetail.sku_image)" mode=""></image>
							<view class="video-open">
								<view class="iconfont iconarrow-" @click="openVideo"></view>
							</view>
						</view>
						<!-- #endif -->
					</view>

					<!-- 切换视频、图片 -->
					<view class="media-mode" v-if="goodsSkuDetail.video_url != ''">
						<text :class="{ 'media-mode-bg-color': switchMedia == 'video' }" @click="switchMedia = 'video'">{{ $lang('video') }}</text>
						<text :class="{ 'media-mode-bg-color': switchMedia == 'img' }" @click="switchMedia = 'img'">{{ $lang('image') }}</text>
					</view>
					<image v-if="goodsSkuDetail.stock==0" :src="$util.img('public/static/youpin/product-sell-out.png')" class="over"></image>
				</view>
				<view @touchmove.prevent.stop class="videoPopup-box">
					<uni-popup ref="videoPopup" type="center">
						<view class="pop-video">
							<video :src="$util.img(goodsSkuDetail.video_url)" :poster="$util.img(goodsSkuDetail.sku_image)" objectFit="contain"></video>
						</view>
					</uni-popup>
				</view>

				<!-- 限时折扣 -->
				<view v-if="preview == 0 && goodsSkuDetail.promotion_type == 1 && goodsSkuDetail.discountTimeMachine && addonIsExit.discount"
				 class="goods-discount">
					<view class="price-info">
						<view class="discount-price">
							<text class="symbol">{{ $lang('common.currencySymbol') }}</text>
							<text>{{ goodsSkuDetail.discount_price }}</text>
						</view>
						<view class="original-price">
							<text class="price">{{ $lang('common.currencySymbol') }} {{ goodsSkuDetail.price }}</text>
							<text class="sale-num hide-sales">{{ goodsSkuDetail.sale_num }}{{ goodsSkuDetail.unit }}已售</text>
						</view>
					</view>

					<view class="countdown">
						<view class="txt">距结束仅剩</view>
						<view class="clockrun">
							<uni-count-down :day="goodsSkuDetail.discountTimeMachine.d" :hour="goodsSkuDetail.discountTimeMachine.h" :minute="goodsSkuDetail.discountTimeMachine.i"
							 :second="goodsSkuDetail.discountTimeMachine.s" color="#fff" splitorColor="#000" background-color="#000" />
						</view>
					</view>
				</view>
				<view class="group-warp">
				</view>
				<view class="group-wrap">
					<view class="goods-module-wrap" :class="{ discount: preview == 0 && goodsSkuDetail.promotion_type == 1 }">
						<view class="goods-module-wrap-box">
							<view>
								<template v-if="goodsSkuDetail.promotion_type == 0">
									<text class="goods_tag" v-for="tag in tags" :key="tag.id">{{tag.tag_name}}</text>
									<text class="price-symbol">{{ $lang('common.currencySymbol') }}</text>
									<text class="price">{{ goodsSkuDetail.retail_price }}</text>
									<text class="market-price-symbol" v-if="goodsSkuDetail.market_price > 0">{{ $lang('common.currencySymbol') }}</text>
									<text class="market-price" v-if="goodsSkuDetail.market_price > 0">{{ goodsSkuDetail.market_price }}</text>
								</template>
								<!-- <view class="predict" v-if="goodsSkuDetail.is_shop_owner && tagShowStatus"> -->
									<!--<view class="profit" v-if="token && goodsSkuDetail.is_shop_owner && tags.filter(e=>e.key == 'maidou').length == 0"><text>预计收益</text><text>￥{{goodsSkuDetail.shop_owner_brokerage}}</text></view>-->
									<!-- <view class="profit"><text>分销商价</text><text>￥{{goodsSkuDetail.vip_price}}</text></view> -->
								<!-- </view> -->
                 <view class="predict" v-if="goodsSkuDetail.is_shop_owner && tagShowStatus">
                   <view class="profit">
                     <text>分销商价</text>
                     <text>￥{{goodsSkuDetail.vip_price}}</text>
                   </view>
                    <view class="profit profit-two" v-if="token && goodsSkuDetail.is_shop_owner && tags.filter(e=>e.key == 'maidou').length == 0 && parseFloat(goodsSkuDetail.shop_owner_brokerage)>0">
                      <text>立省</text><text>￥{{goodsSkuDetail.shop_owner_brokerage}}</text>
                    </view>
                   <view class="predict-taxes" v-if="goodsSkuDetail.goods_class == 5">{{goodsSkuDetail.cbec_taxes_price == 0 ? '含税' : `税费:￥${goodsSkuDetail.cbec_taxes_price}`}}</view>
                 </view>
                <view class="sku-name-box">
                  <text class="sku-name" @click="longpress" @longpress="longpress" >
                    <text class="tag-cross-border" v-if="goodsSkuDetail.goods_class == 5">跨境
                      <image :src="$util.img(goodsSkuDetail.cbec_origin_icon)" mode="aspectFit" class="tag-cross-border--flag" v-if="goodsSkuDetail.cbec_origin_icon"></image>
                      <text class="tag-cross-border--full" v-else>{{goodsSkuDetail.cbec_origin_name[0]}}</text>
                    </text>
                    {{ goodsSkuDetail.goods_name }}
                  </text>
                  <view class="showCopybox" v-if="copytextShow">
                    <view class="copytext"><text  @click="$util.copy(goodsSkuDetail.goods_name,copyCallback)" class="fuzhi">复制</text><text class="quxiao" @click="copytextShow = false">取消</text></view>
                  </view>
                </view>
                <view class="goods-discountTag-warp">
                  <view class="discountTag-operation" @click="discountOpen" v-if="DISCOUNTSHOW">
                    {{ discount_info.goodscoupons && discount_info.goodscoupons.length > 0 ? '领取优惠券' : '查看' }}
                    <text class="iconfont icon iconright" style="font-size: 24rpx"></text>
                  </view>
                  <view class="discountTag-list-list">
                    <scroll-view scroll-x="true" class="scroll-discountTag" v-if="discount_content.length">
                      <block v-for="(item, index) in discount_content" :key="index">
                        <template v-if="item.describe == ''">
<!--                          <view class="discountTag-item-red" v-if="item.describe != ''">{{item.describe}}</view>-->
                          <view class="discountTag-list-item" >{{ item.content }}</view>
                        </template>
                      </block>
                    </scroll-view>
                  </view>

                </view>
							</view>
						</view>
						<view class="maidoutip" v-if="goodsSkuDetail.maidou_tag==1 && tagShowStatus">
							<text>送{{goodsSkuDetail.maidou_txt}}</text>
						</view>
					</view>
          <view class="cross-border-information" v-if="goodsSkuDetail.goods_class == 5">
            <view class="cross-border-information-item">
              <view class="cross-border-information-item-title">品牌</view>
              <view class="cross-border-information-item-desc">{{ goodsSkuDetail.cbec_brand_name }}</view>
            </view>
            <view class="cross-border-information-item">
              <view class="cross-border-information-item-title">产地</view>
              <view class="cross-border-information-item-desc">{{goodsSkuDetail.cbec_origin_name}}</view>
            </view>
            <view class="cross-border-information-item">
              <view class="cross-border-information-item-title">贸易类型</view>
              <view class="cross-border-information-item-desc">{{goodsSkuDetail.cbec_trade_type_text}}</view>
            </view>
            <view class="cross-border-information-item">
              <view class="cross-border-information-item-title">商品类型</view>
              <view class="cross-border-information-item-desc">{{goodsSkuDetail.cbec_sku_goods_type}}</view>
            </view>
          </view>
					<view class="goods-module-wrap">
            <view>
              <text class="introduction" v-if="goodsSkuDetail.introduction">{{ goodsSkuDetail.introduction }}</text>
            </view>
						<view class="adds-wrap">
							<block v-if="Development">
								<text v-if="goodsSkuDetail.is_free_shipping">快递免邮</text>
								<text v-else>快递不免邮</text>
							</block>
							<text class="adds-wrap-volume hide-sales">已售 {{ goodsSkuDetail.sale_num }} {{ goodsSkuDetail.unit }}</text>
						</view>
					</view>
					<button class="group-wrap-share-vip" @click="openSharePopup" v-if="token && goodsSkuDetail.is_shop_owner && tags.filter(e=>e.key == 'maidou').length == 0 && parseFloat(goodsSkuDetail.shop_owner_brokerage)>0">
<!--						<view class="iconfont iconfenxiang"></view>-->
            <image :src="$util.img('public/static/youpin/share-new.png')" class="group-wrap-share-vip-share"></image>
						<view class="group-wrap-share-vip-info">
							<text class="group-wrap-share-vip-info-tip">分享</text>
							<text class="group-wrap-share-vip-info-price">￥{{goodsSkuDetail.shop_owner_brokerage}}</text>
						</view>
					</button>
					<button class="group-wrap-share" :plain="true" @click="openSharePopup" v-else>
<!--						<view class="iconfont iconfenxiang"></view>-->
            <image :src="$util.img('public/static/youpin/share-new.png')" class="group-wrap-share-vip-share"></image>
						<text>分享</text>
					</button>
				</view>


<!--				<ns-fenxiao-good-detail :skuId="skuId" ref="fenxiaoPopup"></ns-fenxiao-good-detail>-->

				<view class="group-wrap group-wrap-padding">
					<!-- 已选规格 ( 不属于新人专区或者属于新人专区并且是第一次下单 ) -->
					<view class="goods-cell selected-sku-spec" v-if="(tags.filter(e=>e.key == 'newhand').length==0 && goodsSkuDetail.sku_spec_format) || (goodsSkuDetail.sku_spec_format && tags.filter(e=>e.key == 'newhand').length>0 && goodsSkuDetail.is_first_buy==1)"
					 @click="chooseSkuspecFormat(goodsSkuDetail.maidou_tag)">
						<view class="box">
							<text class="tit">已选择</text>
							<text v-for="(item, index) in goodsSkuDetail.sku_spec_format" :key="index">{{ item.spec_name }}/{{ item.spec_value_name }}</text>
						</view>
						<text class="iconfont iconright"></text>
					</view>
          <!--  配送地址选择      -->
          <view class="goods-cell" @click="showAddressPopup">
            <view class="box goods-cell-left">
              <text class="tit">配送</text>
              <text>至 {{address_info.full_address}}</text>
            </view>
            <view class="goods-cell-right">
              <view v-if="parseFloat(delivery_money)>0">运费:
                <text style="color:var(--custom-brand-color);">{{delivery_money}}元</text>
              </view>
              <view class="free-postage" v-else>包邮</view>
              <text class="iconfont iconright"></text>
            </view>
          </view>
          <!--    服务      -->
          <view class="goods-cell" @click="showServicePopup" v-if="goodsSkuDetail.goods_class == 5">
            <view class="box goods-cell-left">
              <text class="tit">服务</text>
              <text>禁止二次销售|不支持无理由退换</text>
            </view>
            <view class="goods-cell-right">
              <text class="iconfont iconright"></text>
            </view>
          </view>
					<!-- #ifdef MP-WEIXIN -->
					<view v-if="goodsSkuDetail.sku_images" class="goods-cell">
					  <guarantee-bar  pageType="goods_detail" :goodsName="goodsSkuDetail.goods_name" :goodsImg="goodsSkuDetail.sku_images && goodsSkuDetail.sku_images.length ? $util.img(goodsSkuDetail.sku_images[0]) : ''"
					                  align="between" spaceSize="12" :goodsPrice="`${goodsSkuDetail.retail_price}元`"  :bannerStyle="{fontSize: 'mini',fontOpacity: 'gray',}" style="width: 100%"/>
					</view>
					<!-- #endif -->


					<!-- 商品属性 -->
					<view class="goods-cell" @click="openAttributePopup()" v-if="goodsSkuDetail.goods_attr_format && goodsSkuDetail.goods_attr_format.length > 0">
						<view class="box">
							<text class="tit">规格参数</text>
							<!-- 							<text>{{ goodsSkuDetail.goods_attr_format[0].attr_name }} {{ goodsSkuDetail.goods_attr_format[0].attr_value_name }}...</text> -->
						</view>
						<text class="iconfont iconright"></text>
					</view>

					<view @touchmove.prevent.stop>
						<uni-popup ref="attributePopup" type="bottom">
							<view class="goods-attribute-popup-layer">
								<text class="title">规格参数</text>
								<scroll-view scroll-y class="goods-attribute-body">
									<view class="item ns-border-color-gray" v-for="(item, index) in goodsSkuDetail.goods_attr_format" :key="index">
										<text class="ns-text-color-gray">{{ item.attr_name }}</text>
										<text class="value">{{ item.attr_value_name }}</text>
									</view>
								</scroll-view>
								<view class="button-box"><button type="primary" @click="closeAttributePopup()">确定</button></view>
							</view>
						</uni-popup>
					</view>
				</view>

        <view class="buyer-info" v-if="!is_audit_mode && buyerList.length && false">
          <view class="buyer-info-title">
            <view>买家秀（{{buyerList.length}}）</view>
            <view @click="buyersMoreFun(false)" v-if="buyerList.length">更多<text class="iconfont icon iconright" style="font-size: 28rpx"></text></view>
          </view>
          <view class="buyers-box" v-if="buyerList.length">
            <view class="buyers-list" v-for="(item, index) in buyerList" :key="index" @click="buyersDetailFun(item)">
              <view class="buyers-list-left">
                <view class="buyers-list-left-top">
                  <image :src="item.headimg"></image>
                  <view>{{item.nickname}}</view>
                </view>
                <view class="buyers-list-left-bottom">{{item.title}}</view>
              </view>
              <image :src="item.image" class="buyers-list-right"></image>
            </view>
          </view>
          <view class="buyers-not" v-else>
            <view class="buyers-not-tip">快成为首席体验官吧~</view>
            <view class="buyers-not-op" @click="buyersMoreFun(true)">发布买家秀</view>
          </view>
        </view>

        <!-- 组合套餐 （新使用）-->
        <view class="group-wrap combo-info" v-if="combos.length>0">
          <view class="combo-info-title">
            <image :src="$util.img('public/static/youpin/goods/money.png')" class="combo-info-title-icon"></image>
            <view>组合推荐</view>
          </view>
          <view class="combo-info-box">
            <view class="combo-info-box-group">
              <view class="combo-info-box-group-one" v-for="(item,index) in combos">
                <view class="combo-info-box-group-one-img">
                  <view v-for="(info,j) in item.goods_info" class="combo-info-box-group-one-img-row" @click="$util.toProductDetail(info)">
                    <image :src="$util.img(info.image)" mode=""/>
                    <view class="combo-info-box-group-one-img-row-name">{{info.goods_name}}</view>
                  </view>
                </view>
<!--                <view class="combo-info-box-group-one-text overtext-hidden-one">{{item.name}}</view>-->
                <view class="combo-info-box-group-one-price">
                  <view class="combo-info-box-group-one-price-left">
                    <view class="combo-info-box-group-one-price-left-top" v-if="item.combo_price_min!=item.combo_price_max">
                      <text class="combo-info-box-group-one-price-left-top-symbol">￥</text>{{item.combo_price_min}} <text class="combo-info-box-group-one-price-left-top-center">~</text> <text class="combo-info-box-group-one-price-left-top-symbol">￥</text>{{item.combo_price_max}}
                    </view>
                    <view class="combo-info-box-group-one-price-left-top" v-else>
                      <text class="combo-info-box-group-one-price-left-top-symbol">￥</text>{{item.combo_price_min}}
                    </view>
                    <view class="combo-info-box-group-one-price-left-bottom">
                      <text class="combo-info-box-group-one-price-left-bottom-name">组合价</text>
                      <text class="combo-info-box-group-one-price-left-bottom-thrift">最高可省￥{{item.cheap_price}}</text>
                    </view>
                  </view>
                  <view class="combo-info-box-group-one-price-right" @click="toComboDetail(item)">查看详情</view>
                </view>
              </view>
            </view>
          </view>
        </view>

				<!-- 营销活动(废弃) -->
				<view class="group-wrap" v-if="false && preview == 0 && couponList.length">
					<!-- 优惠券 -->
					<!-- <view class="goods-coupon goods-cell">
						<view class="box">
							<text class="tit">优惠券</text>
							<text class="ns-text-color">领取优惠劵</text>
						</view>
						<text class="get-coupon ns-border-color ns-border-color-gray ns-text-color" @click="openCouponPopup()">领取</text>
					</view>

					<view @touchmove.prevent.stop>
						<uni-popup ref="couponPopup" type="bottom">
							<view class="goods-coupon-popup-layer">
								<text class="tax-title ns-text-color-black" @click="closeCouponPopup()">
									优惠券
									<text class="iconfont iconclose"></text>
								</text>
								<scroll-view class="coupon-body" scroll-y>
									<view class="body-item ns-gradient-diy-goods-list" v-for="(item, index) in couponList" :key="index" :data-theme="themeStyle">
										<view class="item-price ns-gradient-detail-coupons-right-border" :data-theme="themeStyle">
											<text class="price" v-if="item.discount > 0">
												<text class="price-num">{{ item.discount }}折</text>
											</text>
											<text class="price" v-else>
												￥
												<text class="price-num">{{ item.money }}</text>
											</text>
											<view class="sub" v-if="item.at_least > 0">满{{ item.at_least }}元使用</view>
										</view>
										<view class="item-info">
											<view class="info-box">
												<text v-if="item.discount > 0" class="sub">{{ '满' + item.at_least + '享' + item.discount }}折优惠</text>
												<text v-else-if="item.money > 0" class="sub">{{ '满' + item.at_least + '减' + item.money }}</text>
												<text v-else class="sub">无门槛优惠券</text>
												<view class="sub" v-if="item.validity_type == 0">有效期至 {{ $util.timeStampTurnTime(item.end_time) }}</view>
												<view class="sub" v-else>领取之日起{{ item.fixed_term }}天内有效</view>
											</view>
											<view class="item-btn ns-bg-color" v-if="item.useState != 2" :data-theme="themeStyle" @click="receiveCoupon(item, index)">
												{{ item.useState != 0 ? '已领取' : '领取' }}
											</view>
										</view>
									</view>
									<view class="free_div"></view>
								</scroll-view>
								<view class="button-box"><button type="primary" @click="closeCouponPopup()">确定</button></view>
							</view>
						</uni-popup>
					</view> -->

					<!-- 满减 -->
					<view class="goods-cell" v-if="manjianList && manjianList.rule_json && addonIsExit.manjian" @click="openManjianPopup()">
						<view class="box">
							<text class="tit">满减</text>
							<text>{{ manjianList.manjian_name }}...</text>
						</view>
						<text class="iconfont iconright"></text>
					</view>
					<view @touchmove.prevent.stop v-if="addonIsExit.manjian">
						<uni-popup ref="manjianPopup" type="bottom">
							<view class="manjian-popup-layer">
								<text class="title">满减</text>
								<scroll-view scroll-y class="manjian-body">
									<view class="item ns-border-color-gray" v-for="(item, index) in manjianList.rule_json" :key="index">
										<text class="manjian-icon ns-bg-color">满减</text>
										<text class="value">{{ '满' + item.money + '减' + item.discount_money }}</text>
									</view>
								</scroll-view>
								<view class="button-box"><button type="primary" @click="closeManjianPopup()">确定</button></view>
							</view>
						</uni-popup>
					</view>

					<!-- 商家服务 -->
					<view class="goods-cell service" @click="openMerchantsServicePopup()" v-show="
							shopInfo.shop_baozh == 1 ||
								shopInfo.shop_qtian == 1 ||
								shopInfo.shop_zhping == 1 ||
								shopInfo.shop_erxiaoshi == 1 ||
								shopInfo.shop_tuihuo == 1 ||
								shopInfo.shop_shiyong == 1 ||
								shopInfo.shop_shiti == 1 ||
								shopInfo.shop_xiaoxie == 1
						">
						<view class="box">
							<text class="tit">{{ $lang('service') }}</text>
							<text v-if="shopInfo.shop_baozh == 1">保证服务</text>
							<text v-if="shopInfo.shop_qtian == 1">7天退换</text>
							<text v-if="shopInfo.shop_zhping == 1">正品保障</text>
							<text v-if="shopInfo.shop_erxiaoshi == 1">两小时发货</text>
							<text v-if="shopInfo.shop_tuihuo == 1">退货承诺</text>
							<text v-if="shopInfo.shop_shiyong == 1">试用中心</text>
							<text v-if="shopInfo.shop_shiti == 1">实体验证</text>
							<text v-if="shopInfo.shop_xiaoxie == 1">消协保证</text>
						</view>
						<text class="iconfont iconright"></text>
					</view>

					<view @touchmove.prevent.stop v-show="
							shopInfo.shop_baozh == 1 ||
								shopInfo.shop_qtian == 1 ||
								shopInfo.shop_zhping == 1 ||
								shopInfo.shop_erxiaoshi == 1 ||
								shopInfo.shop_tuihuo == 1 ||
								shopInfo.shop_shiyong == 1 ||
								shopInfo.shop_shiti == 1 ||
								shopInfo.shop_xiaoxie == 1
						">
						<uni-popup ref="merchantsServicePopup" type="bottom">
							<view class="goods-merchants-service-popup-layer">
								<text class="tax-title ns-text-color-black">{{ $lang('service') }}</text>
								<scroll-view scroll-y>
									<view class="item" v-if="shopInfo.shop_baozh == 1">
										<view class="iconfont icondui ns-text-color"></view>
										<view class="info-wrap">
											<text class="title">保证服务</text>
											<text class="describe">保证服务</text>
										</view>
									</view>
									<view class="item" v-if="shopInfo.shop_qtian == 1">
										<view class="iconfont icondui ns-text-color"></view>
										<view class="info-wrap">
											<text class="title">7天退换</text>
											<text class="describe">满足7天无理由退换货申请的前提下，包邮商品需要买家承担退货邮费，非包邮商品需要买家承担发货和退货邮费</text>
										</view>
									</view>
									<view class="item" v-if="shopInfo.shop_zhping == 1">
										<view class="iconfont icondui ns-text-color"></view>
										<view class="info-wrap">
											<text class="title">正品保障</text>
											<text class="describe">商品支持正品保障服务</text>
										</view>
									</view>
									<view class="item" v-if="shopInfo.shop_erxiaoshi == 1">
										<view class="iconfont icondui ns-text-color"></view>
										<view class="info-wrap">
											<text class="title">两小时发货</text>
											<text class="describe">付款后2小时内发货</text>
										</view>
									</view>
									<view class="item" v-if="shopInfo.shop_tuihuo == 1">
										<view class="iconfont icondui ns-text-color"></view>
										<view class="info-wrap">
											<text class="title">退货承诺</text>
											<text class="describe">退货承诺</text>
										</view>
									</view>
									<view class="item" v-if="shopInfo.shop_shiyong == 1">
										<view class="iconfont icondui ns-text-color"></view>
										<view class="info-wrap">
											<text class="title">试用中心</text>
											<text class="describe">试用中心</text>
										</view>
									</view>
									<view class="item" v-if="shopInfo.shop_shiti == 1">
										<view class="iconfont icondui ns-text-color"></view>
										<view class="info-wrap">
											<text class="title">实体验证</text>
											<text class="describe">实体验证</text>
										</view>
									</view>
									<view class="item" v-if="shopInfo.shop_xiaoxie == 1">
										<view class="iconfont icondui ns-text-color"></view>
										<view class="info-wrap">
											<text class="title">消协保证</text>
											<text class="describe">如有商品质量问题、描述不符或未收到货等，您有权申请退款或退货，来回邮费由卖家承担</text>
										</view>
									</view>
								</scroll-view>
								<view class="button-box"><button type="primary" @click="closeMerchantsServicePopup()">确定</button></view>
							</view>
						</uni-popup>
					</view>
				</view>

				<!-- 组合套餐 （废弃）-->
				<view v-if="false && preview == 0 && bundling.length && bundling[0].bl_name && addonIsExit.bundling">
					<view class="group-wrap" @click="openBundlingPopup()">
						<view class="goods-cell" @click="openBundlingPopup()">
							<view class="box">
								<text class="tit">组合套餐</text>
								<text>{{ bundling[0].bl_name }}</text>
							</view>
							<text class="iconfont iconright"></text>
						</view>

						<view class="combo-goods-wrap ns-text-color-gray">
							<navigator hover-class="none" class="goods ns-border-color-gray" :url="'/pages/goods/detail/detail?sku_id=' + skuId">
								<image :src="$util.img(goodsSkuDetail.sku_image)" @error="imageError()" />
								<text>¥{{ goodsSkuDetail.price }}</text>
							</navigator>
							<view class="iconfont iconadd1 ns-text-color-gray"></view>
							<block v-for="(item, index) in bundling[0].bundling_goods" :key="index">
								<template v-if="index < 3">
									<navigator hover-class="none" class="goods ns-border-color-gray" :url="'/pages/goods/detail/detail?sku_id=' + item.sku_id">
										<image :src="$util.img(item.sku_image)" @error="bundlingImageError(0, index)" />
										<text>¥{{ item.price }}</text>
									</navigator>
								</template>
							</block>
						</view>
					</view>

					<view @touchmove.prevent.stop>
						<uni-popup ref="bundlingPopup" type="bottom">
							<view class="bundling-popup-layer">
								<text class="title">组合套餐</text>
								<scroll-view scroll-y class="bundling-body">
									<block v-for="(item, index) in bundling" :key="index">
										<scroll-view scroll-x>
											<view class="item ns-border-color-gray">
												<navigator hover-class="none" class="value" :url="'/promotionpages/combo/detail/detail?bl_id=' + item.bl_id">
													<text>{{ item.bl_name }}：￥{{ item.bl_price }}</text>
													<view class="right">
														<text class="ns-text-color">查看</text>
														<text class="iconfont iconright"></text>
													</view>
												</navigator>
												<view class="goods-wrap">
													<navigator hover-class="none" class="goods" :url="'/pages/goods/detail/detail?sku_id=' + skuId">
														<image :src="$util.img(goodsSkuDetail.sku_image)" @error="imageError()" />
														<text>¥{{ goodsSkuDetail.price }}</text>
													</navigator>
													<view class="iconfont iconadd1 ns-text-color-gray"></view>
													<block v-for="(goods, goods_index) in item.bundling_goods" :key="goods_index">
														<template v-if="goods_index < 3">
															<navigator hover-class="none" class="goods" :url="'/pages/goods/detail/detail?sku_id=' + goods.sku_id">
																<image :src="$util.img(goods.sku_image)" @error="bundlingImageError(index, goods_index)" />
																<text>¥{{ goods.price }}</text>
															</navigator>
														</template>
													</block>
												</view>
											</view>
										</scroll-view>
									</block>
								</scroll-view>
								<view class="button-box"><button type="primary" @click="closeBundlingPopup()">确定</button></view>
							</view>
						</uni-popup>
					</view>
				</view>

				<!-- 店铺信息 -->
				<block v-if="Development">
					<view class="group-wrap" v-if="preview == 0">
						<view class="shop-wrap">
							<navigator hover-class="none" :url="'/otherpages/shop/index/index?site_id=' + shopInfo.site_id" class="box">
								<view class="shop-logo">
									<image v-if="shopInfo.avatar" :src="$util.img(shopInfo.avatar)" @error="shopInfo.avatar = $util.getDefaultImage().default_shop_img"
									 mode="aspectFit" />
									<image v-else :src="$util.getDefaultImage().default_shop_img" mode="aspectFit" />
								</view>
								<view class="shop-info">
									<text>{{ shopInfo.site_name }}</text>
									<view class="description" v-if="shopInfo.seo_description">{{ shopInfo.seo_description }}</view>
								</view>
							</navigator>
							<navigator hover-class="none" :url="'/otherpages/shop/index/index?site_id=' + shopInfo.site_id" class="box shop-score">
								<text>商品描述{{ shopInfo.shop_desccredit }}</text>
								<text>卖家服务{{ shopInfo.shop_servicecredit }}</text>
								<text>发货速度{{ shopInfo.shop_deliverycredit }}</text>
							</navigator>
							<view class="box">
								<view class="goods-action">
									<navigator hover-class="none" class="ns-text-color ns-border-color" :url="'/otherpages/shop/list/list?site_id=' + shopInfo.site_id">
										全部商品
									</navigator>
									<navigator hover-class="none" class="ns-text-color ns-border-color" :url="'/otherpages/shop/index/index?site_id=' + shopInfo.site_id">
										查看店铺
									</navigator>
								</view>
							</view>
						</view>
					</view>
				</block>

				<!-- 商品评价 -->
				<view class="group-wrap" v-if="preview == 0 && isShowEvaluate">
					<view class="goods-evaluate">
						<view class="tit">
							<view>
								<text>商品评价（{{ goodsSkuDetail.evaluate }}）</text>
							</view>
							<navigator class="ns-text-color" hover-class="none" :url="'/otherpages/goods/evaluate/evaluate?goods_id=' + goodsSkuDetail.goods_id">
								<text>查看更多</text>
								<text class="iconfont iconright"></text>
							</navigator>
						</view>
						<view class="evaluate-item" v-if="goodsEvaluate.content">
							<view class="evaluator">
								<view class="evaluator-face">
									<image v-if="goodsEvaluate.member_headimg" :src="$util.img(goodsEvaluate.member_headimg)" @error="goodsEvaluate.member_headimg = $util.getDefaultImage().default_headimg"
									 mode="aspectFill" />
									<image v-else :src="$util.getDefaultImage().default_headimg" @error="goodsEvaluate.member_headimg = $util.getDefaultImage().default_headimg"
									 mode="aspectFill" />
								</view>
								<text class="evaluator-name">{{ goodsEvaluate.member_name }}</text>
							</view>
							<view class="cont">{{ goodsEvaluate.content }}</view>
							<view class="evaluate-img" v-if="goodsEvaluate.images">
								<view class="img-box" v-for="(item, index) in goodsEvaluate.images" :key="index" @click="previewEvaluate(index, 'images')">
									<image :src="$util.img(item)" mode="aspectFit" />
								</view>
							</view>
							<view class="time">
								<text>{{ $util.timeStampTurnTime(goodsEvaluate.create_time) }}</text>
								<text>{{ goodsEvaluate.sku_name }}</text>
							</view>
						</view>
						<view class="evaluate-item-empty" v-else>该商品暂无评价哦</view>
					</view>
				</view>
				<!-- 详情 -->
				<view class="goods-detail-tab">
					<view class="detail-tab flex-center" v-if="isShowDetailTab">
						<view class="tab-item" :class="detailTab == 'productDetail' ? 'active ns-bg-before' : ''" @click="toPoint"
						 data-id="productDetail">商品详情</view>
						<view class="tab-item" :class="detailTab == 'productSale' ? 'active ns-bg-before' : ''" @click="toPoint" data-id="productSale" v-if="afterSale">售后保障</view>
						<view class="tab-item" :class="detailTab == 'productServe' ? 'active ns-bg-before' : ''" @click="toPoint" data-id="productServe" v-if="service">服务说明</view>
					</view>
					<view class="detail-content">
						<view class="detail-content-item" id="productDetail">
							<view class="goods-details-title">
								<view></view>
								<view>图文详情</view>
								<view></view>
							</view>
							<view class="goods-details" v-if="goodsSkuDetail.goods_content">
<!--								<rich-text :nodes="goodsSkuDetail.goods_content" @click="goodsContentPreviewImage" id="goods_content"></rich-text>-->
                <mphtml  :content="goodsSkuDetail.goods_content" :preview-img="true"/>
							</view>
							<view class="goods-details active" v-else>该商家暂无上传相关详情哦！</view>
						</view>
            <diy-goods-detail-more-goodies :sku_id="skuId"/>
						<view class="detail-content-item" id="productSale" v-if="afterSale">
							<view class="goods-details-title">
								<view></view>
								<view>售后保障</view>
								<view></view>
							</view>
							<view class="goods-details">
								<rich-text :nodes="afterSale"></rich-text>
							</view>
<!--							<view class="goods-details active" v-else>该商品暂无相关售后哦！</view>-->
						</view>
						<view class="detail-content-item" id="productServe" v-if="service">
							<view class="goods-details-title">
								<view></view>
								<view>服务说明</view>
								<view></view>
							</view>
							<view class="goods-details">
								<rich-text :nodes="service"></rich-text>
							</view>
<!--							<view class="goods-details active" v-else>该商品暂无相关服务说明哦！</view>-->
						</view>
					</view>
				</view>

				<!--		<view v-if="preview == 0"><nsGoodsRecommend ref="goodrecommend"></nsGoodsRecommend></view>-->

				<!-- SKU选择 -->
				<ns-goods-sku v-if="!isLoading" ref="goodsSku" @refresh="refreshGoodsSkuDetail" :limitBuy="tags.filter(e=>e.key == 'newhand').length>0"
				 :goods-detail="goodsSkuDetail" :entrance="goodsSkuDetail.maidou_tag && String(goodsSkuDetail.maidou_tag)" @openModel="openModel" @closeModel="closeModel"></ns-goods-sku>

				<!-- 海报 -->
				<view @touchmove.prevent.stop>
					<uni-popup ref="posterPopup" type="bottom" class="poster-layer">
						<template v-if="poster != '-1'">
							<view :style="{ height: posterHeight > 0 ? posterHeight + 80 + 'px' : '' }">
								<view class="image-wrap">
									<image :src="$util.img(poster)" :style="{ height: posterHeight > 0 ? posterHeight + 'px' : '' }" />
								</view>
								<!-- #ifdef MP || APP-PLUS  -->
								<view class="save" @click="saveGoodsPoster()">保存图片</view>
								<!-- #endif -->
								<!-- #ifdef H5 -->
								<view class="save">长按保存图片</view>
								<!-- #endif -->
							</view>
							<view class="close iconfont iconclose" @click="closePosterPopup()"></view>
						</template>
						<view v-else class="msg">{{ posterMsg }}</view>
					</uni-popup>
				</view>

				<share-popup v-if="isShowCanvas" :canvasOptions="canvasOptions" ref="sharePopup" :sharePopupOptions="sharePopupOptions"></share-popup>

				<!-- 分享弹窗 -->
				<!-- <view @touchmove.prevent.stop>
					<uni-popup ref="sharePopup" type="bottom" class="share-popup">
						<view>
							<view class="share-title">分享</view>
							<view class="share-content"> -->
				<!-- #ifdef MP -->
				<!-- <view class="share-box">
									<button class="share-btn" :plain="true" open-type="share">
										<view class="iconfont iconiconfenxianggeihaoyou"></view>
										<text>链接</text>
									</button>
								</view> -->
				<!-- #endif -->
				<!-- <view class="share-box" @click="openPosterPopup">
									<button class="share-btn" :plain="true">
										<view class="iconfont iconpengyouquan"></view>
										<text>保存图片</text>
									</button>
								</view>
							</view>
							<view class="share-footer" @click="closeSharePopup"><text>取消分享</text></view>
						</view>
					</uni-popup>
				</view> -->
				<ns-login ref="login"></ns-login>
			</view>
		</view>
		<loading-cover ref="loadingCover"></loading-cover>
		<!-- 商品底部导航 -->
		<ns-goods-action>
			<template v-if="goodsSkuDetail.goods_state == 1 && goodsSkuDetail.verify_state == 1">

				<!-- 分销客入口 -->
				<!-- <block v-if="!is_join_activity">
          <view v-if="!isDistributor" @click="$util.redirectTo('/otherpages/fenxiaoke/apply/apply')">
            <ns-hint class="distribution-guest" :text="'成为分销客分享该商品有机会赚取¥ ' + goodsSkuDetail.share_brokerage " ></ns-hint>
          </view>
        </block> -->
				<ns-goods-action-icon text="首页" icon="iconshouye" :imgicon="$util.img('public/static/youpin/home-icon.png')" @click="goHome" />
				<!-- <ns-goods-action-icon :text="whetherCollection == 1 ? '已收藏' :'收藏'" :imgicon="whetherCollection == 1 ? $util.img('public/static/youpin/collect-has-icon.png') : $util.img('public/static/youpin/collect-icon.png')" @click="editCollection()"/> -->
				<ns-goods-action-icon text="客服" icon="iconkefu" v-if="addonIsExit.servicer" @click="$util.getCustomerService()" />
				<!--				<ns-goods-action-icon text="客服" icon="iconkefu" v-else open-type="contact" :send-data="contactData" />-->
				<ns-goods-action-icon v-if="goodsSkuDetail.maidou_tag==0 && tags.filter(e=>e.key == 'newhand').length==0" text="购物车"
				 icon="icongouwuche" :imgicon="$util.img('public/static/youpin/shop-icon.png')" :corner-mark="cartCount > 0 ? cartCount + '' : ''"
				 @click="goCart" />

				<template v-if="!isDistributor && goodsSkuDetail.maidou_tag==0 && tags.filter(e=>e.key == 'newhand').length==0 && goodsSkuDetail.is_in_cart && goodsSkuDetail.is_invite_goods == 0">
					<ns-goods-action-button class="goods-action-button" :class="goodsSkuDetail.is_virtual == 0 ? 'active1' : ''" text="加入购物车"
					 background="var(--custom-brand-color-10)" disabledText="加入购物车" :disabled="goodsSkuDetail.stock ? false : true" @click="joinCart" v-if="goodsSkuDetail.is_virtual == 0" />
				</template>
				<template v-if="isDistributor && goodsSkuDetail.maidou_tag==0 && tags.filter(e=>e.key == 'newhand').length==0">
					<!-- 分销客才显示此按钮 -->
					<button :disabled="goodsSkuDetail.stock ? false : true" class="distributor-share-button goods-action-button"
					 :class="[goodsSkuDetail.is_virtual == 0 ? 'active1' : '',goodsSkuDetail.stock ? '':'disabled-share-btn']" :plain="true"
					 open-type="share">
						<ns-goods-action-button text="立即分享" :textPrice="goodsSkuDetail.share_brokerage && isDistributor  ? '赚￥' + goodsSkuDetail.share_brokerage : ''"
						 background="var(--custom-brand-color-10)" disabledText="立即分享" :disabled="goodsSkuDetail.stock ? false : true" v-if="goodsSkuDetail.is_virtual == 0" />
					</button>
				</template>
				<!-- 非新人专享 或者 新人第一次下单 -->
				<ns-goods-action-button v-if="tags.filter(e=>e.key == 'newhand').length==0 || (tags.filter(e=>e.key == 'newhand').length>0 && goodsSkuDetail.is_first_buy==1)"
				 class="goods-action-button" :class="goodsSkuDetail.is_virtual == 0 ? 'active2' : 'active4'" text="立即购买" :textPrice="goodsSkuDetail.purchase_brokerage && isDistributor != 0 ? '赚￥' + goodsSkuDetail.purchase_brokerage : ''"
				 disabledText="立即购买" :disabled="goodsSkuDetail.stock ? false : true" background="var(--custom-brand-color)" @click="buyNow" />
				<ns-goods-action-button v-else class="goods-action-button" :class="goodsSkuDetail.is_virtual == 0 ? 'active2' : 'active4'"
				 text="立即分享" disabledText="立即分享" background="var(--custom-brand-color)" @click="openSharePopup" />

			</template>
			<template v-else>
				<ns-goods-action-button class="goods-action-button active3" disabled-text="该商品已下架" :disabled="true" />
			</template>
		</ns-goods-action>
		<!-- 授权登录弹窗 -->
		<yd-auth-popup ref="ydauth"></yd-auth-popup>
    <ns-login ref="login"></ns-login>
    <uni-coupon-pop ref="couponPop"></uni-coupon-pop>
		<!-- 返回顶部按钮 -->
		<image :src="$util.img('public/static/youpin/to-top.png')" class="to-top" v-show="showTop" @click="scrollToTopNative"></image>
		<!-- 分享的页面不是用户绑定的id,提示无法购物弹窗 -->
		<uni-popup ref="popup" :maskClick="false">
			<view class="popup-dialog">
				<view class="popup-dialog-header">提示</view>
				<view class="popup-dialog-body">你目前只能进入{{shop_name_array.shop_name}}店铺购物，无法进入{{shop_name_array.share_shop_name}}店铺购物！</view>
				<view class="popup-dialog-footer">
					<button class="button red" @click="goHomeClear()">知道了</button>
				</view>
			</view>
		</uni-popup>
		<!-- 分享的页面不是用户绑定的id,提示无法购物弹窗 -->
		<uni-popup ref="popupBan" :maskClick="false">
			<view class="popup-dialog">
				<view class="popup-dialog-header">提示</view>
				<view class="popup-dialog-body">哎哟~系统好像出了点问题，暂时不能支付，请联系客服。</view>
				<view class="popup-dialog-footer">
					<button class="button red" @click="$refs.popupBan.close()">知道了</button>
				</view>
			</view>
		</uni-popup>

    <!--  配置地址选择弹窗  -->
    <uni-popup ref="addressPopup" :maskClick="false" type="bottom" class="address-popup">
      <view class="address-popup-content">
        <view class="address-popup-header">
          <text>选择地址</text>
          <text class="address-popup-header-close iconfont iconclose" @click="closeAddressPopup"></text>
        </view>
        <template v-if="addressList.length">
          <view class="address-popup-list">
            <view class="address-popup-list-one" :class="{'address-popup-list-one-active':addressIndex==index}"
                  v-for="(item,index) in addressList" :key="index" @click="changeAddress(index)">
              <view class="address-popup-list-one-left">
                <uni-icons type="checkbox-filled" :color="addressIndex==index ? 'var(--custom-brand-color)' : 'rgba(204,204,204,1)'" size="20"></uni-icons>
              </view>
              <view class="address-popup-list-one-right">
                <view class="address-popup-list-one-right-one"><text class="address-popup-list-one-right-one-name">{{item.name}}</text>{{item.mobile}}<text v-if="item.is_default" class="address-popup-list-one-right-one-tip">默认</text></view>
                <view class="address-popup-list-one-right-two">{{item.full_address}}{{item.address}}</view>
              </view>
            </view>
          </view>
          <view class="address-popup-op">
            <text @click="toAddAddress" v-if="token">添加新地址</text>
          </view>
        </template>
        <view class="address-popup-empty" v-else>
          <image :src="$util.img('public/static/youpin/goods/address_empty.png')" class="address-popup-empty-img"></image>
          <text class="address-popup-empty-tip">暂无收货地址</text>
          <text class="address-popup-empty-op" @click="toAddAddress" v-if="token">新增收货地址</text>
        </view>
      </view>
    </uni-popup>

    <!-- 跨境商品购物须知 服务信息弹窗   -->
    <uni-popup ref="servicePopup" type="bottom" class="service-popup">
      <view class="service-popup-content">
        <text class="service-popup-content-close iconfont iconclose" @click="closeServicePopup"></text>
        <view class="service-popup-content-header">
          <text class="service-popup-content-header-title">跨境商品购物须知</text>
        </view>
        <view class="service-popup-content-body">
<!--          <mphtml  :content="goodsSkuDetail.goods_content" :preview-img="true"/>-->
          <view class="service-popup-content-body-title">禁止二次销售</view>
          <view class="service-popup-content-body-content">您购买行为必须遭守“合理自用”原则，若消费者购买商品超出个人自用原则或其他违反海关监管规定的情况，将受到执法部门核查，情节严重构成犯罪的，将依法被追究刑事责任。</view>
          <view class="service-popup-content-body-title">特别说明</view>
          <view class="service-popup-content-body-content">跨境商品遵循原产国标准，可能与中国大陆标准存在差异。部分商品无中文标签，详情请参考商品网页或咨询客服。境外产品注重环保，包装简约，可能无塑封或纸盒，使用环保材料。因此，买家收到商品后，如见其包装简洁，运输中可能产生的轻微划痕，请勿诧异，此属正常现象，敬请理解。</view>
          <view class="service-popup-content-body-title">发货说明</view>
          <view class="service-popup-content-body-content">商品下单后会在3-5个工作日发货，如当前保税仓无货系统会自动转至其他仓库进行发货；同订单商品可能涉及不同仓库发货，因此货物配送时效稍有差别，请以实际物流为准；部分偏远地区目前不能送达，敬请谅解。</view>
          <view class="service-popup-content-body-title">不支持无条件退换货</view>
          <view class="service-popup-content-body-content">由于跨境购物自身的特殊性，所售商品不支持任何无理由退换货，不支持无理由拒收，商城订单下单成功后，不支持无条件取消订单，如需取消请尽快联系客服尝试拦截，已出库的商品拦截取消，需缴纳物流拦截费等相关费用，敬请谅解。</view>
          <view class="service-popup-content-body-title">售后服务范围及标准</view>
          <view class="service-popup-content-body-content">收到商品请您当场开箱验货，如未当场验货，签收24小时后发现产品爆罐破损等问题，我们无法与快递达成协商因而造成理赔失败的后果需要消费者自行承担，为了避免不必要的麻烦，还请您务必当面开箱验货。</view>
          <view class="service-popup-content-body-title">温馨提示</view>
          <view class="service-popup-content-body-content">请确保在合法合规的情况下使用本平台的服务，如有违法违规的情况，平台有权关停、注销账号，并保留追求相关责任的权利。</view>
        </view>
      </view>
    </uni-popup>

    <diy-floating-rolling-order :top="isOnXianMaiApp ? isShowDetailTab ? '168rpx' : '108rpx' : isShowDetailTab ? '80rpx' : '20rpx'" positionType="goods_detail"></diy-floating-rolling-order>
		<!--  升级vip的按钮  -->
<!--		<yp-upgrade-vip-button :is-show="!goodsSkuDetail.is_shop_owner" :positions="{position: 'fixed',right: 0,bottom: '600rpx'}"></yp-upgrade-vip-button>-->
		<diy-share :canvasOptions="canvasOptions_share" ref="sharePopup_share" :sharePopupOptions="sharePopupOptions_share"
		 @childByValue="getShareImg"></diy-share>
    <diy-share-navigate-h5 ref="shareNavigateH5"></diy-share-navigate-h5>
	<!-- <view class="copymask" @click="copymackFunc" v-if="maskShow"></view> -->
	<ns-goods-discount ref="discountPopup" @closeModel="closeModel" @openModel="openModel" :sku_id="goodsSkuDetail.sku_id"/>
	</view>
</template>

<script>
	import nsGoodsAction from '@/components/ns-goods-action/ns-goods-action.vue';
	import nsGoodsActionIcon from '@/components/ns-goods-action-icon/ns-goods-action-icon.vue';
	import nsGoodsActionButton from '@/components/ns-goods-action-button/ns-goods-action-button.vue';
	import uniPopup from '@/components/uni-popup/uni-popup.vue';
	import nsGoodsSku from '@/components/ns-goods-sku/ns-goods-sku.vue';
	import nsGoodsRecommend from '@/components/ns-goods-recommend/ns-goods-recommend.vue';
	import uniCountDown from '@/components/uni-count-down/uni-count-down.vue';
	import detail from '../public/js/detail.js';
	import scroll from '@/common/mixins/scroll-view.js';
	// import nsFenxiaoGoodDetail from '@/components/ns-fenxiao-goods-detail/ns-fenxiao-goods-detail.vue';
	import globalConfig from 'common/mixins/golbalConfig.js'
	import sharePopup from '@/components/share-popup/share-popup.vue';
	import diyShare from '@/components/diy-share/diy-share.vue';
	import diyShareNavigateH5 from '@/components/diy-share-navigate-h5/diy-share-navigate-h5'
  import uniNavBar from '@/components/uni-nav-bar/uni-nav-bar.vue';
  import appInlineH5 from "../../../common/mixins/appInlineH5";
  import nsGoodsDiscount from '@/components/ns-goods-discount/ns-goods-discount.vue'
  import diyFloatingRollingOrder from '@/components/diy-floating-rolling-order/diy-floating-rolling-order.vue';
  import UniIcons from "../../../components/uni-icons/uni-icons.vue";
  import mphtml from "../../../components/mp-html/mp-html.vue"
  import diyGoodsDetailMoreGoodies from "../../../components/diy-goods-detail-more-goodies/diy-goods-detail-more-goodies.vue";
  // #ifdef H5
  import {isOnXianMaiApp} from "../../../common/js/h5/appOP";
  // #endif
	export default {
		components: {
      UniIcons,
			nsGoodsAction,
			nsGoodsActionIcon,
			nsGoodsActionButton,
			uniPopup,
			nsGoodsSku,
			nsGoodsRecommend,
			uniCountDown,
			// nsFenxiaoGoodDetail,
			sharePopup,
			diyShare,
      diyShareNavigateH5,
      uniNavBar,
	  nsGoodsDiscount,
      diyFloatingRollingOrder,
      mphtml,
      diyGoodsDetailMoreGoodies
		},
		data() {
			return {
				isShowEvaluate: false,
				isShowDetailTab: false,
				// #ifdef H5
				isOnXianMaiApp:isOnXianMaiApp,
				// #endif
				copytextShow: false,
				fixedHeight: false, // 是否固定高度禁止上下滑动
			};
		},
		onShow() {
			if (uni.getStorageSync('is_register')) {
				this.$util.toShowCouponPopup(this)
				uni.removeStorageSync('is_register');
			}
		},
		computed: {
			DISCOUNTSHOW() {
				return this.discount_info.goodscoupons && this.discount_info.goodscoupons.length > 0 || this.discount_info.multiple_discounts && this.discount_info.multiple_discounts.length > 0 || this.discount_info.multiple_discounts && this.discount_info.multiple_discounts.length > 0
			},
			// 弹窗后固定高度禁止上下滑动
			FixedHeight() {
				return this.fixedHeight ? 'FixedHeight' : ''
			},
			//是否为需发布状态
			Development() {
				// return this.$store.state.Development;
				return false
			},
			themeStyle() {
				return 'theme-' + this.$store.state.themeStyle;
			},
			addonIsExit() {
				return this.$store.state.addonIsExit;
			},
		},
		mixins: [detail, scroll, globalConfig,appInlineH5],
		methods: {
			openModel() {
				this.fixedHeight = true
			},
			closeModel() {
				this.fixedHeight = false
			},
			// 弹起活动折扣弹窗
			discountOpen() {
			  this.$refs.discountPopup.getGoodsSkuDetail(this.goodsSkuDetail.sku_id)
			  this.$refs.discountPopup.open()
			},
			longpress() {
				this.copytextShow = true
			},
			copyCallback() {
				this.copytextShow = false
			},
      showAddressPopup(){
        this.$refs.addressPopup.open()
      },
      closeAddressPopup(){
        this.$refs.addressPopup.close()
      },
      showServicePopup(){
        this.$refs.servicePopup.open()
      },
      closeServicePopup(){
        this.$refs.servicePopup.close()
      },
      async changeAddress(index){
        this.addressIndex = index
        this.address_info = this.addressList[index];
        this.district_id = this.addressList[index].district_id;
        uni.setStorageSync('member_address',this.addressList[index]);
        this.closeAddressPopup()
        await this.getDeliveryMoney();
      },
      toAddAddress(){
        this.$util.redirectTo('/otherpages/member/address/address')
      },
      async getAddressList(){
        let res = await this.$api.sendRequest({
          url: '/api/memberaddress/page',
          async:false,
          data: {
            page: 1,
            page_size: 20
          }
        })
        if(res.code ==0){
          this.addressList = res.data.list;
          // 用户是否有设置默认地址
          let has_default = this.addressList.filter(item=>item.is_default==1).length>0 ? true : false;
          if(has_default){
            this.addressList.map((item,index)=>{
              if(item.is_default == 1 && !this.district_id){
                this.addressIndex = index
                this.address_info = item;
                this.district_id = item.district_id;
              }
            })
          }else{
            if(this.addressList.length>0 && !this.district_id){
              this.addressIndex = 0;
              this.address_info = this.addressList[0];
              this.district_id = this.addressList[0].district_id;
            }
          }
        }
      }
		}
	};
</script>

<style lang="scss">
	@import '../public/css/detail.scss';

	.ns-text-color {
		color: var(--custom-brand-color) !important;
	}
</style>
<style scoped>
	/deep/ .uni-video-cover {
		background: none;
	}

	/deep/ .uni-video-cover-duration {
		display: none;
	}

	/deep/ .uni-video-cover-play-button {
		border-radius: 50%;
		border: 4rpx solid #fff;
		width: 120rpx;
		height: 120rpx;
		background-size: 30%;
	}

	.poster-layer>>>.uni-popup__wrapper-box {
		max-height: initial !important;
	}

	/deep/ .sku-layer .uni-popup__wrapper-box {
		overflow-y: initial !important;
	}

	.goods-discount .countdown .clockrun>>>.uni-countdown__number {
		min-width: 32rpx;
		height: 32rpx;
		text-align: center;
		line-height: 32rpx;
		background: #000;
		/* // #690b08 */
		border-radius: 4px;
		display: inline-block;
		padding: 4rpx;
		margin: 0;
		border: none;
	}

	.goods-discount .countdown .clockrun>>>.uni-countdown__splitor {
		width: 10rpx;
		height: 32rpx;
		line-height: 36rpx;
		text-align: center;
		display: inline-block;
		color: #000;
	}

	.goods-discount .countdown .clockrun>>>.uni-countdown__splitor.day {
		width: initial;
	}

	/deep/ .uni-popup__wrapper.uni-custom .uni-popup__wrapper-box {
		max-height: unset !important;
	}

	/deep/ .goods-action-button.active1 {
		padding-left: 10px;
	}

	/deep/ .goods-action-button.active2 {
		padding-right: 10px;
	}

	/deep/ .goods-action-button.active3 {
		padding: 0 10px;
	}

	/deep/ .goods-action-button.active4 {
		padding: 0 10px;
	}

	/deep/ .goods-action-button.active1 .action-buttom-wrap {
    color: var(--custom-brand-color);
		border: 1px solid  var(--custom-brand-color);
		border-radius: 40rpx;
		box-sizing: border-box;
		margin-right: 14rpx;
	}

	/deep/ .goods-action-button.active2 .action-buttom-wrap {
		border-radius: 40rpx;
		box-sizing: border-box;
	}

	/deep/ .goods-action-button.active3 .action-buttom-wrap {
		border-radius: 36px;
		margin: 20rpx 0;
	}

	/deep/ .goods-action-button.active4 .action-buttom-wrap {
		border-radius: 36px;
	}

	/* 底部分享按钮 */
	.distributor-share-button {
		width: auto !important;
		height: auto !important;
		border: none;
		margin: 0;
		line-height: auto;
		;
		padding: 0;
	}

	.disabled-share-btn {
		background-color: transparent !important;
	}

	.to-top {
		width: 144rpx;
		height: 152rpx;
		position: fixed;
		right: 0;
		bottom: 200rpx;
	}

	.maidoutip {
		font-size: 28rpx;
		color: var(--custom-brand-color);
	}
</style>
<style lang="scss" scoped>
.FixedHeight {
	width: 100%;
	height: 100vh;
	position: fixed;
	top: 0;
	bottom: 0;
	left: 0;
	right: 0;
}
	.goods_tag {
		padding: 5rpx;
		background: linear-gradient(270deg, var(--custom-brand-color-80) 0%, var(--custom-brand-color) 100%);
		border-radius: 8rpx;
		font-size: 20rpx;
		color: #fff;
		text-align: center;
		line-height: 24rpx;
		margin-right: 10rpx;
	}

	.predict {
		line-height: 1;
		display: flex;
		align-items: center;
    width: 100%;

		.profit {
      display: flex;
      align-items: center;
      background: var(--custom-brand-color);
      font-size: 24rpx;
      font-weight: 400;
      line-height: 32px;
      color: rgba(255, 255, 255, 1);
      height: 32rpx;
      opacity: 1;
      border-radius: 20rpx;
      padding: 0 20rpx;
      box-sizing: border-box;
      &-two{
        background: var(--custom-brand-color-10);
        color: var(--custom-brand-color);
        margin-left: -14rpx;
      }
		}
    &-taxes{
      font-size: 24rpx;
      color: #999;
      margin-left: auto;
    }
	}
  .page-title{
    width: 360rpx;
    overflow:hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    -o-text-overflow:ellipsis;
    text-align: center;
  }
  .sku-name-box {
	  width: 100%;
	  position: relative;
  }
  .showCopybox {
	  position: absolute;
	  top: -66rpx;
	  left: 45%;
	.copytext {
	  text-align: center;
	  border-radius: 10rpx;
	  color: #fff;
	  font-size: 24rpx;
	  position: relative;
	  &::after {
		content: '';
		display: block;
		width: 20rpx;
		height: 20rpx;
		background: #1F2022;
		position: absolute;
		bottom: -16rpx;
		left: 30rpx;
		transform: rotate(45deg);
	  }
	}
	.fuzhi {
		border-right: 1px solid rgba(255,255,255,0.7);
		padding:16rpx 24rpx;
		background: #1F2022;
		border-radius: 5px 0 0 5px;
	}
	.quxiao {
		padding:16rpx 24rpx;
		background: #1F2022;
		border-radius: 0 5px 5px 0;
	}
  }
  .service-popup{
    /deep/.uni-popup__wrapper{
      border-radius: 40rpx 40rpx 0 0;
    }
    &-content{
      padding: 36rpx 32rpx 0 32rpx;
      box-sizing: border-box;
      min-height: 700rpx;
      position: relative;
      &-close{
        width: 32rpx;
        height: 32rpx;
        font-size: 20rpx;
        background: rgba(229, 229, 229, 1);;
        border-radius: 50%;
        line-height: 34rpx;
        text-align: center;
        color: #fff;
        position: absolute;
        right: 32rpx;
        top: 32rpx;
      }
      &-header{
        display: flex;
        justify-content: center;
        align-items: center;
        &-title{
          font-size: 32rpx;
          font-weight: bold;
          color: #333;
        }
      }
      &-body{
        height: 82vh;
        overflow-y: auto;
        font-size: 24rpx;
        padding-bottom: 24rpx;
        box-sizing: border-box;
        &-title{
          font-size: 24rpx;
          font-weight: bold;
          margin-top: 36rpx;
        }
        &-content{
          font-size: 22rpx;
        }
      }
    }
  }
</style>
