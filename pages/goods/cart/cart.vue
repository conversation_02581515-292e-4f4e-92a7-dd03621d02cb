<template>
	<view :class="themeStyle" :style="[themeColorVar]">

		<view class='nav bg-white' :style="{ height: navHeight + 'px' }">
			<view class='nav-title'>
				<text class="top-operationa" @click="isEdit=!isEdit"
					v-if="cartData.length>0">{{ isEdit ? $lang('complete') : $lang('edit') }}</text>
				<text class="cart_title">购物车</text>
			</view>
		</view>


		<view class="container" :class="isBottom?'bottom':''" :style="{'padding-top':navHeight +'px'}">
			<block v-if="isBottom">
				<!-- <view class="top-operation" @click="isEdit=!isEdit" :style="{top:navHeight + 'px'}">{{ isEdit ? $lang('complete') : $lang('edit') }}</view> -->
				<view class="cart-wrap" v-for="(siteItem, siteIndex) in cartData" :key="siteIndex">
					<view class="cart-header" style="display: none;">
						<view class="iconfont"
							:class="siteItem.checked ? 'iconyuan_checked ns-text-color' : 'iconyuan_checkbox'"
							@click="siteAllElection(!siteItem.checked, siteIndex)"></view>
						<view class="iconfont icondianpu"></view>
						<view class="shop-info">
							<text>{{ siteItem.siteName }}</text>
						</view>
					</view>
					<block v-for="(item, cartIndex) in siteItem.cartList" :key="cartIndex">
						<view class="cart-goods" @touchstart="touchS" @touchmove="touchM" :data-siteIndex="siteIndex"
							:data-cartIndex="cartIndex">
							<view class="goods-wrap"
								:class="{ show: selectSiteIndex == siteIndex && selectCartIndex == cartIndex }">
								<view class="iconfont"
									:class="item.checked ? 'iconyuan_checked ns-text-color' : 'iconyuan_checkbox'"
									@click="singleElection(siteIndex, cartIndex)"></view>
								<view class="goods-img" @click="goProductDetail(item.sku_id)">
									<image :src="$util.img(item.sku_image)"
										@error="imageError(siteIndex,cartIndex)" mode='aspectFit'></image>
								</view>
								<view class="goods-info">
									<view class="goods-name" @click="goProductDetail(item.sku_id)">
										{{ item.sku_name }}
									</view>
									<view class="sku" @click.stop="changeCart(item)" v-if="item.spec_name">
										{{item.spec_name}}
										<uni-icons class="arrow-bottom" type="arrowdown" color="#333" size="12">
										</uni-icons>
									</view>
									<view class="goods-sub-section" :class="{'goods-sub-section-shopper' : is_shopper && item.shop_owner_brokerage != '0.00'}">
										<view class="goods-sub-section-left">
                      <view class="goods-price ns-text-color">
                        <text class="unit">{{ $lang('common.currencySymbol') }}</text>
                        {{ is_shopper ? item.vip_price : item.price_format }}
                      </view>
                      <view class="price-distributor" v-if="is_shopper && item.shop_owner_brokerage != '0.00'"><text>分销已减￥{{item.shop_owner_brokerage}}</text></view>
										</view>
										<view class="cart-number" style="float: right;">
											<uni-number-box :min="1" :max="item.stock" type="cart" :value="item.num"
												size="small" :modifyFlag="modifyFlag"
												@change="cartNumChange($event, { siteIndex, cartIndex })" />
										</view>

									</view>
								</view>
							</view>
							<!-- 左滑出现移入收藏和删除按钮 -->
							<view class="item-operation"
								:class="{ show: selectSiteIndex == siteIndex && selectCartIndex == cartIndex }">
								<!-- <view class="item-moveIn" @click="editCollection(item,siteIndex, cartIndex)">移入 收藏</view> -->
								<view class="item-del ns-bg-color" @click="openPopup(siteIndex, cartIndex)">
									{{ $lang('del') }}</view>
							</view>
						</view>
					</block>
				</view>

				<view class="cart-wrap" v-if="invalidGoods.length">
					<view class="cart-header">
						<view class="shop-info">失效商品</view>
						<view class="cart-operation ns-text-color ns-font-size-sm" @click="clearInvalidGoods">清空失效商品
						</view>
					</view>
					<block v-for="(goodsItem, goodsIndex) in invalidGoods" :key="goodsIndex">
						<view class="cart-goods invalid-goods">
							<view class="invalid-mark">失效</view>
							<view class="goods-wrap">
								<view class="goods-img">
									<image :src="$util.img(goodsItem.sku_image)" mode='aspectFit'></image>
								</view>
								<view class="goods-info">
									<view class="goods-name">{{ goodsItem.sku_name }}</view>
									<!-- <text class="sku"></text>
									<view class="goods-sub-section">
										<text class="goods-price ns-text-color">
											<text class="unit">{{ $lang('common.currencySymbol') }}</text>
											{{ goodsItem.discount_price }}
										</text>
									</view> -->
								</view>
							</view>
						</view>
					</block>
				</view>
			</block>
			<block v-else>
				<view class="cart-empty">
					<ns-empty entrance="cart" :text="$lang('emptyTips')" v-if="token != ''" :fixed="!1"></ns-empty>
					<ns-empty :text="$lang('emptyTips')" :emptyBtn="emptyBtn1" v-else :fixed="!1"></ns-empty>
				</view>
			</block>
      <view>
        <nsGoodsRecommend ref="goodrecommend"></nsGoodsRecommend>
      </view>
		</view>
		<!-- 底部部分 -->
		<view class="cart-bottom" v-if="cartData.length||invalidGoods.length">
			<view class="all-election" @click="allElection">
				<view class="iconfont" :class="checkAll ? 'iconyuan_checked ns-text-color' : 'iconyuan_checkbox'">
				</view>
				<text>{{ $lang('allElection') }}</text>
			</view>
			<view class="settlement-info" v-if="!isEdit">
				<text>
					{{ $lang('total') }}：
					<text class="ns-text-color">
						{{ $lang('common.currencySymbol') }}
						<text class="total-price">{{ totalPrice }}</text>
					</text>
				</text>
				<view v-if="is_shopper && shopOwnerBrokerage != '0.00'" class="preferential-price">
					分销合计优惠￥{{shopOwnerBrokerage}}
				</view>
			</view>
			<view class="operation-btn" v-if="!isEdit">
				<button type="primary" size="mini" @click="settlement"
					v-if="totalCount != 0">{{ $lang('settlement') }}({{ totalCount }})</button>
				<button type="primary" size="mini" @click="settlement" disabled
					v-else>{{ $lang('settlement') }}({{ totalCount }})</button>
			</view>
			<view class="delete-btn" v-if="isEdit" @click="openPopup">{{ $lang('del') }}</view>
		</view>
		<!-- 加载动画 -->
		<loading-cover ref="loadingCover"></loading-cover>
		<!-- 底部tabBar -->
		<diy-bottom-nav type="shop" :site-id="shop_id" v-if="openBottomNav"></diy-bottom-nav>
		<!-- 返回顶部 -->
		<to-top v-if="showTop" @toTop="scrollToTopNative()"></to-top>

		<ns-goods-sku ref="goodsSku" @refresh="refreshGoodsSkuDetail" entrance="cart" :goods-detail="selectCartItem">
		</ns-goods-sku>

		<!-- 提示弹窗 -->
		<diy-uni-popup ref="popup" text="确定要从购物车删除该商品吗？" cancleText="我再想想" @confirm="deleteCart(siteIndex,cartIndex)"></diy-uni-popup>
    <ns-login ref="login"></ns-login>
    <uni-coupon-pop ref="couponPop"></uni-coupon-pop>
	</view>
</template>

<script>
	import uniNumberBox from '@/components/uni-number-box/uni-number-box.vue';
	import diyBottomNav from '@/components/diy-bottom-nav/diy-bottom-nav.vue';
	import diyHorzBlank from '@/components/diy-horz-blank/diy-horz-blank.vue';
	import toTop from '@/components/toTop/toTop.vue';
	import scroll from '@/common/mixins/scroll-view.js';
	import nsGoodsSku from '@/components/ns-goods-sku/ns-goods-sku.vue';
	import apiurls from '@/common/js/apiurls.js'
	import uniPopup from '@/components/uni-popup/uni-popup.vue';
	import nsGoodsRecommend from '@/components/ns-goods-recommend/ns-goods-recommend.vue';
  import system from "../../../common/js/system";
  import golbalConfig from "../../../common/mixins/golbalConfig";
	export default {
		components: {
			uniNumberBox,
			diyBottomNav,
			diyHorzBlank,
			toTop,
			nsGoodsSku,
			uniPopup,
			nsGoodsRecommend
		},
		mixins: [scroll,golbalConfig],
		data() {
			return {
				navHeight: 0,
				isEdit: false,
				startX: 0,
				startY: 0,
				selectSiteIndex: -1,
				selectCartIndex: -1,
				siteIndex: -1,
				cartIndex: -1,
				selectCartItem: null, //选中的对象商品，修改sku
				shop_id: null,
				openBottomNav: false,
				token: '',
				cartData: [], // 购物车
				checkAll: false,
				totalPrice: '0.00',
				totalCount: 0,
				modifyFlag: false,
				isSub: false,
				invalidGoods: [], // 失效商品集合
				emptyBtn1: {
					text: "去逛逛",
					// url: '/pages/login/login/login'
				},
				is_shopper: 0, //是否是店主
				shopOwnerBrokerage:0,
			};
		},
		onLoad() {
			let shop_id = uni.getStorageSync('shop_id');
			this.shop_id = shop_id;
			this.openBottomNav = true;
			uni.getSystemInfo({
				success: res => {
					//导航高度
					let navHeight = res.statusBarHeight + 46;
					this.navHeight = navHeight;
				},
				fail(err) {
					console.log(err);
				}
			})
			if (uni.getStorageSync('is_shopper')) {
				this.is_shopper = uni.getStorageSync('is_shopper');
			}
		},
		async onShow() {
			// 刷新多语言
			this.$langConfig.refresh();
			await system.wait_staticLogin_success();
			// #ifdef H5
			this.$util.toShowLoginPopup(this,null,'/pages/goods/cart/cart');
			// #endif
			if (uni.getStorageSync('token')) this.getCartData();
			if (uni.getStorageSync('is_register')) {
				this.$util.toShowCouponPopup(this)
				uni.removeStorageSync('is_register');
			}
		},
		onReady() {
			if (!uni.getStorageSync('token')) {
				if (this.$refs.loadingCover) this.$refs.loadingCover.hide();
			}
		},
		computed: {
			isBottom() {
				return this.cartData.length > 0 || this.invalidGoods.length > 0
			},

			//vueX页面主题
			themeStyle() {
				return 'theme-' + this.$store.state.themeStyle
			},
			systemInfo() {
				return uni.getSystemInfoSync();
			}
		},
    onReachBottom(){
      this.$refs.goodrecommend.scrollPage()
    },
		methods: {
			// 跳转详情
			goProductDetail(sku_id){
				this.$util.redirectTo('/pages/goods/detail/detail', { sku_id });
			},
			/**
			 * 获取购物车数据
			 */
			getCartData() {
				this.$api.sendRequest({
					url: '/api/cart/lists',
					success: res => {
						if (res.code >= 0) {
							this.token = uni.getStorageSync('token');
							if (res.data.length) this.handleCartData(res.data);
							else this.cartData = [];
						} else {
							this.token = '';
						}
						if (this.$refs.loadingCover) this.$refs.loadingCover.hide();
					},
					fail: res => {
						if (this.$refs.loadingCover) this.$refs.loadingCover.hide();
					}
				});
			},
			/**
			 * 处理购物车数据结构
			 */
			handleCartData(data) {
				this.invalidGoods = [];
				this.cartData = [];
				var temp = {};
				data.forEach((item, index) => {
					if (item.goods_state == 1 && item.verify_state == 1) {
						item.checked = true;
						if (item.sku_spec_format) item.sku_spec_format = JSON.parse(item.sku_spec_format)
						if (temp['site_' + item.site_id] != undefined) {
							temp['site_' + item.site_id].cartList.push(item);
						} else {
							temp['site_' + item.site_id] = {
								siteId: item.site_id,
								siteName: item.site_name,
								edit: false,
								checked: true,
								cartList: [item]
							};
						}
					} else {
						this.invalidGoods.push(item);
					}
				});

				this.cartData = [];
				Object.keys(temp).forEach(key => {
					this.cartData.push(temp[key]);
				});
				this.calculationTotalPrice();
			},
			/**
			 * 单选
			 * @param {Object} index
			 */
			singleElection(siteIndex, index) {
				this.cartData[siteIndex].cartList[index].checked = !this.cartData[siteIndex].cartList[index].checked;
				this.calculationTotalPrice();
			},
			/**
			 * 店铺全选
			 * @param {Object} checked
			 */
			siteAllElection(checked, index) {
				this.cartData[index].checked = checked;
				this.cartData[index].cartList.forEach(item => {
					item.checked = checked;
				});
				this.calculationTotalPrice();
			},
			/**
			 * 全选
			 */
			allElection(checked) {
				if (typeof checked == 'boolean') {
					this.checkAll = checked;
				} else {
					this.checkAll = !this.checkAll;
				}
				if (this.cartData.length) {
					this.cartData.forEach(siteItem => {
						siteItem.checked = this.checkAll;
						siteItem.cartList.forEach(item => {
							item.checked = this.checkAll;
						});
					});
				}
				this.calculationTotalPrice();
			},
			/**
			 * 计算购物车总价
			 */
			calculationTotalPrice() {
				if (this.cartData.length) {
					let totalPrice = 0,
						totalCount = 0,
						siteAllElectionCount = 0,
						shopOwnerBrokerage = 0;
					this.cartData.forEach(siteItem => {
						let siteGoodsCount = 0;
						siteItem.cartList.forEach(item => {
							if (item.checked) {
								siteGoodsCount += 1;
								totalCount += item.num;
								totalPrice += item.discount_price * item.num;
								shopOwnerBrokerage += Number(item.shop_owner_brokerage) * item.num
							}
						});
						if (siteItem.cartList.length == siteGoodsCount) {
							siteItem.checked = true;
							siteAllElectionCount += 1;
						} else {
							siteItem.checked = false;
						}
					});

					this.shopOwnerBrokerage = shopOwnerBrokerage.toFixed(2);
					this.totalPrice = totalPrice.toFixed(2);
					this.totalCount = totalCount;
					this.checkAll = this.cartData.length == siteAllElectionCount;
				} else {
					this.totalPrice = '0.00';
					this.totalCount = 0;
				}
				this.modifyFlag = false;
			},
			/**
			 * 删除购物车
			 * @param {Object} siteIndex
			 * @param {Object} cartIndex
			 */
			deleteCart(siteIndex, cartIndex) {
				let cart_id = ''
				let sku_id = [],
					num = []
				//判断是否点击编辑按钮
				if (this.isEdit) {
					cart_id = [];
					this.cartData.forEach(siteItem => {
						siteItem.cartList.forEach(item => {
							if (item.checked) cart_id.push(item.cart_id)
							if (item.checked) sku_id.push(item.sku_id)
							if (item.checked) num.push(item.num)
						});
					});
					//判断是否选中商品
					if (!cart_id.length) {
						this.$util.showToast({
							title: '请选择要删除的商品'
						})
						return
					}
				} else {
					cart_id = siteIndex > -1 && this.cartData[siteIndex] && this.cartData[siteIndex].cartList && this.cartData[siteIndex].cartList[cartIndex] ? this.cartData[siteIndex].cartList[cartIndex].cart_id : '';
				}

				this.$api.sendRequest({
					url: '/api/cart/delete',
					data: {
						cart_id,
						is_collect: 0
					},
					success: res => {
						if (res.code >= 0) {

							if (!this.isEdit) {
								this.cartData[siteIndex].cartList.splice(cartIndex, 1);
								if (this.cartData[siteIndex].cartList.length == 0) this.cartData.splice(
									siteIndex, 1);
							} else {
								let list = JSON.parse(JSON.stringify(this.cartData));
								this.cartData = []
								for (var i = list.length - 1; i >= 0; i--) {
									if (list[i].checked) list.splice(i, 1)
									else {
										for (var v = list[i].cartList.length - 1; v >= 0; v--) {
											if (list[i].cartList[v].checked) list[i].cartList.splice(v, 1)
										}
									}
								}
								this.cartData = list
							}
							this.selectSiteIndex = -1;
							this.selectCartIndex = -1;
							this.calculationTotalPrice();
							this.getCartNumber();

							this.$buriedPoint.purchaseGoods({
								id: sku_id,
								action_type: 1,
								action_num: num,
								is_goods_page: 0
							})

						} else {
							this.$util.showToast({
								title: res.message
							});
						}
						this.$refs.popup.closePopup()
					}
				});
			},

			openPopup(siteIndex, cartIndex) {
				this.siteIndex = siteIndex;
				this.cartIndex = cartIndex
				this.$refs.popup.open()
			},

			editCollection(item, siteIndex, cartIndex) {
				this.$api.sendRequest({
					url: '/api/cart/delete',
					data: {
						cart_id: item.cart_id,
						is_collect: 1
					},
					success: res => {
						if (res.code == 0) {
							this.cartData[siteIndex].cartList.splice(cartIndex, 1);
							if (this.cartData[siteIndex].cartList.length == 0) this.cartData.splice(siteIndex,
								1);
							this.selectSiteIndex = -1;
							this.selectCartIndex = -1;
							this.calculationTotalPrice();
							this.getCartNumber();
							this.$util.showToast({
								title: '收藏成功'
							})
						}
					}
				});
			},

			async changeCart(e) {
				let res = await this.$api.sendRequest({
					url: '/api/goodssku/detail',
					async: false,
					data: {
						sku_id: e.sku_id
					}
				});
				let data = res.data;

				data.goods_sku_detail.goods_spec_format = data.goods_sku_detail.goods_spec_format ? JSON.parse(data
					.goods_sku_detail.goods_spec_format) : ''
				data.goods_sku_detail.show_price = data.goods_sku_detail.discount_price;
				if (data.goods_sku_detail.goods_attr_format) {
					let goods_attr_format = JSON.parse(data.goods_sku_detail.goods_attr_format);
					data.goods_sku_detail.goods_attr_format = JSON.parse(data.goods_sku_detail.goods_attr_format);
					data.goods_sku_detail.goods_attr_format = this.$util.unique(data.goods_sku_detail
						.goods_attr_format, "attr_id");
					for (var i = 0; i < data.goods_sku_detail.goods_attr_format.length; i++) {
						for (var j = 0; j < goods_attr_format.length; j++) {
							if (data.goods_sku_detail.goods_attr_format[i].attr_id == goods_attr_format[j].attr_id &&
								data.goods_sku_detail.goods_attr_format[
									i].attr_value_id != goods_attr_format[j].attr_value_id) {
								data.goods_sku_detail.goods_attr_format[i].attr_value_name += "、" + goods_attr_format[
									j].attr_value_name;
							}
						}
					}
				}
				data.goods_sku_detail.sku_spec_format = data.goods_sku_detail.sku_spec_format ? JSON.parse(data
					.goods_sku_detail.sku_spec_format) : []
				if (!data.goods_sku_detail.goods_spec_format) return
				data.goods_sku_detail.cart_id = e.cart_id
				data.discount_content && (data.goods_sku_detail.discount_content = data.discount_content)

				this.selectCartItem = data.goods_sku_detail;
				this.chooseSkuspecFormat()
			},

			chooseSkuspecFormat() {
				this.$refs.goodsSku.show("update_cart", () => {
					this.getCartData()
				});
			},

			/**
			 * 刷新商品详情数据
			 * @param {Object} goodsSkuDetail
			 */
			refreshGoodsSkuDetail(goodsSkuDetail) {
				Object.assign(this.selectCartItem, goodsSkuDetail);
			},

			/**
			 * 变更购物车数量
			 * @param {Object} params
			 */
			cartNumChange(num, params) {

				let item = this.cartData[params.siteIndex].cartList[params.cartIndex]
				if (num < 1) num = 1;
				this.modifyFlag = true;
				this.$api.sendRequest({
					url: '/api/cart/edit',
					data: {
						num,
						cart_id: item.cart_id
					},
					success: res => {
						if (res.code >= 0) {
							let preNum = item.num

							this.cartData[params.siteIndex].cartList[params.cartIndex].num = parseInt(num);
							this.calculationTotalPrice();

							this.$buriedPoint.purchaseGoods({
								id: item.sku_id,
								action_type: preNum < num ? 0 : 1,
								action_num: [preNum < num ? num - preNum : preNum - num],
								is_goods_page: 0
							})

						} else {
							this.$util.showToast({
								title: res.message
							});
						}
					}
				});
			},
			/**
			 * 结算
			 */
			settlement() {
				if (this.totalCount > 0) {
					let cart_ids = [];
					let sku_ids = [];
					let num = [];
					this.cartData.forEach(siteItem => {
						siteItem.cartList.forEach(item => {
							if (item.checked) {
								cart_ids.push(item.cart_id);
								sku_ids.push(item.sku_id);
								num.push(item.num);
							}
						});
					});

					this.$buriedPoint.submitOrderContent({ sku_ids, num, pages: 0 })
					if (this.isSub) return;
					this.isSub = true;

					uni.setStorage({
						key: 'orderCreateData',
						data: {
							cart_ids: cart_ids.toString()
						},
						success: () => {
							this.$util.redirectTo('/pages/order/payment/payment');
							this.isSub = false;
						}
					});
				}
			},
			edit(index) {
				this.cartData[index].edit = !this.cartData[index].edit;
			},
			/**
			 * 清空失效商品
			 */
			clearInvalidGoods() {
				var cart_ids = [];
				this.invalidGoods.forEach(goodsItem => {
					cart_ids.push(goodsItem.cart_id);
				});
				if (cart_ids.length) {
					this.$api.sendRequest({
						url: '/api/cart/delete',
						data: {
							cart_id: cart_ids.toString()
						},
						success: res => {
							if (res.code >= 0) {
								this.invalidGoods = [];
								this.getCartNumber()
							} else {
								this.$util.showToast({
									title: res.message
								});
							}
						}
					});
				}
			},
			imageError(siteIndex, cartIndex) {
				this.cartData[siteIndex].cartList[cartIndex].sku_image = this.$util.getDefaultImage().default_goods_img;
				this.$forceUpdate();
			},
			// 购物车数量
			getCartNumber() {
				if (uni.getStorageSync("token")) {
					this.$store.dispatch('getCartNumber')
				}
			},
			touchS(e) {
				if (this.isEdit) return
				// 获得起始坐标
				this.startX = e.touches[0].clientX;
				this.startY = e.touches[0].clientY;
			},
			touchM(e) {
				if (this.isEdit) return
				// 获得当前坐标
				this.currentX = e.touches[0].clientX;
				this.currentY = e.touches[0].clientY;
				const x = this.startX - this.currentX; //横向移动距离
				const y = Math.abs(this.startY - this.currentY); //纵向移动距离，若向左移动有点倾斜也可以接受
				if (x > 35 && y < 110) {
					// 向左滑获取选中的商品索引显示删除按钮
					this.selectSiteIndex = e.currentTarget.dataset.siteindex
					this.selectCartIndex = e.currentTarget.dataset.cartindex
				} else if (x < -35 && y < 110) {
					this.selectSiteIndex = -1;
					this.selectCartIndex = -1;
				}
			},
      /**
       *分享参数组装
       */
      getSharePageParams() {
        return this.$util.unifySharePageParams('/otherpages/shop/home/<USER>', this.shareTitle || '先迈商城',
            '',{}, this.$util.img(this.shareImg) || this.$util.img('public/static/youpin/home_share.jpg'))
      }
		},
    /**
     * 自定义分享内容
     * @param {Object} res
     */
    onShareAppMessage(res) {
      let { title, link, imageUrl, query } = this.getSharePageParams()
      return this.$buriedPoint.pageShare(link , imageUrl, title);
    },
	};
</script>

<style lang="scss">
	.container {
		width: 100%;
		height: 100%;
		padding-bottom: 0;
		padding-bottom: calc(120rpx + constant(safe-area-inset-bottom));
		padding-bottom: calc(120rpx + env(safe-area-inset-bottom));
		padding-top: calc(80rpx + env(safe-area-inset-top));
	}

	.nav {
		width: 100%;
		overflow: hidden;
		position: fixed;
		z-index: 999;
		background-color: #ffffff;
	}

	.nav-title {
		width: 100%;
		height: 88rpx;
		line-height: 88rpx;
		// text-align: center;
		position: absolute;
		bottom: 0;
		left: 0;
		z-index: 10;
    text-align: center;
	}

	.top-operation {
		background: #fff;
		display: flex;
		justify-content: flex-end;
		line-height: 80rpx;
		padding-right: 30rpx;
		height: 80rpx;
		position: fixed;
		left: 0;
		// top: env(safe-area-inset-top);
		top: 128rpx;
		width: 100%;
		box-sizing: border-box;
		z-index: 99;
	}

	.cart-wrap {
		margin: 20rpx;
		background: #fff;
		border-radius: $ns-border-radius;
		overflow: hidden;
		width: calc(100% - 20px);

		.cart-header {
			padding: 20rpx;
			overflow: hidden;
			display: flex;
			align-items: center;
			line-height: 40rpx;

			.shop-info {
				flex: 1;
				line-height: inherit;
				font-size: 28rpx;
				font-weight: bold;
			}

			.iconyuan_checkbox {
				font-size: 36rpx;
				color: #898989;
				margin-right: 14rpx;
				line-height: 1;
			}

			.iconyuan_checked {
				font-size: 36rpx;
				margin-right: 14rpx;
				line-height: 1;
			}

			.icondianpu {
				display: inline-block;
				margin-right: 10rpx;
				line-height: inherit;
			}

			.cart-operation {
				line-height: inherit;
			}
		}

		.cart-goods {
			padding: 0 20rpx 20rpx 20rpx;
			border-radius: 4px;
			background: #fff;
			box-sizing: border-box;
			position: relative;

			.goods-wrap {
				display: flex;
				position: relative;
				padding-left: 52rpx;
				transition: all 0.3s;
				width: 100%;
				box-sizing: border-box;

				&.show {
					margin-left: -100rpx;
					width: calc(100% - 20rpx);

					.goods-info {
						width: calc(100% - 400rpx);
					}
				}

				&.edit {
					transform: translateX(-120rpx);
				}

				&>.iconfont {
					font-size: 36rpx;
					position: absolute;
					top: 50%;
					left: 0;
					transform: translateY(-50%);
				}

				&>.iconyuan_checkbox {
					color: $ns-text-color-gray;
				}

				.goods-img {
					width: 200rpx;
					height: 200rpx;
					margin: 20rpx 20rpx 0 0;
          border-radius: 20px;
          background: rgba(217, 217, 217, 0.2);

          image {
						width: 100%;
						height: 100%;
					}
				}

				.goods-info {
					flex: 1;
					position: relative;
					padding: 20rpx 0 0 0;
					max-width: calc(100% - 200rpx);

					.goods-name {
						display: -webkit-box;
						-webkit-box-orient: vertical;
						-webkit-line-clamp: 2;
						overflow: hidden;
						line-height: 1.07;
						font-size: 28rpx;
					}

					.sku {
            height: 44rpx;
            line-height: 44rpx;
            font-size: 24rpx;
            font-weight: 400;
            color: rgba(56, 56, 56, 1);
						display: inline-block;
						overflow: hidden;
						text-overflow: ellipsis;
						white-space: nowrap;
            border-radius: 8rpx;
            background: rgba(245, 245, 245, 1);
						padding: 0 16rpx;
            margin-top: 14rpx;
            max-width: 350rpx;

						.arrow-bottom {
							margin-left: 8rpx;
						}
					}

					.goods-sub-section {
						position: absolute;
						left: 0;
						bottom: -14rpx;
						width: 100%;
            display: flex;
            justify-content: space-between;
            align-items: center;
            &-shopper{
              bottom: -2rpx;
            }
            &-left{
              display: flex;
              flex-direction: column;
              justify-content: center;
              align-items: flex-start;
            }

						.goods-price {
							font-weight: bold;
							font-size: 32rpx;
              line-height: 37rpx;
              &-desc{
                font-size: 20rpx;
                font-weight: 400;
                margin-left: 10rpx;
              }
						}
            .price-distributor {
              line-height: 0;

              text {
                height: 24rpx;
                border-radius: 4rpx;
                font-size: 20rpx;
                font-weight: 400;
                line-height: 23.44rpx;
                color: rgba(128, 128, 128, 1);
                position: relative;
                display: flex;
                justify-content: center;
                align-items: center;
              }
            }

						.unit {
							font-size: 20rpx;
							margin-right: 8rpx;
						}

						.cart-number {
							float: right;

							/deep/ .uni-numbox {
								overflow: hidden;
								-webkit-backface-visibility: hidden;
								-webkit-transform: translate3d(0, 0, 0);
                &:after{
                  border:none;
                }
								.uni-numbox__minus,
								.uni-numbox__plus {
									border-radius: 10rpx;
                  width: 40rpx;
                  height: 40rpx;
                  line-height: 40rpx;
                  font-size: 32rpx;
								}

								.uni-numbox__value {
									width: 80rpx;
                  height: 40rpx;
                  line-height: 40rpx;
								}
							}
						}
					}
				}
			}

			.item-operation {
				position: absolute;
				width: 100rpx;
				top: 0;
				right: -220rpx;
				height: 100%;
				transition: all 0.3s;
				display: flex;
				align-items: center;

				.item-del,
				.item-moveIn {
					width: 100%;
					height: 100%;
					color: #fff;
					flex-shrink: 0;
					text-align: center;
					vertical-align: middle;
					display: flex;
					align-items: center;
					justify-content: center;
					padding: 0 10rpx;
					box-sizing: border-box;
					flex-wrap: wrap;
				}

				.item-moveIn {
					background: var(--custom-brand-color);
				}

				&.show {
					right: 0;
				}
			}


		}

		.invalid-goods {
			.invalid-mark {
				background: #9a9a9a;
				color: #fff;
				padding: 4rpx 8rpx;
				display: inline-block;
				line-height: 1;
				font-size: 24rpx;
				position: absolute;
				border-radius: 24rpx;
				top: 50%;
				transform: translateY(-50%);
			}

			.goods-name {
				color: #9a9a9a;
			}

			.goods-wrap {
				padding-left: 80rpx;

				.goods-img {
					image {
					}
				}
			}
		}
	}

	.cart-bottom {
		position: fixed;
		z-index: 5;
		width: 100vw;
		height: 120rpx;
		bottom: 110rpx;
		background: #fff;
		// bottom: var(--window-bottom);
		overflow: hidden;
		display: flex;
		justify-content: space-between;
		// bottom: 56px;
		// bottom: calc(56px + constant(safe-area-inset-bottom));
		// bottom: calc(56px + env(safe-area-inset-bottom));

		.all-election {
			height: 120rpx;
			position: relative;
			padding-left: 20rpx;
			display: inline-block;

			&>.iconfont {
				font-size: 36rpx;
				position: absolute;
				top: 50%;
				left: 24rpx;
				transform: translateY(-50%);
			}

			&>text {
				margin-left: 56rpx;
				line-height: 120rpx;
			}
		}

		.settlement-info {
			flex: 1;
			text-align: right;
			padding-right: 20rpx;
			line-height: 120rpx;
			position: relative;
			.total-price {
				font-size: 36rpx;
				font-weight: bold;
				margin-left: 6rpx;
			}
			.preferential-price {
				position: absolute;
				top: 80rpx;
				right: 20rpx;
				color: var(--custom-brand-color);
				font-size: 22rpx;
			}
		}

		.operation-btn {
			width: 200rpx;
			height: 80rpx;
			line-height: 80rpx;
			border-radius: 40rpx;
			margin-top: 20rpx;
			margin-right: 24rpx;
			display: flex;
			justify-content: center;
			align-items: center;

			button {
				width: 100%;
				line-height: 74rpx;
			}
		}

		.delete-btn {
			height: 80rpx;
			float: right;
			width: 200rpx;
			line-height: 80rpx;
			text-align: center;
			font-size: 28rpx;
			color: #343434;
			border: 2rpx solid #9A9A9A;
			border-radius: 40rpx;
			margin-top: 10rpx;
			margin-right: 24rpx;
			box-sizing: border-box;

		}


	}

	.cart-empty {
		text-align: center;
		font-size: 24rpx;
		padding: 80rpx $ns-padding 80rpx $ns-padding;

		.empty {
			padding-top: 0;
			padding-bottom: 50rpx;
			text-align: center;
		}

		image {
			margin: 10px auto;
			width: 220rpx;
			height: 120rpx;
			display: block;
		}

		navigator {
			display: inline;
		}
	}
	.top-operationa{
		position: absolute;
    left: 20rpx;
    top: 50%;
    transform: translateY(-50%);
	}
	.cart_title{
		//margin-left: 257rpx;
	}
</style>
