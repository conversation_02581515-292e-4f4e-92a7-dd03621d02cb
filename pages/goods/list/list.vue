<template>
	<view class="content" :class="themeStyle">
		<view class="head-wrap">
			<!-- 搜索区域 -->
			<view class="search-wrap uni-flex uni-row">
				<view class="flex-item input-wrap">
					<input class="uni-input" maxlength="50" v-model="keyword" confirm-type="search" @confirm="search()" placeholder="搜索你喜欢的商品" />
					<uni-icons class="icon-clear" type="clear" @click="reset()" size="20" color="#A0A1A7"></uni-icons>
				</view>
			</view>

			<!-- 排序 -->
			<view class="sort-wrap">
				<view class="comprehensive-wrap" :class="{ 'high-text-color': orderType === '' }" @click="sortTabClick('')">
					<text :class="{ 'high-text-color': orderType === '' }">综合</text>
				</view>
				<view :class="{ 'high-text-color': orderType === 'sales' }" @click="sortTabClick('sales')">销量</view>
				<view class="price-wrap" @click="sortTabClick('discount_price')">
					<text :class="{ 'high-text-color': orderType === 'discount_price' }">价格</text>
					<view class="iconfont-wrap">
						<view class="iconfont iconiconangledown-copy" :class="{ 'high-text-color': priceOrder === 'ASC' && orderType === 'discount_price' }"></view>
						<view class="iconfont iconiconangledown" :class="{ 'high-text-color': priceOrder === 'DESC' && orderType === 'discount_price' }"></view>
					</view>
				</view>
			</view>
		</view>

		<!-- top:350 -->
		<mescroll-uni top="190" ref="mescroll" @getData="getGoodsList">
			<block slot="list">
				<view class="goods-list largest" :class="listStyle">
					<view v-for="(item, index) in goodsList" :key="index" class="goods-item" @click="navToDetailPage(item)">
						<view class="image-wrap">
							<image :src="$util.img(item.sku_image)" @error="imageError(index)" mode="aspectFill" />
						</view>
						<view class="goods-info">
							<text class="goods-name">{{ item.goods_name }}</text>
							<view class="sold-out">已售{{ item.sale_num }}</view>
							<view class="price-wrap">
								<text class="unit high-text-color">¥</text>
								<text class="price high-text-color">{{ item.retail_price }}</text>
								<text class="original-price">{{ item.market_price }}</text>
							</view>
						</view>
					</view>
				</view>
				<view v-if="goodsList.length==0">
					<view class="search-default">没找到你要的商品哦~</view>
					<view class="recommend" v-if="goodsList.length!=0">
						<view class="title">
							<image :src="$util.img('public/static/youpin/goods/img-recommend.png')" mode=""></image>
						</view>
						<view class="goods-list largest" :class="listStyle">
							<view v-for="(item, index) in goodsList" :key="index" class="goods-item" @click="navToDetailPage(item)">
								<view class="image-wrap">
									<image :src="$util.img(item.sku_image)" @error="imageError(index)" mode="aspectFill" />
								</view>
								<view class="goods-info">
									<text class="goods-name">{{ item.goods_name }}</text>
									<view class="price-wrap">
										<text class="unit high-text-color">¥</text>
										<text class="price high-text-color">{{ item.discount_price }}</text>
										<text class="original-price">{{ item.discount_price }}</text>
									</view>
								</view>
							</view>
						</view>
					</view>
				</view>
			</block>
		</mescroll-uni>
		<loading-cover ref="loadingCover"></loading-cover>
	</view>
</template>

<script>
	import uniDrawer from '@/components/uni-drawer/uni-drawer.vue';
	import uniTag from '@/components/uni-tag/uni-tag.vue';
	import list from '../public/js/list.js';

	export default {
		components: {
			uniDrawer,
			uniTag
		},
		data() {
			return {};
		},
		computed: {
			// 使用对象展开运算符将此对象混入到外部对象中
			themeStyle(){
				return 'theme-'+this.$store.state.themeStyle
			},
			addonIsExit(){
				return this.$store.state.addonIsExit
			} 
		},
		mixins: [list]
	};
</script>

<style lang="scss">
	@import '../public/css/list.scss';
</style>
