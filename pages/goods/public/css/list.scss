.high-text-color {
	color: #FF1010;
}
.head-wrap {
	background: #fff;
	position: fixed;
	width: 100%;
	left: 0;
	z-index: 1;

	.search-wrap {
		flex: 0.5;
		padding: 16rpx 30rpx;
		font-size: $ns-font-size-sm;
		display: flex;
		align-items: center;
		.input-wrap {
			flex: 1;
			display: flex;
			justify-content: space-between;
			align-items: center;
			background: $uni-bg-color-grey;
			height: 60rpx;
			border-radius: 40rpx;
			input {
				width: 90%;
				background: $uni-bg-color-grey;
				font-size: $ns-font-size-base;
				height: 60rpx;
				line-height: 60rpx;
				padding-left: 25rpx;
				border-radius: 40rpx;
			}
			text {
				font-size: 40rpx;
				color: $ns-text-color-gray;
				width: 80rpx;
				text-align: center;
			}
			.icon-clear {
				width: 80rpx;
				text-align: center;
			}
		}
		.category-wrap,
		.list-style {
			display: flex;
			justify-content: center;
			align-items: center;
			.iconfont {
				font-size: 50rpx;
				color: $ns-text-color-gray;
			}
			text {
				display: block;
				margin-top: 60rpx;
			}
		}
	}

	.sort-wrap {
		display: flex;
		padding: 10rpx 0;
		> view {
			flex: 1;
			text-align: center;
			font-size: $ns-font-size-base;
			height: 60rpx;
			line-height: 60rpx;
		}
		.comprehensive-wrap {
			display: flex;
			justify-content: center;
			align-items: center;
			.iconfont-wrap {
				display: inline-block;
				margin-left: 10rpx;
				width: 40rpx;
				.iconfont {
					font-size: 32rpx;
					line-height: 1;
					margin-bottom: 5rpx;
				}
			}
		}
		.price-wrap {
			display: flex;
			justify-content: center;
			align-items: center;
			.iconfont-wrap {
				display: flex;
				justify-content: center;
				align-items: center;
				flex-direction: column;
				margin-left: 10rpx;
				width: 40rpx;
				margin-bottom: 12rpx;
				.iconfont {
					float: left;
					font-size: 32rpx;
					line-height: 1;
					height: 20rpx;
				}
			}
		}
		.screen-wrap {
			display: flex;
			justify-content: center;
			align-items: center;
			.iconfont-wrap {
				display: inline-block;
				margin-left: 10rpx;
				width: 40rpx;
				.iconfont {
					font-size: 36rpx;
					line-height: 1;
				}
			}
		}
	}
}

.goods-list {
	padding: 20rpx 20rpx 0;
	.goods-item {
		margin-bottom: 20rpx;
		background: #ffffff;
		padding: $ns-padding;
		display: flex;
		&:last-child {
			margin-bottom: 0;
		}
	}

	.image-wrap {
		display: inline-block;
		width: 343rpx;
		height: 343rpx;
		line-height: 343rpx;
		image {
			width: 100%;
			height: 100%;
			opacity: 1;
			border-radius: 0rpx;
		}
	}
	.goods-info {
		flex: 1;
		.goods-name {
			margin-top: 20rpx;
			margin-bottom: 15rpx;
			overflow: hidden;
			display: -webkit-box;
			-webkit-line-clamp: 2;
			-webkit-box-orient: vertical;
			word-break: break-all;
			line-height: 1.3;
			font-size: 26rpx;
		}
		.introduction {
			color: $ns-text-color-gray;
			overflow: hidden;
			display: -webkit-box;
			font-size: $ns-font-size-sm;
			-webkit-line-clamp: 1;
			-webkit-box-orient: vertical;
			word-break: break-all;
		}
		.price-wrap {
			height: 80rpx;
			line-height: 80rpx;
			.unit {
				font-weight: bold;
				font-size: $ns-font-size-sm;
				margin-right: 4rpx;
			}
			.price {
				font-size: 36rpx;
				font-weight: bold;
				margin-right: 8rpx;
			}
			.original-price {
				font-size: 24rpx;
				text-decoration: line-through;
				color: #999999;

			}
			.sale-num {
				color: $ns-text-color-gray;
			}
		}
		.other-info {
			color: $ns-text-color-gray;
			font-size: $ns-font-size-sm;
			.sales {
				float: right;
			}
		}
		.is_discount{
			display: inline-block;
			margin-right: $ns-margin;
			height: 40rpx;
			// width: 140rpx;
			padding: 0 $ns-padding;
			box-sizing: border-box;
			border: 1rpx solid #ffffff;
			text-align: center;
			line-height: 40rpx;
			font-size: $ns-font-size-sm;
			border-radius: $ns-border-radius;
		}
	}

	&.largest {
		display: flex;
		flex-wrap: wrap;
		padding: 0 24rpx 0;
		background: transparent;
		.goods-item {
			flex-direction: column;
			width: 343rpx;
			padding: 0;
			margin-bottom: 32rpx;
			border-bottom: none;
			background: #ffffff;
			overflow: hidden;
			&:nth-child(2n + 1) {
				margin-right: 16rpx;
			}
			.goods-info {
				padding-left: 17rpx;
				padding-right: 15rpx;
			}
		}
		.image-wrap {
			width: 100%;
			height: 343rpx;
			overflow: hidden;
		}
	}
}

.category-list-wrap {
	height: 100%;
	.first {
		font-size: $ns-font-size-lg;
		font-weight: bold;
		display: block;
		// background: $page-color-base;
		padding: 20rpx;
	}
	.second {
		border-bottom: 2rpx solid $ns-border-color-gray;
		padding: 20rpx;
		display: block;
	}
	.third {
		padding: 0 20rpx 20rpx;
		overflow: hidden;
		> view {
			display: inline-block;
			margin-right: 20rpx;
			margin-top: 20rpx;
		}
		.uni-tag {
			padding: 0 20rpx;
		}
	}
}

.screen-wrap {
	.title {
		font-size: $ns-font-size-lg;
		padding: $ns-padding;
		background: #f6f4f5;
	}
	scroll-view {
		height: 85%;
		.item-wrap {
			border-bottom: 1px solid #f0f0f0;
			.label {
				font-size: $ns-font-size-lg;
				padding: $ns-padding;
				view {
					display: inline-block;
					font-size: 60rpx;
					height: 40rpx;
					vertical-align: middle;
					line-height: 40rpx;
				}
			}

			.list {
				margin: $ns-margin;
				overflow: hidden;
				> view {
					display: inline-block;
					margin-right: 25rpx;
					margin-bottom: 25rpx;
				}
				.uni-tag {
					padding: 0 $ns-padding;
					font-size: $ns-font-size-base;
					background: #f5f5f5;
				}
			}
			.price-wrap {
				display: flex;
				justify-content: center;
				align-items: center;
				padding: $ns-padding;
				input {
					flex: 1;
					background: #f5f5f5;
					height: 50rpx;
					padding: 15rpx 25rpx;
					line-height: 50rpx;
					font-size: 28rpx;
					border-radius: 50rpx;
					text-align: center;
					&:first-child {
						margin-right: 10rpx;
					}
					&:last-child {
						margin-left: 10rpx;
					}
				}
			}
		}
	}
	.footer {
		height: 90rpx;
		display: flex;
		justify-content: center;
		align-items: flex-start;
		display: flex;
		// position: absolute;
		bottom: 0;
		width: 100%;
		.footer-box{
			width: 40%;
			height: 60rpx;
			background: $ns-bg-color-gray;
			border-top-left-radius: 30rpx;
			border-bottom-left-radius: 30rpx;
			text-align: center;
			line-height: 60rpx;
			color: $ns-text-color-gray;
		}
		.footer-box1{
			width: 40%;
			height: 60rpx;
			border-top-right-radius: 30rpx;
			border-bottom-right-radius: 30rpx;
			text-align: center;
			line-height: 60rpx;
			color: #ffffff;
		}
	}
}
.footer.safe-area{
	padding-bottom: 0;
	padding-bottom: constant(safe-area-inset-bottom);
	padding-bottom: env(safe-area-inset-bottom);
}
.search-default {
	height: 266rpx;
	line-height: 266rpx;
	font-size: 28rpx;
	color: #999999;
	text-align: center;
}
.recommend {
	.title {
		margin-bottom: 30rpx;
		text-align: center;
		image {
			width: 292rpx;
			height: 32rpx;
		}
	}
}
.sold-out {
	color: #BBC0C3;
	font-size: 24rpx;
}
