<template>
</template>

<script>
export default{
	name:'productDetail',
	data(){
		return{}
	},
	onLoad(options) {
		let data = {}
		data.sku_id = options.singalProductId
		if(options.share_activity_id){
			data.activity_id = options.share_activity_id
		}
		if(options.shopId){
			data.shop_id = options.shopId
		}
		this.$util.redirectTo('/promotionpages/fenxiangzhuan/goodsDetail/goodsDetail',data,'redirectTo')
	},
	methods:{
    /**
     *分享参数组装
     */
    getSharePageParams() {
      return this.$util.unifySharePageParams('/otherpages/shop/home/<USER>', this.shareTitle || '先迈商城',
          '',{}, this.$util.img(this.shareImg) || this.$util.img('public/static/youpin/home_share.jpg'))
    }
  },
  /**
   * 自定义分享内容
   * @param {Object} res
   */
  onShareAppMessage(res) {
    let { title, link, imageUrl, query } = this.getSharePageParams()
    return this.$buriedPoint.pageShare(link , imageUrl, title);
  },
}
</script>

<style>
</style>
