<template>
<view class="page">
	<view v-if="canIpay" class="payment">
	    <view class="money">￥<text>{{money}}</text></view>
	    <view class="receipt"><text class="left">收款方</text><text class="right">{{merchant_name}}</text></view>
	    <view class="btn-pay" @click="payment">立即支付</view>
    <button class="back-app" open-type="launchApp" app-parameter="wechat" binderror="launchAppError" v-if="openType=='app'">返回商户</button>
	    <button class="back-app" @click="toHome" v-else>返回首页</button>
	</view>
	<view v-if="isPay" class="pay-tips">
	    <view class="box">
	        <view><image class="img-pay" :src="$util.img('public/static/youpin/paySuccess-icon.png')"></image></view>
	        <view class="pay-text">{{payTips}}</view>
          <button class="btn-app" open-type="launchApp" app-parameter="wechat" binderror="launchAppError" v-if="openType=='app'">点击返回App</button>
	        <button class="btn-app" @click="toHome" v-else>点击返回首页</button>
	    </view>
	</view>
  <yd-auth-popup ref="ydauth"></yd-auth-popup>
  <ns-login ref="login"></ns-login>
	<uni-coupon-pop ref="couponPop"></uni-coupon-pop>
  <loading-cover ref="loadingCover"></loading-cover>
</view>
</template>

<script>
import system from "../../common/js/system";
import apiurls from "../../common/js/apiurls";
import AuthService from "../../common/services/auth";

export default{
	name:'payment',
	data(){
		return{
			canIpay:true,
			isPay:false,
			money:'',
			merchant_name:'',
			payTips:'',
      out_trade_no:'',
      openid:'',
      firstIn:true,
      openType:'',   //从什么应用跳转到这个页面  如app
		}
	},
	onLoad(options) {
		this.out_trade_no = options.out_trade_no
    this.openType = options.openType ? options.openType : '';
	},
	async onShow(){
		// uni.hideHomeButton()
    await system.wait_staticLogin_success();
    if(!this.isPay){
      await this.getOpenid();
      await this.getOrderInfo();
    }
    if (this.$refs.loadingCover) this.$refs.loadingCover.hide();
    if(this.firstIn){
      this.firstIn=false;
      this.payment();
    }
		if (uni.getStorageSync('is_register')) {
				this.$util.toShowCouponPopup(this)
				uni.removeStorageSync('is_register');
			}
	},
	methods:{
	  toHome(){
      this.$util.redirectTo('/otherpages/shop/home/<USER>', {}, 'reLaunch');
    },
	  async getOpenid(){
      let [code,_]=await AuthService.getCode();
	    let res=await this.$api.sendRequest({
        url:apiurls.h5wxSnsapiBaseUrl,
        async:false,
        data:{
          code
        }
      })
      if(res.code==0){
        this.openid=res.data.openid;
      }
    },
    async getOrderInfo(){
      let res=await this.$api.sendRequest({
        url:apiurls.h5PayPageUrl,
        async:false,
        data:{
          out_trade_no:this.out_trade_no
        }
      })
      if(res.code==0){
        this.money=res.data.pay_money;
        this.merchant_name=res.data.mch_title;
      }
    },
		payment(){
      this.$util.toShowLoginPopup(this,null,`/pages/payment/payment?out_trade_no=${this.out_trade_no}`);
      if(!uni.getStorageSync('token')){
        return;
      }
      this.createBuriedPoint('out_trade_no',this.out_trade_no,3);
      uni.showLoading({
        mask: true,
        title: '加载中'
      });
			this.$api.sendRequest({
				url: '/api/pay/pay',
				data: {
					out_trade_no: this.out_trade_no,
					pay_type: 'h5',
          openid:this.openid
				},
				success: res => {
					uni.hideLoading();
					if (res.code >= 0) {
            this.$util.wechatPay(res.data.pay_type,res.data.pay_type=='adapay'? res.data.payment : res.data.pay_info, (res)=>{
              uni.hideLoading();
             this.isPay=true;
              this.payTips = '支付成功'
              this.createBuriedPoint('out_trade_no',this.out_trade_no,11);
            },(err)=>{
              this.createBuriedPoint('out_trade_no',this.out_trade_no,9001)
              uni.hideLoading();
            },(err)=>{
              uni.hideLoading();
            })
					} else {
						if(res.message) {
							this.$util.showToast({
								title: res.message
							});
						} else {
							uni.hideLoading();
						}
					}
				},
				fail: res => {
					this.$util.showToast({
						title: 'request:fail'
					});
				}
			})
		},
    // 订单埋点
    createBuriedPoint(idName,out_trade_no,status){
      this.$buriedPoint.orderStatus({ [idName]:out_trade_no, status })
    },
    /**
     *分享参数组装
     */
    getSharePageParams() {
      return this.$util.unifySharePageParams('/otherpages/shop/home/<USER>', this.shareTitle || '先迈商城',
          '',{}, this.$util.img(this.shareImg) || this.$util.img('public/static/youpin/home_share.jpg'))
    }
	},
  /**
   * 自定义分享内容
   * @param {Object} res
   */
  onShareAppMessage(res) {
    let { title, link, imageUrl, query } = this.getSharePageParams()
    return this.$buriedPoint.pageShare(link , imageUrl, title);
  },
}
</script>

<style scoped>
	.payment {
	    height: 100vh;
	    background-color: #F5F5F5;
	}
	.money {
	    padding-top: 98rpx;
	    padding-bottom: 64rpx;
	    font-weight: bold;
	    font-size: 80rpx;
	    color: #000;
	    text-align: center;
	}
	.receipt {
	    display: flex;
	    padding: 0 32rpx;
	    justify-content: space-between;
	    align-items: center;
	    height: 100rpx;
	    background-color: #ffffff;
	    font-size: 28rpx;
	}
	.receipt .left {
	    color: #666666;
	}
	.receipt .right {
	    color: #333333;
	}
	.btn-pay {
	    margin: 0 auto;
	    margin-top: 140rpx;
	    width:690rpx;
	    height:88rpx;
	    line-height:88rpx;
	    text-align: center;
	    background:rgba(9,187,7,1);
	    border-radius:10rpx;
	    color: #fff;
	}
	.back-app {
	    position: absolute;
	    bottom: 90rpx;
	    text-align: center;
	    width: 100%;
	    background-color: transparent;
	    font-size: 26rpx;
	    color: #576B95;
	    text-decoration: underline;
	}
	button{
		margin:0
	}
	button::after{
	    border: none;
	}
	.pay-tips {
	    z-index: 1;
	    position: fixed;
	    display: flex;
	    justify-content: center;
	    align-items: center;
	    width: 100%;
	    height: 100vh;
	    background-color: rgba(0, 0, 0, 0.5);
	    top: 0;
	    left: 0;
	}
	.btn-app {
	    height: 100rpx;
	    line-height: 100rpx;
	    color: #1AAD19;
	    font-size: 36rpx;
	    border-top: 1rpx solid #EEEEEE;
	    background-color: transparent;
	}
	.box {
	    width: 580rpx;
	    padding-top: 60rpx;
	    background-color: #fff;
	    text-align: center;
	    border-radius: 20rpx;
	}
	.box image {
	    width: 85rpx;
	    height: 75rpx;
	    margin-bottom: 34rpx;
	}
	.pay-text {
	    color: #000000;
	    font-size: 40rpx;
	    margin-bottom: 60rpx;
	}
</style>
