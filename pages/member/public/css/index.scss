%flex-row-centered {
	display: flex;
	justify-content: space-around;
	align-items: center;
}

%flex-column-centered {
	display: flex;
	flex-direction: column;
	justify-content: center;
	align-items: center;
}

%flex-list {
	display: flex;
	justify-content: space-between;
	padding: 0 30rpx;
	height: 95rpx;
	line-height: 95rpx;
}

.container{
	height: 100%;
	// background-color: #FFFFFF;
	padding-bottom: 55px;
	padding-bottom: calc(55px + constant(safe-area-inset-bottom))constant(safe-area-inset-bottom);
	padding-bottom: calc(55px + env(safe-area-inset-bottom));
}
.container.active{
	padding-bottom: 90px;
	padding-bottom: calc(90px + constant(safe-area-inset-bottom))constant(safe-area-inset-bottom);
	padding-bottom: calc(90px + env(safe-area-inset-bottom));
}
.user-section {
	// position: relative;
	/*  #ifdef MP */
	// height: 530rpx;
	padding: 178rpx 0 0;
	/* #endif */
	/*  #ifdef H5 || MP-ALIPAY || APP-PLUS*/
	// height: 430rpx;
	padding: 70rpx 0 0 0;
	/* #endif */
	box-sizing: border-box;
	position: relative;
	.bg-img{
		position: absolute;
		width: 100%;
		height: 400rpx;
		top: 0;
		background: linear-gradient(177.1deg, var(--custom-brand-color) 0%, var(--custom-brand-color-70) 40%, rgba(255, 255, 255, 0) 70%);
		overflow: hidden;
		&-inner{
			width: 100%;
		}
	}
	.user-section-box{
		width: 100%;
		padding: 0 24rpx;
		box-sizing: border-box;
		position: relative;
		z-index: 5;
	}
	.user-info-box {
		display: flex;
		align-items: center;

		&.no-log {
			position: relative;
			z-index: 333;
		}
		.user-title {
			color: #333;
			overflow: hidden;
			text-overflow: ellipsis;
			white-space: nowrap;
			font-size: 36rpx;
			font-weight: bolder;
			color: rgba(56, 56, 56, 1);
			//max-width: 45%;
		}
		.user-desc {
			position: relative;
			display: flex;
			align-items: center;
			&-badge{
				height: 50rpx;
				display: flex;
				align-items: center;
				position: relative;
				&-img{
					position: absolute;
					width: 40rpx;
					height: 40rpx;
					left: -4rpx;
					top: 6rpx;
				}
				&-text{
					height: 32rpx;
					line-height: 32rpx;
					border-radius: 100rpx;
					background: linear-gradient(90deg, rgba(255, 163, 163, 1) 0%, rgba(246, 93, 114, 1) 100%);
					font-size: 20rpx!important;
					font-weight: 400;
					color: rgba(255, 255, 255, 1)!important;
					padding: 0 10rpx 0 34rpx;
					box-sizing: border-box;
					display: inline-block;
				}
			}
			&-item{
				height: 32rpx;
				line-height: 32rpx;
				border-radius: 100rpx;
				background-color: #fff;
				border: 1px solid var(--custom-brand-color);
				font-size: 20rpx!important;
				font-weight: 400;
				color: var(--custom-brand-color)!important;
				padding: 0 10rpx;
				box-sizing: border-box;
				display: inline-block;
				margin-left: 6rpx;
			}
			&-thrift{
				height: 32rpx;
				line-height: 32rpx;
				border-radius: 100rpx;
				background-color: var(--custom-brand-color);
				padding: 0 12rpx;
				box-sizing: border-box;
				font-size: 24rpx!important;
				font-weight: 400;
				color: rgba(255, 255, 255, 1)!important;
				text-align: center;
				display: inline-block;
				margin-left: 6rpx;
				margin-top: 6rpx;
			}
		}

		.user-head {
			box-sizing: content-box;
			width: 120rpx;
			height: 120rpx;
			// border: 1rpx solid #fff;
			border-radius: 50%;
			overflow: hidden;
			position: relative;
			image {
				width: 104rpx;
				height: 104rpx;
				border-radius: 50%;
				vertical-align: middle;
			}
		}

		.user-box {
			width:calc(100% - 280rpx);
			margin-left: 20rpx;
			overflow: hidden;
			display: flex;
			flex-direction: column;
			color: #fff;
			view {

				&:last-of-type {
					font-size: $ns-font-size-lg;
					color: #fff;
				}
				.user-title-edit{
					margin-left: 4rpx;
					width: 32rpx;
					height: 32rpx;
				}
			}
			.user-label {
				display: inline-block;
				max-width: 90%;
				overflow: hidden;
				text-overflow: ellipsis;
				white-space: nowrap;
				text-align: center;
				font-size: $ns-font-size-sm;
				padding: 8rpx 8rpx;
				line-height: 1;
				background:rgba(#ffffff,0.7);
				border-radius:18rpx;
				padding: 5rpx 20rpx;
			}
		}
	}

	.user-set {
		position: absolute;
		width: 40rpx;
		height: 40rpx;
		right: 30rpx;
		/* #ifdef  MP*/
		top: 10rpx;
		/* #endif */
		/* #ifdef  H5 || MP-ALIPAY || APP-PLUS */
		top: 30rpx;
		/* #endif */

		.iconfont{
			font-size: 40rpx;
			color: #fff;
			line-height: 1;
		}
	}
}
.member-body {
	position: relative;
	// margin-top: -58rpx;
	// padding: 0 20rpx;
}

.member-sction {
	@extend %flex-row-centered;
	height: 140rpx;
	border-radius: 10rpx;
	margin-top: $ns-margin;

	.line{
		width: 2rpx;
		height: 40%;
		background:rgba(243,243,243,.5);
	}
	.sction-item {
		@extend %flex-column-centered;
		line-height: 1.5;
		text {
			font-size: $ns-font-size-base;
			color: #FFFFFF;
		}
		.sction-item-name{
			color: rgba($color: #ffffff, $alpha: 0.9);
		}

		.num {
			font-size: $ns-font-size-lg + 8rpx;
		}
	}
}

.pingtuan-section {
	width: 710rpx;
	position: relative;
	border-radius: 20rpx;
	margin: 0 auto;
	box-sizing: border-box;
	background-color: white;
	padding: 32rpx 28rpx;
	.pingtuan-head {
		//@extend %flex-list;
		width: 100%;
		display: flex;
		justify-content: space-between;
		align-items: center;
		box-sizing: border-box;
		&-left{
			font-size: 30rpx;
			font-weight: 700;
			color: rgba(0, 0, 0, 1);
		}
		&-right{
			font-size: 26rpx;
			font-weight: 400;
			color: rgba(166, 166, 166, 1);
			.iconright{
				font-size: 24rpx;
			}
		}

		.order-tip {
			margin-left: auto;
			margin-right: 6rpx;
			color: #9A9A9A;
			font-size: $ns-font-size-sm;
		}

		.order-more {
			color: #CCCCCC;
			font-size: $ns-font-size-sm;
		}
	}

	.pingtuan-body {
		@extend %flex-row-centered;
		//height: 152rpx;
		width: 100%;
		box-sizing: border-box;
		margin: 0 auto;
		margin-top: 34rpx;

		.order-item {
			@extend %flex-column-centered;

			.order-icon {
				position: relative;
				line-height: 1;

				.order-num {
					position: absolute;
					top: -18rpx;
					right: -22rpx;
					width: 30rpx;
					height: 30rpx;
					font-size: $ns-font-size-sm;
					line-height: 30rpx;
					text-align: center;
					color: #fff;
					padding: 6rpx;
					border-radius: 50%;
					z-index: 99;
				}
				image{
					width: 50rpx;
					height: 50rpx;
				}
				.iconfont {
					font-size: $ns-font-size-lg + 16rpx;
				}
			}

			.order-name {
				//margin-top: 10rpx;
				color: darken($ns-text-color-gray, 15%);
				font-size: $ns-font-size-sm;
				color:rgba(0,0,0,1);
			}
		}
	}
}
	.member-level{
		width: 100%;
		padding: 0 $ns-padding;
		box-sizing: border-box;
		position: relative;
		background:#FFFFFF;
		.member{
			position: absolute;
			width: 69rpx;
			height: 75rpx;
			right: 32rpx;
			bottom: 0;
			image{
				width: 69rpx;
				height: 75rpx;
			}
		}
		.member-level-box{
			width: 702rpx;
			height: 88rpx;
			padding: 0 $ns-padding;
			border-radius: 6px;
			background: #1D1D1D;
			background: linear-gradient(to right, darken($base-help-color, 43%), #1D1D1D);
			box-sizing: border-box;
			display: flex;
			justify-content: space-between;
			align-items: center;
			color: #e5ce75;

			.img-wrap{
				display: flex;
				color: #e5ce75;
				font-size: 24rpx;
				.img-v{
					height: 22rpx;
					width: 22rpx;
					margin-right: 10rpx;
				}
			}

			.memeber-tit{
				margin-right: 16rpx;
			}
			.member-title{
				margin-right: 10rpx;
				font-size: 20rpx;
			}
			text{
				font-size: 24rpx;
				line-height: 1;
				display: flex;
				align-items: center;
				text{
					display: inline-block;
				}
			}
		}
	}
.advertising{
	width: 710rpx;
	height: 240rpx;
	margin-top: 24rpx;
	&-item{
		width: 100%;
		height: 240rpx;
	}
}
.distribution-section {
	margin-top: 20rpx;
	background-color: #fff;
	border-radius: 10rpx;

	.distribution-head {
		@extend %flex-list;
		border: none;

		.distribution-tip {
			margin-left: auto;
			margin-right: 6rpx;
			color: darken($ns-text-color-gray, 15%);
		}

		.distribution-tit {
			font-size: $ns-font-size-lg + 4rpx;
		}

		.iconfont {
			color: lighten($ns-text-color-gray, 10%);
			font-size: $ns-font-size-sm;
		}
	}

	.distribution-body {
		@extend %flex-row-centered;
		height: 160rpx;
		box-sizing: border-box;

		.distribution-item {
			@extend %flex-column-centered;

			.num {
				font-size: $ns-font-size-lg + 8rpx;
				line-height: 1;
			}

			.name {
				margin-top: 10rpx;
				color: lighten($ns-text-color-gray, 10%);
			}
		}
	}
}

.example-body {
	padding: 20rpx 0rpx 30rpx;
	background: #fff;
	.example-body-head{
		display: flex;
		-webkit-box-pack: justify;
		-webkit-justify-content: space-between;
		justify-content: space-between;
		padding: 0 30rpx;
		height: 100rpx;
		line-height: 100rpx;
	}
	.example-tit{
		font-size: 32rpx;
		font-weight: 600;
	}
}


.image {
	width: 48rpx;
	height: 48rpx;
}

.text {
	font-size: $ns-font-size-sm;
	margin-top: 10rpx;
	color:#343434
}

.copyright-info {
	margin-bottom: 40rpx;
	@extend %flex-column-centered;

	.copyright-pic {
		width: 180rpx;
		height: 180rpx;
		margin: auto;
		image {
			width:100%;
			height: 100% !important;
		}
	}

	text {
		font-size: $ns-font-size-sm;
		height: 100rpx;
		line-height: 100rpx;
		color: $ns-text-color-gray;
	}

	.copyright-desc {
		color: lighten($ns-text-color-gray, 30%);
		font-size: $ns-font-size-sm;
		text-shadow: 0 0 1px lighten($ns-text-color-gray, 40%);
	}
}

.member-adv {
	display: flex;
	justify-content: space-between;
	height: 152rpx;
	box-sizing: border-box;
	border-radius: 10rpx;
	margin: 20rpx;

	> navigator {
		position: relative;
		width: 344rpx;

		image {
			display: block;
			max-width: 100%;
			max-height: 100%;
		}
	}
}
/deep/ .container {
	background-color: transparent;
}

.member-body {
	margin: 0 20rpx;
	// margin-top: -95rpx;
	margin-top: 20rpx;
	.order-section {
		margin-bottom: 20rpx;
		border-radius: 20rpx;
	}
	.example-body {
		margin: 0;
		margin-top: 24rpx;
		margin-bottom: 24rpx;
		padding: 17rpx 0;
		border-radius: 20rpx;
		.user-tools {
			position: relative;
			height: 100rpx;
			display: flex;
			align-items: center;
			overflow: hidden;
			.text {
				font-size: 30rpx;
			}
			.line {
				position: absolute;
				left: 80rpx;
				bottom: 0;
				width: 100%;
				height: 1rpx;
				background-color: #EEEEEE;
			}
			.arrow-right {
				position: absolute;
				right: 20rpx;
			}
			.image-box {
				width: 80rpx;
				height: 100rpx;
				display: flex;
				align-items: center;
				justify-content: center;
			}
		}
	}
}
.user-card {
	//padding: 0;
	//height: 282rpx;
	background: none!important;
}
.ns {
  width: 100%;
  height: 128;
  font-size: 0;
}
.ns image {
  width: 100%;
  height: 128rpx;
}
.user-phone {
	font-size: 26rpx!important;
	display: flex;
	align-items: center;
	color: rgba(128, 128, 128, 1)!important;
	.bind-btn{
		background: rgba(255,255,255,0.3);
		width: 120rpx;
		height: 36rpx;
		line-height: 36rpx;
		text-align: center;
		border-radius: 18rpx;
		color:#fff;
		font-size: $ns-font-size-sm - 2rpx !important;
		margin-left: 12rpx;
	}
	.level{
		width: 156rpx;
		height: 36rpx;
		background: linear-gradient(15deg, #626262 0%, #262826 100%);
		border-radius: 18rpx;
		margin-left: 12rpx;
		color:var(--custom-brand-color-40) !important;
		font-size: $ns-font-size-sm - 2rpx !important;
		overflow: hidden;
		// text-align: center;
		padding: 0 14rpx;
		image{
			width: 20rpx;
			height: 20rpx;
			margin-right: 10rpx;
		}
	}
}
.high-bg-color {
	background-color: var(--custom-brand-color);
}

.qrcode{
	display: flex;
	flex-direction: column;
	.code-item{
		background: #fff;
		border: 1px solid var(--custom-brand-color);
		width: 140rpx;
		height: 56rpx;
		line-height: 56rpx;
		font-size: 24rpx;
		color: var(--custom-brand-color);
		border-radius: 28rpx;
		display: inline-flex;
		align-items: center;
		justify-content: center;
		box-sizing: border-box;
		&:not(:first-child){
			margin-top: 6rpx;
		}
		&-sign{
			background-color: var(--custom-brand-color);
			color: #fff;
			border: none;
		}
		//padding: 4rpx 24rpx;
		.icon{
			width: 20rpx;
			height: 20rpx;
			margin-right: 10rpx;
		}
		.arrow-icon{
			width: 6rpx;
			height: 8rpx;
			margin-left: 10rpx;
		}
	}
}
.user-setting{
	flex: auto;
	text-align: end;
	align-self: flex-start;
	margin-top: 10rpx;
	&-image{
		font-size: 36rpx;
		color: rgba(56, 56, 56, 1);
		font-weight: bold;
	}
}

// 钱包和优惠券
.wallet-body{
	width: 710rpx;
	margin: 0 auto;
	margin-top: 30rpx;
	/*  #ifdef MP */
	margin-top: 30rpx;
	/* #endif */
	display: flex;
	justify-content: space-between;
	position: relative;
	&-one{
		width: 344rpx;
		height: 106rpx;
		border-radius: 20rpx;
		background-color: rgba(255, 255, 255, 1);
		display: flex;
		align-items: center;
		justify-content: space-between;
		padding: 0 16rpx;
		box-sizing: border-box;
		&-left{
			display: flex;
			align-items: center;
			&-icon{
				width: 40rpx;
				height: 40rpx;
				margin-right: 6rpx;
			}
			&-center{
				display: flex;
				align-items: center;
				&-text{
					font-size: 26rpx;
					font-weight: 400;
					color: rgba(128, 128, 128, 1);
					margin-right: 20rpx;
				}
				&-number{
					font-size: 36rpx;
					font-weight: 700;
					color: rgba(56, 56, 56, 1);
					&-symbol{
						font-size: 24rpx;
					}
				}
			}
		}
		&-right{
			.iconright{
				font-size: 24rpx;
				color: rgba(128, 128, 128, 1);
			}
		}
	}
}


// 订单模块
.balance-body{
	position: relative;
	width: 710rpx;
	border-radius: 20rpx;
	margin: 0 auto;
	margin-top: 20rpx;
	box-sizing: border-box;
	background-color: white;
	padding: 32rpx 28rpx;
	.order-header{
		display: flex;
		justify-content: space-between;
		align-items: center;
		&-left{
			font-size: 30rpx;
			font-weight: 700;
			color: rgba(0, 0, 0, 1);
		}
		&-right{
			font-size: 26rpx;
			font-weight: 400;
			color: rgba(166, 166, 166, 1);
			.iconright{
				font-size: 24rpx;
			}
		}
	}
	.order-section{
		width: 100%;
		box-sizing: border-box;
		margin: 0 auto;
		margin-top: 34rpx;

		.order-body {
			@extend %flex-row-centered;
			justify-content: space-between;
			box-sizing: border-box;
			margin-top: 0;
			height: 100%;
			align-items: center;

			.order-item {
				@extend %flex-column-centered;

				.order-icon {
					position: relative;
					line-height: 1;

					.order-num {
						position: absolute;
						top: -18rpx;
						right: -22rpx;
						width: 30rpx;
						height: 30rpx;
						font-size: $ns-font-size-sm;
						line-height: 30rpx;
						text-align: center;
						color: #fff;
						padding: 6rpx;
						border-radius: 50%;
						z-index: 99;
					}
					image{
						width: 50rpx;
						height: 50rpx;
					}
					.iconfont {
						font-size: $ns-font-size-lg + 16rpx;
					}
				}

				.order-name {
					//margin-top: 10rpx;
					color: darken($ns-text-color-gray, 15%);
					font-size: $ns-font-size-sm;
					color:rgba(0,0,0,1);
				}
			}
		}
		.order-body{

		}
	}
	.order-wait-pay{
		display: flex;
		justify-content: space-between;
		border-radius: 10rpx;
		background-color: var(--custom-brand-color-10);
		padding-right: 18rpx;
		box-sizing: border-box;
		margin-top: 20rpx;
		&-left{
			display: flex;
			align-items: center;
			&-img{
				width: 80rpx;
				height: 80rpx;
				border-radius: 10rpx 0 0 10rpx;
			}
			&-info{
				display: flex;
				flex-direction: column;
				margin-left: 12rpx;
				&-title{
					width: 410rpx;
					font-size: 26rpx;
					font-weight: 400;
					text-overflow: ellipsis;
					overflow: hidden;
					word-break: break-all;
					white-space: nowrap;
					color: #000;
					line-height: 28rpx;
				}
				&-two{
					color: rgba(56, 56, 56, 1);
					font-size: 22rpx;
					display: flex;
					align-items: center;
					line-height: 24rpx;
					margin-top: 6rpx;
					&-price{
						color: var(--custom-brand-color);
					}
					/deep/ .custom {
						display: flex;
						align-items: center;
					}
					/deep/ .custom :nth-child(odd) {
						color: var(--custom-brand-color);
						border-radius: 6rpx;
						font-size:22rpx;
						line-height: 24rpx;
					}
					/deep/ .custom :nth-child(even) {
						padding: 0 2rpx;
						color: var(--custom-brand-color);
						line-height: 24rpx;
					}
				}
			}
		}
		&-right{
			display: flex;
			align-items: center;
			&-btn{
				width: 120rpx;
				height: 52rpx;
				border-radius: 100rpx;
				background: var(--custom-brand-color);
				display: flex;
				justify-content: center;
				align-items: center;
				font-size: 26rpx;
				font-weight: 400;
				color: rgba(255, 255, 255, 1);
			}
		}
	}
}
.fenxiao-btn{
	position: absolute;
	right: 0;
	top: 36rpx;
	z-index: 99;
	width: 176rpx;
	background: #FFF5DA;
	height: 56rpx;
	border-radius: 28rpx 0 0 28rpx;
	display: flex;
	justify-content: center;
	align-items: center;
	font-size:$ns-font-size-sm - 2rpx;
	color: var(--custom-brand-color);
	image{
		width: 32rpx;
		height: 32rpx;
		margin-right: 10rpx;
	}
}
.profit-section{
	background-color: #fff;
	display: flex;
	align-items: center;
	border-radius: 20rpx;
	margin-bottom: 20rpx;
	.profit-item{
		width: 33%;
		text-align: center;
		padding: 30rpx 0;
		.top{
			font-size: $ns-font-size-lg + 4rpx;
			color: #343434;
			text{
				font-size:$ns-font-size-base;
				font-weight: bold;
			}
		}
		.title{
			font-size: $ns-font-size-sm;
			color: #9A9A9A;
			display: flex;
			align-items: center;
			justify-content: center;
			image{
				width: 23rpx;
				height: 23rpx;
				margin-left: 6rpx;
			}
		}
		&::after{
			content:'';
			width: 1px;
			height: 60rpx;
			background: #eee;
			display: inline-block;
			float: right;
			margin-top: -80rpx;
		}
		&:last-child{
			&::after{
				display: none;
			}
		}
	}

}
