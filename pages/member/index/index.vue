<template>
	<view :class="themeStyle" :style="[themeColorVar]">
		<view class="container">
			<!-- 签到入口 -->
			<!-- <view class="signIn" :style="{ top: navHeight + 'rpx' }" @click="redirectToLink('/otherpages/member/signin/signin')">签到</view> -->
			<view class="member-container">
				<view class="user-section user-card" :class="themeStyle">
					<view class="bg-img">
<!--            <view class="bg-img-one"></view>-->
<!--            <view class="bg-img-two"></view>-->
            <image :src="$util.img('public/static/youpin/member/round.png')" mode="widthFix" class="bg-img-inner"></image>
          </view>
					<view class="user-section-box">
						<view class="user-info-box" v-if="token">
							<view hover-class="none" class="user-head" @click="redirectToLink('/otherpages/member/update_headimg_nickname/update_headimg_nickname')">
								<image
									:src="memberInfo.headimg ? $util.img(memberInfo.headimg) : $util.getDefaultImage().default_headimg"
									mode="aspectFill"
									@error="memberInfo.headimg = $util.getDefaultImage().default_headimg"
								></image>
							</view>
							<view class="user-box">
								<view class="user-title" @click="redirectToLink('/otherpages/member/update_headimg_nickname/update_headimg_nickname')">{{ memberInfo.nickname }}
<!--                  <uni-icons type="compose" size="15" color='rgba(0, 0, 0, 0.314)' class="user-title-edit"></uni-icons>-->
                  <image :src="$util.img('public/static/youpin/member/edit.png')" class="user-title-edit"></image>
                </view>
								<view class="user-phone">
									{{ memberInfo.mobile }}
									<view class="level" v-if="false && !memberInfo.show_apply_btn"><image :src="$util.img('public/static/youpin/level_icon.png')" mode=""></image>{{memberInfo.grade_name || '--'}}</view>
									<!-- 是否显示强绑定按钮 -->
									<view v-if="token && memberInfo.show_enforce_bind_btn" class="bind-btn" @click="$refs.popup.open()">强绑店铺</view>
								</view>
                <view class="user-desc">
                  <view class="user-desc-badge">
                    <image :src="$util.img('public/static/youpin/member/medal.png')" class="user-desc-badge-img"></image>
                    <text class="user-desc-badge-text">{{memberInfo.reg_day}}天</text>
                  </view>
<!--                  <view class="user-desc-thrift" v-if="is_shopper">累计省：0元</view>-->
                  <view class="user-desc-item" @click="qrcode_popup('wechat_qrcode')" v-if="memberInfo.wechat_qrcode_img">+ 加店主</view>
                  <view class="user-desc-item" @click="qrcode_popup('wechat_group_qrcode')" v-if="memberInfo.wechat_group_qrcode_img">+ 加群聊</view>
                </view>
							</view>
              <view class="qrcode">
                <!--							<view class="code-item" v-if="memberInfo.wechat_qrcode_img" @click="qrcode_popup('wechat_qrcode')"><image class="icon" src="/static/imgs/qrcode-icon.png" mode="widthFix"></image>加店主<image class="arrow-icon" src="/static/imgs/mbr-qrcode-arrowright.png" mode=""></image></view>-->
                <!--							<view class="code-item" v-if="memberInfo.wechat_group_qrcode_img" @click="qrcode_popup('wechat_group_qrcode')"><image class="icon" src="/static/imgs/qrcode-icon.png" mode="widthFix"></image>加群聊<image class="arrow-icon" src="/static/imgs/mbr-qrcode-arrowright.png" mode=""></image></view>-->
                <view class="code-item code-item-sign" @click="redirectToLink(`/otherpages/member/signin/sign_in_product_rewards?sign_activity_id=${sign_activity_id}`)" v-if="sign_activity_id">签到</view>
              </view>
<!--              <view class="user-setting">-->
<!--                <text class="iconfont iconshezhi user-setting-image"></text>-->
<!--              </view>-->
						</view>
						<view class="user-info-box no-log" v-if="!token" @click="redirectToLink('/pages/member/index/index')">
							<view class="user-head"><image :src="$util.getDefaultImage().default_headimg"></image></view>
							<view class="user-box">
								<text class="user-title">立即登录</text>
							</view>
						</view>
					</view>

					<!-- 是否显示申请分销客按钮 -->
					<view v-if="false && token && memberInfo.show_apply_btn" class="fenxiao-btn" @click="redirectToLink('/otherpages/fenxiaoke/apply/apply')">
						<image :src="$util.img('public/static/youpin/fenxiao-icon.png')" mode=""></image>
						申请分销客
					</view>

				</view>

        <!--   钱包和优惠券     -->
        <view class="wallet-body">
          <view class="wallet-body-one" @click="redirectToLink('/otherpages/member/balance/balance')">
            <view class="wallet-body-one-left">
              <image :src="$util.img('public/static/youpin/member/balance.png')" mode="" class="wallet-body-one-left-icon"></image>
              <view class="wallet-body-one-left-center">
                <text class="wallet-body-one-left-center-text">余额</text>
                <view class="wallet-body-one-left-center-number"><text class="wallet-body-one-left-center-number-symbol">￥</text>{{memberInfo.balance_money}}</view>
              </view>
            </view>
            <view class="wallet-body-one-right">
              <text class="iconfont iconright"></text>
            </view>
          </view>
          <view class="wallet-body-one" @click="redirectToLink('/otherpages/member/coupon/coupon')">
            <view class="wallet-body-one-left">
              <image :src="$util.img('public/static/youpin/member/coupon.png')" mode="" class="wallet-body-one-left-icon"></image>
              <view class="wallet-body-one-left-center">
                <text class="wallet-body-one-left-center-text">优惠券</text>
                <view class="wallet-body-one-left-center-number">{{couponNum.receipt_count}}</view>
              </view>
            </view>
            <view class="wallet-body-one-right">
              <text class="iconfont iconright"></text>
            </view>
          </view>
        </view>

				<view class="balance-body">
<!--					<view class="order-header" @click="redirectToLink('/pages/order/list/list')">-->
<!--            <text class="order-header-left">我的订单</text>-->
<!--            <view class="order-header-right">全部<text class="iconfont iconright"></text></view>-->
<!--          </view>-->
          <!-- 订单模块 -->
          <view class="order-section">
            <view class="order-body">
              <!-- 待支付 -->
              <view class="order-item" @click="redirectToLink('/pages/order/list/list?status=waitpay')">
                <view class="order-icon">
                  <text v-if="orderNum.waitPay > 99" class="order-num high-bg-color">99+</text>
                  <text v-else-if="orderNum.waitPay > 0" class="order-num high-bg-color">{{ orderNum.waitPay }}</text>
                  <image :src="$util.img('public/static/youpin/member/order_1.png')" mode=""></image>
                </view>
                <text class="order-name">待付款</text>
              </view>
              <!-- 待发货 -->
              <view class="order-item" @click="redirectToLink('/pages/order/list/list?status=waitsend')">
                <view class="order-icon">
                  <text v-if="orderNum.readyDelivery > 99" class="order-num high-bg-color">99+</text>
                  <text v-else-if="orderNum.readyDelivery > 0" class="order-num high-bg-color">{{ orderNum.readyDelivery }}</text>
                  <image :src="$util.img('public/static/youpin/member/order_2.png')" mode=""></image>
                </view>
                <text class="order-name">待发货</text>
              </view>
              <!-- 已发货 -->
              <view class="order-item" @click="redirectToLink('/pages/order/list/list?status=waitconfirm')">
                <view class="order-icon">
                  <text v-if="orderNum.waitDelivery > 99" class="order-num high-bg-color">99+</text>
                  <text v-else-if="orderNum.waitDelivery > 0" class="order-num high-bg-color">{{ orderNum.waitDelivery }}</text>
                  <image :src="$util.img('public/static/youpin/member/order_3.png')" mode=""></image>
                </view>
                <text class="order-name">已发货</text>
              </view>
              <!-- 售后单 -->
              <view class="order-item" @click="redirectToLink('/pages/order/activist/activist')">
                <view class="order-icon">
                  <text v-if="orderNum.refunding > 99" class="order-num high-bg-color">99+</text>
                  <text v-else-if="orderNum.refunding > 0" class="order-num high-bg-color">{{ orderNum.refunding }}</text>
                  <image :src="$util.img('public/static/youpin/member/order_4.png')" mode=""></image>
                </view>
                <text class="order-name">售后</text>
              </view>
              <view class="order-item" @click="redirectToLink('/pages/order/list/list')">
                <view class="order-icon">
                  <image :src="$util.img('public/static/youpin/member/order_0.png')" mode=""></image>
                </view>
                <text class="order-name">我的订单</text>
              </view>
            </view>
          </view>
          <!--   最新未支付订单       -->
          <template v-if="waitPayOrderList.length>0">
            <view class="order-wait-pay" v-for="(item,index) in waitPayOrderList.slice(0,1)" :key="index">
              <view class="order-wait-pay-left">
                <image :src="$util.img(item.order_goods[0].sku_image)" class="order-wait-pay-left-img"
                       @error="orderImageChange(index,0)"></image>
                <view class="order-wait-pay-left-info" v-for="(goods,j) in item.order_goods.slice(0,1)" :key="j">
                  <view class="order-wait-pay-left-info-title">{{goods.goods_name}}</view>
                  <view class="order-wait-pay-left-info-two">
                    已享优惠<text class="order-wait-pay-left-info-two-price">{{goods.promotion_money}}元</text>，剩余
                    <countdown-timer ref="countdown" :time="item.rest_pay_time*1000" @finish="onFinish" autoStart></countdown-timer>
                  </view>
                </view>
              </view>
              <view class="order-wait-pay-right">
                <view class="order-wait-pay-right-btn" @click="orderDetail(item)" v-if="sign_activity_id">去支付</view>
              </view>
            </view>
          </template>
				</view>

				<!-- 升级成为店主 -->
<!--				<view class="up-dianzhu" v-if="token && !is_shopper">-->
<!--					<image v-if="memberInfo.self_shop_status!=1" :src="$util.img('public/static/youpin/maidou/uptoowner.png')" mode="" @click="redirectToLink(memberInfo.upgrade_vip_url)"></image>-->
<!--					<image v-else :src="$util.img('public/static/youpin/maidou/uptoshoper.png')" mode=""  @click="redirectToLink(memberInfo.upgrade_vip_url)"></image>-->
<!--				</view>-->

				<view class="member-body">

					<!-- 收益模块 -->
<!--					<view class="profit-section" v-if="!memberInfo.show_apply_btn && token">-->
<!--						<view class="profit-item" @click="redirectToLink('/otherpages/member/earnings/earnings?type=1')">-->
<!--							<view class="top"><text>￥</text>{{memberInfo.today_benefit||'0.00'}}</view>-->
<!--							<view class="title">今日收益<image :src="$util.img('public/static/youpin/icon-question.png')" mode="" @click.stop="openDescPopup(memberInfo.today_benefit_desc)"></image></view>-->
<!--						</view>-->
<!--						<view class="profit-item" @click="redirectToLink('/otherpages/member/earnings/earnings?type=2')">-->
<!--							<view class="top"><text>￥</text>{{memberInfo.earned_benefit||'0.00'}}</view>-->
<!--							<view class="title">累计收益<image :src="$util.img('public/static/youpin/icon-question.png')" mode="" @click.stop="openDescPopup(memberInfo.earned_benefit_desc)"></image></view>-->
<!--						</view>-->
<!--						<view class="profit-item" @click="redirectToLink('/otherpages/member/earnings/earnings?type=3')">-->
<!--							<view class="top"><text>￥</text>{{memberInfo.pre_bennefit||'0.00'}}</view>-->
<!--							<view class="title">预估收益<image :src="$util.img('public/static/youpin/icon-question.png')" mode="" @click.stop="openDescPopup(memberInfo.pre_benefit_desc)"></image></view>-->
<!--						</view>-->
<!--					</view>-->
					<!-- 拼团订单模块 -->
					<view class="pingtuan-section" v-if="false">
<!--            <image class="pingtuan-section-bg" :src="$util.img('public/static/youpin/member/long-bg.png')" mode="widthFix"></image>-->
						<view class="pingtuan-head" @click="redirectToLink('/promotionpages/pintuan/order/list/list?status=all')">
							<text class="pingtuan-head-left">拼团订单</text>
              <view class="pingtuan-head-right">全部<text class="iconfont iconright"></text></view>
						</view>
						<view class="pingtuan-body">
							<!-- 待支付 -->
							<view class="order-item" @click="redirectToLink('/promotionpages/pintuan/order/list/list?status=0')">
								<view class="order-icon">
									<text v-if="pintuanNum.waitPay > 99" class="order-num high-bg-color">99+</text>
									<text v-else-if="pintuanNum.waitPay > 0" class="order-num high-bg-color">{{ pintuanNum.waitPay }}</text>
									<image :src="$util.img('public/static/youpin/member/order_1.png')" mode=""></image>
								</view>
								<text class="order-name">待付款</text>
							</view>
							<!-- 进行中 -->
							<view class="order-item" @click="redirectToLink('/promotionpages/pintuan/order/list/list?status=2')">
								<view class="order-icon">
									<text v-if="pintuanNum.waitComeIn > 99" class="order-num high-bg-color">99+</text>
									<text v-else-if="pintuanNum.waitComeIn > 0" class="order-num high-bg-color">{{ pintuanNum.waitComeIn }}</text>
									<image :src="$util.img('public/static/youpin/pintuan/peding.png')" mode=""></image>
								</view>
								<text class="order-name">进行中</text>
							</view>
							<!-- 已成功 -->
							<view class="order-item" @click="redirectToLink('/promotionpages/pintuan/order/list/list?status=3')">
								<view class="order-icon">
									<text v-if="pintuanNum.success > 99" class="order-num high-bg-color">99+</text>
									<text v-else-if="pintuanNum.success > 0" class="order-num high-bg-color">{{ pintuanNum.success }}</text>
									<image :src="$util.img('public/static/youpin/pintuan/success.png')" mode=""></image>
								</view>
								<text class="order-name">已成功</text>
							</view>
							<!-- 已失败 -->
							<view class="order-item" @click="redirectToLink('/promotionpages/pintuan/order/list/list?status=1')">
								<view class="order-icon">
									<text v-if="pintuanNum.failed > 99" class="order-num high-bg-color">99+</text>
									<text v-else-if="pintuanNum.failed > 0" class="order-num high-bg-color">{{ pintuanNum.failed }}</text>
									<image :src="$util.img('public/static/youpin/pintuan/shibai.png')" mode=""></image>
								</view>
								<text class="order-name">已失败</text>
							</view>
						</view>
					</view>
<!--          <view class="two-area">-->
<!--            <view class="two-area-one" :style="'background-image:url('+$util.img('public/static/youpin/member/short-bg.png')+')'">-->
<!--              <view class="two-area-one-title" @click="redirectToLink('/otherpages/member/coupon/coupon')">我的优惠券<text class="iconfont iconright"></text></view>-->
<!--              <view class="two-area-one-content">-->
<!--                <view class="two-area-one-content-item" @click="redirectToLink('/otherpages/member/coupon/coupon')">-->
<!--                  <text class="two-area-one-content-item-number">{{couponNum.receipt_count}}</text>-->
<!--                  <text class="two-area-one-content-item-label">待使用</text>-->
<!--                </view>-->
<!--                <view class="two-area-one-content-item" @click="redirectToLink('/otherpages/member/coupon/coupon?selectIndex=2')">-->
<!--                  <text class="two-area-one-content-item-number">{{couponNum.used_count}}</text>-->
<!--                  <text class="two-area-one-content-item-label">已使用</text>-->
<!--                </view>-->
<!--              </view>-->
<!--            </view>-->
<!--            <view class="two-area-one" :style="'background-image:url('+$util.img('public/static/youpin/member/short-bg.png')+')'">-->
<!--              <view class="two-area-one-title" @click="redirectToLink('/promotionpages/seeding/seeding_home_page/seeding_home_page')">我的种草<text class="iconfont iconright"></text></view>-->
<!--              <view class="two-area-one-content" v-if="seedingNum.check_nums > 0 || seedingNum.nums>0">-->
<!--                <view class="two-area-one-content-item" @click="redirectToLink('/promotionpages/seeding/seeding_home_page/seeding_home_page')">-->
<!--                  <text class="two-area-one-content-item-number">{{seedingNum.check_nums}}</text>-->
<!--                  <text class="two-area-one-content-item-label">审核中</text>-->
<!--                </view>-->
<!--                <view class="two-area-one-content-item" @click="redirectToLink('/promotionpages/seeding/seeding_home_page/seeding_home_page')">-->
<!--                  <text class="two-area-one-content-item-number">{{seedingNum.nums}}</text>-->
<!--                  <text class="two-area-one-content-item-label">已发布</text>-->
<!--                </view>-->
<!--              </view>-->
<!--              <view class="two-area-one-content two-area-one-empty" v-else>-->
<!--                <view class="two-area-one-empty-title">主人您还没发布过贴子哦</view>-->
<!--                <view class="two-area-one-empty-btn" @click="redirectToLink('/promotionpages/seeding/seeding-list/seeding-list?show_publish=1')">去发布</view>-->
<!--              </view>-->
<!--            </view>-->
<!--          </view>-->
          <!--   广告位   -->
          <swiper class="swiper advertising" circular v-if="adList && adList.length">
            <swiper-item v-for="(item,index) in adList" :key="index">
              <view class="swiper-item" @click="toAd(item)">
                <image :src="$util.img(item.image_url)" class="advertising-item"></image>
              </view>
            </swiper-item>
          </swiper>
					<!-- 常用功能模块 -->
					<view class="example-body">
						<uni-grid :column="4" :show-border="false" :square="false">
              <!-- 账单记录 -->
              <view @click="redirectToLink('/otherpages/member/balance/balance')">
                <uni-grid-item>
                  <image class="image" :src="$util.img('public/static/youpin/member/bill.png')" mode="aspectFill" />
                  <text class="text">账单记录</text>
                </uni-grid-item>
              </view>
							<!-- 我的优惠券 -->
<!--							<view  @click="redirectToLink('/otherpages/member/coupon/coupon')">-->
<!--								<uni-grid-item>-->
<!--									<image class="image" :src="$util.img('upload/uniapp/member/index/discount.png')" mode="aspectFill" />-->
<!--									<text class="text">{{ $lang('couponList') }}</text>-->
<!--								</uni-grid-item>-->
<!--							</view>-->
							<!-- 我的店铺 -->
<!--							<view @click="redirectToLink('/otherpages/member/my_shop/my_shop')" v-if="memberInfo.self_shop_status==1">-->
							<view @click="redirectToLink('/otherpages/member/shop_manage/shop_manage')" v-if="memberInfo.self_shop_status==1">
								<uni-grid-item>
									<image class="image" :src="$util.img('public/static/youpin/maidou/shops.png')" mode="aspectFill" />
									<text class="text">我的店铺</text>
								</uni-grid-item>
							</view>
							<!-- 我的迈豆 -->
<!--							<view @click="redirectToLink('/otherpages/member/myMaidou/myMaidou')">-->
<!--								<uni-grid-item>-->
<!--									<image class="image" :src="$util.img('public/static/youpin/maidou/maidou.png')" mode="aspectFill" />-->
<!--									<text class="text">{{ $lang('myMaidou') }}</text>-->
<!--								</uni-grid-item>-->
<!--							</view>-->
              <!-- 实名认证 -->
              <view  @click="redirectToLink('/otherpages/member/real_name_authentication/real_name_authentication')">
                <uni-grid-item>
                  <image class="image" :src="$util.img('public/static/youpin/realname.png')" mode="aspectFill" />
                  <text class="text">实名认证</text>
                </uni-grid-item>
              </view>
              <!-- 我的足迹 -->
              <view @click="redirectToLink('/otherpages/member/footprint/footprint')">
                <uni-grid-item>
                  <image class="image" :src="$util.img('public/static/youpin/member/footprint.png')" mode="aspectFill" />
                  <text class="text">我的足迹</text>
                </uni-grid-item>
              </view>
							<!-- 我的收藏 -->
							<!-- <view @click="redirectToLink('/otherpages/member/collection/collection')">
								<uni-grid-item>
									<image class="image" :src="$util.img('public/static/youpin/like.png')" mode="aspectFill" />
									<text class="text">{{ $lang('myCollection') }}</text>
								</uni-grid-item>
							</view> -->
              <!-- 银行卡管理 -->
              <view  @click="redirectToLink('/otherpages/member/bank_card_list/bank_card_list')">
                <uni-grid-item>
                  <image class="image" :src="$util.img('public/static/youpin/bankcard.png')" mode="aspectFill" />
                  <text class="text">银行卡管理</text>
                </uni-grid-item>
              </view>
              <!-- 在线客服 -->
              <view  @click="$util.getCustomerService()">
                <uni-grid-item>
                  <image class="image" :src="$util.img('public/static/youpin/online.png')" mode="aspectFill" />
                  <text class="text">客服</text>
                </uni-grid-item>
              </view>
              <!-- 资质 -->
              <view @click="redirectToLink('/otherpages/member/certification/certification')">
                <uni-grid-item>
                  <image class="image" :src="$util.img('public/static/youpin/certification.png')" mode="aspectFill" />
                  <text class="text">平台资质</text>
                </uni-grid-item>
              </view>
              <!-- 设置 -->
              <view @click="redirectToLink('/otherpages/member/setting/setting?phonenum='+memberInfo.phone)">
                <uni-grid-item>
                  <image class="image" :src="$util.img('public/static/youpin/maidou/setting.png')" mode="aspectFill" />
                  <text class="text">设置</text>
                </uni-grid-item>
              </view>
							<!-- 收货地址 -->
							<view  @click="redirectToLink('/otherpages/member/address/address')">
								<uni-grid-item>
									<image class="image" :src="$util.img('public/static/youpin/address.png')" mode="aspectFill" />
									<text class="text">收货地址</text>
								</uni-grid-item>
							</view>
              <!-- 商家入驻 -->
<!--              <view  @click="$util.diyCompateRedirectTo({wap_url:'https://www.xianmaism.com/join_mall'})">-->
<!--                <uni-grid-item>-->
<!--                  <image class="image" :src="$util.img('public/static/youpin/member/merchants-settle-in.png')" mode="aspectFill" />-->
<!--                  <text class="text">商家入驻</text>-->
<!--                </uni-grid-item>-->
<!--              </view>-->
              <!-- 升级邀请 -->
              <view  @click="redirectToLink('/otherpages/member/up_to_shopkeeper/upgrade_invitation')" v-if="memberInfo.show_shop_invite_page">
                <uni-grid-item>
                  <image class="image" :src="$util.img('public/static/youpin/member/yidengluvip.png')" mode="aspectFill" />
                  <text class="text">升级邀请</text>
                </uni-grid-item>
              </view>
              <!-- 每日签到 -->
              <view  @click="redirectToLink(`/otherpages/member/signin/sign_in_product_rewards?sign_activity_id=${sign_activity_id}`)" v-if="sign_activity_id">
                <uni-grid-item>
                  <image class="image" :src="$util.img('public/static/youpin/member/sign_in_icon.png')" mode="aspectFill" />
                  <text class="text">每日签到</text>
                </uni-grid-item>
              </view>
							<!-- 活动申请 -->
<!--							<view @click="redirectToLink('/otherpages/member/event_application_list/event_application_list')">-->
<!--								<uni-grid-item>-->
<!--									<image class="image" :src="$util.img('public/static/youpin/eventapply.png')" mode="aspectFill" />-->
<!--									<text class="text">{{ $lang('eventApplication') }}</text>-->
<!--								</uni-grid-item>-->
<!--							</view>-->
							<!-- 我的礼品 -->
<!--							<view @click="redirectToLink('/promotionpages/game/gift/gift')">-->
<!--								<uni-grid-item>-->
<!--									<image class="image" src="/static/imgs/gift.png" mode="aspectFill" />-->
<!--									<text class="text">{{ $lang('winningRecord') }}</text>-->
<!--								</uni-grid-item>-->
<!--							</view>-->
							<!-- 邀请有礼 -->
<!--							<view @click="redirectToLink('/promotionpages/pintuan/gift_invitation/gift_invitation')">-->
<!--								<uni-grid-item>-->
<!--									<image class="image" :src="$util.img('public/static/youpin/member/invite.png')" mode="aspectFill" />-->
<!--									<text class="text">{{ $lang('invite') }}</text>-->
<!--								</uni-grid-item>-->
<!--							</view>-->
              <!-- 种草主页 -->
              <view @click="redirectToLink('/promotionpages/seeding/seeding-list/seeding-list?is_not_show_nav=1')" v-if="!is_audit_mode && false">
                <uni-grid-item>
                  <image class="image" :src="$util.img('public/static/youpin/grass_home.png')" mode="aspectFill" />
                  <text class="text">种草主页</text>
                </uni-grid-item>
              </view>
              <!--      党支部        -->
              <view @click="$util.diyCompateRedirectTo({wap_url:'https://www.xianmaism.com/dangzhibu'})" v-if="!is_audit_mode">
                <uni-grid-item>
                  <image class="image" :src="$util.img('public/static/youpin/member/party.png')" mode="aspectFill" />
                  <text class="text">党支部</text>
                </uni-grid-item>
              </view>
              <!--     我的贡献值         -->
              <view @click="redirectToLink('/otherpages/member/join/contribution_list/contribution_list')" v-if=is_shopper>
                <uni-grid-item>
                  <image class="image" :src="$util.img('public/static/youpin/member/contribution/contribution.png')" mode="aspectFill" />
                  <text class="text">贡献有礼</text>
                </uni-grid-item>
              </view>
						</uni-grid>
					</view>
				</view>
        <nsGoodsRecommend ref="goodrecommend"></nsGoodsRecommend>
				<ns-login ref="login"></ns-login>
				<!-- 底部tabBar -->
				<diy-bottom-nav type="shop" :site-id="shop_id" v-if="openBottomNav"></diy-bottom-nav>

        <yd-auth-popup ref="ydauth"></yd-auth-popup>
        <ns-login ref="login"></ns-login>
        <uni-coupon-pop ref="couponPop"></uni-coupon-pop>

		<diy-uni-popup ref="popup" text="确定要强绑定该店铺吗？" @confirm="bindStore"></diy-uni-popup>

		<!-- 收益说明 -->
		<uni-popup ref="tips_popup" :maskClick="false">
		    <view class="popup-dialog desc-dialog">
				<view class="popup-dialog-body"><rich-text :nodes="descContent"></rich-text></view>
				<view class="popup-dialog-footer">
					<button class="button red" @click="$refs.tips_popup.close()">知道了</button>
				</view>
			</view>
		</uni-popup>

		<uni-popup ref="qrcode_popup">
		    <view class="popup-dialog">
				<view class="popup-dialog-body qrcode-dialog">
					<!-- 店主二维码 -->
					<view v-if="qrCodeType=='wechat_qrcode'">
						<view class="title">店主微信</view>
						<view class="sub-title">加店主微信，获得更多优惠折扣信息~</view>
						<image :src="$util.img(memberInfo.wechat_qrcode_img)" mode="widthFix"></image>
						<view class="content">
							<view class="">操作说明</view>
							<view class="">长按二维码保存至相册，再打开微信“扫一扫”功能扫码相册中的二维码图片打开二维码；</view>
						</view>
					</view>
					<!-- 店主群二维码 -->
					<view v-if="qrCodeType=='wechat_group_qrcode'">
						<view class="title">微信群聊</view>
						<view class="sub-title">加入群，跟大伙们一起交流心得~</view>
						<image :src="$util.img(memberInfo.wechat_group_qrcode_img)" mode="widthFix"></image>
						<view class="content">
							<view class="">操作说明</view>
							<view class="">长按二维码保存至相册，再打开微信“扫一扫”功能扫码相册中的二维码图片打开二维码；</view>
						</view>
					</view>
				</view>
				<!-- <view class="popup-dialog-footer">
					<button class="button red" @click="$refs.qrcode_popup.close()">关闭</button>
				</view> -->
			</view>
		</uni-popup>
			</view>
		</view>
    <diy-animation-popup :animation-data="animationData" :audioUrls="audioUrls" :audioPlayTime="audioPlayTime" ref="diyAnimationPopupRef" @startPlay="startPlay" @click="redirectToLink(animationPath)">
      <template v-if="showIncomeCount">
        <view class="income-count">￥<count-to :startVal="0" :endVal="income_money" :decimals="2" :duration="2000"></count-to></view>
      </template>
    </diy-animation-popup>
    <!-- 返回顶部 -->
    <to-top v-if="showTop" @toTop="scrollToTopNative()"></to-top>
	</view>
</template>

<script>
import diyBottomNav from '@/components/diy-bottom-nav/diy-bottom-nav.vue';
import scroll from '@/common/mixins/scroll-view.js';
import globalConfig from '@/common/mixins/golbalConfig.js';
import apiurls from '@/common/js/apiurls'
import system from "../../../common/js/system";
import nsGoodsRecommend from '@/components/ns-goods-recommend/ns-goods-recommend.vue';
import diyAnimationPopup from '@/components/diy-animation-popup/diy-animation-popup.vue'
import countTo from '@/components/dash-countTo/dash-countTo.vue'
import toTop from '@/components/toTop/toTop.vue'
import golbalConfig from "../../../common/mixins/golbalConfig";

export default {
	components: {
		diyBottomNav,
    nsGoodsRecommend,
    diyAnimationPopup,
    countTo,
    toTop
	},
	data() {
		return {
      shop_id:null,
      openBottomNav:false,
			token: '',
			memberInfo: {
				balance: 0,
				balance_money: 0,
				point: 0
			},
			couponNum: {
        receipt_count:0, // 待使用数量
        used_count:0 //已使用数量
      },
      seedingNum:{
        nums: 0, //已发布数量
        check_nums: 0 //审核中数量
      },
			orderNum: {
				waitPay: 0, //待付款
				readyDelivery: 0, //待发货
				waitDelivery: 0, //待收货
				waitEvaluate: 0, // 待评价
				refunding: 0 // 退款中
			},
			pintuanNum: {
				waitPay: 0, //待付款
				failed: 0, //拼团失败
				waitComeIn: 0, //组团中
				success: 0, // 拼团成功
			},
			isVerification: true,
			copyrightLoad: 0,
			bottom_info: {},
			authInfo: {
				is_verifier: false
			},

			shopTop: false,
			scrollTop: 0,
			shopConfig: null,
			promoterInfo: null,
			withdrawConfig: null,
			fenxiaoBasicsConfig: null,
			Bulge:false,
			descContent:'', //收益说明文本
			qrCodeType:'wechat_qrcode',// 二维码类型

			showSignIn:false, // 是否已签到
			navHeight:0,
      is_shopper:0,  //是否是店主
      adList:[],
      sign_activity_id:null, //每日签到活动id
      animationData: {},
      audioUrls:[],
      audioPlayTime:[],
      animationPath:'',
      showIncomeCount:false,
      income_money:0,
      action_time: 10*60*1000,
      waitPayOrderList: [],
		};
	},
	mixins: [scroll,globalConfig,golbalConfig],
	onLoad() {
    let shop_id=uni.getStorageSync('shop_id');
    this.shop_id=shop_id;
    this.openBottomNav=true;

	uni.getSystemInfo({
	  success: res => {
	    //导航高度
	    let navHeight = res.statusBarHeight + 36;
	    this.navHeight = navHeight;
	  },
	  fail(err) {
	    console.log(err);
	  }
	})
  },
  computed:{
    is_audit_mode(){
      return this.$store.state.audit_mode;
    }
  },
	async onShow() {
		// 刷新多语言
		this.$langConfig.refresh();
    await system.wait_staticLogin_success();
		if (uni.getStorageSync('is_register')) {
			this.$util.toShowCouponPopup(this)
			uni.removeStorageSync('is_register');
		}
    // #ifdef H5
    this.$util.toShowLoginPopup(this,null,'/pages/member/index/index');
    // #endif

		this.token = uni.getStorageSync('token');

		if (uni.getStorageSync('userInfo')) {
			this.memberInfo = uni.getStorageSync('userInfo');
		}

		if (uni.getStorageSync('authInfo')) {
			this.authInfo = uni.getStorageSync('authInfo');
		}

    if (uni.getStorageSync('is_shopper')) {
      this.is_shopper = uni.getStorageSync('is_shopper');
    }

		if (this.token) {
			await this.getMemberInfo();
			this.getOrderNum();
			this.getPintuanNum();
			this.getCouponNum();
      this.getSeedingNum();
      this.getPersonalCenter()
			this.checkIsVerifier();
      await this.getListData()
			// if (this.addonIsExit.memberwithdraw) {
			// 	this.getWithdrawConfig();
			// }
			// if (this.addonIsExit.fenxiao) {
			// 	this.getFenxiaoBasicsConfig();
			// }
		} else {
			this.initInfo();
		}
	},
  async onReady(){
    await system.wait_staticLogin_success();
    this.income_money = this.$store.state.incomeInfo.income_money
    if(this.$store.state.incomeInfo.income_nums && this.income_money){
      await this.getIncomeConfigData()
      this.income_money = parseFloat(this.income_money)
      this.$refs.diyAnimationPopupRef.showAnimation()
    }
  },
  onReachBottom(){
    this.$refs.goodrecommend.scrollPage()
  },
	methods: {
    async getIncomeConfigData(){
      try {
        let res = await this.$api.sendRequest({
          url: apiurls.getIncomeConfigUrl,
          async: false
        })
        if(res.code==0){
          await this.getAnimationData(res.data.animationData)
          this.audioUrls = res.data.audioUrls
          this.audioPlayTime = res.data.audioPlayTime
          this.animationPath = res.data.path
        }
      }catch (e) {

      }
    },
    async getAnimationData(animationDataUrl){
      try {
        let res = await this.$api.sendRequest({
          url: animationDataUrl,
          async: false
        })
        this.animationData = res
      }catch (e) {

      }
    },
    startPlay(){
      setTimeout(()=>{
        this.showIncomeCount = true
      },300)
    },
		qrcode_popup(type){
			this.qrCodeType = type;
			this.$refs.qrcode_popup.open()
		},
		isBulge(e){
			this.Bulge=e;
		},
		redirectToLink(url) {
			if (!uni.getStorageSync('token')) {
				// this.$refs.login.open(url);
        this.$util.toShowLoginPopup(this,null,'/pages/member/index/index');
			} else {
				this.$util.redirectTo(url);
			}
		},
		initInfo() {
			this.token = '';
			this.memberInfo = {
				balance: '0.00',
				balance_money: '0.00',
				point: 0
			};
			this.couponNum = {
        receipt_count:0, // 待使用数量
        used_count:0 //已使用数量
      };
      this.seedingNum = {
        nums: 0, //已发布数量
        check_nums: 0 //审核中数量
      };
			this.orderNum = {
				waitPay: 0, //待付款
				readyDelivery: 0, //待发货
				waitDelivery: 0, //待收货
				waitEvaluate: 0, // 待评价
				refunding: 0 // 退款中
			};
			this.authInfo = {
				is_verifier: false
			};
			uni.stopPullDownRefresh();
		},
		// 获取会员基础信息
		async getMemberInfo() {
			let res = await this.$api.sendRequest({
				url: '/api/member/info',
				async: false
			});
			if (res.code >= 0 && res.data) {
				this.token = uni.getStorageSync('token');
        this.sign_activity_id = res.data.sign_activity_id;
				this.memberInfo = res.data;
				this.memberInfo.phone = this.memberInfo.mobile
				if(this.memberInfo.mobile) {
					this.memberInfo.mobile = this.memberInfo.mobile.substr(0,3)+"****"+this.memberInfo.mobile.substr(7);
				}
				uni.setStorageSync('userInfo', this.memberInfo);
			} else {
				this.token = '';
				this.initInfo();
				uni.removeStorageSync('token');
			}
			uni.stopPullDownRefresh();
		},
		// 下拉刷新
		onPullDownRefresh() {
			if (uni.getStorageSync('token')) this.getMemberInfo();
			else this.initInfo();
		},
		// 订单数量
		getOrderNum() {
			this.$api.sendRequest({
				url: '/api/order/num',
				data: {
					order_status: 'waitpay,waitsend,waitconfirm,waitrate,refunding'
				},
				success: res => {
					if (res.code == 0) {
						this.orderNum.waitPay = res.data.waitpay;
						this.orderNum.readyDelivery = res.data.waitsend;
						this.orderNum.waitDelivery = res.data.waitconfirm;
						this.orderNum.waitEvaluate = res.data.waitrate;
						this.orderNum.refunding = res.data.refunding;
					}
				}
			});
		},
		// 拼团订单数量
		getPintuanNum(){
			this.$api.sendRequest({
				url:'/api/PintuanOrder/num',
				data:{},
				success:(res)=>{
					if (res.code == 0) {
						this.pintuanNum.waitPay = res.data.waitpay;
						this.pintuanNum.failed = res.data.failed;
						this.pintuanNum.waitComeIn = res.data.waitComeIn;
						this.pintuanNum.success = res.data.success;
					}
				}
			})
		},
		// 优惠券数量
		getCouponNum() {
			this.$api.sendRequest({
				// url: '/api/member/couponnum',
				url: apiurls.goodscouponNumInfoUrl,
				success: res => {
					if (res.code == 0) {
						this.couponNum = res.data;
					}
				}
			});
		},
    // 获取种草数量
    getSeedingNum(){
      this.$api.sendRequest({
        url: apiurls.usershareexperienceNumInfoUrl,
        success: res => {
          if (res.code == 0) {
            this.seedingNum = res.data;
          }
        }
      });
    },
		checkIsVerifier() {
			this.$api.sendRequest({
				url: '/api/verify/checkisverifier',
				success: res => {
					if (res.data) this.authInfo.is_verifier = true;
					else this.authInfo.is_verifier = false;
					uni.setStorageSync('authInfo', this.authInfo);
				}
			});
		},
		jumpLevel() {
			if (this.token) {
				this.$util.redirectTo('/otherpages/member/level/level');
			} else {
				this.$refs.login.open('/pages/member/index/index');
			}
		},
		/**
		 * 获取余额提现配置
		 */
		getWithdrawConfig() {
			this.$api.sendRequest({
				url: '/api/memberwithdraw/config',
				success: res => {
					if (res.code >= 0 && res.data) {
						this.withdrawConfig = res.data;
					}
				}
			});
		},

		/**
		 * 获取分销基本配置
		 */
		getFenxiaoBasicsConfig() {
			this.$api.sendRequest({
				url: '/fenxiao/api/config/basics',
				success: res => {
					if (res.code >= 0) {
						this.fenxiaoBasicsConfig = res.data;
					}
				}
			});
		},
		toLevel(){
			this.$util.r
		},
		/**
		 * 强绑店铺
		**/
		bindStore(){
			this.$refs.popup.closePopup()
		},
		openDescPopup(value){
			this.descContent = value || '暂无数据';
			this.$refs.tips_popup.open()
		},
    /*
			 * 根据广告位获取广告图片
			 * @param sign 广告位置
			 * */
    async getAdInfo(sign) {
      let data = [];
      let res = await this.$api.sendRequest({
        url: apiurls.specialBannerUrl,
        async: false,
        data: {
          sign
        }
      })
      if (res.code != 0) {
        return
      }
      data = res.data.list;
      return data;
    },
    // 获取广告
    async getPersonalCenter(){
      try{
        this.adList = await this.getAdInfo('personalCenter-1')
      }catch (e) {

      }
    },
    toAd(item) {
      this.$buriedPoint.diyReportAdEvent(
          {diy_ad_location_type:'personal_center',diy_material_path:item.image_url,diy_ad_type:'image',diy_target_page:item.banner_url,diy_ad_id:item.id,diy_action_type:'click'})
      this.$util.specialBannerReportByClick(item.id)
      if (item.banner_url) {
        this.$util.diyCompateRedirectTo({
          wap_url: item.banner_url
        })
      }
    },
    /**
     *分享参数组装
     */
    getSharePageParams() {
      return this.$util.unifySharePageParams('/otherpages/shop/home/<USER>', this.shareTitle || '先迈商城',
          '',{}, this.$util.img(this.shareImg) || this.$util.img('public/static/youpin/home_share.jpg'))
    },
    async getListData(){
      let params = {
        page: 1,
        page_size: 10,
        order_status: 'waitpay',
      }
      try{
        let res = await this.$api.sendRequest({
          url: '/api/order/lists',
          data: params,
          async:false
        })
        if(res.code==0){
          this.waitPayOrderList = res.data.list;
        }
      }catch (e) {
        console.log(e)
      }
    },
    orderDetail(data) {
      switch (data.order_type ? parseInt(data.order_type) : 1) {
        case 2:
          // 自提订单
          this.$util.redirectTo('/pages/order/detail_pickup/detail_pickup', {
            order_id: data.order_id
          });
          break;
        case 3:
          // 本地配送订单
          this.$util.redirectTo('/pages/order/detail_local_delivery/detail_local_delivery', {
            order_id: data.order_id
          });
          break;
        case 4:
          // 虚拟订单
          this.$util.redirectTo('/pages/order/detail_virtual/detail_virtual', {
            order_id: data.order_id
          });
          break;
        default:
          this.$util.redirectTo('/pages/order/detail/detail', {
            order_id: data.order_id
          });
          break;
      }
    },
    orderImageChange(index,j){
      let order_detail = this.waitPayOrderList[index];
      order_detail.order_goods[j].sku_image = this.$util.getDefaultImage().default_goods_img
      this.$set(this.waitPayOrderList,index,order_detail);
    },
    onFinish(){

    }
	},
  /**
   * 自定义分享内容
   * @param {Object} res
   */
  onShareAppMessage(res) {
    let { title, link, imageUrl, query } = this.getSharePageParams()
    return this.$buriedPoint.pageShare(link , imageUrl, title);
  },
};
</script>

<style lang="scss">
@import '../public/css/index.scss';
</style>
<style scoped lang="scss">
	.signIn{
		position: absolute;
		z-index: 999;
		right: 231rpx;
		top: 0;
		color: #fff;
	}
	.popup-dialog {
		overflow: hidden;
		background: #FFFFFF;
		box-sizing: border-box;
		&.desc-dialog{
			.popup-dialog-body {
				color: #656565;
				text-align: center;
				padding: 0 30rpx;
				padding-top: 30rpx;
				max-height: 400rpx;
				overflow-y: scroll;
			}
		}
		.popup-dialog-footer {
			margin: 0 32rpx;
			height: 140rpx;
			display: flex;
			align-items: center;
			justify-content: space-around;
			.button {
				width: 220rpx;
				height: 68rpx;
				line-height: 68rpx;
				text-align: center;
				border-radius: 34rpx;
				box-sizing: border-box;
				margin: 0;
				&.white {
					color: var(--custom-brand-color);
					background: #FFFFFF;
					border: 1rpx solid var(--custom-brand-color);
				}
				&.red {
					color: #FFFFFF;
					background: var(--custom-brand-color);

				}
			}
		}
	}
	.qrcode-dialog{
		padding:20rpx;
		.title{
			font-size: $ns-font-size-lg;
			font-weight: bold;
			text-align: center;
			margin-bottom: 30rpx;
		}
		.sub-title{
			font-size: $ns-font-size-sm;
			text-align: center;
		}
		image{
			width: 400rpx;
			display: block;
			margin: 20rpx auto
		}
		.content{}
	}
	.up-dianzhu{
		text-align: center;
		image{
			width: 702rpx;
			height: 68rpx;
		}
	}
  .two-area{
    width: 710rpx;
    height: 233rpx;
    margin: 0 auto;
    margin-top: 24rpx;
    display: flex;
    justify-content: space-between;
    align-items: center;
    &-one{
      width: 343rpx;
      height: 100%;
      background-repeat: no-repeat;
      background-size: 100% 100%;
      &-title{
        font-size: 28rpx;
        font-weight: 800;
        color: var(--custom-brand-color);
        display: flex;
        align-items: center;
        margin-left: 30rpx;
        height: 78rpx;
        line-height: 78rpx;
        .iconfont{
          font-size: 24rpx;
        }
      }
      &-content{
        width: 333rpx;
        height: 151rpx;
        background-color: white;
        border-radius: 12rpx;
        box-shadow: rgba(130, 130, 130, 0.35) 0px 0px 2rpx;
        margin: 0 auto;
        display: flex;
        &-item{
          width: 50%;
          height: 100%;
          display: flex;
          flex-direction: column;
          justify-content: center;
          align-items: center;
          &-number{
            font-size: 36rpx;
            font-weight: bolder;
            color: var(--custom-brand-color);
          }
          &-label{
            font-size: 22rpx;
            color: #2A2A2A;
          }
        }
      }
      &-empty{
        flex-direction: column;
        justify-content: center;
        align-items: center;
        &-title{
          color: rgba(0, 0, 0, 0.313725490196078);
          font-size: 22rpx;
        }
        &-btn{
          width: 148rpx;
          height: 56rpx;
          background: var(--custom-brand-color);
          border-radius: 28rpx;
          line-height: 56rpx;
          text-align: center;
          font-size: $ns-font-size-base;
          color:#fff;
        }
      }
    }
  }
  /deep/ .income-count{
    position: absolute;
    left: 176rpx;
    top: 530rpx;
    font-size: 36rpx;
    color: var(--custom-brand-color);
    font-weight: 700;
    display: flex;
    justify-content: center;
    align-items: baseline;
    width: 400rpx;
    /deep/ .count{
      font-size: 112rpx;
    }
    .count{
      font-size: 112rpx;
    }
  }
</style>
