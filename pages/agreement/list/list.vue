<template>
<view class="agreement">
	<uni-list class="agreement-list" :border='false'>
		<uni-list-item
		v-for="(item, index) in list" :key="index"
		:title="item.title"
		link :to='"../detail/detail?id="+item.id'
		@click="onClick($event,1)"
		:ellipsis='1'
		>
		</uni-list-item>
	</uni-list>
	<loading-cover ref="loadingCover"></loading-cover>
</view>
</template>

<script>
	import apiurls from '@/common/js/apiurls.js'
	export default {
		components: {
		},
		data() {
			return {
				list:[]
			};
		},
		onLoad() {
			this.getRegisterAggrement()
		},
		mounted(){
			if(this.$refs.loadingCover) this.$refs.loadingCover.show()
		},

		methods: {
			onClick() {
				this.$emit('click');
			},
			getRegisterAggrement(){
				this.$api.sendRequest({
					url:apiurls.registerAggrementUrl,
					success:res=>{
						if(this.$refs.loadingCover) this.$refs.loadingCover.hide()
						this.list = res.data
					},
					fail:err=>{
						if(this.$refs.loadingCover) this.$refs.loadingCover.hide()
					}
				})
			},
      /**
       *分享参数组装
       */
      getSharePageParams() {
        return this.$util.unifySharePageParams('/otherpages/shop/home/<USER>', this.shareTitle || '先迈商城',
            '',{}, this.$util.img(this.shareImg) || this.$util.img('public/static/youpin/home_share.jpg'))
      }
		},
		computed: {
		},
		filters: {
		},
    /**
     * 自定义分享内容
     * @param {Object} res
     */
    onShareAppMessage(res) {
      let { title, link, imageUrl, query } = this.getSharePageParams()
      return this.$buriedPoint.pageShare(link , imageUrl, title);
    },
	};
</script>

<style lang="scss" scoped>

	/deep/ .uni-list {
		margin-top: 20rpx;
	}
	/deep/ .uni-list-item__container {
		padding: 22rpx 24rpx;
	}
	/deep/ .uni-list--border::after {
		// height: 0;
		width: 702rpx;
		left: 24rpx;
		background-color: #EEEEEE;

	}
	/deep/ .uni-icons {
		color: #cccccc!important;
	}
</style>
