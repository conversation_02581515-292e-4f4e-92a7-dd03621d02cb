<template>
	<view class="agreement-detail">
		<rich-text :nodes="content"></rich-text>
		<loading-cover ref="loadingCover"></loading-cover>
	</view>
</template>

<script>
	import apiurls from '@/common/js/apiurls.js'
	export default {
		components: {
		},
		data() {
			return {
				id: 0,
				// title:"柚品用户协议",
				content:"",
			};
		},
		onLoad(option) {
			if (option.id) this.id = option.id;
			this.getAggrementInfo()
		},
		mounted(){
			if(this.$refs.loadingCover) this.$refs.loadingCover.show()
		},
		methods: {
			getAggrementInfo(){
				if(!this.id) return
				this.$api.sendRequest({
					url:apiurls.aggrementInfoUrl,
					data:{
						id:this.id
					},
					success:res=>{
						if(this.$refs.loadingCover) this.$refs.loadingCover.hide()
						uni.setNavigationBarTitle({
							title:res.data.title
						})
						this.content = res.data.content
					},
					fail:err=>{
						if(this.$refs.loadingCover) this.$refs.loadingCover.hide()
					}
				})
			},
      /**
       *分享参数组装
       */
      getSharePageParams() {
        return this.$util.unifySharePageParams('/otherpages/shop/home/<USER>', this.shareTitle || '先迈商城',
            '',{}, this.$util.img(this.shareImg) || this.$util.img('public/static/youpin/home_share.jpg'))
      }
		},
		computed: {
		},
		filters: {
		},
    /**
     * 自定义分享内容
     * @param {Object} res
     */
    onShareAppMessage(res) {
      let { title, link, imageUrl, query } = this.getSharePageParams()
      return this.$buriedPoint.pageShare(link , imageUrl, title);
    },
	};
</script>

<style lang="scss">
	.agreement-detail {
		min-height: 100vh;
		background-color: #FFFFFF;
		padding: 30rpx;
		color: #343434;
		font-size: 28rpx;
	}
</style>
