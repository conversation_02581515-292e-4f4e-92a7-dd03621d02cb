<template>
	<scroll-view scroll-y="true" class="container" :class="themeStyle" :style="[themeColorVar]">
		<view class="body-wrap">
      <view class="body-wrap-header">
        <image :src="$util.img('public/static/youpin/home-logo.png')" class="body-wrap-header-logo"></image>
        <view class="body-wrap-header-change">
          <text @click="switchLoginMode" class="body-wrap-header-change-one" :class="{'body-wrap-header-change-one-active':loginMode == 'account'}">密码登录</text>
          <text @click="switchLoginMode" class="body-wrap-header-change-one" :class="{'body-wrap-header-change-one-active':loginMode == 'mobile'}">短信登录</text>
        </view>
      </view>
			<view class="form-wrap">
				<view class="input-wrap" v-show="loginMode == 'mobile'">
					<text class="iconfont iconshouji"></text>
					<view class="content">
<!--						<view class="area-code">+86</view>-->
						<input type="number" placeholder="请输入手机号" placeholder-class="input-placeholder" class="input" maxlength="11" v-model="formData.mobile" />
					</view>
				</view>
				<view class="input-wrap" v-show="loginMode == 'account'">
					<text class="iconfont iconshouji"></text>
					<view class="content"><input type="number" placeholder="请输入手机号码" placeholder-class="input-placeholder" class="input" v-model="formData.account" maxlength="11"/></view>
				</view>
				<view class="input-wrap" v-show="loginMode == 'account'">
					<text class="iconfont iconmima"></text>
					<view class="content"><input type="password" placeholder="请输入密码" placeholder-class="input-placeholder" class="input" v-model="formData.password" /></view>
				</view>
				<view class="input-wrap" v-show="false">
					<text class="iconfont iconLjianpanyanzhengma-"></text>
					<view class="content">
						<input type="text" placeholder="请输入验证码" placeholder-class="input-placeholder" class="input" v-model="formData.vercode" />
						<image :src="captcha.img" mode="" class="captcha" @click="getCaptcha"></image>
					</view>
				</view>
				<view class="input-wrap" v-show="loginMode == 'mobile'">
					<text class="iconfont iconyuechi"></text>
					<view class="content">
						<input type="text" placeholder="请输入验证码" placeholder-class="input-placeholder" class="input" v-model="formData.dynacode" maxlength="4" @input="changeauthCd"/>
						<view class="dynacode" :class="dynacodeData.seconds == 120 ? 'ns-text-color' : 'ns-text-color-gray'" @click="sendMobileCode">
							{{ dynacodeData.codeText }}
						</view>
					</view>
				</view>
			</view>
			<view class="forget-section">
				<view>
<!--					<text @click="switchLoginMode" v-show="loginMode == 'mobile'">账号登录</text>-->
<!--					<text @click="switchLoginMode" v-show="loginMode == 'account' && registerConfig.dynamic_code_login == 1">短信快捷登录</text>-->
				</view>
				<view class="align-right" v-show="loginMode == 'account'"><text @click="forgetPassword">忘记密码？</text></view>
			</view>
			<button type="primary" @click="login" class="login-btn ns-border-color ns-bg-color">登录</button>
<!--			<button type="warn" @click="authLogin" v-if="isBind" class="auth-login ns-border-color ns-text-color">一键授权登录</button>-->
      <button type="default" @click="toRegister" class="login-btn ns-border-color ns-text-color login-btn-register" v-if="registerConfig.is_enable == 1">注册</button>
      <view class="login-agreement">
        登录即代表您已同意
        <text class="ns-text-color ns-font-size-sm" @click="toAgreement">《先迈网用户协议》和《隐私政策》</text>
      </view>
		</view>
    <yd-auth-popup ref="ydauth"></yd-auth-popup>
		<uni-coupon-pop ref="couponPop"></uni-coupon-pop>
	</scroll-view>
</template>

<script>
import validate from 'common/js/validate.js';
import auth from 'common/mixins/auth.js';
import globalConfig from '@/common/mixins/golbalConfig.js';
import apiurls from "common/js/apiurls";
import util from "../../../common/js/util";
import statistics from "../../../common/mixins/statistics";
export default {
	data() {
		return {
			loginMode: 'account',
			formData: {
				mobile: '',
				account: '',
				password: '',
				vercode: '',
				dynacode: '',
				key: ''
			},
			captcha: {
				id: '',
				img: ''
			},
			isSub: false, // 提交防重复
			back: '', // 返回页
			redirect: 'reLaunch', // 跳转方式
			openidIsExits: false,
			isBind: false,
			dynacodeData: {
				seconds: 120,
				timer: null,
				codeText: '获取验证码',
				isSend: false
			},
			registerConfig: {
				is_enable: 1
			}
		};
	},
	mixins: [auth,globalConfig],
	onLoad(option) {
		let url = '';
		if (option) {
			url = option.back;
      if(!url){
        url='/otherpages/shop/home/<USER>'
      }
			Object.keys(option).forEach(function(key) {
				if (key != 'back') {
					if (url.indexOf('?') != -1) {
						url += '&' + key + '=' + option[key];
					} else {
						url += '?' + key + '=' + option[key];
					}
				}
			});
			this.back = url;
		}
		// this.getCaptcha();
		this.getRegisterConfig();

		if (uni.getStorageSync('authInfo')) {
			let data = uni.getStorageSync('authInfo');
			if (data.authInfo) this.authInfo = data.authInfo;
			if (data.userInfo) this.userInfo = data.userInfo;
			this.checkOpenidIsExits();
		}
	},
	onShow() {
		// 刷新多语言
		this.$langConfig.refresh();
	},

	methods: {
    changeauthCd(){
      if (this.formData.dynacode.length > 4) {
        this.formData.dynacode = this.formData.dynacode.slice(0, 4)
      }
    },
		/**
		 * 获取注册配置
		 */
		getRegisterConfig() {
			this.$api.sendRequest({
				url: '/api/register/config',
				success: res => {
					if (res.code >= 0) {
						this.registerConfig = res.data.value;
						// if (this.registerConfig.dynamic_code_login == 1) this.loginMode = 'mobile';
						// else this.loginMode = 'account';
					}
				}
			});
		},
		/**
		 * 切换登录方式
		 */
		switchLoginMode() {
			this.loginMode = this.loginMode == 'mobile' ? 'account' : 'mobile';
		},
		/**
		 * 获取验证码
		 */
		getCaptcha() {
			this.$api.sendRequest({
				url: '/api/captcha/captcha',
				data: {
					captcha_id: this.captcha.id
				},
				success: res => {
					if (res.code >= 0) {
						this.captcha = res.data;
						this.captcha.img = this.captcha.img.replace(/\r\n/g, '');
					}
				}
			});
		},
		/**
		 * 去注册
		 */
		toRegister() {
			if (this.back)
				this.$util.redirectTo('/pages/login/register/register', {
					back: this.back
				});
			else this.$util.redirectTo('/pages/login/register/register');
		},
		/**
		 * 忘记密码
		 */
		forgetPassword() {
			if (this.back){
        this.$util.redirectTo('/otherpages/login/find/find', {
          back: this.back
        });
      }else{
        this.$util.redirectTo('/otherpages/login/find/find');
      }
		},
    jumpTo(){
      if (this.back != '') {
        this.$util.redirectTo(this.back, {}, this.redirect);
      } else {
        this.$util.redirectTo('/otherpages/shop/home/<USER>', {}, this.redirect);
      }
    },
		/**
		 * 登录
		 */
		async login() {
		  let url=apiurls.h5LoginUrl;
		  let data={};
			if (this.loginMode == 'account') {
				data = {
          mobile: this.formData.account,
					password: this.formData.password,
          type:'password',
				};
			} else {
					data = {
						mobile: this.formData.mobile,
						code: this.formData.dynacode,
            type:'sms_code'
					};
			}
			if (Object.keys(this.authInfo).length) {
				data['openid_arr']=JSON.stringify(this.authInfo);
			}
      if (Object.keys(this.userInfo).length) {
        data['userinfo']=JSON.stringify(this.userInfo);
      }
			if (this.verify(data)) {
				if (this.isSub) return;
				this.isSub = true;
        uni.showLoading({
          title: '登录中...'
        })
				this.$api.sendRequest({
					url,
					data,
					success: async res => {
            uni.hideLoading()
            if(res.code==-10010){
              this.isShow=false;
              uni.showModal({
                title: '提示',
                content: res.message,
                showCancel:false,
              });
              await this.$util.clearUserInfo();
            }else if(res.code==-10011){
              this.$refs.ydauth.openid_arr=res.data.openid_arr;
              this.$refs.ydauth.mobile=res.data.mobile;
              this.$refs.ydauth.init(()=>{
                this.jumpTo();
              },true)
            }else if (res.code >= 0) {
              let token=res.data.token;
              let shop_id=res.data.shop_id;
              let member_id=res.data.member_id;
              let is_distributor=res.data.is_distributor;
              let shop_name=res.data.site_name;
              let is_shopper = res.data.is_shopper;  //是否为店主，1：是，0：否
              this.$util.setUserInfo({shop_id,member_id,is_distributor,shop_name,is_shopper,token});
              statistics.shopInterview(this);
              uni.removeStorageSync('loginLock');
              uni.removeStorageSync('unbound');
              this.jumpTo();
						} else {
							this.isSub = false;
							// this.getCaptcha();
							this.$util.showToast({
								title: res.message
							});
						}
					},
					fail: res => {
						this.isSub = false;
            uni.hideLoading()
						// this.getCaptcha();
					}
				});
			}
		},
		/**
		 * 登录验证
		 * @param {Object} data
		 */
		verify(data) {
			let rule = [];
			// 手机号验证
			if (this.loginMode == 'mobile') {
				rule = [
					{
						name: 'mobile',
						checkType: 'required',
						errorMsg: '请输入手机号'
					},
					{
						name: 'mobile',
						checkType: 'phoneno',
						errorMsg: '请输入正确的手机号'
					}
				];
				// if (this.captcha.id != '')
				// 	rule.push({
				// 		name: 'captcha_code',
				// 		checkType: 'required',
				// 		errorMsg: this.$lang('captchaPlaceholder')
				// 	});
				rule.push({
					name: 'code',
					checkType: 'required',
					errorMsg: this.$lang('dynacodePlaceholder')
				});
			}

			// 账号验证
			if (this.loginMode == 'account') {
				rule = [
					{
						name: 'mobile',
						checkType: 'required',
						errorMsg: '请输入手机号'
					},
					{
						name: 'password',
						checkType: 'required',
						errorMsg: this.$lang('passwordPlaceholder')
					}
				];
				// if (this.captcha.id != '')
				// 	rule.push({
				// 		name: 'captcha_code',
				// 		checkType: 'required',
				// 		errorMsg: this.$lang('captchaPlaceholder')
				// 	});
			}

			var checkRes = validate.check(data, rule);
			if (checkRes) {
				return true;
			} else {
				this.$util.showToast({
					title: validate.error
				});
				return false;
			}
		},
		/**
		 * 授权登录
		 */
		authLogin() {
			if (this.isSub) return;
			this.isSub = true;
			this.$api.sendRequest({
				url: '/api/login/auth',
				data: this.authInfo,
				success: res => {
					if (res.code >= 0) {
						uni.setStorage({
							key: 'token',
							data: res.data.token,
							success: () => {
								uni.removeStorageSync('loginLock');
								uni.removeStorageSync('unbound');
								uni.removeStorageSync('authInfo');
								if (this.back != '') {
									this.$util.redirectTo(this.back, {}, this.redirect);
								} else {
									this.$util.redirectTo('/pages/member/index/index', {}, this.redirect);
								}
							}
						});
					} else {
						this.isSub = false;
						this.$util.showToast({
							title: res.message
						});
					}
				},
				fail: res => {
					this.isSub = false;
					this.$util.showToast({
						title: 'request:fail'
					});
				}
			});
		},
		/**
		 * 发送手机动态码
		 */
		sendMobileCode() {
			if (this.dynacodeData.seconds != 120) return;
			var data = {
				mobile: this.formData.mobile,
				captcha_code: this.formData.vercode,
        type:9
			};
			var rule = [
				{
					name: 'mobile',
					checkType: 'required',
					errorMsg: '请输入手机号'
				},
				{
					name: 'mobile',
					checkType: 'phoneno',
					errorMsg: '请输入正确的手机号'
				},
				// {
				// 	name: 'captcha_code',
				// 	checkType: 'required',
				// 	errorMsg: '请输入验证码'
				// }
			];
			var checkRes = validate.check(data, rule);
			if (!checkRes) {
				this.$util.showToast({
					title: validate.error
				});
				return;
			}
			if (this.dynacodeData.isSend) return;
			this.dynacodeData.isSend = true;
			this.$api.sendRequest({
				url: apiurls.sendMobileCodeUrl,
				data,
				success: res => {
					this.dynacodeData.isSend = false;
					if (res.code >= 0) {
						if (this.dynacodeData.seconds == 120 && this.dynacodeData.timer == null) {
							this.dynacodeData.timer = setInterval(() => {
								this.dynacodeData.seconds--;
								this.dynacodeData.codeText = this.dynacodeData.seconds + 's后可重新获取';
							}, 1000);
              this.$util.showToast({
                title: '发送验证码成功'
              });
						}
					} else {
						this.$util.showToast({
							title: res.message
						});
					}
				},
				fail: () => {
					this.$util.showToast({
						title: 'request:fail'
					});
					this.dynacodeData.isSend = false;
				}
			});
		},
		checkOpenidIsExits() {
			if (Object.keys(this.authInfo).length) {
				uni.setStorage({
					key: 'authInfo',
					data: {
						authInfo: this.authInfo,
						userInfo: this.userInfo
					}
				});
				// this.$api.sendRequest({
				// 	url: '/api/login/openidisexits',
				// 	data: this.authInfo,
				// 	success: res => {
				// 		if (res.code >= 0 && res.data > 0) {
				// 			this.isBind = true;
				// 		}
				// 	}
				// });
			}
		},
		// 获取运营商
		getProvider() {
			uni.getProvider({
				service: 'oauth',
				success: res => {
					this.loginProviderArr = res.provider;
				},
				error: res => {
					this.$util.showToast({
						title: '获取运营商失败'
					});
				}
			});
		},
    toAgreement(){
      this.$util.redirectTo('/pages/agreement/list/list')
    }
	},
	watch: {
		'dynacodeData.seconds': {
			handler(newValue, oldValue) {
				if (newValue == 0) {
					clearInterval(this.dynacodeData.timer);
					this.dynacodeData = {
						seconds: 120,
						timer: null,
						codeText: '获取动态码',
						isSend: false
					};
				}
			},
			immediate: true,
			deep: true
		}
	}
};
</script>

<style lang="scss">
@import '../public/css/common.scss';
.iconfont{
  color: #ababab!important;
}
.container{
  height: auto;
}
.body-wrap-header{
  &-logo{
    width: 120rpx;
    height: 120rpx;
    margin: 0 auto;
    display: block;
  }
  &-change{
    display: flex;
    justify-content: center;
    align-items: baseline;
    margin-top: 80rpx;
    &-one{
      font-size: 32rpx;
      font-weight: 500;
      color: #999999;
      &-active{
        font-size: 38rpx;
        font-weight: bold;
        color: #333333;
      }
      &:first-child{
        margin-right: 189rpx;
      }
    }
  }
}
.align-right{
  font-size: 26rpx;
  font-weight: 500;
  color: #BCBCBC;
}
.login-btn{
  margin-top: 0!important;
  &-register{
    margin-top: 30rpx!important;
    background-color: white;
  }
}
.login-agreement{
  font-size: 24rpx;
  font-weight: 500;
  color: #333333;
  text-align: center;
  margin-top: 260rpx;
}
</style>
