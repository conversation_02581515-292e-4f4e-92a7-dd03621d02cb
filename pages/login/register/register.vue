<template>
	<scroll-view scroll-y="true" class="container" :class="themeStyle"  :style="[themeColorVar]" v-if="addonIsExit.memberregister">
		<view class="body-wrap">
      <view class="body-wrap-header">
        <image :src="$util.img('public/static/youpin/home-logo.png')" class="body-wrap-header-logo"></image>
      </view>
			<view class="form-wrap">
				<view class="input-wrap">
					<text class="iconfont iconshouji"></text>
					<view class="content"><input type="number" placeholder="请输入手机号码" placeholder-class="input-placeholder" class="input"
						 v-model="formData.mobile" maxlength="11"/></view>
				</view>
				<view class="input-wrap">
					<text class="iconfont iconmima"></text>
					<view class="content"><input type="password" placeholder="请输入密码" placeholder-class="input-placeholder" class="input"
						 v-model="formData.password" /></view>
				</view>
<!--				<view class="input-wrap">-->
<!--					<text class="iconfont iconmima"></text>-->
<!--					<view class="content">-->
<!--						<input type="password" placeholder="请确认密码" placeholder-class="input-placeholder" class="input" v-model="formData.rePassword" />-->
<!--					</view>-->
<!--				</view>-->
<!--				<view class="input-wrap">-->
<!--					<text class="iconfont iconLjianpanyanzhengma-"></text>-->
<!--					<view class="content">-->
<!--						<input type="text" placeholder="请输入短信验证码" placeholder-class="input-placeholder" class="input" v-model="formData.vercode" />-->
<!--						<image :src="captcha.img" mode="" class="captcha" @click="getCaptcha"></image>-->
<!--					</view>-->
<!--				</view>-->
        <view class="input-wrap">
          <text class="iconfont iconyuechi"></text>
          <view class="content">
            <input type="text" placeholder="请输入验证码" placeholder-class="input-placeholder" class="input" v-model="formData.dynacode" maxlength="4" @input="changeauthCd"/>
            <view class="dynacode" :class="dynacodeData.seconds == 120 ? 'ns-text-color' : 'ns-text-color-gray'" @click="sendMobileCode">
              {{ dynacodeData.codeText }}
            </view>
          </view>
        </view>
			</view>
<!--			<view class="forget-section">-->
<!--				<view></view>-->
<!--				<view class="align-right"><text @click="toLogin">已有账号，立即登录</text></view>-->
<!--			</view>-->
			<button type="primary" @click="register" class="login-btn  ns-border-color ns-bg-color">注册</button>
			<button type="default" @click="toLogin" class="login-btn ns-border-color ns-text-color login-btn-two">登录</button>
			<view class="regisiter-agreement">
				点击注册即代表您已同意
				<text class="ns-text-color ns-font-size-sm" @click="toAgreement">《先迈网用户协议》和《隐私政策》</text>
			</view>
		</view>

<!--		<view @touchmove.stop>-->
<!--			<uni-popup ref="registerPopup" type="center" class="wap-floating">-->
<!--				<view class="conten-box">-->
<!--					<view class="title ns-text-color">{{ regisiterAgreement.title }}</view>-->
<!--					<view class="con">-->
<!--						<rich-text :nodes="regisiterAgreement.content"></rich-text>-->
<!--					</view>-->
<!--				</view>-->
<!--			</uni-popup>-->
<!--		</view>-->
	</scroll-view>
</template>

<script>
	import uniPopup from '@/components/uni-popup/uni-popup.vue';
	import validate from 'common/js/validate.js';
	import auth from 'common/mixins/auth.js';
  import apiurls from "common/js/apiurls";
  import statistics from "../../../common/mixins/statistics";
  import golbalConfig from "../../../common/mixins/golbalConfig";

	export default {
		components: {
			uniPopup
		},
		data() {
			return {
				allowRegister: true, // 是否允许注册
				registerMode: 'mobile',
        redirect: 'reLaunch', // 跳转方式
				formData: {
					mobile: '',
					account: '',
					password: '',
					rePassword: '',
					vercode: '',
					dynacode: '',
					key: ''
				},
				regisiterAgreement: {
					// 注册协议
					title: '',
					content: ''
				},
				captcha: {
					// 验证码
					id: '',
					img: ''
				},
        dynacodeData: {
          seconds: 120,
          timer: null,
          codeText: '获取验证码',
          isSend: false
        },
				isSub: false,
				back: '' // 返回页
			};
		},
		mixins: [auth,golbalConfig],
		onLoad(option) {
			if (option.back) this.back = option.back;
			// this.getCaptcha();

			if (uni.getStorageSync('authInfo')) {
				let data = uni.getStorageSync('authInfo');
				if (data.authInfo) this.authInfo = data.authInfo;
				if (data.userInfo) this.userInfo = data.userInfo;
			}
		},
		computed: {
			// 使用对象展开运算符将此对象混入到外部对象中
			themeStyle() {
				return 'theme-' + this.$store.state.themeStyle
			},
			addonIsExit(){
				return this.$store.state.addonIsExit
			}
		},
		onShow() {
			// 刷新多语言
			this.$langConfig.refresh();
			if(!this.addonIsExit.memberregister){
				this.$util.showToast({
					title:'注册插件未安装',
					mask:true,
					duration:2000
				})
				setTimeout(()=>{
					this.$util.redirectTo('/pages/index/index/index',{},'redirectTo')
				},2000);
				return;
			}

			// this.getRegisiterAggrement();
			this.getRegisterConfig();
		},
		methods: {
      changeauthCd(){
        if (this.formData.dynacode.length > 4) {
          this.formData.dynacode = this.formData.dynacode.slice(0, 4)
        }
      },
			/**
			 * 切换注册方式
			 */
			switchRegisterMode() {
				if (this.registerMode == 'mobile') {
					if (this.registerConfig.type.indexOf('plain') != -1) this.registerMode = 'account';
				} else if (this.registerMode == 'account') {
					if (this.registerConfig.type.indexOf('mobile') != -1) this.registerMode = 'mobile';
				}
			},
			/**
			 * 展示注册协议
			 */
			openPopup() {
				if (this.regisiterAgreement.content != '') {
					this.$refs.registerPopup.open();
				}
			},
			/**
			 * 获取注册协议
			 */
			getRegisiterAggrement() {
				this.$api.sendRequest({
					url: '/api/register/aggrement',
					success: res => {
						if (res.code >= 0) {
							this.regisiterAgreement = res.data;
						}
					}
				});
			},
			/**
			 * 获取注册配置
			 */
			getRegisterConfig() {
				this.$api.sendRequest({
					url: '/api/register/config',
					success: res => {
						if (res.code >= 0) {
							this.registerConfig = res.data.value;
							if (this.registerConfig.is_enable != 1) {
								this.$util.showToast({
									title: '平台未启用注册!',
									success: () => {
										setTimeout(() => {
											this.$util.redirectTo('/pages/index/index/index', {}, 'reLaunch');
										}, 1500);
									}
								});
							}
						}
					}
				});
			},
			/**
			 * 获取验证码
			 */
			getCaptcha() {
				this.$api.sendRequest({
					url: '/api/captcha/captcha',
					data: {
						captcha_id: this.captcha.id
					},
					success: res => {
						if (res.code >= 0) {
							this.captcha = res.data;
							this.captcha.img = this.captcha.img.replace(/\r\n/g, '');
						}
					}
				});
			},
      jumpTo(){
        if (this.back != '') {
          this.$util.redirectTo(this.back, {}, this.redirect);
        } else {
          this.$util.redirectTo('/otherpages/shop/home/<USER>', {}, this.redirect);
        }
      },
			/**
			 * 注册
			 */
			async register() {
				var data = {
					mobile: this.formData.mobile.trim(),
					password: this.formData.password,
          code: this.formData.dynacode,
				};
        if (Object.keys(this.authInfo).length) {
          data['openid_arr']=JSON.stringify(this.authInfo);
        }
        if (Object.keys(this.userInfo).length) {
          data['userinfo']=JSON.stringify(this.userInfo);
        }
        let recommend_member_id=uni.getStorageSync('recommend_member_id');
        if(recommend_member_id){
          data=Object.assign(data,{recommend_member:recommend_member_id})
        }

				if (this.verify(data)) {
					if (this.isSub) return;
					this.isSub = true;
          uni.showLoading({
            title: '注册中...'
          })
					this.$api.sendRequest({
						url: apiurls.h5RegisterUrl,
						data,
						success: async res => {
              uni.hideLoading()
              if(res.code==-10010){
                this.isShow=false;
                uni.showModal({
                  title: '提示',
                  content: res.message,
                  showCancel:false,
                });
                await this.$util.clearUserInfo();
              }else if (res.code >= 0) {
								uni.setStorageSync('is_register', res.data.is_register)
                let token=res.data.token;
                let shop_id=res.data.shop_id;
                let member_id=res.data.member_id;
                let is_distributor=res.data.is_distributor;
                let shop_name=res.data.site_name;
                let is_shopper = res.data.is_shopper;  //是否为店主，1：是，0：否
                this.$util.setUserInfo({shop_id,member_id,is_distributor,shop_name,is_shopper,token});
                statistics.shopInterview(this);
                uni.removeStorageSync('loginLock');
                uni.removeStorageSync('unbound');
                uni.removeStorageSync('authInfo');
                this.jumpTo();
							} else {
								this.isSub = false;
								// this.getCaptcha();
								this.$util.showToast({
									title: res.message
								});
							}
						},
						fail: res => {
							this.isSub = false;
              uni.hideLoading()
							// this.getCaptcha();
						}
					});
				}
			},
			verify(data) {
				let rule = [{
							name: 'mobile',
							checkType: 'required',
							errorMsg: '请输入手机号码'
						},
						{
							name: 'password',
							checkType: 'required',
							errorMsg: '请输入密码'
						},
              {
                name: 'code',
                checkType: 'required',
                errorMsg: this.$lang('dynacodePlaceholder')
              }
					],
					regConfig = this.registerConfig;

				if (regConfig.pwd_complexity != '') {
					let passwordErrorMsg = '密码需包含',
						reg = '';
					if (regConfig.pwd_complexity.indexOf('number') != -1) {
						reg += '(?=.*?[0-9])';
						passwordErrorMsg += '数字';
					}
					if (regConfig.pwd_complexity.indexOf('letter') != -1) {
						reg += '(?=.*?[a-z])';
						passwordErrorMsg += '、小写字母';
					}
					if (regConfig.pwd_complexity.indexOf('upper_case') != -1) {
						reg += '(?=.*?[A-Z])';
						passwordErrorMsg += '、大写字母';
					}
					if (regConfig.pwd_complexity.indexOf('symbol') != -1) {
						reg += '(?=.*?[#?!@$%^&*-])';
						passwordErrorMsg += '、特殊字符';
					}
					rule.push({
						name: 'password',
						checkType: 'reg',
						checkRule: reg,
						errorMsg: passwordErrorMsg
					});
				}
				var checkRes = validate.check(data, rule);
				if (checkRes) {
					return true;
				} else {
					this.$util.showToast({
						title: validate.error
					});
					return false;
				}
			},
			/**
			 * 去登录
			 */
			toLogin() {
				if (this.back) this.$util.redirectTo('/pages/login/login/login', {
					back: this.back
				});
				else this.$util.redirectTo('/pages/login/login/login');
			},
      /**
       * 发送手机动态码
       */
      async sendMobileCode() {
        let recommend_member_id=uni.getStorageSync('recommend_member_id');
        if(recommend_member_id && (this.$store.state.wechatVerCode.codeType!='register' || (this.$store.state.wechatVerCode.codeType=='register' && !this.$store.state.wechatVerCode.verifyState))){
          await this.$util.toWechatVerificationCode('register')
          return
        }
        if (this.dynacodeData.seconds != 120) return;
        var data = {
          mobile: this.formData.mobile,
          captcha_code: this.formData.vercode,
          type:1
        };
        var rule = [
          {
            name: 'mobile',
            checkType: 'phoneno',
            errorMsg: '请输入正确的手机号'
          },
        ];
        var checkRes = validate.check(data, rule);
        if (!checkRes) {
          this.$util.showToast({
            title: validate.error
          });
          return;
        }
        if (this.dynacodeData.isSend) return;
        this.dynacodeData.isSend = true;
        this.$api.sendRequest({
          url: apiurls.sendMobileCodeUrl,
          data: data,
          success: res => {
            this.dynacodeData.isSend = false;
            if (res.code >= 0) {
              if (this.dynacodeData.seconds == 120 && this.dynacodeData.timer == null) {
                this.dynacodeData.timer = setInterval(() => {
                  this.dynacodeData.seconds--;
                  this.dynacodeData.codeText = this.dynacodeData.seconds + 's后可重新获取';
                }, 1000);
                this.$util.showToast({
                  title: '发送验证码成功'
                });
              }
            } else {
              this.$util.showToast({
                title: res.message
              });
            }
          },
          fail: () => {
            this.$util.showToast({
              title: 'request:fail'
            });
            this.dynacodeData.isSend = false;
          }
        });
      },
      toAgreement(){
        this.$util.redirectTo('/pages/agreement/list/list')
      }
		},
    watch: {
      'dynacodeData.seconds': {
        handler(newValue, oldValue) {
          if (newValue == 0) {
            clearInterval(this.dynacodeData.timer);
            this.dynacodeData = {
              seconds: 120,
              timer: null,
              codeText: '获取动态码',
              isSend: false
            };
          }
        },
        immediate: true,
        deep: true
      }
    }
	};
</script>

<style lang="scss">
	@import '../public/css/common.scss';
  .iconfont{
    color: #ababab!important;
  }
  .container{
    height: auto;
  }
  .body-wrap-header {
    &-logo {
      width: 120rpx;
      height: 120rpx;
      margin: 0 auto;
      display: block;
    }
  }
  .login-btn{
    margin-top: 105rpx!important;
    &-two{
      margin-top: 30rpx!important;
      background-color: white;
    }
  }
  .regisiter-agreement{
    font-size: 24rpx;
    font-weight: 500;
    color: #333333;
    text-align: center;
    margin-top: 260rpx!important;
  }
</style>
