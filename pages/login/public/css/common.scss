// [data-theme]{
	page {
		width: 100%;
		background: #fff!important;
	}

	.align-right{
		text-align: right;
	}

	.container{
		width: 100vw;
		height: 100vh;
	}

	.header-wrap {
		width: 100%;
		height: 400rpx;
		background-repeat: no-repeat;
		background-size: contain;
		background-position: bottom;
		position: relative;
		.header-bg{
			width: 100%;
			margin-top: 60rpx;
		}
		.bottom-wrap {
			position: absolute;
			height: 100rpx;
			width: 100%;
			left: 0;
			bottom: 0;
			background-image: url(data:image/png;base64,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);
			background-repeat: no-repeat;
			background-size: 100% 100%;
			background-position: bottom;
		}
		
		.face-wrap{
			width: 140rpx;
			height: 140rpx;
			border-radius: 50%;
			overflow: hidden;
			border: 4rpx solid #f5f5f5;
			position: absolute;
			bottom: 0;
			left: 50%;
			transform: translateX(-50%) translateY(50%);
			image{
				width: 100%;
				height: 100%;
				border-radius: 50%;
			}
		}
	}

	.body-wrap{
		margin-top: 100rpx;
		padding-bottom: 100rpx;
		
		.form-wrap {
			width: 80%;
			margin: 0 auto;
			
			.input-wrap {
				position: relative;
				width: 100%;
				box-sizing: border-box;
				height: 60rpx;
				padding-left: 60rpx;
				margin-top: 60rpx;
				
				
				.iconfont {
					width: 60rpx;
					height: 60rpx;
					position: absolute;
					left: 0;
					right: 0;
					line-height: 60rpx;
					font-size: 36rpx;
					color: #333;
					font-weight: 600;
				}
				
				.content {
					display: flex;
					height: 60rpx;
					border-bottom: 1px solid #eee;
					
					.input{
						flex: 1;
						height: 60rpx;
						line-height: 60rpx;
						font-size: 28rpx;
					}
					
					.input-placeholder {
						font-size: 28rpx;
						color: #e5e5e5;
						line-height: 60rpx;
					}
					
					.captcha {
						margin: 4rpx;
						height: 52rpx;
						width: 140rpx;
					}
					
					.dynacode{
						line-height: 60rpx;
						font-size: 24rpx;
					}
					
					.area-code{
						line-height: 60rpx;
						margin-right: 20rpx;
					}
				}
			}
		}
		
		.forget-section{
			display: flex;
			width: 80%;
			margin: 40rpx auto;
			
			view{
				flex: 1;
				font-size: 24rpx;
				line-height: 1;
			}
		}
		
		.login-btn, .auth-login{
			width: 80%;
			margin: 0 auto;
			margin-top: 50rpx;
			height: 90rpx;
			line-height: 90rpx;
			border-radius: 90rpx;
			color: #fff;
			text-align: center;
			border: 1px solid #ffffff;
		}
		
		.auth-login{
			margin-top: 20rpx;
			background-color: #fff;
		}
		
		.regisiter-agreement{
			text-align: center;
			margin-top: 30rpx;
			line-height: 1;
		}
		
		.regisiter-agreement{
			font-size: 24rpx;
		}
	}

	.conten-box {
		padding: 0 $ns-padding $ns-padding;
		
		.title {
			line-height: 100rpx;
			font-size: $ns-font-size-lg + 4rpx;
			font-weight: bold;
			border-bottom: 2rpx solid $ns-border-color-gray;
			margin-bottom: $ns-margin;
		}
		.con {
			width: 100%;
			min-height: 600rpx;
			overflow-y: scroll;
			text-align: left;
			text-indent: 50rpx;
		}
	}

	.login-btn-box{
		margin-top: 50rpx; 
	}
	.login-btn-box.active{
		margin: 30rpx 0 50rpx;
	}
	
	.third-login{
		display: flex;
		justify-content: center;
		margin-top: 50rpx;
		&>view{
			display: flex;
			justify-content: center;
			flex-direction: column;
			align-items: center;
			margin: 0 80rpx;
		}
		image{
			width: 80rpx;
			height: 80rpx;
		}
	}	
// }

