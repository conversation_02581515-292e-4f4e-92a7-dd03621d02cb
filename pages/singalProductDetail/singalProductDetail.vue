<template>
  <loading-cover ref="loadingCover"></loading-cover>
</template>

<script>
import apiurls from "../../common/js/apiurls";
import system from "../../common/js/system";

export default{
	name:'singalProductDetail',
	data(){
		return{
      xm_shop_id:null,
      xm_goods_id:null,
      xm_uid:null,
      options:{}
    }
	},
	onLoad(options) {
    this.options=options;
    if(this.options.uid){
      this.xm_uid=this.options.uid;
    }
	},
  async onShow(){
    let sceneDict=this.parseScene(this.options);
    await system.wait_staticLogin_success();
    if(this.options.shopId && this.options.singalProductId){
      this.xm_shop_id=this.options.shopId;
      this.xm_goods_id=this.options.singalProductId;
      if(!uni.getStorageSync('shop_id')){
        uni.setStorageSync('shop_id',0);
      }
      await this.getData();
    }else if(sceneDict.hasOwnProperty('i') && sceneDict.hasOwnProperty('p')){
      this.xm_shop_id=sceneDict['i'];
      this.xm_goods_id=sceneDict['p'];
      if(!uni.getStorageSync('shop_id')){
        uni.setStorageSync('shop_id',0);
      }
      await this.getData();
    }
  },
	methods:{
    async getData(){
      let res=await this.$api.sendRequest({
        url: apiurls.transformParam,
        async: false,
        data: {
          xm_shop_id: this.xm_shop_id,
          xm_goods_id: this.xm_goods_id,
          xm_uid: this.xm_uid
        },
      });
      if(res.code!=0){
        return
      }else{
        if (this.$refs.loadingCover) this.$refs.loadingCover.hide();
        let options={
          shop_id:res.data.shop_id,
          sku_id:res.data.sku_id
        }
        await system.AppVueInit(options,this);
        this.$util.redirectTo('/pages/goods/detail/detail',options,'reLaunch')
      }
    },
    parseScene(options){
      let sceneDict={}; //扫二维进入小程序的参数
      let sceneParams = decodeURIComponent(options.scene);
      sceneParams = sceneParams.split('&');
      if (sceneParams.length) {
        sceneParams.forEach(item => {
          let oneItme=item.split('=');
          if(oneItme.length>1){
            sceneDict[oneItme[0]]=oneItme[1];
          }
        });
      }
      return sceneDict
    }
  }
}
</script>

<style>
</style>
