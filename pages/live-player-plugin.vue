<template>
  <div>
    <div v-if="endShow">
      <view class="end-box end-all">
        <view class="nav bg-white" :style="{ height: navHeight + 'px' }">
          <view class="nav-title">
            <view class="logo">
              <image
                :src="$util.img('public/static/youpin/home.png')"
                mode="widthFix"
                @click="backFun()"
              ></image>
              <view
                >直播{{
                  liveStatus == 3 ? "结束" : wondfulStatus[liveStatus]
                }}</view
              >
            </view>
          </view>
        </view>
        <view
          class="img"
          :style="{
            'background-image': `url(${$util.img(roomInfo.feeds_img)})`,
          }"
        ></view>
        <view
          class="scroll-box"
        >
          <view class="end-info">
            <!-- <view class="room-info"> -->
              <img :src="$util.img(roomInfo.feeds_img)" mode="" />
              <view class="name">{{roomInfo.anchor_name}}</view>
            <!-- </view> -->
            <!-- <view class="end-text">直播{{ wondfulStatus[liveStatus] }}</view> -->
            <view class="btn">
              <view
                class="back-nav"
                @click="backNav"
                v-if="liveStatus == 3 && backShow"
                >观看回放</view
              >
              <view class="more-btn" @click="liveListFun">更多直播</view>
            </view>
          </view>
          <view class="wonderful-box">
            <view class="wonderful-top">
              <view>
                <view>精彩直播</view>
                <view @click="liveListFun"
                  >更多 <text class="iconfont iconright"></text
                ></view>
              </view>
            </view>
            <view class="live-box">
              <template v-if="roomList.length">
                <view
                  class="live-list"
                  v-for="(item, index) in roomList"
                  :key="index"
                  @click="roomFun(item)"
                >
                  <view class="left">
                    <img
                      class="live-img"
                      @error="imageError(index)"
                      :src="$util.img(item.feeds_img)"
                      alt=""
                    />
                    <view class="info">
                      <view
                        ><img
                          :src="
                            $util.img(
                              `/public/static/youpin/${item.roomStatusClass}.png`
                            )
                          "
                          alt=""
                        />{{item.roomStatus}}</view
                      >
                    </view>
                  </view>
                  <view class="right">
                    <view class="title">{{ item.name }}</view>
                    <view class="wonderful-info">
                      <view>{{ item.anchor_name }}</view>
                    </view>
                    <view class="goods">
                      <img
                        :src="goods.cover_img"
                        alt=""
                        v-for="(goods, goodsIndex) in item.goods"
                        :key="goodsIndex"
                      />
                    </view>
                    <view class="btn">
                      <view @click.stop.prevent="newCommQrcode(item)">
                        <img
                          :src="
                            $util.img('public/static/youpin/forwarding.png')
                          "
                          alt=""
                        />
                        转发
                      </view>
                    </view>
                  </view>
                </view>
              </template>
              <view v-else class="empty-box">
                <image
                  :src="$util.img('public/static/youpin/empty_wondeful.png')"
                  mode=""
                />
                <view class="empty-info">暂无更多直播，敬请期待</view>
              </view>
            </view>
          </view>
        </view>
      </view>
    </div>
    <!-- 直播异常（如果直播被删除了）   -->
    <div v-if="live_status<0">
      <view class="end-box end-all">
        <view class="nav bg-white" :style="{ height: navHeight + 'px' }">
          <view class="nav-title">
            <view class="logo">
              <image
                  :src="$util.img('public/static/youpin/home.png')"
                  mode="widthFix"
                  @click="backFun()"
              ></image>
              <view
              >直播</view
              >
            </view>
          </view>
        </view>
        <view class="img"></view>
        <view class="end-box-remove">该直播已下架</view>
      </view>
    </div>
    <!-- 分享 -->
    <share-popup
      v-if="isShowCanvas"
      :canvasOptions="canvasOptions"
      ref="sharePopup"
      :sharePopupOptions="sharePopupOptions"
      style="padding-top: 66px"
    ></share-popup>
  </div>
</template>
<script>
let livePlayer = requirePlugin("live-player-plugin");
import { scenePare } from "../common/js/scene_handle";
import { query_to_scene } from "../common/js/scene_handle";
import system from "@/common/js/system.js";
export default {
  data() {
    return {
      endShow: false,
      isShowCanvas: false,
      canvasOptions: {
        width: "634",
        height: "832",
        borderRadius: "20rpx",
      },
      sharePopupOptions: [],
      backShow: true,
      // 结束页
      navHeight: 0,
      height: 0,
      scorllStatus: true,
      roomList: [],
      roomInfo: {},
      media_url: "",
      wondfulStatus: [
        "",
        "进行中",
        "未开始",
        "回看",
        "禁播",
        "暂停",
        "异常",
        "已过期",
      ],
      wondfulStatusClass: [
        "",
        "ongoing",
        "begin",
        "back",
        "bear",
        "stop",
        "alarm",
        "",
      ],
      liveStatus: 0,
      live_status:0,
      room_id: 0,
      customParams: {},
      curLiveInfo: "",
    };
  },
  onLoad(option) {
    //share_openid判断是直播内的分享还是列表的分享
    if (!option.share_openid) {
      // 小程序扫码进入
      scenePare(false, option);
    }

    this.room_id = option.room_id;

    uni.getSystemInfo({
      success: (res) => {
        //导航高度
        this.navHeight = res.statusBarHeight + 46;
        this.height = res.screenHeight - this.navHeight;
      },
      fail(err) {
        console.log(err);
      },
    });

    uni.hideShareMenu()
  },
  async onShow() {
    await system.wait_staticLogin_success();
    //除了结束，暂停，其他回列表
    if (this.liveStatus != 3 && this.liveStatus != 5 && this.liveStatus != 0) {
      this.liveListFun();
      return false;
    }
    uni.showLoading({
      title: "加载中",
      mask: true,
    });
    this.$api.sendRequest({
      url: this.$apiUrl.roominfo,
      data: {
        room_id: this.room_id,
      },
      success: (res) => {
        uni.hideLoading();
        const liveStatus = res.data.live_status;
        this.live_status = liveStatus
        this.roomInfo = res.data;
        this.backShow = res.data.close_replay ? false:true;
        this.liveStatus = liveStatus.toString().substring(2, 3);
        this.shareRoomIdStatus(parseInt(liveStatus));
      },
    });
  },
  methods: {
    // 返回首页
    backFun() {
      this.$util.redirectTo("/otherpages/shop/home/<USER>", {}, "redirectTo");
    },
    // 跳转回放
    backNav() {
      if (!this.backShow) {
        this.$util.showToast({
          title: "该主播暂未开启回放功能",
        });
        return false;
      }
      this.$util.redirectTo(`/otherpages/live/end/end?room_id=${this.room_id}`);
    },
    // 跳转列表
    liveListFun() {
      this.$util.redirectTo("/otherpages/live/list/liveList", {}, "redirectTo");
    },
    // 点击列表跳转
    roomFun(e) {
      if (e.live_status == 103) {
        this.$util.redirectTo(`/otherpages/live/end/end?room_id=${e.roomid}`);
      } else {
        this.playerLive(e.roomid);
      }
    },
    // 跳转直播间
    playerLive(roomid) {
      this.$util.redirectTo(this.$util.livePlayerPageUrl(roomid,true))
    },
    // 初始化列表
    shareRoomIdStatus(liveStatus) {
      let that = this;
      uni.hideLoading();
      console.log('liveStatus', liveStatus)
      if(this.live_status<0){  //定义状态  小于0直播是异常
        return
      }
      switch (liveStatus) {
        case 103:
        case 105:
          that.endShow = true;
          //精彩直播列表
          that.$api.sendRequest({
            url: that.$apiUrl.livePage,
            data: {
              page: 1,
              page_size: 100,
              type: 0,
            },
            success: (res) => {
              let room_info = [];
              res.data.list.map((v) => {
                let status = v.live_status.toString().substring(2, 3);
                v.roomStatus = that.wondfulStatus[parseInt(status)];
                v.roomStatusClass = that.wondfulStatusClass[parseInt(status)];
                if (room_info.length < 5) {
                  room_info.push(v);
                }
              });
              that.roomList = room_info;
            },
          });
          break;
        default:
          this.endShow = false;
          this.playerLive(this.room_id);
          break;
      }
      this.changeLiveStatus(liveStatus);
    },
    // 往后间隔1分钟或更慢的频率去轮询获取直播状态
    changeLiveStatus(status) {
      var timer = setInterval(() => {
        livePlayer
          .getLiveStatus({ room_id: this.room_id })
          .then((res) => {
            const liveStatus = res.liveStatus;
            this.live_status = liveStatus
            this.liveStatus = liveStatus.toString().substring(2, 3);
            if (liveStatus && liveStatus != status) {
              this.shareRoomIdStatus(liveStatus);
            }
            if (this.$util.inArray(liveStatus, [103, 104, 106, 107])) {
              clearInterval(timer);
            }
          })
          .catch((err) => {
            console.log("get live status", err);
          });
      }, 60000);
    },
    // 储存用户信息
    setStorageFun(recommend_member_id) {
      if (
        recommend_member_id &&
        recommend_member_id != uni.getStorageSync("member_id")
      ) {
        this.$util.setUserInfo({
          recommend_member_id,
        });
      }
    },
    newCommQrcode(data) {
      // 模拟二维码参数 scene=rd%3D10%26s%3D206%26r%3D343
      let querys = {
        room_id: data.roomid,
        shop_id: uni.getStorageSync("shop_id"),
      };
      if (uni.getStorageSync("member_id")) {
        querys.recommend_member_id = uni.getStorageSync("member_id");
      }
      let scene = query_to_scene(querys);
      let userInfo = uni.getStorageSync("userInfo")
      if(uni.getStorageSync("token") ==  "") {
        userInfo = ""
      }
      this.isShowCanvas = false;
      this.$api.sendRequest({
        url: "/api/Website/newCommQrcode",
        data: {
          // path: "pages/index/index/index", // 测试用
          path: 'pages/live-player-plugin',
          scene,
        },
        success: (res) => {
          if (res.code == 0) {
            if (userInfo == "") {
              let user = {}
              this.$api.sendRequest({
                url: '/api/member/info',
                success: info => {
                  if (info.code == 0 && uni.getStorageSync("token") != '') {
                    user = {
                      headimg: info.data.headimg,
                      nickname: info.data.nickname
                    };
                  }else{
                    user = {
                      headimg:
                        "https://youpin-dev.jiufuwangluo.com:8443//upload/default/default_img/head.png",
                      nickname: "请登录",
                    };
                  }
                  this.drawCanvas(res.data.qrcodeUrl, data, user);
                  setTimeout(() => {
                    this.$refs.sharePopup.open();
                  }, 0);
                }
              })
            }else{
              this.drawCanvas(res.data.qrcodeUrl, data, userInfo);
              setTimeout(() => {
                this.$refs.sharePopup.open();
              }, 0);
            }
          } else {
            this.$util.showToast({
              title: res.message,
            });
          }
        },
      });
    },
    drawCanvas(qrcodeUrl, data, user) {
      this.curLiveInfo = data;
      this.sharePopupOptions = [
        {
          background: "#fff",
          x: 0,
          y: 0,
          width: 634,
          height: 832,
          type: "image",
        },
        {
          // 头图
          path: this.$util.img(data.share_img),
          x: 0,
          y: 0,
          width: 634,
          height: 507,
          type: "image",
        },
        {
          // 头像
          path: user.headimg,
          radius: 36,
          x: 40,
          y: 567,
          width: 56,
          height: 56,
          type: "image",
        },
        {
          text: user.nickname,
          size: 28,
          color: "#333",
          fontWeight: "bold",
          x: 130,
          y: 582,
          type: "text",
        },
        {
          text: data.name,
          size: 26,
          color: "#999",
          x: 130,
          y: 630,
          width: 310,
          lineNum: 2,
          lineHeight: 34,
          type: "text",
        },
        {
          path: qrcodeUrl,
          x: 466,
          y: 536,
          width: 128,
          height: 128,
          type: "image",
        },
        {
          background: "#F8F8F8",
          x: 0,
          y: 692,
          width: 634,
          height: 140,
          type: "image",
        },
        {
          path: this.$util.img("public/static/youpin/qrcodetips.png"),
          x: 40,
          y: 710,
          width: 554,
          height: 106,
          type: "image",
        },
      ];
      this.isShowCanvas = true;
    },
    // 图片加载失败显示默认图片
    imageError(index) {
      this.roomList[index].feeds_img =
          this.$util.getDefaultImage().default_goods_img;
    },
    /**
     *分享参数组装(注意需要分享的那一刻再调此方法)
     */
    getSharePageParams(){
      let data = this.curLiveInfo
      return this.$util.unifySharePageParams('/pages/live-player-plugin',data.name,
          '', {room_id:data.roomid},this.$util.img(data.share_img))
    },
  },
  onShareAppMessage(res) {
    let share_data=this.getSharePageParams();
    return this.$buriedPoint.pageShare(share_data.link, share_data.imageUrl,  share_data.desc);
  },
};
</script>

<style lang="scss" scoped>
.end-box {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  // bottom: 0;
  .header {
    // position: fixed;
    // top: 0;
    // left: 0;
    // right: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    // position: relative;
    z-index: 1111;

    .title {
      font-size: 18px;
      color: #fff;
      height: 88rpx;
    }
  }
  .end-info {
    position: relative;
    z-index: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    // padding-top: 20rpx;
    img {
      width: 128rpx;
      height: 128rpx;
      border-radius: 50%;
      margin: 40rpx 0 12rpx;
    }
    .name {
      font-size: 28rpx;
      color: #fff;
      line-height: 30rpx;
      // margin-top: 20rpx;
    }
    .end-text {
      font-size: 40rpx;
      color: #fff;
      font-weight: bold;
      margin-top: 40rpx;
    }
    .btn {
      display: flex;
      margin: 68rpx 0 48rpx;
      & > view {
        display: flex;
        justify-content: center;
        align-items: center;
        width: 280rpx;
        border-radius: 44rpx;
        height: 88rpx;
      }
      .back-nav {
        border: 1px solid #ffffff;
        color: #fff;
        margin-right: 60rpx;
        box-sizing: border-box;
      }
      .more-btn {
        border: 1px solid #ffffff;
        color: #fff;
        box-sizing: border-box;
      }
    }
  }
  .img {
    position: fixed;
    top: -2%;
    left: -8%;
    right: 0;
    height: 946rpx;
    width: 115%;
    background-size: 100% 100%;
    background-repeat: no-repeat;
    filter: blur(4px);
    user-select: none;
    &::after {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      content: "";
      display: block;
      background: rgba(0, 0, 0, 0.7);
    }
  }
  .wonderful-top {
    position: sticky;
    top: 0;
    z-index: 111;
    background: rgba(74, 74, 74, 0.9);
    &::after {
      content: "";
      position: absolute;
      left: 0;
      top: 0;
      right: 0;
      height: 20rpx;
      filter: blur(4px);
      background: rgba(67, 57, 61, 1);
      z-index: 1;
    }
    & > view {
      display: flex;
      justify-content: space-between;
      align-items: center;
      background: #f5f5f5;
      padding: 24rpx;
      border-radius: 20rpx 20rpx 0 0;
      position: relative;
      z-index: 111;
      & > view {
        &:first-child {
          font-size: 30rpx;
          color: #222;
          line-height: 42rpx;
        }
        &:last-child {
          font-size: 24rpx;
          color: #666;
          text {
            font-size: 24rpx;
          }
        }
      }
    }
  }
  .wonderful-box {
    flex: 1;
    z-index: 1;
    // border-radius: 40rpx 40rpx 0 0;
    // padding: 0 24rpx 24rpx;
    // background: #F5F5F5;
    position: relative;
    display: flex;
    flex-direction: column;
    height: 100%;
    .top {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 24rpx;
      & > view {
        &:first-child {
          font-size: 30rpx;
          color: #222;
          line-height: 42rpx;
        }
        &:last-child {
          font-size: 24rpx;
          color: #666;
          text {
            font-size: 24rpx;
          }
        }
      }
    }
    .live-box {
      // overflow-y: scroll;
      // margin: 0 0 30rpx;
      padding: 0 24rpx;
      background: #f5f5f5;
      height: 924rpx;
      overflow-y: auto;
      .live-list {
        display: flex;
        background-color: #fff;
        padding: 24rpx;
        margin-bottom: 24rpx;
        border-radius: 20rpx;
        .left {
          position: relative;
          width: 372rpx;
          height: 372rpx;
          .live-img {
            width: 372rpx;
            height: 372rpx;
            border-radius: 20rpx;
          }
          .info {
            position: absolute;
            top: 12rpx;
            left: 12rpx;
            // width: 256rpx;
            height: 36rpx;
            display: flex;
            align-items: center;
            background-color: rgba(0, 0, 0, 0.5);
            border-radius: 18rpx;
            & > view:first-child {
              background: linear-gradient(180deg, #ec624d 0%, #ff3333 100%);
              border-radius: 18px;
              // width: 80rpx;
              height: 36rpx;
              font-size: 20rpx;
              color: #fff;
              padding: 0 8rpx;
              display: flex;
              align-items: center;
              justify-content: center;
              img {
                width: 20rpx;
                height: 20rpx;
                margin-right: 4rpx;
              }
            }
            // &>view:last-child{
            //   font-size: 20rpx;
            //   padding: 0 20rpx 0 4rpx;
            //   color: #fff;
            // }
          }
        }
        .right {
          display: flex;
          flex-direction: column;
          flex: 1;
          padding-left: 24rpx;
          .title {
            line-height: 44rpx;
            text-overflow: ellipsis;
            display: -webkit-box;
            -webkit-box-orient: vertical;
            -webkit-line-clamp: 2;
            overflow: hidden;
            min-height: 88rpx;
          }
          .wonderful-info {
            display: flex;
            margin-top: 40rpx;
            align-items: center;
            img {
              width: 44rpx;
              height: 44rpx;
              border-radius: 50%;
              margin-right: 4rpx;
            }
            view {
              font-size: 24rpx;
              color: #666;
              line-height: 30rpx;
            }
          }
          .goods {
            display: flex;
            margin-top: 14rpx;
            width: 258rpx;
            overflow-x: scroll;
            img {
              flex-shrink: 0;
              width: 120rpx;
              height: 120rpx;
              border-radius: 10rpx;
              margin-right: 20rpx;
              &:last-child {
                margin-right: 20rpx;
              }
            }
          }
          .btn {
            display: flex;
            align-items: flex-end;
            flex-direction: row-reverse;
            flex: 1;
            view {
              display: flex;
              justify-content: center;
              align-items: center;
              width: 126rpx;
              height: 48rpx;
              background-color: #ff3333;
              border-radius: 48rpx;
              color: #fff;
              img {
                width: 32rpx;
                height: 32rpx;
                margin-right: 4rpx;
              }
            }
          }
        }
      }
    }
  }
  .fixedTop {
    position: fixed;
    top: 24rpx;
    left: 24rpx;
    right: 24rpx;
  }
  &-remove{
    position: fixed;
    top:300rpx;
    left: 50%;
    transform: translateX(-50%);
    color: white;
    font-size: 36rpx;
  }
}
.end-all {
  display: flex;
  flex-direction: column;
  .scroll-box {
    flex: 1;
    padding-bottom: 30rpx;
  }
}
.nav {
  width: 100%;
  position: relative;
  z-index: 10;
}
.nav-title {
  width: 100%;
  height: 88rpx;
  line-height: 88rpx;
  text-align: center;
  position: absolute;
  bottom: 0;
  z-index: 10;
}
.nav .logo {
  position: relative;
  width: 100%;
  display: flex;
  height: 88rpx;
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  left: 0rpx;
  // padding:0 24rpx;
  display: flex;
  align-items: center;
  justify-content: flex-start;
  image {
    position: absolute;
    left: 18rpx;
    width: 48rpx;
    height: 48rpx;
  }
  view {
    flex: 1;
    display: block;
    width: calc(100% - 120rpx);
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    margin-left: 10rpx;
    text-align: center;
    font-size: 36rpx;
    color: #fff;
    // font-weight: bold;
  }
}
.empty-box {
  display: flex;
  flex-direction: column;
  align-items: center;
  image {
    width: 402rpx;
    height: 282rpx;
    margin: 208rpx 0 42rpx;
  }
  .empty-info {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100%;
    font-size: 32rpx;
    line-height: 44rpx;
  }
}
</style>
