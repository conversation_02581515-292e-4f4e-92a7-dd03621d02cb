<template>
	<view class="container" :class="themeStyle" :style="[themeColorVar]">
			<view class="image-wrap">
				<image :src="$util.img('public/static/youpin/order/paymentSuccess.png')" mode="" class="result-image"></image>
			</view>
			<view class="msg">支付成功</view>
			<view class="operation">
				<view class="btn go-home" @click="goHome()">回首页逛逛</view>
				<view class="btn" @click="toOrder()" v-if="token">查看订单</view>
			</view>
		<loading-cover ref="loadingCover"></loading-cover>

		<!-- <view class="tuijianimg" v-if="tuijianList.length>0">
			<image :src="$util.img('public/static/youpin/maidou/tuijan.png')"></image>
		</view>
		<view class="tuijianlist">
			<view class="seckill-box-item" v-for="(item, key) in tuijianList" :key="key" @click="toDetail(item)">
				<view class="seckill-item">
					<view class="seckill-item-image">
						<image :src="$util.img(item.goods_image)"></image>
					</view>
					<view class="seckill-item-new-name">{{item.goods_name}}</view>
					<view class="seckill-item-new-price ns-text-color">
						<text>￥</text>
						{{item.retail_price}}
					</view>
					<text class="seckill-item-old-price">￥{{item.market_price}}</text>
					<view class="song_maidou">
						<text class="song">送迈豆</text>
						<text class="mdnum">{{item.send_maidou}}</text>
					</view>
				</view>
			</view>
		</view> -->
		<view><nsGoodsRecommend ref="goodrecommend"></nsGoodsRecommend></view>
    <!-- 返回顶部 -->
    <to-top v-if="showTop" @toTop="scrollToTopNative()"></to-top>
	</view>
</template>

<script>
	import uniIcons from "@/components/uni-icons/uni-icons.vue"
	import nsGoodsRecommend from '@/components/ns-goods-recommend/ns-goods-recommend.vue';
  import toTop from '@/components/toTop/toTop.vue';
  import scroll from '@/common/mixins/scroll-view.js';
  import golbalConfig from "../../../common/mixins/golbalConfig";
	export default {
		components: {uniIcons,nsGoodsRecommend,toTop},
    mixins: [scroll,golbalConfig],
		data() {
			return {
				payInfo: {},
				outTradeNo: '',
				token: null,
				order_ids:[],
				tuijianList: '',
			};
		},
		onLoad(option) {
			if (option.code) this.outTradeNo = option.code;
			if (option.order_ids){
				this.order_ids = option.order_ids.split(",")
			}
			if (this.$refs.loadingCover) this.$refs.loadingCover.hide();
			this.getTuijian()
		},
		onShow() {
			// 刷新多语言
			this.$langConfig.refresh();
			if (uni.getStorageSync('token')) this.token = uni.getStorageSync('token');
		},
    onReady(){
		// #ifdef H5
		if(this.$refs.loadingCover) this.$refs.loadingCover.hide();
		// #endif
    },
		computed: {
			themeStyle() {
				return 'theme-' + this.$store.state.themeStyle
			}
		},
    onReachBottom(){
      this.$refs.goodrecommend.scrollPage()
    },
		methods: {
			goHome() {
				this.$util.redirectTo('/otherpages/shop/home/<USER>', {}, 'reLaunch');
			},
			toOrder() {
				if(this.order_ids.length>1) {
					this.$util.redirectTo('/pages/order/list/list?status=waitsend', {}, 'redirectTo')
				} else if(this.order_ids.length == 1) {
					this.$util.redirectTo('/pages/order/detail/detail?order_id='+this.order_ids[0], {}, 'redirectTo')
				}
			},
			toDetail(res) {
				this.$util.redirectTo('/pages/goods/detail/detail', {
					sku_id: res.sku_id
				});
			},
			getTuijian() {
				this.$api.sendRequest({
					url: this.$apiUrl.recommandGoodList,
					data: {

					},
					success: res => {
						let msg = res.message;
						if (res.code == 0 && res.data) {
							this.tuijianList = res.data.list
						} else {
							this.$util.showToast({
								title: msg
							})
						}
					},
					fail() {

					}
				});
			},
      /**
       *分享参数组装
       */
      getSharePageParams() {
        return this.$util.unifySharePageParams('/otherpages/shop/home/<USER>', this.shareTitle || '先迈商城',
            '',{}, this.$util.img(this.shareImg) || this.$util.img('public/static/youpin/home_share.jpg'))
      }
		},
    /**
     * 自定义分享内容
     * @param {Object} res
     */
    onShareAppMessage(res) {
      let { title, link, imageUrl, query } = this.getSharePageParams()
      return this.$buriedPoint.pageShare(link , imageUrl, title);
    },
	};
</script>

<style lang="scss">
	.container {
		width: 100vw;
		height: 100vh;
		background: #F5F5F5;

		.image-wrap {
			display: flex;
			justify-content: center;
			padding: 160rpx 0 36rpx 0;

			.result-image {
				width: 120rpx;
				height: 120rpx;
			}
		}

		.msg {
			text-align: center;
			font-size: 36rpx;
			line-height: 1;
			margin-bottom: 200rpx;
		}


		.operation {
			display: flex;
			justify-content: space-around;
			padding: 0 30rpx;
			.btn {
				text-align: center;
				width: 280rpx;
				height: 80rpx;
				line-height: 80rpx;
				text-align: center;
				border-radius: 40rpx;
				font-size: 32rpx;
				box-sizing: border-box;
				color: #FFFFFF;
				background-color: var(--custom-brand-color);
				&.go-home {
					border: 1rpx solid #DDDDDD;
					color: #333333;
					background-color: transparent;
				}
			}

		}
	}
	.tuijianimg {
		padding: 40rpx 0 10rpx 0;
		text-align: center;

		image {
			width: 292rpx;
			height: 32rpx;
		}
	}
	.tuijianlist {
		margin: 20rpx;
		background: #FFFFFF;
		border-radius: 20rpx !important;
		display: flex;
		flex-wrap: wrap;
	}

	.seckill-box-item {
		width: 30%;
		height: 100%;
		display: flex;
		flex-direction: column;
		align-items: center;
		background: #ffffff;
		padding: 10rpx;
		margin-left: 3rpx;
		border-radius: 20rpx;

		.seckill-item {
			width: 100%;
			padding: 20rpx;
		}

		.seckill-item-image {
			width: 100%;
			height: 205rpx;
			border-radius: 20rpx;
			overflow: hidden;
			display: flex;
			justify-content: center;
			align-items: center;

			image {
				width: 100%;
				height: 205rpx;
				padding: 0;
				margin: 0;
			}
		}

		.seckill-item-new-name {
			white-space: normal;
			margin: 30rpx 0 20rpx 0;
			font-size: $ns-font-size-xm;
			color: #333333;
			line-height: 1.3;
			height: 64rpx;
			// height: 60rpx;
			word-break: break-all;
			text-overflow: ellipsis; //显示为省略号
			display: -webkit-box; //对象作为伸缩盒子模型显示
			-webkit-box-orient: vertical; //设置或检索伸缩盒对象的子元素的排列方式
			-webkit-line-clamp: 2; //显示行数## 标题文字 ##
			overflow: hidden;
		}

		.seckill-item-new-price {
			font-size: 36rpx;
			line-height: 1;

			text:first-child {
				font-size: $ns-font-size-xm;
			}
		}

		.seckill-item-old-price {
			font-size: $ns-font-size-sm;
			color: $ns-text-color-gray;
			text-decoration: line-through;
			line-height: 1;
		}

		.song_maidou {
			background: #FFEFEF;
			font-size: 22rpx;
			border-radius: 8rpx;
			width: 70%;
			margin-top: 10rpx;

			.song {
				color: #333333;
				margin-left: 10rpx;
			}

			.mdnum {
				color: var(--custom-brand-color);
			}

		}
	}
</style>
