<template>
  <loading-cover ref="loadingCover"></loading-cover>
</template>

<script>
import apiurls from "../../common/js/apiurls";
import system from "../../common/js/system";

export default{
	name:'home',
	data(){
		return{
      xm_shop_id:null,
      xm_uid:null,
      options:{}
    }
	},
	onLoad(options) {
		this.options = options;
    if(this.options.uid){
      this.xm_uid=this.options.uid;
    }
	},
  onShow(){
    let sceneDict=this.parseScene(this.options);
    if(this.options.shopId){
      this.xm_shop_id=this.options.shopId;
      if(!uni.getStorageSync('shop_id')){
        uni.setStorageSync('shop_id',0);
      }
      this.getData();
    }else if(sceneDict.hasOwnProperty('i')){
      this.xm_shop_id=sceneDict['i'];
      if(!uni.getStorageSync('shop_id')){
        uni.setStorageSync('shop_id',0);
      }
      this.getData();
    }
  },
	methods:{
    async getData(){
      let res=await this.$api.sendRequest({
        url: apiurls.transformParam,
        async:false,
        data: {
          xm_shop_id:this.xm_shop_id,
          xm_uid:this.xm_uid
        },
      });
      if(res.code!=0){
        return
      }else{
        let options={
          shop_id:res.data.shop_id
        }
        if (this.$refs.loadingCover) this.$refs.loadingCover.hide();
        await system.AppVueInit(options,this);
        this.$util.redirectTo('/otherpages/shop/home/<USER>',options,'reLaunch')
      }
    },
    parseScene(options){
      let sceneDict={}; //扫二维进入小程序的参数
      let sceneParams = decodeURIComponent(options.scene);
      sceneParams = sceneParams.split('&');
      if (sceneParams.length) {
        sceneParams.forEach(item => {
          let oneItme=item.split('=');
          if(oneItme.length>1){
            sceneDict[oneItme[0]]=oneItme[1];
          }
        });
      }
      return sceneDict
    },
    /**
     *分享参数组装
     */
    getSharePageParams() {
      return this.$util.unifySharePageParams('/otherpages/shop/home/<USER>', this.shareTitle || '先迈商城',
          '',{}, this.$util.img(this.shareImg) || this.$util.img('public/static/youpin/home_share.jpg'))
    }
  },
  /**
   * 自定义分享内容
   * @param {Object} res
   */
  onShareAppMessage(res) {
    let { title, link, imageUrl, query } = this.getSharePageParams()
    return this.$buriedPoint.pageShare(link , imageUrl, title);
  },
}
</script>

<style>
</style>
