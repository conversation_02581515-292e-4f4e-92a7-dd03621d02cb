import Vue from 'vue'
import Vuex from 'vuex'
Vue.use(Vuex)

import Http from '../common/js/request/http.js'
import util from '../common/js/util'

const store = new Vuex.Store({
    state: {
		cartNumber:0,						//购物车数量
		wholeSaleNumber:0,					//进货单数量
		themeStyle:'',
		themeColorVar:{},
		Development: 0,
		addonIsExit: {
			bundling: 0,
			coupon: 0,
			discount: 0,
			fenxiao: 0,
			gift: 0,
			groupbuy: 0,
			manjian: 0,
			memberconsume: 0,
			memberrecharge: 0,
			memberregister: 0,
			membersignin: 0,
			memberwithdraw: 0,
			pintuan: 0,
			pointexchange: 0,
			seckill: 0,
			store: 0,
			topic: 0
		},
		share_member_id:null,  //打开分享商品时，分享者的分销客id
		share_shop_id:null,   //打开分享商品时，分享者的分享的店铺id
		is_shopkeeper:null,   //是否掌柜从app个人中心页面分享出来的
		scene:null,   //小程序进入的场景
		buried_shop_id:null, //分享商品时shop_id，url传参作埋点用
		isPopHomeAd:false,  //是否已经弹过首页弹窗
		incomeInfo:{  // 钱包未读收益，（给底部导航用）
			income_money: 0,
			income_nums: 0
		},
		wechatVerCode:{
			verifyState:false,  // 验证状态  true:验证成功  false:验证失败
			codeType:'',  //验证码类型  login:登录  register:注册  forget:忘记密码  bind:绑定手机号 http：请求触发  httpAndRefresh:请求触发,验证完成后刷新当前页面
		},  // 腾讯验证码是否显示结构
		wechatCodeBill:'', //腾讯验证码的票据
		loadingAnimationData:null,  //加载动画的json数据
		audit_mode: 1, //审核模式
	},
    mutations: {
		setCartNumber(state, cartNumber) {
			state.cartNumber = cartNumber
		},
		setWholeSaleNumber(state, wholeSaleNumber) {
			state.wholeSaleNumber = wholeSaleNumber
		},
		setThemeStyle(state, ThemeStyle) {
			state.themeStyle = ThemeStyle
		},
		setAddonIsexit(state,addonIsExit){
			state.addonIsExit=Object.assign(state.addonIsExit, addonIsExit);
		},
		setThemeColor(state, ThemeColor) {
			state.themeColorVar = ThemeColor
		},
		setShareMemberId(state,id){
			state.share_member_id=id;
		},
		setShareShopId(state,id){
			state.share_shop_id=id;
		},
		setIsShopkeeper(state,is_shopkeeper){
			state.is_shopkeeper=is_shopkeeper;
		},
		setScener(state,scene){
			state.scene=scene;
		},
		setBuriedShopId(state,shop_id){
			state.buried_shop_id=shop_id;
		},
		setIsPopHomeAd(state,isPopHomeAd){
			state.isPopHomeAd=isPopHomeAd;
		},
		setIncomeInfo(state,incomeInfo){
			state.incomeInfo = incomeInfo
		},
		setWechatVerCode(state,wechatVerCode){
			state.wechatVerCode = wechatVerCode
		},
		setWechatCodeBill(state,wechatCodeBill){
			state.wechatCodeBill = wechatCodeBill
		},
		setLoadingAnimationData(state,loadingAnimationData){
			state.loadingAnimationData = loadingAnimationData
		},
		setAuditMode(state,audit_mode){
			state.audit_mode = audit_mode
		}
	},
    actions: {
		//查询购物车数量
		getCartNumber() {
			if (uni.getStorageSync("token")) {
				return new Promise((resolve, reject)=>{
					Http.sendRequest({
						url: '/api/cart/count',
						success: res => {
							if (res.code == 0) {
								this.commit('setCartNumber', res.data)
								resolve(res.data)
							}
						}
					});
				})
			}
		},
		//查询进货单数量
		getWholeSaleNumber(){
			Http.sendRequest({
				url: '/wholesale/api/cart/count',
				success: res => {
					if (res.code == 0) {
						this.commit('setWholeSaleNumber',res.data)
					}
				}
			});
		},
		getThemeStyle() {
			if(uni.getStorageSync('setThemeStyle')){
				this.commit('setThemeStyle', uni.getStorageSync('setThemeStyle'))
			}
			Http.sendRequest({
				url: '/api/diyview/style',
				success: res => {
					if (res.code == 0) {
						if(res.data.audit_mode == 0 || res.data.audit_mode == 1){
							this.commit('setAuditMode',res.data.audit_mode);
						}
						this.commit('setThemeStyle', res.data.style_theme)
						uni.setStorageSync('setThemeStyle',res.data.style_theme)
					}
				}
			});
		},
		// 获取插件是否安装
		getAddonIsexit() {
			if(uni.getStorageSync('memberAddonIsExit')){
				this.commit('setAddonIsexit', uni.getStorageSync('memberAddonIsExit'))
			}
			Http.sendRequest({
				url: '/api/addon/addonisexit',
				success: res => {
					if (res.code == 0) {
						uni.setStorageSync('memberAddonIsExit',res.data);
						this.commit('setAddonIsexit', res.data)
					}
				}
			})
		},

		//获取主题颜色（品牌颜色）
		async getThemeColor(){
			try{
				let shop_id = uni.getStorageSync('shop_id');
				let res = await Http.sendRequest({
					url: '/api/diyview/info',
					data: {
						name: 'DIYVIEW_SHOP_INDEX',
						site_id: shop_id || 0
					},
					async: false,
				});
				if (res.code == 0) {
					let data = JSON.parse(res.data.value)
					let theme_color = {};
					if(data.global.brandColor){
						theme_color['--custom-brand-color'] = data.global.brandColor;
						let rgbaColors = util.generateRGBAColors(data.global.brandColor)
						for (let i = 0; i < rgbaColors.length; i++) {
							theme_color[`--custom-brand-color-${10+i*10}`] = rgbaColors[i]
						}
					}
					this.commit('setThemeColor', theme_color)
				}
			}catch (e) {

			}

		},

		// 记录分享者的分销客id
		writeShareMemberId({commit},id){
			commit('setShareMemberId',id);
		},
		// 记录分享者的分享的店铺id
		writeShareShopId({commit},id){
			commit('setShareShopId',id);
		},
		writeIsShopkeeper({commit},is_shopkeeper){
			commit('setIsShopkeeper',is_shopkeeper);
		},
		writeScene({commit},scene){
			commit('setScener',scene);
		},
		writeBuriedShopId({commit},id){
			commit('setBuriedShopId',id);
		},
		writeIsPopHomeAd({commit},isPopHomeAd){
			commit('setIsPopHomeAd',isPopHomeAd)
		},
		writeIncomeInfo({commit}, incomeInfo){
			commit('setIncomeInfo', incomeInfo)
		},
		writeWechatVerCode({commit}, wechatVerCode){
			commit('setWechatVerCode', wechatVerCode)
		},
		writeWechatCodeBill({commit}, wechatCodeBill){
			commit('setWechatCodeBill', wechatCodeBill)
		},
		writeLoadingAnimationData({commit},loadingAnimationData){
			commit('setLoadingAnimationData',loadingAnimationData)
		}
    }
})
export default store
