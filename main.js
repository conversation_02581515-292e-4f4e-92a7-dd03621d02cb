import Vue from 'vue'
import App from './App'
import store from './store'
import Util from './common/js/util.js'
import Http from './common/js/request/http.js'
import apiUrl from './common/js/apiurls.js'
import Lang from './common/js/lang.js'
import Config from './common/js/config.js'
// import Socket from './common/js/socket.js'
import buriedPoint from './common/js/buriedPoint.js'
import {H5ReportSentry, WechatReportSentry} from './common/js/report-sentry'

Vue.config.productionTip = false
Vue.prototype.$bus = new Vue();  //事件总线
Vue.prototype.$store = store; //挂在vue
Vue.prototype.$util = Util; //工具类
Vue.prototype.$api = Http; //请求包
Vue.prototype.$apiUrl = apiUrl; //全局API路径
Vue.prototype.$langConfig = Lang; //语言包对象
Vue.prototype.$lang = Lang.lang; //解析语言包
Vue.prototype.$config = Config; //小程序配置
// Vue.prototype.$Socket = Socket; //webSocket连接方法，暂时无用
Vue.prototype.$buriedPoint = buriedPoint; //上报埋点

App.mpType = 'app'
// #ifdef MP-WEIXIN
if(process.env.NODE_ENV === 'production'){
	let uma=require('./common/js/uma')
	Vue.use(uma);
}
new WechatReportSentry().run()
let token = uni.getStorageSync('token')
let member_id = uni.getStorageSync('member_id')
WechatReportSentry.configureScope(token,member_id)
// #endif

// #ifdef H5
if(Util.isWeiXin()){
	Util.addScript('https://wx.gtimg.com/pay_h5/goldplan/js/jgoldplan-1.0.0.js')
}
Vue.prototype.$util.InitJsSDKconfig().then(wxJS=>{
	Vue.prototype.wxJS=wxJS;
});
Vue.prototype.$util.baiduStatistics();
Vue.prototype.$util.tencentCaptchaJS();
Vue.prototype.$util.sentrySrc();
new H5ReportSentry().run();
// #endif


//常用组件
import diyBottomNav from '@/components/diy-bottom-nav/diy-bottom-nav.vue';
Vue.component('diy-bottom-nav', diyBottomNav);

import loadingCover from '@/components/loading-cover/loading-cover.vue';
Vue.component('loading-cover', loadingCover);

import nsEmpty from '@/components/ns-empty/ns-empty.vue';
Vue.component('ns-empty', nsEmpty);

import MescrollUni from "@/components/mescroll/my-list-mescroll.vue";
Vue.component("mescroll-uni", MescrollUni); //上拉加载,下拉刷新组件

import MescrollBody from "@/components/mescroll/mescroll-body.vue"
Vue.component('mescroll-body', MescrollBody)

import NsLogin from "@/components/ns-login/ns-login.vue"
Vue.component('ns-login', NsLogin)

import ydAuthPopup from "@/components/yd-auth-popup/yd-auth-popup.vue"
Vue.component('yd-auth-popup',ydAuthPopup);

import ypUpgradeVipButton from "@/components/yp-upgrade-vip-button/yp-upgrade-vip-button.vue"
Vue.component('yp-upgrade-vip-button',ypUpgradeVipButton);

// import ypWechatPrivacyPopup from "@/components/yp-wechat-privacy-popup/yp-wechat-privacy-popup.vue"
// Vue.component('yp-wechat-privacy-popup', ypWechatPrivacyPopup);

import diyPageGesture from "@/components/diy-page-gesture/diy-page-gesture.vue"
Vue.component('diy-page-gesture', diyPageGesture);

// #ifdef H5
// 在app中启用h5调试工具
import Vconsole from 'vconsole'
import {isOnXianMaiApp} from "./common/js/h5/appOP";
if(isOnXianMaiApp){
	// 解决app调用webview的函数出现报错的情况
	window.updateLoginData = function(data) {
	}
}
if(isOnXianMaiApp && process.env.NODE_ENV === 'development'){
	let vConsole = new Vconsole()
}
// #endif


const app = new Vue({
	...App,store
})

app.$mount()
