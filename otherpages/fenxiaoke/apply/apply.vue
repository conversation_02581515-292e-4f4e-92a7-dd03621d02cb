<template>
	<view class="fenxiaoke-apply">
    <rich-text :nodes="html"></rich-text>
    <view class="op">
      <button type="warn" @click="toApply">立即申请</button>
    </view>


    <!--提示弹窗 start-->
    <uni-popup type="center" ref="applyTip" :maskClick="false">
      <view class="apply-tip">
        <view class="apply-tip-title">提示</view>
        <view class="apply-tip-content">申请成功，我们将在3-5个工作日给您审核，请耐心等待哦！~</view>
        <button type="warn" class="apply-tip-op" @click="closeOp">好的</button>
      </view>
    </uni-popup>
    <!--提示弹窗 end-->
    <loading-cover ref="loadingCover"></loading-cover>
	</view>
</template>

<script>
import htmlParser from '@/common/js/html-parser';
import uniPopup from '@/components/uni-popup/uni-popup.vue';
import apiurls from "../../../common/js/apiurls";
	export default {
    components:{
      uniPopup
    },
		data() {
			return {
				html:"",
        tmplId:"",
        tmpKey:null
			}
		},
		methods: {
      getData(){
        let that=this;
        this.$api.sendRequest({
          url: apiurls.fenxiaokeDistributorUrl,
          data: {},
          success:(res)=>{
            if(res.code!=0){
              that.$util.showToast({
                title: res.message,
              })
            }else{
             that.html=htmlParser(res.data.content);
            }
            that.$refs.loadingCover.hide();
          }
        });

      },
      toApply(){
        uni.showLoading({
          title:"加载中",
          mask:true
        });
        let that=this;
        let token=uni.getStorageSync('token');
        this.$api.sendRequest({
          url: apiurls.fenxiaokeApplyDistributorUrl,
          data: {
            token
          },
          success:(res)=>{
            uni.hideLoading()
            if(res.code!=0){
              that.$util.showToast({
                title: res.message,
              })
            }else{
              that.tmplId=res.data.template_id;
              that.tmpKey=res.data.key;
              that.$refs.applyTip.open();
            }
          },
        });
      },
      closeOp(){
        this.$refs.applyTip.close();
        if(!this.tmplId || !this.tmpKey){
          return
        }
        let that=this;
        uni.requestSubscribeMessage({
          tmplIds: [this.tmplId],
          success (res) {
            console.log(res)
            let token=uni.getStorageSync('token');
            that.$api.sendRequest({
              url: apiurls.bookNoticeUrl,
              data: {
                token,
                key:that.tmpKey,
                type:1
              },
              success:(res)=>{
              },
            });
          },
          fail(err){
            console.log(err)
          }
        })
      }
		},
		onLoad(){
			uni.setNavigationBarTitle({
				title:"申请分销客"
			})
      this.getData();
    },
	}
</script>

<style lang="scss">
  page{
    background-color: #fff;
  }
  .fenxiaoke-apply{
    padding: 0 24rpx;
    box-sizing: border-box;
    .op{
      margin: 0;
      width: 100;
      height: 97rpx;
      background-color: #ffffff;
      position: fixed;
      left: 0;
      bottom: 0rpx;
      bottom:  calc(0rpx + constant(safe-area-inset-bottom));
      bottom:  calc(0rpx + env(safe-area-inset-bottom));
      display: flex;
      justify-content: center;
      align-items: center;
      button{
        width: 654rpx;
        height: 80rpx;
        background: #F2280C;
        border-radius: 40rpx;
        color: #FFFFFF;
      }
    }
  }
  .apply-tip{
    width: 540rpx;
    background: #FFFFFF;
    border-radius: 20rpx;
    padding: 36rpx 40rpx;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
    align-items: center;
    &-title{
      font-weight: bold;
      color: #343434;
    }
    &-content{
      font-size: 28rpx;
      font-weight: 500;
      color: #666666;
      text-align: center;
      margin-top: 35rpx;
    }
    &-op{
      width: 220rpx;
      background: #F2280C !important;
      color: #FFFFFF !important;
      margin-top: 43rpx;
    }
  }
</style>
