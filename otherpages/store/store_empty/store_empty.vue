<template>
	<view class="page">
		<view class='nav bg-white' :style="{ height: navHeight + 'px' }">
		  <view class='nav-title'>
		    <image  :src="$util.img('public/static/youpin/home-logo.png')" mode='aspectFit' class='home-logo'></image>
		  </view>
		</view>
		<view class="empty" :style="{paddingTop:360+navHeight*2+'rpx'}">
      <view class="empty_forbidden" v-if="code==403">
        <image :src="$util.img('public/static/youpin/http_403.png')" mode="aspectFit"></image>
      </view>
			<view class="empty_img" v-else>
        <image :src="$util.img('public/static/youpin/store_empty.png')" mode="aspectFit"></image>
      </view>
			<view class="ns-text-color-gray ns-margin-top ns-margin-bottom" v-if="message[code]">{{message[code]}}</view>
		</view>
	</view>
</template>

<script>
export default{
	data(){
		return{
			navHeight: 0,
      code:0,
      message:{
        0: '哎哟~店铺打烊了~'
      }
		}
	},
  onLoad(option){
    // #ifdef MP-WEIXIN
    uni.hideHomeButton()
    // #endif
	uni.getSystemInfo({
	  success: res => {
	    //导航高度
	    let navHeight = res.statusBarHeight + 46;
	    this.navHeight = navHeight;
	  },
	  fail(err) {
	    console.log(err);
	  }
	})
    this.code = parseInt(option.status) || 0;
  },
	onShow(){},
  methods:{
    /**
     *分享参数组装
     */
    getSharePageParams() {
      return this.$util.unifySharePageParams('/otherpages/shop/home/<USER>', this.shareTitle || '先迈商城',
          '',{}, this.$util.img(this.shareImg) || this.$util.img('public/static/youpin/home_share.jpg'))
    }
  },
  /**
   * 自定义分享内容
   * @param {Object} res
   */
  onShareAppMessage(res) {
    let { title, link, imageUrl, query } = this.getSharePageParams()
    return this.$buriedPoint.pageShare(link , imageUrl, title);
  },
}
</script>

<style lang="scss" scoped>
	.page{
		background: #f5f5f5 !important;
		width: 100%;
		height: 100vh;
	}
	.empty {
		width: 100%;
		display: flex;
		flex-direction: column;
		align-items: center;
		box-sizing: border-box;
    .empty_forbidden{
      image{
        width: 492rpx;
        height: 492rpx;
      }
    }
		.empty_img {
			width: 400rpx;
			height: 280rpx;
			margin-bottom:26rpx;
			image {
				width: 100%;
				height: 100%;
				padding-bottom: $ns-margin;
			}
		}
		.ns-text-color-gray{
			color:#999 !important;
      text-align: center;
		}
	}
	.nav{
	  width: 100%;
	  overflow: hidden;
	  position: fixed;
	  top: 0;
	  left: 0;
	  z-index: 10;
	  background-color: #ffffff;
	}
	.nav-title{
	  width: 100%;
	  height: 88rpx;
	  line-height: 88rpx;
	  text-align: center;
	  position: absolute;
	  bottom: 0;
	  left: 0;
	  z-index: 10;
	}
	.nav .home-logo{
	  width: 60rpx;
	  height: 60rpx;
	  position: absolute;
	  top: 50%;
	  transform: translateY(-50%);
	  left: 0rpx;
	  padding:0 24rpx;
	}
</style>
