page {
	background-color: #f3f3f3;
}

.content {
	&-showfn {
		padding-bottom: 0rpx;
		padding-bottom: calc(420rpx + constant(safe-area-inset-bottom));
		padding-bottom: calc(420rpx + env(safe-area-inset-bottom));
		/* #ifdef MP-WEIXIN */
		/* #endif */
	}
	&-box {
		width: 100%;
		height: calc(100vh - env(safe-area-inset-top) - 100rpx);
		box-sizing: content-box;
		position: relative;
		padding-bottom: 100rpx;

		/* #ifdef APP-PLUS  */
		margin-bottom: 0rpx;
		margin-bottom: constant(safe-area-inset-bottom);
		margin-bottom: env(safe-area-inset-bottom);
		/* #endif */
		/* #ifdef MP-WEIXIN */
		padding-bottom: 0rpx;
		padding-bottom: calc(100rpx + constant(safe-area-inset-bottom));
		padding-bottom: calc(100rpx + env(safe-area-inset-bottom));
		/* #endif */

		&-bg {
			width: 100%;
			position: fixed;
			/* #ifdef MP-WEIXIN */
			bottom: 0;
			bottom: constant(safe-area-inset-bottom);
			bottom: env(safe-area-inset-bottom);
			/* #endif */
			/* #ifndef MP-WEIXIN */
			top: 0;
			left: 0;
			/* #endif */
		}

		&-loading {
			text-align: center;
			padding: 20rpx 0;
		}

		.message {
			padding: 13rpx 20rpx;
			position: relative;
		}
		.left .content {
			padding: 20rpx;
			max-width: 450rpx;
			border-radius: 40rpx;
			border-top-left-radius: 10rpx;
			font-size: 28rpx;
		}
		.right .content {
			padding: 20rpx;
			max-width: 450rpx;
			border-radius: 40rpx;
			border-top-right-radius: 10rpx;
			font-size: 28rpx;
		}

		.message-item {
			display: flex;
			justify-content: flex-start;
			align-items: flex-start;
			align-content: flex-start;
			flex-wrap: nowrap;
			flex-direction: row;

			.img {
				width: 80rpx;
				height: 80rpx;
				border-radius: 50%;
				position: relative;
			}

			//图片
			.contentType3 {
				padding: 0;
				border-radius: 2rpx;
				background-color: transparent !important;
				.img {
					width: 200rpx;
					height: auto;
					max-width: 300rpx;
					max-height: 400rpx;
				}
			}
			.contentType3::after {
				border: none !important;
				display: none !important;
			}
			.content-type-right {
				flex-direction: row-reverse;
			}

			&.right {
				flex-direction: row-reverse;

				.content {
					background-color: #4cd964;
					margin-right: 28rpx;
					word-break: break-all;
					line-height: 36rpx;
					position: relative;
				}
			}

			&.left {
				.content {
					background-color: #ffffff;
					margin-left: 28rpx;
					word-break: break-all;
					line-height: 36rpx;
					position: relative;
				}
			}
		}
	}

	.input-box {
		position: fixed;
		bottom: 0;
		left: 0;
		width: 100%;
		box-sizing: content-box;
		z-index: 999;
		background-color: #ffffff;

		/* #ifdef APP-PLUS */
		margin-bottom: 0rpx;
		margin-bottom: constant(safe-area-inset-bottom);
		margin-bottom: env(safe-area-inset-bottom);
		/* #endif */
		/* #ifdef MP-WEIXIN */
		padding-bottom: 0rpx;
		padding-bottom: constant(safe-area-inset-bottom);
		padding-bottom: env(safe-area-inset-bottom);
		/* #endif */

		&-flex {
			min-height: 100rpx;
			display: flex;
			justify-content: flex-start;
			align-items: center;
			flex-wrap: nowrap;
			flex-direction: row;
			padding: 10rpx 20rpx;
			box-sizing: border-box;
			image {
				width: 56rpx;
				height: 56rpx;
			}
			.icon_img {
				margin-right: 20rpx;
			}
			.icon_btn_add {
				margin-left: 20rpx;
			}
			&-grow {
				flex-grow: 1;

				.content {
					box-sizing: border-box;
					background-color: #fff;
					height: 80rpx;
					padding: 0 20rpx;
					border-radius: 12rpx;
					font-size: 28rpx;
					caret-color: #4cd964;
				}

				.voice_title {
					text-align: center;
					background-color: #ffffff;
					height: 80rpx;
					line-height: 80rpx;
					border-radius: 12rpx;
				}
			}

			.btn {
				margin-left: 20rpx;
				background-color: #19be6b;
				border: none;
			}
		}
		.iconfont {
			color: #aeacad;
			line-height: 1;
			font-size: 40rpx;
		}
		textarea {
			width: auto;
		}
		.fun-box {
			opacity: 0;
			transition: all 0.1s ease-in-out;
			height: 0;
			.option-box {
				width: 25%;
				height: 150rpx;
				display: inline-block;
			}
			.option-content-box {
				display: flex;
				width: 100%;
				height: 100%;
				justify-content: center;
				align-items: center;
				flex-direction: column;
				.grid-text {
					color: #999;
				}
				.iconfont {
					font-size: 48rpx;
					color: #999;
					line-height: 1.3;
				}
			}
		}
		.show-fun-box {
			opacity: 1;
			height: 300rpx;
		}
	}

	.input-box-mpInputMargin {
		/* #ifdef MP-WEIXIN */
		padding-bottom: 0rpx;
		/* #endif */
	}
	.voice_an {
		width: 300rpx;
		position: fixed;
		top: 50%;
		left: 50%;
		transform: translate(-50%, -55%);
		background-color: rgba(41, 41, 41, 0.7);
		color: white;
		display: flex;
		flex-direction: column;
		align-items: center;
		text-align: center;
		border-radius: 10rpx;
		.text {
			padding-bottom: 15rpx;
			color: #fff;
		}
		@keyframes runVoice {
			0% {
				height: 10%;
			}
			20% {
				height: 50%;
			}
			50% {
				height: 100%;
			}
			80% {
				height: 50%;
			}
			100% {
				height: 0%;
			}
		}
		.wave {
			width: 6rpx;
			height: 100%;
			margin-left: 10rpx;
			border-radius: 50rpx;
			background-color: #999;
			vertical-align: middle;
			display: inline-block;
		}
		.voice_an_icon {
			width: 200rpx;
			height: 100rpx;
			line-height: 50rpx;
			margin: 50rpx 0;
		}
		.voice_an_icon #one {
			animation: runVoice 0.6s infinite 0.1s;
		}
		.voice_an_icon #two {
			animation: runVoice 0.6s infinite 0.3s;
		}
		.voice_an_icon #three {
			animation: runVoice 0.6s infinite 0.6s;
		}
		.voice_an_icon #four {
			animation: runVoice 0.6s infinite 0.1s;
		}
		.voice_an_icon #five {
			animation: runVoice 0.6s infinite 0.3s;
		}
		.voice_an_icon #six {
			animation: runVoice 0.6s infinite 0.6s;
		}
		.voice_an_icon #seven {
			animation: runVoice 0.6s infinite 0.1s;
		}
	}
	.iconmn_fasong_fill{
		line-height: 80rpx;
	}
}
