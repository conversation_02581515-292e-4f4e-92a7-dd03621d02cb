<template>
  <view class="model">
    <view class="model-bg" @click.stop="closeFun"></view>
    <view class="model-content" :class="transform ? 'transform':''">
      <view class="header">
        <view>{{title}}</view>
        <text class="iconfont iconclose icon" @click="closeFun"></text>
      </view>
      <view class="content">
        <slot name="content" />
      </view>
    </view>
  </view>
</template>

<script>
  export default {
    props: {
      title: {
        type: String,
        default: '标题'
      },
      show: {
        type: Boolean,
        default: false
      }
    },
    data() {
      return {
        transform: false
      }
    },
    created () {
      setTimeout(() => {
        this.transform = this.show
      }, 200)
    },
    methods: {
      closeFun() {
        this.transform = false
        setTimeout(() => {
          this.$emit('closeFun')
        }, 200)
      }
    },
    watch: {
      show(val) {
        this.transform = val
      }
    }
  }
</script>

<style lang="scss" scoped>
.model{
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  .model-bg{
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba($color: #000000, $alpha: 0.5);
  }
  .model-content{
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    -webkit-transform: translateY(100%);
    transform: translateY(100%);
    transition: all 0.3s;
    .header{
      background-color: #fff;
      padding: 54rpx 0 24rpx;
      text-align: center;
      line-height: 44rpx;
      border-radius: 20rpx 20rpx 0 0;
      &>view{
        font-size: 32rpx;
        font-weight: bold;
      }
      &>text{
        position: absolute;
        right: 20rpx;
        top: 20rpx;
        font-size: 36rpx;
        color: #ccc;
      }
    }
    .content{
      background-color: #fff;
      height: 710rpx;
      overflow-y: auto;
    }
  }
  .transform{
    transform: translateY(0);
  }
}
</style>