<!--新品专区模块-->
<template>
  <view class="new-product-area">
    <view  class="new-product-area-model" v-if="dataList.length">
      <view class="new-product-area-one" :style="{backgroundColor:config.backgroundColor,backgroundImage:`url(${config.backgroundImg})`}" v-if="config.selectedTemplate==1">
        <view class="new-product-area-one-title" :style="{color:config.fontColor}">
          <text class="new-product-area-one-title-text">{{config.title}}</text>
          <view class="new-product-area-one-title-more" :style="{color: config.fontColor}" @click="toList">{{config.moreText}}
            <uni-icons type="forward" size="24rpx" :color="config.fontColor" class="new-product-area-one-title-more-icon"></uni-icons>
          </view>
        </view>
        <view class="new-product-area-one-list">
          <view class="new-product-area-one-list-item" v-for="(item,index) in dataList" :key="index" @click="toProductDetail(item)">
            <view class="new-product-area-one-list-item-image">
              <image :src="$util.img(item.goods_image)" @error="imageError(index)" mode='aspectFit' class="new-product-area-one-list-item-image-img"></image>
              <image :src="$util.img('public/static/youpin/product-sell-out.png')" class="goods_img-over" v-if="item.goods_stock == 0"></image>
              <text class="new-product-area-one-list-item-image-serial" :style="{backgroundColor:serialColor[index]}">{{index+1}}</text>
            </view>
            <view class="new-product-area-one-list-item-info">
              <view class="new-product-area-one-list-item-info-name">
                <text class="tag-cross-border" v-if="item.cbec_origin_name">跨境
                  <image :src="$util.img(item.cbec_origin_icon)" mode="aspectFit" class="tag-cross-border--flag" v-if="item.cbec_origin_icon"></image>
                  <text class="tag-cross-border--full" v-else>{{item.cbec_origin_name[0]}}</text>
                </text>
                {{item.goods_name}}
              </view>
              <view class="new-product-area-one-list-item-info-price"><text class="new-product-area-one-list-item-info-price-symbol">￥</text>{{item.sale_price}}</view>
              <view class="new-product-area-one-list-item-info-original-price">￥{{item.market_price}}</view>
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>


<script>
export default {
  name: "diy-new-product-area",
  props:{
    config:{
      type:Object,
      default(){
        return {}
      }
    },
  },
  data(){
    return{
      serialColor:['rgba(245, 93, 93, 1)','rgba(251, 107, 13, 1)','rgba(255, 195, 0, 1)'],
      dataList: [],
    }
  },
  async created(){
    await this.getData()
  },
  methods:{
    async getData(){
      try{
        let res = await this.$api.sendRequest({
          url: this.$apiUrl.newProductAreaGoodsList,
          async: false,
          data: {
            page: 1,
            page_size: 3,
          },
        })
        if(res.code == 0){
          this.dataList = res.data.list
          this.$buriedPoint.exposeGoods(res.data.list, 'sku_id')
        }
      }catch (e) {

      }
    },
    async toList(){
      this.$util.diyCompateRedirectTo({
        wap_url: `/promotionpages/new_product_area/list/list`
      })
    },
    toProductDetail(item){
      this.$util.toProductDetail(item,(wap_url)=>{
        this.$buriedPoint.diyReportDecorationComponentInteractionEvent({diy_template_name:'newProductArea',diy_text:item.goods_name,diy_link:wap_url,diy_image:this.$util.img(item.sku_image)})
      })
    },
    imageError(index) {
      this.dataList[index].goods_image = this.$util.getDefaultImage().default_goods_img;
      this.$forceUpdate();
    },
  }
}
</script>

<style scoped lang="scss">
.new-product-area{
  width: 100%;
  &-model{
    margin-top: 20rpx;
  }
  &-one{
    width: 100%;
    height: 506rpx;
    overflow: hidden;
    background-repeat: no-repeat;
    background-size: 100% 100%;
    border-radius: 20rpx;
    padding: 24rpx 20rpx 0 20rpx;
    box-sizing: border-box;
    &-title{
      display: flex;
      justify-content: space-between;
      align-items: center;
      &-text{
        font-size: 36rpx;
        font-weight: 700;
        line-height: 42.2rpx;
      }
      &-more {
        font-size: $ns-font-size-xm;
        display: flex;
        align-items: center;
        &-icon{
          border: 1px solid;
          border-radius: 50%;
          height: 26rpx;
          width: 26rpx;
          display: flex;
          align-items: center;
          justify-content: center;
          margin-left: 6rpx;
        }
      }
    }
    &-list{
      margin-top: 20rpx;
      display: flex;
      &-item{
        width: 212rpx;
        border-radius: 20rpx;
        background-color: white;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: flex-start;
        padding: 0 0 12rpx 0;
        box-sizing: border-box;
        &:not(:last-child){
          margin-right: 20rpx;
        }
        &-image{
          width: 212rpx;
          height: 212rpx;
          border-radius: 20rpx;
          position: relative;
          &-img{
            width: 212rpx;
            height: 212rpx;
            border-radius: 20rpx;
            display: block;
          }
          .goods_img-over {
            width: 100rpx;
            height: 100rpx;
            position: absolute;
            left: 50%;
            top: 50%;
            transform: translate(-50%,-50%);
          }
          &-serial{
            width: 42rpx;
            height: 32rpx;
            border-radius: 20rpx 0 20rpx 0;
            position: absolute;
            left: 0;
            top: 0;
            font-size: 24rpx;
            font-weight: 700;
            line-height: 24rpx;
            color: rgba(255, 255, 255, 1);
            display: flex;
            justify-content: center;
            align-items: center;
          }
        }
        &-info{
          width: 196rpx;
          margin-top: 16rpx;
          padding-bottom: 10rpx;
          box-sizing: border-box;
          &-name{
            font-size: 28rpx;
            font-weight: 400;
            line-height: 32.6rpx;
            color: rgba(56, 56, 56, 1);
            word-break: break-all;
            text-overflow: ellipsis; //显示为省略号
            display: -webkit-box; //对象作为伸缩盒子模型显示
            -webkit-box-orient: vertical; //设置或检索伸缩盒对象的子元素的排列方式
            -webkit-line-clamp: 2; //显示行数## 标题文字 ##
            overflow: hidden;
            min-height: 64rpx;
          }
          &-price{
            margin-top: 10rpx;
            display: flex;
            font-size: 36rpx;
            font-weight: 700;
            line-height: 36rpx;
            color: var(--custom-brand-color);
            &-symbol{
              align-self: flex-end;
              font-size: 24rpx;
              font-weight: 700;
              line-height: 44rpx;
            }
          }
          &-original-price{
            margin-top: 10rpx;
            font-size: 20rpx;
            font-weight: 400;
            line-height: 20rpx;
            text-decoration-line: line-through;
            color: rgba(166, 166, 166, 1);
          }
        }
      }
    }
  }
}
</style>
