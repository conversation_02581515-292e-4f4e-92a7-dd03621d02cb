<template>
	<view class="channel" v-if="channels.length>0">
		<view v-if="scrollSetting == 'horizontal-scroll'">
			<swiper class="channel--list" @change="changeChannel" :style="{height:channels.length<=5?'160rpx':'350rpx'}">
				<swiper-item v-for="(item,index) in list" v-bind:key="index" class="channel--list--item" >
					<view class="channel--list--item--channel" v-for="(channel,j) in item" v-bind:key="j" @click="todetail(channel)">
						<image :src="$util.img(channel.imageUrl)"></image>
						<view>{{channel.title}}</view>
					</view>
				</swiper-item>
			</swiper>
			<view class="channel--dot" v-if="list.length > 1">
				<text v-bind:class="{'channel--dot--active':index==channelsIndex}"
					class="channel--dot--index" v-for="(item,index) in list"
					v-bind:key="index"></text>
			</view>
		</view>
		<view class="channel--list" style="height: auto;" v-else>
			<view v-for="(item,index) in list" v-bind:key="index" class="channel--list--item" >
				<view class="channel--list--item--channel" v-for="(channel,j) in item" v-bind:key="j" @click="todetail(channel)">
					<image :src="$util.img(channel.imageUrl)"></image>
					<view>{{channel.title}}</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		props:{
			channels:{
				type:Array,
				default:[]
			},
			scrollSetting:{
				type:String,
				default:'horizontal-scroll'
			}
		},
		data() {
			return {
				channelsIndex:0,
				list:[]
			}
		},
		created() {
			if(this.channels.length <= 10){
				this.list = [this.channels]
			}else {
				let arr = [[],[]]
				this.channels.map((item,index)=>{
					if(index<10){
						arr[0].push(item)
					}else {
						arr[1].push(item)
					}
				})
				this.list = arr
			}
		},
		methods: {
			changeChannel(e) {
				let index = e.detail.current;
				this.channelsIndex = index;
			},
			// 跳转
			todetail(data){
        this.$buriedPoint.diyReportDecorationComponentInteractionEvent({diy_template_name:'GraphicNav',diy_text:data.title,diy_link:data.link.url,diy_image:data.imageUrl})
				if(data.link.url){
					this.$util.diyCompateRedirectTo({
						wap_url: data.link.url
					})
				}else {
					uni.showToast({
						title: '网络故障，请联系客服处理',
						icon: 'none'
					})
				}
			},
		}
	}
</script>

<style lang="scss">
	.channel {
		margin-top: 35rpx;

		&--list {
			height: 350rpx;

			&--item {
				display: flex;
				flex-wrap: wrap;
				justify-content: flex-start;
				align-items: flex-start;

				&--channel {
					width: 138rpx;
					display: flex;
					flex-direction: column;
					align-items: center;

					image {
						width: 96rpx;
						height: 96rpx;
						border-radius: 50%;
					}

					view {
						font-size: 24rpx;
						font-weight: 500;
						color: #9A9A9A;
						margin-top: 20rpx;
					}

					;
				}
			}
		}

		&--dot {
			display: flex;
			justify-content: center;
			align-items: center;
			margin-top: 15rpx;

			&--index {
				width: 43rpx;
				height: 6rpx;
				background: #D4D8DE;
				border-radius: 3rpx;
			}

			&--active {
				background: #F2280C;
			}
		}
	}
</style>
