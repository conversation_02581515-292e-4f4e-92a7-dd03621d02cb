<template>
  <view v-if="dataList.type == 0 && dataList.action_time > 0 && showList.length >0">
    <template v-if="value.item.selectedTemplate == 1">
      <view class="diy-seckill" v-if="showList" :style="{backgroundColor: value.item.backgroundColor,backgroundImage: `url(${value.item.backgroundImg})`}">
        <view class="diy-seckill-top">
          <view class="seckill-title">
            <image :src="$util.img('public/static/youpin/pintuan/seckill-icon.png')" class="seckill-title-hot"></image>
            <text class="seckill-title-name" :style="{color: value.item.textColor}">{{name}}</text>
            <view class="last-time flex-start-end">
              <!--					<view class="ns-margin-right">{{dataList.type == 0 ? '距离结束' : '距离开始'}}</view>-->
              <view class="clockrun">
                <countdown-timer ref="countdown" :time="dataList.action_time" @finish="onFinish" autoStart></countdown-timer>
              </view>
            </view>
            <!-- <text class="seckill-titie-tit">{{dataList.seckill_name ? dataList.seckill_name: '-'}} - {{dataList.type == 1 ? '未开始' : '已开始'}}</text> -->
          </view>
          <view class="seckill-more" :style="{color: value.item.textColor}" @click="toMore(dataList.seckill_id)">{{moreText}}
            <uni-icons type="forward" size="24rpx" :color="value.item.textColor" class="seckill-more-icon"></uni-icons>
          </view>
        </view>
        <scroll-view class="diy-seckill-box" scroll-x="true">
          <view class="seckill-box-item" v-for="(item, i) in showList" :key="i" @click="toDetail(item)">
            <view class="seckill-item">
              <view class="seckill-item-image">
                <image :src="$util.img(item.goods_image)" @error="imageError(showList,i)" mode='aspectFit'></image>
                <image :src="$util.img('public/static/youpin/product-sell-out.png')" class="goods_img-over" v-if="item.goods_stock == 0"></image>
              </view>
              <view class="seckill-item-info">
                <view class="seckill-item-new-name">{{item.goods_name }}</view>
                <text class="seckill-item-info-tip">秒杀价</text>
                <view class="seckill-item-info-bottom">
                  <view class="seckill-item-new-price">
                    <text>￥</text>
                    <text class="seckill-item-new-price-integer">{{ item.seckill_price|getInteger }}</text>
                    <text class="seckill-item-new-price-Decimals">{{ item.seckill_price|getDecimals }}</text>
                  </view>
                  <image :src="$util.img('public/static/youpin/rob.png')" class="seckill-item-info-bottom-img"></image>
                </view>
              </view>
            </view>
          </view>
        </scroll-view>
      </view>
    </template>
    <template v-if="value.item.selectedTemplate == 2">
      <view class="seckill-two">
        <view class="seckill-two-item" v-for="(item, i) in showList" :key="i" @click="toDetail(item)">
          <view class="seckill-two-item-image">
            <image :src="$util.img(item.goods_image)" @error="imageError(showList,i)" mode='widthFix' class="seckill-two-item-image-goods"></image>
            <image :src="$util.img('public/static/youpin/product-sell-out.png')" class="goods_img-over" v-if="item.goods_stock == 0"></image>
            <view class="seckill-two-item-image-area">
              <view class="seckill-two-item-image-area-left">立省￥{{item.thrift_money}}</view>
              <view class="seckill-two-item-image-area-clockrun">
                <text class="seckill-two-item-image-area-clockrun-tip">距结束</text>
                <countdown-timer ref="countdown" :show-day-symbol="true" :time="dataList.action_time" @finish="onFinish" autoStart></countdown-timer>
              </view>
            </view>
          </view>
          <view class="seckill-two-item-info">
            <view class="seckill-two-item-info-name">{{item.goods_name }}</view>
            <view class="seckill-two-item-info-subname">{{item.introduction }}</view>
            <view class="seckill-two-item-info-price">
              <view class="seckill-two-item-info-price-left">
                <view class="seckill-two-item-info-price-left-sell">
                  <text class="seckill-two-item-info-price-left-sell-symbol">￥</text>
                  <text class="seckill-two-item-info-price-left-sell-price">{{ item.seckill_price }}</text>
                </view>
                <view class="seckill-two-item-info-price-left-original">
                  <text class="seckill-two-item-info-price-left-original-price">原价￥{{ item.market_price }}</text>
                </view>
              </view>
              <view class="seckill-two-item-info-price-right">
                <text class="seckill-two-item-info-price-right-thrift">抢购</text>
              </view>
            </view>
<!--            <view class="seckill-two-item-info-keep">-->
<!--              <view class="seckill-two-item-info-keep-clockrun">-->
<!--                <text class="seckill-two-item-info-keep-clockrun-tip">距离结束</text>-->
<!--                <countdown-timer ref="countdown" :show-day-symbol="true" :time="dataList.action_time" @finish="onFinish" autoStart></countdown-timer>-->
<!--              </view>-->
<!--              <view class="seckill-two-item-info-keep-right" :style="{backgroundImage:'url('+robBg+')'}">-->
<!--                <image :src="$util.img('public/static/youpin/rob-two.png')" class="seckill-two-item-info-keep-right-rob"></image>-->
<!--              </view>-->
<!--            </view>-->
          </view>
        </view>
        <view class="seckill-two-more" :style="{color: value.item.textColor}" @click="toMore(dataList.seckill_id)">
          {{moreText}} <uni-icons type="forward" size="24rpx" :color="value.item.textColor"></uni-icons>
        </view>
      </view>
    </template>
  </view>

</template>

<script>
	import countdownTimer from '@/components/countdown-timer/countdown-timer.vue';
  import UniIcons from "@/components/uni-icons/uni-icons.vue";
  import Config from "@/common/js/config";
	export default {
		components: {
      UniIcons,
			countdownTimer
		},
		name: 'diy-seckill',
		props: {
			value: {
				type: Object
			},
			dataList:{
				type: Object,
				default: () => {
					return {};
				}
			},
		},
    filters:{
      getInteger(value){
        return String(parseFloat(value)).split('.')[0]
      },
      getDecimals(value){
        return '.'+String(parseFloat(value).toFixed(2)).split('.')[1]
      }
    },
		data() {
			return {
				name: '限时秒杀',
				moreText: '查看更多',
        robBg:'',
        showList:[],
			};
		},
    watch:{
      dataList(val){
        if(val && val.list && val.list.length > 0) {
          let goods_list = val.list.map(item=>{
            item.goods_image = this.$util.imageCdnResize(item.goods_image,{image_process:'resize,w_700','x-oss-process':'image/resize,w_700'})
            return item;
          })
          this.showList = this.value.item.selectedTemplate == 2 ? goods_list.slice(0,this.value.item.displayGoodsCount) : goods_list
        }
      }
    },
		async created() {
      if (this.value.item) {
				let it = this.value.item
				it.name && (this.name = it.name)
				it.moreText && (this.moreText = it.moreText)
			}
      let color = this.$util.colorToHex(this.$store.state.themeColorVar['--custom-brand-color']).slice(1);
      // this.robBg = encodeURI(this.$util.img( `seckill/api/seckill/getRobBg?color=${color}`))
      this.robBg = encodeURI(this.$util.img( `api/website/svgChangeFillColor?svg_name=rob-bg&color=${color}`))
      if(this.dataList.list){
        let goods_list = this.dataList.list.map(item=>{
          item.goods_image = this.$util.imageCdnResize(item.goods_image,{image_process:'resize,w_700','x-oss-process':'image/resize,w_700'})
          return item;
        })
        this.showList = this.value.item.selectedTemplate == 2 ? goods_list.slice(0,this.value.item.displayGoodsCount) : goods_list
      }
		},
		methods: {
			toMore(id) {
        this.$buriedPoint.diyReportDecorationComponentInteractionEvent({diy_template_name:'Seckill',diy_text:this.moreText,diy_link:`/promotionpages/new_seckill/list/list?seckill_id=${id}`,diy_image:''})
				this.$util.diyCompateRedirectTo({
					wap_url: `/promotionpages/new_seckill/list/list?seckill_id=${id}`
				});
			},
			toDetail(res) {
				if(this.dataList.type == 0 ){
          this.$buriedPoint.diyReportDecorationComponentInteractionEvent({diy_template_name:'Seckill',diy_text:res.goods_name,diy_link:`/promotionpages/new_seckill/detail/detail?sku_id=${res.sku_id}`,diy_image:this.$util.img(res.sku_image)})
					this.$util.diyCompateRedirectTo({
						wap_url: `/promotionpages/new_seckill/detail/detail?sku_id=${res.sku_id}`
					});
				}
				if(this.dataList.type == 1 ){
          this.$buriedPoint.diyReportDecorationComponentInteractionEvent({diy_template_name:'Seckill',diy_text:res.goods_name,diy_link:`/pages/goods/detail/detail?sku_id=${res.sku_id}`,diy_image:this.$util.img(res.sku_image)})
					this.$util.diyCompateRedirectTo({
						wap_url: `/pages/goods/detail/detail?sku_id=${res.sku_id}`
					});
				}
			},
			imageError(dataList,index) {
        dataList[index].goods_image = this.$util.getDefaultImage().default_goods_img;
				this.$forceUpdate();
			},
			onFinish(){
				this.$emit('finish')
			},
		}
	};
</script>

<style lang="scss" scoped>
	.diy-seckill {
		margin-top:20rpx;
		border-radius: 20rpx;
		width: 100%;
    padding: $ns-padding 0 $ns-padding $ns-padding;
		box-sizing: border-box;
    background-size: 100% 100%;
    background-repeat: no-repeat;
	}

	.diy-seckill-top {
		width: 100%;
		display: flex;
		justify-content: space-between;
		align-items: center;
    padding-right: 20rpx;
    box-sizing: border-box;

		.seckill-title {
			display: flex;
			align-items: center;
      &-hot{
        width: 33rpx;
        height: 33rpx;
        margin-right: 14rpx;
      }

			.seckill-title-name {
        font-size: 36rpx;
        font-weight: bolder;
				margin-right: $ns-margin;
			}

			/* .seckill-titie-tit{
				border-radius: 50rpx;
				background-color:$base-color;
				color:#ffffff;
				font-size: 22rpx;
				padding: 0 15rpx;
				line-height: 1.6;
				font-size: $ns-font-size-sm;
			} */

		}

		.seckill-more {
      font-size: $ns-font-size-xm;
      display: flex;
      align-items: center;
      &-icon{
        border: 1px solid;
        border-radius: 50%;
        height: 26rpx;
        width: 26rpx;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-left: 6rpx;
      }
		}
	}

	.diy-seckill-box {
		width: 100%;
		/*white-space 不能丢  */
		white-space: nowrap;
		box-sizing: border-box;
		margin-top: 30rpx;
	}

	.seckill-box-item {
		width: 188rpx;
		height: 100%;
		vertical-align: top;
		display: inline-block;
		background: #ffffff;
    border-radius: 20rpx;
    margin-right: $ns-margin;
    box-sizing: border-box;

		.seckill-item {
			width: 100%;
			height: 100%;
		}

		.seckill-item-image {
			width: 100%;
			height: 188rpx;
      border-radius: 20rpx 20rpx 0 0;
			overflow:hidden;
			display: flex;
			justify-content: center;
			align-items: center;
			position: relative;
			image {
				padding: 0;
				margin: 0;
			}
			.goods_img-over {
       			width: 100rpx;
       			height: 100rpx;
       			position: absolute;
       			left: 50%;
       			top: 50%;
       			transform: translate(-50%,-50%);
			}
		}
    .seckill-item-info{
      padding: 0 8rpx;
      box-sizing: border-box;
      &-tip{
        width: 60rpx;
        height: 20rpx;
        border-radius: 4rpx;
        background: var(--custom-brand-color-10);
        font-size: 16rpx;
        font-weight: 400;
        color: var(--custom-brand-color);
        display: inline-flex;
        justify-content: center;
        align-items: center;
      }
      &-bottom{
        display: flex;
        justify-content: space-between;
        padding-bottom: 10rpx;
        box-sizing: border-box;
        &-img{
          width: 58rpx;
          height: 44rpx;
          background: linear-gradient(90deg, var(--custom-brand-color-60) 0%, var(--custom-brand-color) 100%);
          border-radius: 100rpx;
        }
      }
    }
		.seckill-item-new-name{
			white-space:normal;
      margin-top: 12rpx;
      box-sizing: border-box;
      height: 66rpx;
      line-height: 1.3;
			font-size: $ns-font-size-xm;
      color: rgba(56, 56, 56, 1);
      font-weight: 400;
			word-break:break-all;
			text-overflow:ellipsis;//显示为省略号
			display:-webkit-box;//对象作为伸缩盒子模型显示
			-webkit-box-orient:vertical;//设置或检索伸缩盒对象的子元素的排列方式
			-webkit-line-clamp:2;//显示行数## 标题文字 ##
			overflow:hidden;
		}

		.seckill-item-new-price {
      font-size: 24rpx;
      font-weight: 700;
      line-height: 30rpx;
      color: var(--custom-brand-color);
      align-self: flex-end;
			text:first-child{
				font-size:$ns-font-size-sm;
			}
      &-integer{
        font-size: 36rpx;
      }
		}

		.seckill-item-old-price {
			font-size: $ns-font-size-sm;
			color: $ns-text-color-gray;
			text-decoration: line-through;
			line-height: 1;
		}
	}
  .ns-margin-right{
    font-size: 22rpx;
    font-weight: 500;
    color: #333333;
	padding-bottom: 5rpx;
  }
  /* #ifdef H5 */
	.last-time{
	  padding:4rpx 0;
	  font-size:20rpx !important;
	  .clockrun {
	    /deep/ .custom {
	      display: flex;
	    }
	    /deep/ .custom :nth-child(odd) {
	      background-color: var(--custom-brand-color);
	      width:40rpx;
	      height:40rpx;
	      line-height:40rpx;
	      color: white;
	      border-radius: 6upx;
	      font-size:22rpx;
	      text-align: center;
	      overflow:hidden;
	    }
	    /deep/ .custom :nth-child(even) {
	      padding: 0 6rpx;
	      color: var(--custom-brand-color);
	    }
	  }


	}
  /* #endif */


  .seckill-two{
    margin-top: 20rpx;
    &-item{
      border-radius: 20rpx;
      background: rgba(255, 255, 255, 1);
      box-sizing: border-box;
      &:not(:first-child){
        margin-top: 20rpx;
      }
      &-image{
        position: relative;
        &-goods{
          width: 100%;
          //height: 600rpx;
          border-radius: 20rpx 20rpx 0 0;
          display: block;
        }
        .goods_img-over {
          width: 300rpx;
          height: 300rpx;
          position: absolute;
          left: 50%;
          top: 50%;
          transform: translate(-50%,-50%);
        }
        &-area{
          position: absolute;
          left: 0;
          top: 0;
          display: flex;
          align-items: center;
          &-left{
            height: 50rpx;
            line-height: 50rpx;
            border-radius: 20rpx 0 0 0;
            background: var(--custom-brand-color);
            padding: 0 18rpx;
            box-sizing: border-box;
            font-size: 26rpx;
            font-weight: 400;
            color: rgba(255, 255, 255, 1);
          }
          &-clockrun{
            display: flex;
            align-items: center;
            padding: 0 24rpx 0 18rpx;
            box-sizing: border-box;
            height: 50rpx;
            line-height: 50rpx;
            border-radius: 0 0 20rpx 0;
            background: rgba(0, 0, 0, 0.5);
            &-tip{
              font-size: 26rpx;
              font-weight: 400;
              color: rgba(255, 255, 255, 1);
              margin-right: 10rpx;
            }
          }
        }
      }
      &-info{
        padding: 32rpx 14rpx;
        box-sizing: border-box;
        &-name{
          font-size: 32rpx;
          font-weight: 700;
          color: rgba(56, 56, 56, 1);
          text-overflow: ellipsis;
          overflow: hidden;
          word-break: break-all;
          white-space: nowrap;
        }
        &-subname{
          font-size: 28rpx;
          font-weight: 400;
          color: rgba(166, 166, 166, 1);
        }
        &-price{
          display: flex;
          justify-content: space-between;
          align-items: baseline;
          &-left{
            display: flex;
            align-items: baseline;
            &-sell{
              &-symbol{
                font-size: 24rpx;
                color: var(--custom-brand-color);
              }
              &-price{
                font-size: 40rpx;
                font-weight: 700;
                color: var(--custom-brand-color);
              }
            }
            &-original{
              &-price{
                font-size: 26rpx;
                font-weight: 400;
                text-decoration-line: line-through;
                color: rgba(166, 166, 166, 1);
                margin-left: 16rpx;
              }
            }
          }
          &-right{
            display: flex;
            align-items: center;
            &-thrift{
              width: 154rpx;
              height: 60rpx;
              line-height: 60rpx;
              border-radius: 100rpx;
              background: var(--custom-brand-color);
              font-size: 32rpx;
              font-weight: 400;
              color: rgba(255, 255, 255, 1);
              display: flex;
              justify-content: center;
              align-items: center;
            }
          }
        }
        &-keep{
          width: 100%;
          height: 80rpx;
          border-radius: 20rpx;
          background: var(--custom-brand-color-10);
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding-left: 16rpx;
          box-sizing: border-box;
          &-clockrun{
            display: flex;
            align-items: center;
            &-tip{
              font-size: 32rpx;
              font-weight: 400;
              color: var(--custom-brand-color);
              margin-right: 12rpx;
            }
          }
          &-right{
            width: 200rpx;
            height:80rpx;
            background-repeat: no-repeat;
            background-size: 100% 100%;
            display: flex;
            justify-content: center;
            align-items: center;
            &-rob{
              width: 58rpx;
              height: 64rpx;
              margin-left: 30rpx;
            }
          }
        }
      }
    }
    &-more{
      width: 100%;
      height: 72rpx;
      border-radius: 20rpx;
      background: rgba(255, 255, 255, 1);
      display: flex;
      justify-content: center;
      align-items: center;
      margin-top: 20rpx;
    }
  }
  /* #ifdef H5 */
  .seckill-two-item-image-area-clockrun {
    /deep/ .custom {
      display: flex;
    }
    /deep/.day {
      font-size: 26rpx;
      font-weight: 400;
      color: #fff;
    }

    /deep/.day-symbol {
      font-size: 26rpx;
      font-weight: 400;
      color: #fff;
      margin: 0 6rpx;
    }

    /deep/.hour, /deep/.minute, /deep/.second {
      font-size: 26rpx;
      font-weight: 400;
      color: #fff;
      display: flex;
      justify-content: center;
      align-items: center;
    }

    /deep/.hour-symbol, /deep/.minute-symbol, /deep/.second-symbol {
      font-size: 26rpx;
      font-weight: 400;
      color: #fff;
      margin: 0 6rpx;
    }
  }
  /* #endif */

</style>
