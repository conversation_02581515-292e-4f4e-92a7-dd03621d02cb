<template>
	<view class="search_product" :style="[objStyle]">

		<view v-if="!handleName" class="search-box" :style="[inputStyle]" @click="tolink()">
<!--			<image :src="$util.img('public/static/youpin/home-search.png')"></image>-->
      <text class="iconfont iconsousuo" :style="{color: searchObj.fontColor || 'transparent'}"></text>
			<view class="txt" :style="{color: searchObj.fontColor || 'transparent'}">{{tipName}}</view>
		</view>
		<view v-else class="search-box" :style="[inputStyle]">
			 <text class="iconfont iconsousuo" :style="{color: searchObj.fontColor||'transparent'}"></text>
<!--			<image :src="$util.img('public/static/youpin/home-search.png')"></image>-->
			<input :style="{width:'540rpx'}" maxlength="50" v-model="inputValue" confirm-type="search"
				@confirm="searchBut()" :placeholder="tipName" :placeholder-style="`color:${searchObj.fontColor||'transparent'}`" />
		</view>

		<view v-if="handleName" class="text" @click="searchBut()">{{handleName}}</view>
	</view>
</template>

<script>
	export default {
		name: 'diy-search',
		props: {
			color: {
				type: String,
				default: '#999'
			},
			sbgc: {
				type: String,
				default: ''
			},
			searchObj: {
				type: Object
			},
			handleName: {
				type: String,
				default: ''
			},
		},
		data() {
			return {
				tipName: '搜索你喜欢的商品',
				objStyle: {},
				inputStyle: {},
				inputValue: '',
			}
		},
		watch: {
			searchObj: {
				handler(val) {
					if(val){
						this.tipName = val.placeholder
					}
				},
				immediate: true,
				deep: true
			},
			sbgc: {
				handler(val) {
					if(val){
            let height = 32
            // #ifdef MP-WEIXIN
            // 获取微信胶囊的位置信息 width,height,top,right,left,bottom
            const custom = uni.getMenuButtonBoundingClientRect()
            height = custom.height
            // #endif
						this.inputStyle.height = `${height}px`
            if(this.searchObj.imageUrl){
              this.inputStyle['background-image'] = 'url(' + this.searchObj.imageUrl + ')'
            }
            if(this.searchObj.backgroundColor){
              this.inputStyle['background-color'] =  this.searchObj.backgroundColor
            }
					}
				},
				immediate: true,
				deep: true
			}
		},
    // 组件所在页面显示调用的方法(只是微信小程序生效)
    onPageShow(){
      console.log('onPageShow')
      this.changeBgImage()
    },
    // 页面keep-alive重新显示，调用（h5生效）
    activated(){
      this.changeBgImage()
    },
		methods: {
      iconAddParams(icon_url){
        let queryObject = this.$util.GetRequestQuery(icon_url);
        queryObject['ct'] = (new Date()).getTime()
        let base_url  = icon_url.split('?')[0]
        return base_url + '?' + Object.keys(queryObject).map((item) => item + '=' + queryObject[item]).join('&')
      },
      changeBgImage(){
        if(this.searchObj.imageUrl){
          let tmp = JSON.parse(JSON.stringify(this.inputStyle))
          tmp['background-image'] = 'url(' + this.iconAddParams(this.searchObj.imageUrl) + ')'
          this.inputStyle = tmp
          console.log(this.inputStyle)
        }
      },
			//
			inputInput(e) {
				console.log('inputInput', e)
			},
			// 操作按钮
			searchBut() {
        this.$buriedPoint.diyReportDecorationComponentInteractionEvent({diy_template_name:'Search',diy_text:'',diy_link:'',diy_image:''})
				if (this.handleName == '搜索') {
					this.$emit('searchProduct', this.inputValue)
				} else {
					uni.navigateBack({
						delta: 1
					});
				}
			},
			// 搜索
			tolink() {
        this.$buriedPoint.diyReportDecorationComponentInteractionEvent({diy_template_name:'Search',diy_text:'',diy_link:this.searchObj.link || '/otherpages/goods/search/search',diy_image:''})
				if(this.searchObj.isJump == 1){
					this.$util.redirectTo(this.searchObj.link || '/otherpages/goods/search/search')
				}
			},

		}
	}
</script>

<style lang="scss">
	.search_product {
		width: 100%;
		//padding: 20rpx 20rpx 0 20rpx;
		box-sizing: border-box;
		display: flex;
		align-items: center;
		input {
			font-size: $ns-font-size-base;
			height: 60rpx;
			line-height: 60rpx;
			width: calc(100% - 120rpx);
		}

		text {
			font-size: 40rpx;
			color: $ns-text-color-gray;
			width: 64rpx;
			text-align: center;

		}

		.text {
			color: #333;
			font-size: 28rpx;
			width: 115rpx;
			text-align: center;
		}

		.search-box {
      width: 100%;
			//height: 64rpx;
			//background: $uni-bg-color-grey;
			display: flex;
			align-items: center;
			border-radius: 40rpx;
      padding: 0 24rpx;
      background-repeat: no-repeat;
      background-size: 100% 100%;

			.txt {
				color: $ns-text-color-gray;
        line-height: 1.5;
			}

			image {
				width: 28rpx;
				height: 28rpx;
				margin: 0rpx 10rpx 0rpx 0rpx;
			}
		}
	}
</style>
