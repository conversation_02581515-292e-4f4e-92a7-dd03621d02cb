<!--商品专题模块-->
<template>
  <view class="product-topic" :class="[productTopicIndex]">
    <view  class="product-topic-model" v-if="(config.selectedTemplate != 4 && config.selectedTemplate != 5) && (dataList.length || originalList.length)">
      <view class="product-topic-one" :style="{backgroundColor:config.backgroundColor,backgroundImage:`url(${config.backgroundImg})`}" v-if="config.selectedTemplate==1">
        <view class="product-topic-one-title" :style="{color:config.fontColor}">
          <text class="product-topic-one-title-text">{{config.title}}</text>
          <view class="product-topic-one-title-more" :style="{color: config.fontColor}" @click="toTopicList(null)">{{config.moreText}}
            <uni-icons type="forward" size="24rpx" :color="config.fontColor" class="product-topic-one-title-more-icon"></uni-icons>
          </view>
        </view>
        <view class="product-topic-one-list">
          <view class="product-topic-one-list-item" v-for="(item,index) in dataList" :key="index" @click="toProductDetail(item)">
            <view class="product-topic-one-list-item-image">
              <image :src="$util.img(item.sku_image)" @error="imageError(i)" mode='aspectFit' class="product-topic-one-list-item-image-img"></image>
              <image :src="$util.img('public/static/youpin/product-sell-out.png')" class="goods_img-over" v-if="item.goods_stock == 0"></image>
              <text class="product-topic-one-list-item-image-serial" :style="{backgroundColor:serialColor[index]}">{{index+1}}</text>
            </view>
            <view class="product-topic-one-list-item-info">
              <view class="product-topic-one-list-item-info-name">
                <text class="tag-cross-border" v-if="item.cbec_origin_name">跨境
                  <image :src="$util.img(item.cbec_origin_icon)" mode="aspectFit" class="tag-cross-border--flag" v-if="item.cbec_origin_icon"></image>
                  <text class="tag-cross-border--full" v-else>{{item.cbec_origin_name[0]}}</text>
                </text>
                {{item.goods_name}}
              </view>
              <view class="product-topic-one-list-item-info-price"><text class="product-topic-one-list-item-info-price-symbol">￥</text>{{item.sale_price}}</view>
              <view class="product-topic-one-list-item-info-original-price">￥{{item.market_price}}</view>
            </view>
          </view>
        </view>
      </view>
      <view class="product-topic-two" :style="{backgroundColor:config.backgroundColor,backgroundImage:`url(${config.backgroundImg})`}" v-if="config.selectedTemplate==2">
        <view class="product-topic-two-title" :style="{color:config.fontColor}">
          <text class="product-topic-two-title-text">{{config.title}}</text>
          <view class="product-topic-two-title-more" :style="{color: config.fontColor}" @click="toTopicList(null)">{{config.moreText}}
            <uni-icons type="forward" size="24rpx" :color="config.fontColor" class="product-topic-two-title-more-icon"></uni-icons>
          </view>
        </view>
        <scroll-view class="product-topic-two-list" scroll-x>
          <view class="product-topic-two-list-item" v-for="(item,index) in dataList" :key="index" @click="toProductDetail(item)">
            <view class="product-topic-two-list-item-image">
              <image :src="$util.img(item.sku_image)" @error="imageError(i)" mode='aspectFit' class="product-topic-two-list-item-image-img"></image>
              <image :src="$util.img('public/static/youpin/product-sell-out.png')" class="goods_img-over" v-if="item.goods_stock == 0"></image>
            </view>
            <view class="product-topic-two-list-item-info">
              <view class="product-topic-two-list-item-info-name">{{item.goods_name}}</view>
              <view class="product-topic-two-list-item-info-price"><text class="product-topic-two-list-item-info-price-symbol">￥</text>{{item.sale_price}}</view>
              <view class="product-topic-two-list-item-info-original-price">￥{{item.market_price}}</view>
            </view>
          </view>
        </scroll-view>
      </view>
      <view class="product-topic-three" v-if="config.selectedTemplate==3">
        <view class="product-topic-three-list">
          <view class="product-topic-three-list-item" v-for="(item,index) in originalList.slice(0+showPage*9,9+showPage*9)" :key="index" @click="toProductDetail(item)">
            <view class="product-topic-three-list-item-image">
              <image :src="$util.img(item.sku_image)" @error="imageError(i)" mode='aspectFit' class="product-topic-three-list-item-image-img"></image>
              <image :src="$util.img('public/static/youpin/product-sell-out.png')" class="goods_img-over" v-if="item.goods_stock == 0"></image>
            </view>
            <view class="product-topic-three-list-item-info">
              <view class="product-topic-three-list-item-info-name">{{item.goods_name}}</view>
              <view class="product-topic-three-list-item-info-price"><text class="product-topic-three-list-item-info-price-symbol">￥</text>{{item.sale_price}}</view>
              <view class="product-topic-three-list-item-info-original-price">￥{{item.market_price}}</view>
            </view>
          </view>
        </view>
        <view class="product-topic-three-op" @click="changeData">
          <uni-icons type="loop"></uni-icons>
          <text class="product-topic-three-op-text">换一批</text>
        </view>
      </view>
    </view>
    <view class="product-topic-four" v-if="(config.selectedTemplate==4 || config.selectedTemplate==5 ) && tab_list.length">
      <scroll-view class="product-topic-four-tab" v-if="is_scroll_to_top"
                   :style="{top: showTop+'px'}" :scroll-x="true" :scroll-with-animation="true" :scroll-into-view="scroll_into_id">
        <block v-for="(tab,index) in tab_list" :key="index">
          <view class="product-topic-four-tab-one"
                :id="`scroll_into_topic_id_${tab.topic_id}`"
                :class="{'product-topic-four-tab-one-active':tab.topic_id == currentTopicId}"
                :style="{backgroundColor: tab.topic_id == currentTopicId ? config.selectBgColor : selectBgColorList.length && selectBgColorList[0], color:  tab.topic_id == currentTopicId ? '#fff' : config.selectBgColor}" @click="changeTab(tab.topic_id)">{{tab.topic_name}}</view>
<!--          <view class="product-topic-four-tab-one" @click="changeTab(tab.topic_id)"-->
<!--                :id="`scroll_into_topic_id_${tab.topic_id}`"-->
<!--                :style="{backgroundColor: selectBgColorList.length && selectBgColorList[0], color: config.selectBgColor}" v-else>{{tab.topic_name}}</view>-->
        </block>
      </scroll-view>
      <view class="product-topic-four-area" v-for="(area,j) in tab_list" :key="j" :class="'product-topic-four-area-'+area.topic_id">
        <template v-if="all_data_dict[area.topic_id] && all_data_dict[area.topic_id].length>0">
          <view class="product-topic-four-header" :class="{'product-topic-four-header-not-img':!area.topic_adv}">
            <image class="product-topic-four-header-img" mode="widthFix" :src="$util.img(area.topic_adv)" v-if="area.topic_adv"></image>
            <template v-else>
              <view class="product-topic-four-header-title">{{area.topic_name}}</view>
              <view class="product-topic-four-header-subtitle">{{area.remark}}</view>
            </template>
          </view>
          <view class="product-topic-four-list" v-if="config.selectedTemplate==4">
            <uv-waterfall ref="fallsFlow" v-model="all_data_dict[area.topic_id]" columnGap="20rpx" :add-time="300" @changeList="changeList($event,area.topic_id)" @finish="waterfallFinish">
              <template v-slot:list1>
                <block v-for="(item,index) in list1_dict[area.topic_id]" :key="index">
                  <view class="product-topic-four-list--one"
                        v-if="!item.type_link"
                        @click="toProductDetail(item)">
                    <view class="product-topic-four-list--one--img">
                      <image :src="$util.img(item.sku_image)" @error="waterfallImageError(index,list1_dict[area.topic_id])" mode='widthFix'>
                      </image>
                      <!-- 商品显示已抢光，其他显示已售罄 -->
                      <image :src="$util.img('public/static/youpin/product-over.png')" class="over"
                             v-if="item.goods_stock==0 && item.is_seckill == 1"></image>
                      <image :src="$util.img('public/static/youpin/product-sell-out.png')" class="over"
                             v-if="item.goods_stock==0 && item.is_seckill == 0"></image>
                    </view>
                    <view class="product-topic-four-list--one--row">
                      <view class="product-topic-four-list--one--name">
                        <text class="tag-cross-border" v-if="item.cbec_origin_name">跨境
                          <image :src="$util.img(item.cbec_origin_icon)" mode="aspectFit" class="tag-cross-border--flag" v-if="item.cbec_origin_icon"></image>
                          <text class="tag-cross-border--full" v-else>{{item.cbec_origin_name[0]}}</text>
                        </text>
                        <text v-if="item.is_recommend" class="product-topic-four-list--one--name--tag">推荐</text>
                        <template v-else-if="(item.tags && item.tags.length>0)">
                          <text v-for="(sub,u) in item.tags" v-bind:key="u" class="product-topic-four-list--one--name--tag">{{sub.tag_name}}</text>
                        </template>
                        {{item.goods_name}}
                      </view>
                      <view class="product-topic-four-list--one--price">
                        <view><text>￥</text>{{item.retail_price}}</view>
                        <view>￥{{item.market_price}}</view>
                      </view>
                      <view class="product-topic-four-list--one--benefit" v-if="item.forecast_price && parseFloat(item.forecast_price)">
                        <view><text class="benefit-color">预估收益</text><text class="product-topic-four-list--one--benefit--price">￥{{item.forecast_price}}</text></view>
                      </view>
                    </view>
                  </view>
                  <view class="product-topic-four-list--more" v-else @click="toTopicList(area.topic_id)">
                    <view class="product-topic-four-list--more--img">
                      <image :src="$util.img(img)" v-for="(img,f) in item.sku_images" :key="f"
                             @error="skuImageError(f,item.sku_images)"
                             class="product-topic-four-list--more--img--one"></image>
                    </view>
                    <view class="product-topic-four-list--more--title">{{area.topic_name}}</view>
                    <view class="product-topic-four-list--more--to">查看更多商品
                      <uni-icons type="arrowthinright" size="12" color="#fff" class="product-topic-four-list--more--to--icon"></uni-icons>
                    </view>
                  </view>
                </block>
              </template>
              <template v-slot:list2>
                <block v-for="(item,index) in list2_dict[area.topic_id]" :key="index">
                  <view class="product-topic-four-list--one"
                        v-if="!item.type_link"
                        @click="toProductDetail(item)">
                    <view class="product-topic-four-list--one--img">
                      <image :src="$util.img(item.sku_image)" @error="waterfallImageError(index,list2_dict[area.topic_id])" mode='widthFix'>
                      </image>
                      <!-- 商品显示已抢光，其他显示已售罄 -->
                      <image :src="$util.img('public/static/youpin/product-over.png')" class="over"
                             v-if="item.goods_stock==0 && item.is_seckill == 1"></image>
                      <image :src="$util.img('public/static/youpin/product-sell-out.png')" class="over"
                             v-if="item.goods_stock==0 && item.is_seckill == 0"></image>
                    </view>
                    <view class="product-topic-four-list--one--row">
                      <view class="product-topic-four-list--one--name">
                        <text class="tag-cross-border" v-if="item.cbec_origin_name">跨境
                          <image :src="$util.img(item.cbec_origin_icon)" mode="aspectFit" class="tag-cross-border--flag" v-if="item.cbec_origin_icon"></image>
                          <text class="tag-cross-border--full" v-else>{{item.cbec_origin_name[0]}}</text>
                        </text>
                        <text v-if="item.is_recommend" class="product-topic-four-list--one--name--tag">推荐</text>
                        <template v-else-if="(item.tags && item.tags.length>0)">
                          <text v-for="(sub,u) in item.tags" v-bind:key="u" class="product-topic-four-list--one--name--tag">{{sub.tag_name}}</text>
                        </template>
                        {{item.goods_name}}
                      </view>
                      <view class="product-topic-four-list--one--price">
                        <view><text>￥</text>{{item.retail_price}}</view>
                        <view>￥{{item.market_price}}</view>
                      </view>
                      <view class="product-topic-four-list--one--benefit" v-if="item.forecast_price && parseFloat(item.forecast_price)">
                        <view><text class="benefit-color">预估收益</text><text class="product-topic-four-list--one--benefit--price">￥{{item.forecast_price}}</text></view>
                      </view>
                    </view>
                  </view>
                  <view class="product-topic-four-list--more" v-else @click="toTopicList(area.topic_id)">
                    <view class="product-topic-four-list--more--img">
                      <image :src="$util.img(img)" v-for="(img,f) in item.sku_images" :key="f"
                             @error="skuImageError(f,item.sku_images)"
                             class="product-topic-four-list--more--img--one"></image>
                    </view>
                    <view class="product-topic-four-list--more--title">{{area.topic_name}}</view>
                    <view class="product-topic-four-list--more--to">查看更多商品
                      <uni-icons type="arrowthinright" size="12" color="#fff" class="product-topic-four-list--more--to--icon"></uni-icons>
                    </view>
                  </view>
                </block>
              </template>
            </uv-waterfall>
          </view>
          <view class="product-topic-five-list" v-if="config.selectedTemplate==5">
            <block v-for="(item,index) in all_data_dict[area.topic_id]" :key="index">
              <view class="product-topic-five-list--one" v-if="!item.type_link" @click="toProductDetail(item)">
                <view class="product-topic-five-list--one--img">
                  <image :src="$util.img(item.sku_image)" @error="waterfallImageError(index,item)" mode='widthFix'></image>
                  <!-- 商品显示已抢光，其他显示已售罄 -->
                  <image :src="$util.img('public/static/youpin/product-over.png')" class="over"
                         v-if="item.goods_stock==0 && item.is_seckill == 1"></image>
                  <image :src="$util.img('public/static/youpin/product-sell-out.png')" class="over"
                         v-if="item.goods_stock==0 && item.is_seckill == 0"></image>
                </view>
                <view class="product-topic-five-list--one--info">
                  <view class="product-topic-five-list--one--name">
                    <text class="tag-cross-border" v-if="item.cbec_origin_name">跨境
                      <image :src="$util.img(item.cbec_origin_icon)" mode="aspectFit" class="tag-cross-border--flag" v-if="item.cbec_origin_icon"></image>
                      <text class="tag-cross-border--full" v-else>{{item.cbec_origin_name[0]}}</text>
                    </text>
                    <text v-if="item.is_recommend" class="product-topic-five-list--one--name--tag">推荐</text>
                    <template v-else-if="(item.tags && item.tags.length>0)">
                      <text v-for="(sub,u) in item.tags" v-bind:key="u" class="product-topic-five-list--one--name--tag">{{sub.tag_name}}</text>
                    </template>
                    {{item.goods_name}}
                  </view>
                  <view class="product-topic-five-list--one--row">
                    <view class="product-topic-five-list--one--price">
                      <view class="product-topic-five-list--one--price--retail"><text class="symbol">￥</text>{{item.retail_price}}</view>
                      <view class="product-topic-five-list--one--price--market">原价￥{{item.market_price}}</view>
                    </view>
                    <text class="product-topic-five-list--one--op">购买</text>
                  </view>
                </view>
              </view>
              <view class="product-topic-five-list--more" v-else @click="toTopicList(area.topic_id)">
                <view class="product-topic-five-list--more--img">
                  <image :src="$util.img(img)" v-for="(img,f) in item.sku_images" :key="f"
                         @error="skuImageError(f,item.sku_images)"
                         class="product-topic-five-list--more--img--one"></image>
                </view>
                <view class="product-topic-five-list--more--right">
                  <view class="product-topic-five-list--more--title">{{area.topic_name}}</view>
                  <view class="product-topic-five-list--more--to">查看更多商品
                    <uni-icons type="arrowthinright" size="12" color="#fff" class="product-topic-five-list--more--to--icon"></uni-icons>
                  </view>
                </view>
              </view>
            </block>

          </view>
          <!--          <view class="product-topic-four-more" :style="{color: config.fontColor}" @click="toTopicList">-->
          <!--            {{config.moreText}} <uni-icons type="forward" size="24rpx" :color="config.fontColor"></uni-icons>-->
          <!--          </view>-->
        </template>
      </view>
    </view>
  </view>
</template>
<script>
import uniIcons from "@/components/uni-icons/uni-icons.vue"
export default {
  name: "diy-product-topic",
  components:{
    uniIcons
  },
  props:{
    config:{
      type:Object,
      default(){
        return {}
      }
    },
    topHeight:{
      type:Number,
      default:0
    },
    showTop:{
      type:Number,
      default:0,
    },
    scrollTop:{
      type:Number,
      default:0,
    }
  },
  watch:{
    scrollTop(val){
      if(this.config.selectedTemplate == 4 || this.config.selectedTemplate == 5){
        this.getStickyPosition()
        this.getTopicAreaRect()

      }
    },
    is_scroll_to_top(newVal,oldVal){
      if(!oldVal && newVal){
        setTimeout(()=>{
          this.getTabHeight()
        },300)
      }
    }
  },
  async created(){
    this.key_index = this.$util.generateRandomString(8);
    this.productTopicIndex = `product-topic-${this.key_index}`
    if(this.config.selectBgColor){
      this.selectBgColorList = this.$util.generateRGBAColors(this.config.selectBgColor)
    }
    this.topic_id_list = this.config.topicId.toString().split(',');

    if(this.config.selectedTemplate == 4 || this.config.selectedTemplate == 5){
      this.page_size = parseInt(this.config.displayGoodsCount)
      await this.checkTopicIds();
      this.currentTopicId = this.tab_list.length > 0 ? this.tab_list[0].topic_id : null;
      this.scroll_into_id = `scroll_into_topic_id_${this.currentTopicId}`
      if(this.config.selectedTemplate == 4){
        this.tab_list.map(item=>{
          this.topic_class_name_list.push(`product-topic-four-area-${item.topic_id}`)
          return item;
        })
        let topic_id = this.tab_list.length && this.tab_list[0].topic_id;
        topic_id && this.getData(topic_id,true)
      }else{
        this.tab_list.map(item=>{
          this.topic_class_name_list.push(`product-topic-four-area-${item.topic_id}`)
          this.getData(item.topic_id,true)
          return item;
        })
      }
    }else{
      this.currentTopicId = this.topic_id_list[0];
      await this.getData(this.currentTopicId)
    }
  },
  data(){
    return{
      currentTopicId:null,
      topic_id_list: [],
      tab_list:[],
      dataList:[],
      list1:[],
      list2: [],
      serialColor:['rgba(245, 93, 93, 1)','rgba(251, 107, 13, 1)','rgba(255, 195, 0, 1)'],
      page: 1,
      page_count:1,
      page_size:  10,
      showPage:0,
      totalCount:0, //总条数
      originalList:[],
      is_scroll_to_top:false,
      selectBgColorList:[],
      all_data_dict:{},
      list1_dict:{},
      list2_dict: {},
      topic_class_name_list: [],
      scroll_into_id: '',
      tab_height: 40,
      key_index: '',
      productTopicIndex: '',
    }
  },
  methods:{
    imageError(index) {
      this.dataList[index].sku_image = this.$util.getDefaultImage().default_goods_img;
      this.$forceUpdate();
    },
    waterfallImageError(index,dataList) {
      dataList[index] && (dataList[index].sku_image = this.$util.getDefaultImage().default_goods_img);
      this.$forceUpdate();
    },
    skuImageError(index,dataList){
      dataList[index] && (dataList[index] = this.$util.getDefaultImage().default_goods_img);
      this.$forceUpdate();
    },
    toProductDetail(item){
      this.$util.toProductDetail(item,(wap_url)=>{
        this.$buriedPoint.diyReportDecorationComponentInteractionEvent({diy_template_name:'ProductTopic',diy_text:item.goods_name,diy_link:wap_url,diy_image:this.$util.img(item.sku_image)})
      })
    },
    async checkTopicIds(){
      try{
        let res = await this.$api.sendRequest({
          url: this.$apiUrl.checkTopicIdsUrl,
          async:false,
          data: {
            all_topic_id: this.topic_id_list
          },
        })
        if(res.code == 0){
          this.tab_list = res.data
          // this.tab_list.map(item=>{
          //   this.$set(this.all_data_dict,item.topic_id,[])
          //   return item;
          // })
        }
      }catch (e) {

      }
    },
    async getData(topic_id,isAsync = false){
      if(!topic_id){
        return
      }
      if(isAsync){
        this.$api.sendRequest({
          url: this.$apiUrl.getTopicGoodsListUrl,
          data: {
            page_size: this.page_size,
            page: this.page,
            topic_id
          },
          success:(res)=>{
            if(res.code == 0){
              res.data.list = res.data.list.map(item=>{
                item.sku_image = this.$util.imageCdnResize(item.sku_image,{image_process:'resize,w_700','x-oss-process':'image/resize,w_700'})
                return item;
              })
              switch (this.config.selectedTemplate) {
                case 1:
                  this.dataList = res.data.list.slice(0,3)
                  break
                case 2:
                  this.dataList = res.data.list
                  break;
                case 3:
                  this.originalList = this.originalList.concat(res.data.list)
                  break;
                case 4:
                  let tmps_1 = res.data.list.slice(0,4).map(tt=>tt.sku_image)
                  res.data.list.push({'type_link':'more',sku_images:tmps_1})
                  this.$set(this.all_data_dict,topic_id,res.data.list)
                  break;
                case 5:
                  let tmps = res.data.list.slice(0,3).map(tt=>tt.sku_image)
                  res.data.list.push({'type_link':'more',sku_images:tmps})
                  this.$set(this.all_data_dict,topic_id,res.data.list)
                  break;
                default:
                  this.dataList = res.data.list
              }
              this.page_count = res.data.page_count
              this.totalCount = res.data.count
            }
          }
        })
      }else{
        try{
          let res = await this.$api.sendRequest({
            url: this.$apiUrl.getTopicGoodsListUrl,
            async:false,
            data: {
              page_size: this.page_size,
              page: this.page,
              topic_id
            },
          })
          if(res.code == 0){
            res.data.list = res.data.list.map(item=>{
              item.sku_image = this.$util.imageCdnResize(item.sku_image,{image_process:'resize,w_700','x-oss-process':'image/resize,w_700'})
              return item;
            })
            switch (this.config.selectedTemplate) {
              case 1:
                this.dataList = res.data.list.slice(0,3)
                break
              case 2:
                this.dataList = res.data.list
                break;
              case 3:
                this.originalList = this.originalList.concat(res.data.list)
                break;
              case 4:
                let tmps_1 = res.data.list.slice(0,4).map(tt=>tt.sku_image)
                res.data.list.push({'type_link':'more',sku_images:tmps_1})
                this.$set(this.all_data_dict,topic_id,res.data.list)
                break;
              case 5:
                let tmps = res.data.list.slice(0,3).map(tt=>tt.sku_image)
                res.data.list.push({'type_link':'more',sku_images:tmps})
                this.$set(this.all_data_dict,topic_id,res.data.list)
                break;
              default:
                this.dataList = res.data.list
            }
            this.page_count = res.data.page_count
            this.totalCount = res.data.count
          }
        }catch (e) {

        }
      }
    },
    async changeData(){
      if(this.originalList.length<this.totalCount){
        this.page+=1
        uni.showLoading({
          title: '加载中',
          mask: true
        })
        await this.getData(this.currentTopicId)
        uni.hideLoading()
      }
      if((this.showPage+1)*9<this.totalCount){
        this.showPage+=1
      }else{
        this.showPage=0
      }
    },
    toTopicList(topic_id){
      this.$util.diyCompateRedirectTo({
        wap_url: `/promotionpages/task/list/list?topic_id=${topic_id || this.currentTopicId}`
      })
    },
    async changeTab(topic_id){
      const query = uni.createSelectorQuery().in(this);
      query.select(`.${this.productTopicIndex} .product-topic-four-area-${topic_id}`).boundingClientRect(data=>{
        let scroll_y_height = 0
        if(data.top >= this.topHeight){
          scroll_y_height = this.scrollTop + (data.top - this.topHeight - this.tab_height) + 5
        }else{
          scroll_y_height = this.scrollTop + (data.top - this.topHeight - this.tab_height) + 5
        }
        this.$emit('scrollToPoint',scroll_y_height)
      }).exec();
    },
    // 这点非常重要：e.name在这里返回是list1或list2，要手动将数据追加到相应列
    changeList(e,topic_id){
      let key = e.name+'_dict'
      let value_list = this[key][topic_id] ? this[key][topic_id] : []
      value_list.push(e.value)
      this.$set(this[key],topic_id, value_list)
      // this[key].push(e.value);
    },
    waterfallFinish(){
      let tmp_top_id = this.tab_list.map(tt=>tt.topic_id)
      let has_top_id = Object.keys(this.all_data_dict).map(tt=>parseInt(tt))
      // console.log('tmp_top_id',tmp_top_id,has_top_id)
      for (let i = 0; i < tmp_top_id.length; i++) {
        if(!has_top_id.includes(tmp_top_id[i])){
          this.getData(tmp_top_id[i],true)
          break;
        }
      }
    },
    getTabHeight(){
      const query = uni.createSelectorQuery().in(this);
      query.select(`.${this.productTopicIndex} .product-topic-four-tab`).boundingClientRect(data => {
        if(data && data.height){
          this.tab_height = data.height;
        }
      }).exec();
    },
    getStickyPosition() {
      const query = uni.createSelectorQuery().in(this);
      query.select(`.${this.productTopicIndex} .product-topic-four`).boundingClientRect(data => {
        if (data) {
          if((data.top <= this.topHeight) && (data.top + data.height) > this.topHeight){
            this.is_scroll_to_top = true
          }else{
            this.is_scroll_to_top = false
          }
          // this.stickyTop = data.top;
          // if(data.top == this.topHeight){
          //   this.is_scroll_to_top = true
          // }else{
          //   this.is_scroll_to_top = false
          // }
        }
      }).exec();
    },
    getTopicAreaRect(){
      const query = uni.createSelectorQuery().in(this);
      this.topic_class_name_list.map(item=>{
        let topic_id = item.split('-').pop();
        query.select(`.${item}`).boundingClientRect(data=>{
          // console.log(`${item}--${JSON.stringify(data)}`)
          if((data.height+data.top > (this.topHeight + this.tab_height)) && (data.top <= (this.topHeight + this.tab_height)) && this.currentTopicId != topic_id){
            this.currentTopicId = topic_id
            this.scroll_into_id = `scroll_into_topic_id_${topic_id}`
          }
        }).exec();
        return item;
      })
    }
  }
}
</script>

<style scoped lang="scss">
.product-topic{
  width: 100%;
  &-model{
    margin-top: 20rpx;
  }
  &-one{
    width: 100%;
    height: 506rpx;
    overflow: hidden;
    background-repeat: no-repeat;
    background-size: 100% 100%;
    border-radius: 20rpx;
    padding: 24rpx 20rpx 0 20rpx;
    box-sizing: border-box;
    &-title{
      display: flex;
      justify-content: space-between;
      align-items: center;
      &-text{
        font-size: 36rpx;
        font-weight: 700;
        line-height: 42.2rpx;
      }
      &-more {
        font-size: $ns-font-size-xm;
        display: flex;
        align-items: center;
        &-icon{
          border: 1px solid;
          border-radius: 50%;
          height: 26rpx;
          width: 26rpx;
          display: flex;
          align-items: center;
          justify-content: center;
          margin-left: 6rpx;
        }
      }
    }
    &-list{
      margin-top: 20rpx;
      column-count: 3;
      gap: 0 20rpx;
      &-item{
        width: 212rpx;
        border-radius: 20rpx;
        background-color: white;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: flex-start;
        padding: 0 0 12rpx 0;
        box-sizing: border-box;
        &-image{
          width: 212rpx;
          height: 212rpx;
          border-radius: 20rpx;
          position: relative;
          &-img{
            width: 212rpx;
            height: 212rpx;
            border-radius: 20rpx;
            display: block;
          }
          .goods_img-over {
            width: 100rpx;
            height: 100rpx;
            position: absolute;
            left: 50%;
            top: 50%;
            transform: translate(-50%,-50%);
          }
          &-serial{
            width: 42rpx;
            height: 32rpx;
            border-radius: 20rpx 0 20rpx 0;
            position: absolute;
            left: 0;
            top: 0;
            font-size: 24rpx;
            font-weight: 700;
            line-height: 24rpx;
            color: rgba(255, 255, 255, 1);
            display: flex;
            justify-content: center;
            align-items: center;
          }
        }
        &-info{
          width: 196rpx;
          margin-top: 16rpx;
          padding-bottom: 10rpx;
          box-sizing: border-box;
          &-name{
            font-size: 28rpx;
            font-weight: 400;
            line-height: 32.6rpx;
            color: rgba(56, 56, 56, 1);
            word-break: break-all;
            text-overflow: ellipsis; //显示为省略号
            display: -webkit-box; //对象作为伸缩盒子模型显示
            -webkit-box-orient: vertical; //设置或检索伸缩盒对象的子元素的排列方式
            -webkit-line-clamp: 2; //显示行数## 标题文字 ##
            overflow: hidden;
            min-height: 64rpx;
          }
          &-price{
            margin-top: 10rpx;
            display: flex;
            font-size: 36rpx;
            font-weight: 700;
            line-height: 36rpx;
            color: var(--custom-brand-color);
            &-symbol{
              align-self: flex-end;
              font-size: 24rpx;
              font-weight: 700;
              line-height: 44rpx;
            }
          }
          &-original-price{
            margin-top: 10rpx;
            font-size: 20rpx;
            font-weight: 400;
            line-height: 20rpx;
            text-decoration-line: line-through;
            color: rgba(166, 166, 166, 1);
          }
        }
      }
    }
  }
  &-two{
    height: 506rpx;
    background-repeat: no-repeat;
    background-size: 100% 100%;
    border-radius: 20rpx;
    padding: 24rpx 20rpx 0 20rpx;
    box-sizing: border-box;
    &-title{
      display: flex;
      justify-content: space-between;
      align-items: center;
      &-text{
        font-size: 36rpx;
        font-weight: 700;
        line-height: 42.2rpx;
      }
      &-more {
        font-size: $ns-font-size-xm;
        display: flex;
        align-items: center;
        &-icon{
          border: 1px solid;
          border-radius: 50%;
          height: 26rpx;
          width: 26rpx;
          display: flex;
          align-items: center;
          justify-content: center;
          margin-left: 6rpx;
        }
      }
    }
    &-list{
      margin-top: 20rpx;
      white-space: nowrap;
      width: 100%;
      &-item{
        width: 212rpx;
        border-radius: 20rpx;
        background-color: white;
        display: inline-flex;
        flex-direction: column;
        align-items: center;
        justify-content: flex-start;
        padding: 0 0 12rpx 0;
        box-sizing: border-box;
        margin-right: 20rpx;
        &-image{
          width: 212rpx;
          height: 212rpx;
          border-radius: 20rpx;
          position: relative;
          &-img{
            width: 212rpx;
            height: 212rpx;
            border-radius: 20rpx;
          }
          .goods_img-over {
            width: 100rpx;
            height: 100rpx;
            position: absolute;
            left: 50%;
            top: 50%;
            transform: translate(-50%,-50%);
          }
          &-serial{
            width: 42rpx;
            height: 32rpx;
            border-radius: 20rpx 0 20rpx 0;
            position: absolute;
            left: 0;
            top: 0;
            font-size: 24rpx;
            font-weight: 700;
            line-height: 24rpx;
            color: rgba(255, 255, 255, 1);
            display: flex;
            justify-content: center;
            align-items: center;
          }
        }
        &-info{
          width: 196rpx;
          margin-top: 16rpx;
          padding-bottom: 10rpx;
          box-sizing: border-box;
          &-name{
            font-size: 28rpx;
            font-weight: 400;
            line-height: 32.6rpx;
            color: rgba(56, 56, 56, 1);
            word-break: break-all;
            text-overflow: ellipsis; //显示为省略号
            display: -webkit-box; //对象作为伸缩盒子模型显示
            -webkit-box-orient: vertical; //设置或检索伸缩盒对象的子元素的排列方式
            -webkit-line-clamp: 2; //显示行数## 标题文字 ##
            overflow: hidden;
            min-height: 64rpx;
          }
          &-price{
            margin-top: 10rpx;
            display: flex;
            font-size: 36rpx;
            font-weight: 700;
            line-height: 36rpx;
            color: var(--custom-brand-color);
            &-symbol{
              align-self: flex-end;
              font-size: 24rpx;
              font-weight: 700;
              line-height: 44rpx;
              color: var(--custom-brand-color);
            }
          }
          &-original-price{
            margin-top: 10rpx;
            font-size: 20rpx;
            font-weight: 400;
            line-height: 20rpx;
            text-decoration-line: line-through;
            color: rgba(166, 166, 166, 1);
          }
        }
      }
    }
  }
  &-three{
    width: 100%;
    box-sizing: border-box;
    background-color: transparent;
    &-list{
      column-count: 3;
      //gap: 20rpx;
      margin-bottom: -26rpx;
      &-item{
        border-radius: 20rpx;
        background-color: white;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: flex-start;
        box-sizing: border-box;
        margin-bottom: 26rpx;
        &-image{
          //width: 220rpx;
          //height: 220rpx;
          border-radius: 20rpx;
          position: relative;
          &-img{
            width: 218rpx;
            height: 218rpx;
            border-radius: 20rpx;
          }
          .goods_img-over {
            width: 100rpx;
            height: 100rpx;
            position: absolute;
            left: 50%;
            top: 50%;
            transform: translate(-50%,-50%);
          }
        }
        &-info{
          width: 196rpx;
          margin-top: 16rpx;
          padding-bottom: 10rpx;
          box-sizing: border-box;
          &-name{
            font-size: 28rpx;
            font-weight: 400;
            line-height: 32.6rpx;
            color: rgba(56, 56, 56, 1);
            word-break: break-all;
            text-overflow: ellipsis; //显示为省略号
            display: -webkit-box; //对象作为伸缩盒子模型显示
            -webkit-box-orient: vertical; //设置或检索伸缩盒对象的子元素的排列方式
            -webkit-line-clamp: 2; //显示行数## 标题文字 ##
            overflow: hidden;
            min-height: 64rpx;
          }
          &-price{
            margin-top: 10rpx;
            display: flex;
            font-size: 36rpx;
            font-weight: 700;
            line-height: 36rpx;
            color: var(--custom-brand-color);
            &-symbol{
              align-self: flex-end;
              font-size: 24rpx;
              font-weight: 700;
              line-height: 44rpx;
              color: var(--custom-brand-color);
            }
          }
          &-original-price{
            margin-top: 10rpx;
            font-size: 20rpx;
            font-weight: 400;
            line-height: 20rpx;
            text-decoration-line: line-through;
            color: rgba(166, 166, 166, 1);
          }
        }
      }
    }
    &-op{
      display: flex;
      justify-content: center;
      align-items: center;
      margin: 0 auto;
      margin-top: 40rpx;
      width: 148rpx;
      height: 48rpx;
      border-radius: 40rpx;
      background: rgba(240, 240, 240, 1);
      font-size: 24rpx;
      font-weight: 400;
      line-height: 27.94rpx;
      color: rgba(56, 56, 56, 1);
    }
  }
  &-four{
    margin-top: 20rpx;
    &-tab{
      width: 100vw;
      margin-left: -20rpx;
      padding-left: 20rpx;
      box-sizing: border-box;
      white-space: nowrap;
      position: fixed;
      top: 0;
      height: 88rpx;
      line-height: 88rpx;
      z-index: 1;
      background-color: white;
      display: block !important;
      .product-topic-four-tab-one{
        padding: 0 18rpx;
        box-sizing: border-box;
        height: 52rpx;
        border-radius: 10rpx;
        color: rgba(56, 56, 56, 1);
        background: white;
        display: inline-block;
        margin-right: 20rpx;
        transition: all 1s;
      }
      .product-topic-four-tab-one-active{
        color: white;
      }
    }
    &-header{
      width: 100%;
      min-height: 160rpx;
      border-radius: 20rpx;
      margin-bottom: 20rpx;
      &-not-img{
        padding: 32rpx 100rpx;
        box-sizing: border-box;
      }
      &-img{
        width: 100%;
        height: auto;
        display: block;
        border-radius: 20rpx;
      }
      &-title{
        font-size: 32rpx;
        font-weight: 700;
        color: var(--custom-brand-color);
        text-align: center;
      }
      &-subtitle{
        font-size: 26rpx;
        font-weight: 400;
        letter-spacing: 26rpx;
        text-indent: 26rpx;
        color: rgba(166, 166, 166, 1);
        text-align: center;
      }
    }
    &-list{
      //margin-top: 20rpx;
      .over {
        width: 120rpx;
        height: 120rpx;
        position: absolute;
        left: 50%;
        top: 50%;
        transform: translate(-50%, -50%);
      }
      &--one {
        width: 344rpx;
        //margin-bottom: 30rpx;
        //padding: 0 20rpx;
        padding-bottom: 20rpx;
        border-radius: 20rpx;
        box-sizing: border-box;
        background-color: white;
        margin-bottom: 20rpx;
        &--img {
          width: 100%;
          position: relative;

          image {
            width: 100%;
            height: 100%;
            border-radius: 20rpx;
            display: block;
          }
        }
        &--row{
          padding: 0 20rpx;
          box-sizing: border-box;
        }
        &--name {
          font-size: 26rpx;
          font-weight: 500;
          color: #343434;
          word-break: break-all;
          text-overflow: ellipsis;
          display: -webkit-box;
          -webkit-box-orient: vertical;
          -webkit-line-clamp: 2;
          overflow: hidden;
          margin-top: 20rpx;
          //min-height: 74rpx;
          box-sizing: border-box;
          position: relative;
          line-height: 1.4;

          &--tag {
            vertical-align: text-bottom;
            padding: 2rpx 10rpx;
            margin-right: 5rpx;
            box-sizing: border-box;
            display: inline-block;
            text-align: center;
            font-size: 20rpx;
            font-weight: 500;
            color: #FFFFFF;
            background: linear-gradient(55deg, var(--custom-brand-color-80), var(--custom-brand-color));
            border-radius: 4rpx;

          }
        }

        &--price {
          display: flex;
          justify-content: flex-start;
          align-items: baseline;

          view:first-child {
            font-size: 36rpx;
            font-family: PingFang SC;
            font-weight: bold;
            color: var(--custom-brand-color);

            text {
              font-size: 26rpx;
            }
          }

          view:last-child {
            font-size: 24rpx;
            font-weight: 500;
            text-decoration: line-through;
            color: #9A9A9A;
            margin-left: 8rpx;
          }
        }
        &--benefit {
          background: var(--custom-brand-color-10);
          font-size: 22rpx;
          border-radius: 8rpx;
          display: inline-block;
          margin-top: 10rpx;
          padding: 0 10rpx;
          &--price{
            color: var(--custom-brand-color);
          }

          .benefit-color {
            color: #333333;
          }
        }
      }
      &--more{
        width: 344rpx;
        //margin-bottom: 30rpx;
        padding: 10rpx;
        border-radius: 20rpx;
        box-sizing: border-box;
        background-color: white;
        margin-bottom: 20rpx;
        &--img{
          display: flex;
          justify-content: space-between;
          flex-wrap: wrap;
          &--one{
            width: 152rpx;
            height: 152rpx;
            border-radius: 20rpx;
            margin-bottom: 20rpx;
          }
        }
        &--title{
          font-size: 32rpx;
          font-weight: 700;
          color: var(--custom-brand-color);
          text-align: left;
          padding-left: 10rpx;
          box-sizing: border-box;
        }
        &--to{
          font-size: 26rpx;
          font-weight: 400;
          color: var(--custom-brand-color);
          text-align: left;
          padding-left: 10rpx;
          box-sizing: border-box;
          display: flex;
          align-items: center;
          &--icon{
            width: 34rpx;
            height: 34rpx;
            line-height: 34rpx;
            display: flex;
            justify-content: center;
            align-items: center;
            background-color: var(--custom-brand-color);
            box-sizing: border-box;
            border-radius: 50%;
            margin-left: 10rpx;
          }
        }
      }
    }
    &-more{
      width: 100%;
      height: 72rpx;
      border-radius: 20rpx;
      background: rgba(255, 255, 255, 1);
      display: flex;
      justify-content: center;
      align-items: center;
    }
  }
}
.product-topic-five-list{
  &--one{
    border-radius: 20rpx;
    background: rgba(255, 255, 255, 1);
    margin-bottom: 20rpx;
    &--img{
      position: relative;
      image{
        width: 100%;
        height: auto;
        border-radius: 20rpx 20rpx 0 0;
        display: block;
      }
      .over{
        width: 200rpx;
        height: 200rpx;
        position: absolute;
        left: 50%;
        top: 50%;
        transform: translate(-50%, -50%);
      }
    }
    &--info{
      padding: 20rpx 18rpx;
      box-sizing: border-box;
    }
    &--name{
      font-size: 32rpx;
      font-weight: 700;
      color: rgba(56, 56, 56, 1);
      text-overflow: ellipsis;
      overflow: hidden;
      word-break: break-all;
      white-space: nowrap;
      line-height: 32rpx;

      &--tag {
        vertical-align: text-bottom;
        padding: 2rpx 10rpx;
        margin-right: 5rpx;
        box-sizing: border-box;
        display: inline-block;
        text-align: center;
        font-size: 20rpx;
        font-weight: 500;
        color: #FFFFFF;
        background: linear-gradient(55deg, var(--custom-brand-color-80), var(--custom-brand-color));
        border-radius: 4rpx;

      }
    }
    &--row{
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-top: 16rpx;
    }
    &--price{
      display: flex;
      align-items: baseline;
      &--retail{
        font-size: 40rpx;
        font-weight: 700;
        color: var(--custom-brand-color);
        .symbol{
          font-size: 24rpx;
        }
      }
      &--market{
        font-size: 26rpx;
        font-weight: 400;
        text-decoration-line: line-through;
        color: rgba(166, 166, 166, 1);
        margin-left: 18rpx;
      }
    }
    &--op{
      width: 154rpx;
      height: 60rpx;
      line-height: 60rpx;
      border-radius: 100px;
      background: var(--custom-brand-color);
      font-size: 32rpx;
      font-weight: 400;
      color: rgba(255, 255, 255, 1);
      display: flex;
      justify-content: center;
      align-items: center;
    }
  }
  &--more{
    border-radius: 20rpx;
    background: rgba(255, 255, 255, 1);
    margin-bottom: 20rpx;
    padding: 20rpx 0 20rpx 20rpx;
    box-sizing: border-box;
    display: flex;
    align-items: center;
    &--img{
      display: flex;
      align-items: center;
      &--one{
        width: 140rpx;
        height: 140rpx;
        border-radius: 20rpx;
        margin-right: 14rpx;
      }
    }
    &--right{
    }
    &--title{
      font-size: 32rpx;
      font-weight: 700;
      color: rgba(56, 56, 56, 1);
    }
    &--to{
      font-size: 26rpx;
      font-weight: 400;
      color: var(--custom-brand-color);
      display: flex;
      align-items: center;
      &--icon{
        width: 26rpx;
        height: 26rpx;
        background: var(--custom-brand-color);
        border-radius: 50%;
        margin-left: 10rpx;
        display: flex;
        justify-content: center;
        align-items: center;
      }
    }
  }
}
</style>
