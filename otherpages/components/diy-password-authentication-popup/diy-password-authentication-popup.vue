<!--密码验证弹窗-->
<template>
  <uni-popup ref="popupRef" :maskClick="false">
    <view class="content">
      <image :src="memberInfo.headimg ? $util.img(memberInfo.headimg) : $util.getDefaultImage().default_headimg"
             mode="aspectFill" @error="memberInfo.headimg = $util.getDefaultImage().default_headimg" class="content-head"></image>
      <view class="content-name">{{ memberInfo.nickname }}</view>
      <view class="password-wrap">
        <myp-one :maxlength="6" :is-pwd="true" @input="input" ref="input" :value="pay_password" :auto-focus="isFocus" type="box"></myp-one>
        <view class="password-wrap-text">请输入您的支付密码验证</view>
      </view>
      <view class="content-op">
        <view class="content-op-one" :class="{'content-op-one-active': is_allow}" @click="comfirm">确认验证</view>
        <view class="content-op-one" @click="goBack"><uni-icons type="arrowleft" color="rgba(128, 128, 128, 1)"></uni-icons>返回个人中心</view>
      </view>
    </view>
  </uni-popup>
</template>

<script>
import UniPopup from "../../../components/uni-popup/uni-popup.vue";
import mypOne from '@/components/myp-one/myp-one.vue';
import UniIcons from "../../../components/uni-icons/uni-icons.vue";
import apiUrl from "../../../common/js/apiurls";

export default {
  name: "diy-password-authentication-popup",
  props: {
    memberInfo:{
      type:Object,
      default:()=>{
        return {
          headimg: '',
          nickname: ''
        }
      }
    }
  },
  components: {UniIcons, UniPopup,mypOne},
  data(){
    return{
      isFocus: false,
      pay_password: '',
    }
  },
  computed:{
    is_allow(){
      return this.pay_password.length == 6
    }
  },
  methods:{
    open(){
      this.$refs.popupRef.open();
    },
    close(){
      this.$refs.popupRef.close();
    },
    input(pay_password){
      this.pay_password = pay_password
    },
    async comfirm(){
      if(this.is_allow){
        uni.showLoading({
          title: '支付密码验证中...',
          mask: true
        })
        let res = await this.$api.sendRequest({
          url: apiUrl.checkpaypasswordUrl,
          data: {
            pay_password:this.pay_password
          },
          async: false
        })
        uni.hideLoading();
        if (res.code >= 0) {
          this.$refs.popupRef.close()
          this.$emit('confirm');
        } else {
          this.pay_password = ''
          this.$util.showToast({
          	title: res.message
          });
        }
      }
    },
    goBack(){
      this.$emit('goBack')
    }
  }
}
</script>

<style scoped lang="scss">
.content{
  width: 610rpx;
  height: 640rpx;
  border-radius: 20rpx;
  background: rgba(255, 255, 255, 1);
  display: flex;
  flex-direction: column;
  align-items: center;
  padding-top: 40rpx;
  box-sizing: border-box;
  &-head{
    width: 100rpx;
    height: 100rpx;
    border-radius: 50%;
  }
  &-name{
    font-size: 32rpx;
    font-weight: 400;
    line-height: 37.5rpx;
    color: rgba(56, 56, 56, 1);
    margin-top: 16rpx;
  }
  .password-wrap{
    width: 522rpx;
    margin-top: 44rpx;
    /deep/ .flex-box .box{
      width: 72rpx;
      height: 80rpx;
      background: rgba(245, 245, 245, 1);
      border: none;
      &:not(:last-child){
        margin-right: 18rpx;
      }
    }
    &-text{
      text-align: center;
      font-size: 26rpx;
      font-weight: 400;
      letter-spacing: 4rpx;
      line-height: 30.48rpx;
      color: var(--custom-brand-color);
      margin-top: 22rpx;
    }
  }
  &-op {
    margin-top: 46rpx;

    &-one {
      width: 522rpx;
      height: 80rpx;
      line-height: 80rpx;
      border-radius: 20rpx;
      background: rgba(245, 245, 245, 1);
      font-size: 28rpx;
      font-weight: 400;
      color: rgba(56, 56, 56, 1);
      display: flex;
      justify-content: center;
      align-items: center;

      &:first-child {
        margin-bottom: 22rpx;
        color: rgba(128, 128, 128, 1);
      }

      &-active {
        background: var(--custom-brand-color);
        color: rgba(255, 255, 255, 1)!important;
      }
    }
  }
}
</style>
