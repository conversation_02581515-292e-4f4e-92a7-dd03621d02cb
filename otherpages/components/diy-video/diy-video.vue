<template>
  <view class="video-block" :class="{'full-video': value.showType==1 }">
    <video v-if="value.videoType==1" :src="value.videoLink" :autoplay="true" :loop="true" :muted="isMuted" :style="{height,borderRadius}"></video>
    <template v-if="value.videoType==0 && vid">
      <slot name="tx_video" :vid="vid" :height="height" :borderRadius="borderRadius" :isMuted="isMuted"></slot>
    </template>
    <image :src="isMuted ? $util.img('public/static/youpin/mute.png') : $util.img('public/static/youpin/unmute.png')"
           class="video-block-mute" @click="changeMute"></image>
  </view>
</template>

<script>
	// 视频
	export default {
		name: 'diy-video',
		props: {
			value: {
				type: Object,
				default: () => {
					return {
            "videoType": 0,
            "showType": 0,
            "videoLink": "",
            "addon_name": "",
            "type": "VIDEO",
            "name": "视频",
            "controller": "Video",
            "width":0,
            "height":0
					};
				}
			},
			siteId: {
				type: [Number, String],
				default: 0
			}
		},
		data() {
			return {
        vid:"",
        borderRadius:'20rpx',
        isMuted:true,
        height:""
      };
		},
		created() {
      if(this.value.showType==1){
        this.borderRadius = ''
      }
      this.calculateVideoShowSize();
			this.parseTXvideoUrl();
		},
		methods: {
      calculateVideoShowSize(){
        if(this.value.showType==1){
          this.height = (750/parseFloat(this.value.width)*parseFloat(this.value.height))+'rpx'
        }else{
          this.height = ((750-20*2)/parseFloat(this.value.width)*parseFloat(this.value.height))+'rpx'
        }
      },
      parseTXvideoUrl(){
        if(this.value.videoType==0){
          try{
            let tmp_list = this.value.videoLink.split('.html')[0].split('/');
            this.vid = tmp_list[tmp_list.length-1];
          }catch (e) {

          }
        }
      },
      changeMute(){
        this.isMuted = !this.isMuted;
      }
    }
	};
</script>

<style scoped lang="scss">
.video-block{
  margin-top: 20rpx;
  position: relative;
  &-mute{
    width: 48rpx;
    height: 48rpx;
    position: absolute;
    top: 20rpx;
    right: 20rpx;
    z-index: 1;
  }
}
.full-video{
  width: 100vw;
  margin-left: -20rpx;
  margin-top: 0rpx;
}
	video {
		width: 100%;
	}
</style>
