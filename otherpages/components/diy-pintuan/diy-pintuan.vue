<template>
	<view class="diy-pintuan" v-if="dataList && dataList.length" :style="{backgroundColor: value.item.backgroundColor,backgroundImage: `url(${value.item.backgroundImg})`}">
		<view class="diy-pintuan-top">
			<view class="pintuan-title">
				<image :src="$util.img('public/static/youpin/pintuan/pintuan-hot.png')" class="pintuan-title-hot"></image>
				<text class="pintuan-title-name" :style="{color: value.item.textColor}">{{name}}</text>
			</view>
			<view class="pintuan-more" :style="{color: value.item.textColor}" @click="toMore()">{{moreText}}
        <uni-icons type="forward" size="24rpx" :color="value.item.textColor" class="pintuan-more-icon"></uni-icons>
      </view>
		</view>
		<scroll-view class="diy-pintuan-box" scroll-x="true">
			<view class="pintuan-box-item" v-for="(item, i) in dataList" :key="i" @click="toDetail(item)">
				<view class="pintuan-item">
					<view class="pintuan-item-image">
            <view class="goods_img">
              <image :src="$util.img(item.sku_image)" @error="imageError(i)" mode='aspectFit'></image>
              <image :src="$util.img('public/static/youpin/product-sell-out.png')" class="goods_img-over" v-if="item.goods_stock == 0"></image>
            </view>
						<text class="pintuan-item-image-new"
							v-if="item.promotion_type=='new'">{{item.promotion_type_desc}}</text>
					</view>
          <view class="pintuan-item-info">
            <view class="pintuan-item-new-name">{{item.goods_name }}</view>
            <view class="pintuan-item-tip">
              <text>{{item.pintuan_num}}人团</text>
              <text>已开{{item.order_num}}团</text>
            </view>
            <view class="pintuan-item-bottom">
              <view class="pintuan-item-bottom-left">
                <view class="pintuan-item-original-price">
                  <text>￥</text>
                  {{ item.market_price }}
                </view>
                <view class="pintuan-item-new-price">
                  <text>￥</text>
                  <text class="pintuan-item-new-price-integer">{{ item.pintuan_price|getInteger }}</text>
                  <text class="pintuan-item-new-price-Decimals">{{ item.pintuan_price|getDecimals }}</text>
                </view>
              </view>
              <uni-icons type="plus-filled" color="var(--custom-brand-color)" size="20" class="pintuan-item-bottom-right"></uni-icons>
            </view>
          </view>
				</view>
			</view>
		</scroll-view>
	</view>
</template>

<script>
	import UniIcons from "@/components/uni-icons/uni-icons.vue";

  export default {
		components: {
      UniIcons

    },
		name: 'diy-pintuan',
		props: {
			value: {
				type: Object
			},
			dataList: {
				type: Array,
				default: () => {
					return [];
				}
			},
		},
    filters:{
      getInteger(value){
        return String(parseFloat(value)).split('.')[0]
      },
      getDecimals(value){
        return '.'+String(parseFloat(value).toFixed(2)).split('.')[1]
      }
    },
		data() {
			return {
				name: '拼团好物',
				moreText: '查看更多'
			};
		},
		watch: {


		},
		created() {
			if (this.value.item) {
				let it = this.value.item
				it.name && (this.name = it.name)
				it.moreText && (this.moreText = it.moreText)
			}
		},
		methods: {
			toMore() {
        this.$buriedPoint.diyReportDecorationComponentInteractionEvent({diy_template_name:'Pintuan',diy_text:this.moreText,diy_link:this.value.item.moreUrl ? this.value.item.moreUrl:'/promotionpages/pintuan/list/list',diy_image:''})
				this.$util.diyCompateRedirectTo({
					wap_url: this.value.item.moreUrl ? this.value.item.moreUrl:'/promotionpages/pintuan/list/list'
				});
			},
			toDetail(res) {
        this.$buriedPoint.diyReportDecorationComponentInteractionEvent({diy_template_name:'Pintuan',diy_text:res.goods_name,diy_link:`/promotionpages/pintuan/detail/detail?id=${res.pintuan_goods_id}&goods_id=${res.goods_id}`,diy_image:this.$util.img(res.sku_image)})
				this.$util.diyCompateRedirectTo({
					wap_url: `/promotionpages/pintuan/detail/detail?id=${res.pintuan_goods_id}&goods_id=${res.goods_id}`
				});
			},
			imageError(index) {
				this.dataList[index].sku_image = this.$util.getDefaultImage().default_goods_img;
				this.$forceUpdate();
			},
			onFinish() {
				this.$emit('finish')
			},
		}
	};
</script>

<style lang="scss" scoped>
	.diy-pintuan {
		margin-top: 20rpx;
		border-radius: 20rpx;
		width: 100%;
		padding: $ns-padding 0 $ns-padding $ns-padding;
		box-sizing: border-box;
    background-size: 100% 100%;
    background-repeat: no-repeat;
	}

	.diy-pintuan-top {
		width: 100%;
		display: flex;
		justify-content: space-between;
		align-items: center;
    padding-right: 20rpx;
    box-sizing: border-box;

		.pintuan-title {
			display: flex;
			align-items: center;
      &-hot{
        width: 33rpx;
        height: 33rpx;
        margin-right: 14rpx;
      }

			.pintuan-title-name {
				font-size: 36rpx;
				font-weight: bolder;
				margin-right: $ns-margin;
			}

			/* .pintuan-titie-tit{
      border-radius: 50rpx;
      background-color:$base-color;
      color:#ffffff;
      font-size: 22rpx;
      padding: 0 15rpx;
      line-height: 1.6;
      font-size: $ns-font-size-sm;
    } */

		}

		.pintuan-more {
			font-size: $ns-font-size-xm;
      display: flex;
      align-items: center;
      &-icon{
        border: 1px solid;
        border-radius: 50%;
        margin-top: 2rpx;
        height: 26rpx;
        width: 26rpx;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-left: 6rpx;
      }
		}
	}

	.diy-pintuan-box {
		width: 100%;
		/*white-space 不能丢  */
		white-space: nowrap;
		box-sizing: border-box;
		margin-top: 28rpx;
	}

	.pintuan-box-item {
		width: 188rpx;
		height: 100%;
		vertical-align: top;
		display: inline-block;
		background: #ffffff;
    border-radius: 20rpx;
		margin-right: $ns-margin;
    box-sizing: border-box;

		.pintuan-item {
			width: 100%;
			height: 100%;
		}

		.pintuan-item-image {
			width: 100%;
			height: 188rpx;
			border-radius: 20rpx 20rpx 0 0;
			overflow: hidden;
			display: flex;
			justify-content: center;
			align-items: center;
			position: relative;
      .goods_img {
        position: relative;
      }
      .goods_img-over {
        width: 100rpx;
        height: 100rpx;
        position: absolute;
        left: 50%;
        top: 50%;
        transform: translate(-50%,-50%);
      }
			image {
				width: 205rpx;
				height: 205rpx;
				padding: 0;
				margin: 0;
        display: block;
			}

			&-new {
				width: 100%;
				height: 40rpx;
				line-height: 40rpx;
				background: #000000;
				opacity: 0.5;
				border-radius: 0 0 20rpx 20rpx;
				font-size: 26rpx;
				font-weight: 500;
				color: #FFFFFF;
				box-sizing: border-box;
				text-align: center;
				position: absolute;
				left: 0;
				bottom: 0;
			}
		}
    .pintuan-item-info{
      padding: 0 8rpx;
      box-sizing: border-box;
    }
		.pintuan-item-new-name {
			white-space: normal;
      margin-top: 12rpx;
      box-sizing: border-box;
			height: 66rpx;
      line-height: 1.3;
			font-size: $ns-font-size-xm;
      color: rgba(56, 56, 56, 1);
      font-weight: 400;
			word-break: break-all;
			text-overflow: ellipsis; //显示为省略号
			display: -webkit-box; //对象作为伸缩盒子模型显示
			-webkit-box-orient: vertical; //设置或检索伸缩盒对象的子元素的排列方式
			-webkit-line-clamp: 2; //显示行数## 标题文字 ##
			overflow: hidden;
		}
    .pintuan-item-bottom{
      display: flex;
      justify-content: space-between;
      margin-top: 14rpx;
      &-left{
      }
      &-right{
        display: flex;
        align-items: flex-end;
        line-height: 40rpx;
        padding-bottom: 6rpx;
        box-sizing: border-box;
      }
    }
    .pintuan-item-original-price{
      font-size: 18rpx;
      font-weight: 400;
      line-height: 20rpx;
      text-decoration-line: line-through;
      color: rgba(166, 166, 166, 1);
    }
		.pintuan-item-new-price {
      font-size: 24rpx;
      font-weight: 700;
      line-height: 36rpx;
      color: var(--custom-brand-color);


      text:first-child {
				font-size: 20rpx;
			}
      &-integer{
        font-size: 36rpx;
      }
		}

		.pintuan-item-tip {
			display: flex;
			align-items: center;
      box-sizing: border-box;
      height: 26rpx;
      border-radius: 40rpx;
      background: var(--custom-brand-color-10);
      margin-top: 6rpx;

			text:first-child {
				height: 26rpx;
        font-size: 16rpx;
        font-weight: 400;
        line-height: 26rpx;
        padding: 0 16rpx;
        box-sizing: border-box;
        color: rgba(252, 252, 252, 1);
        border-radius: 40rpx;
        background: var(--custom-brand-color);
			}

			text:last-child {
        font-size: 16rpx;
        font-weight: 400;
        height: 26rpx;
        line-height: 26rpx;
        color: var(--custom-brand-color);
        margin-left: 6rpx;
			}
		}
	}
</style>
