<!--带下拉联想词的搜索组件-->
<template>
  <view class="search-top-wrap">
    <view class="search-top-wrap-wrapper" :style="{height,width}">
      <input type="text" maxlength="50" v-model="input_value" class="search-top-wrap-input"
             placeholder-class="search-top-wrap-input-placeholder" :style="{height,width:input_width}" @input="to_input_value"
             :placeholder="placeholder" :focus="focus" :confirm-type="confirmType"
             @confirm="toConfirm" @focus="toFocus">
<!--      <text class="uni-icon iconfont iconguanbi" v-if="input_value" @click="toClear"></text>-->
      <text class="iconfont iconsousuo" v-if="input_value" @click="toConfirm"></text>
    </view>

    <view class="search-top-wrap-associate" :style="{top: associateTop, height: `calc(100vh - ${associateTop})`}" v-show="focus && input_value && associate_history[input_value] && associate_history[input_value].length">
      <view class="search-top-wrap-associate-one" v-for="(item,index) in associate_history[input_value]" :key="index" @click="clickAssociate(index)">
        <view class="search-top-wrap-associate-one-left">
          <text v-for="(one,j) in item.keyword_list" :key="one.stat_id">
            {{one}}
            <text class="search-top-wrap-associate-one-left-select" v-if="j<item.keyword_list.length-1">{{input_value}}</text>
          </text>
        </view>
        <view class="search-top-wrap-associate-one-right">
          <uni-icons type="arrowthinright" size="32rpx" color="rgba(196, 196, 196, 1)" class="search-top-wrap-associate-one-right-icon"></uni-icons>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import UniIcons from "@/components/uni-icons/uni-icons.vue";
import apiurls from "@/common/js/apiurls";

export default {
  name: "diy-associate-search",
  components: {UniIcons},
  props:{
    value:{
      type: String,
      default: ''
    },
    placeholder:{
      type: String,
      default: '搜索商品'
    },
    focus:{
      type: Boolean,
      default: false
    },
    confirmType:{
      type: String,
      default: 'search'
    },
    associateTop:{
      type: [Number,String],
      default: 0
    }
  },
  data(){
    return{
      width: '440rpx',
      height: "64rpx",
      input_width: '330rpx',
      input_value: '',
      associate_history: {},
      to_input_value: null,
      search_report_list: []
    }
  },
  watch:{
    value:{
      handler(val){
        this.input_value = val
        if(!val){
          this.focus = true
        }else{
          this.focus = false
        }
      }
    },
    immediate: true
  },
  created(){
    // #ifdef MP-WEIXIN
    // 参考文档  https://juejin.cn/post/7076705501764911118
    // 获取微信胶囊的位置信息 width,height,top,right,left,bottom
    const custom = uni.getMenuButtonBoundingClientRect()
    const sysntemInfo = uni.getSystemInfoSync()
    this.height = custom.height + 'px'
    this.width = custom.left - 52*(sysntemInfo.windowWidth/750) - 30 + 'px'
    this.input_width = parseFloat(this.width) - sysntemInfo.windowWidth/750*80 - 16 +'px'
    // #endif
    this.input_value = this.value
    this.to_input_value = this.debounce(this.toInput, 300)
  },
  methods:{
    debounce(func, delay) {
      let timeout
      return function () {
        let context = this,
            args = arguments;
        clearTimeout(timeout) // 如果持续触发，那么就清除定时器，定时器的回调就不会执行。
        timeout = setTimeout(function (){
          func.apply(context, args)
        }, delay)
      }
    },
    toInput(event){
      let value = event.detail.value;
      if(value) {
        if (!this.associate_history.hasOwnProperty(value)) {
          this.toAssociate()
        }
      }
    },
    async toAssociate(){
      try{
        let res = await this.$api.sendRequest({
          url: apiurls.searchGuessUrl,
          data: {
            search: this.input_value
          },
          async: false
        })
        if(res.code == 0) {
          this.$set(this.associate_history,this.input_value, res.data.map(item=>{
            item.keyword_list = item.keyword_text.split(this.input_value)
            item.is_click = false
            return item
          }))
        }
      }catch (e) {
        console.log(e)
      }

    },
    toClear(){
      this.input_value = ''
      this.$emit('clear')
    },
    toConfirm(){
      this.focus = false
      if(this.input_value){
        this.$emit('confirm',{type:'user_input',value:this.input_value})
        this.searchReport('search',this.input_value,this.input_value)
      }
    },
    async clickAssociate(index){
      this.focus = false
      this.$emit('confirm',{type:'associate',value:this.associate_history[this.input_value][index]})
      await this.hitReport(this.input_value,index)
      await this.searchReport('guess',this.associate_history[this.input_value][index].keyword_text,this.input_value)
      this.input_value = this.associate_history[this.input_value][index].keyword_text
    },
    toFocus(e){
      this.focus = true
      if(this.input_value){
        if(!this.associate_history.hasOwnProperty(this.input_value)){
          this.toAssociate()
        }
      }
    },
    /**
     * 联想词点击上报
     * @param search_text
     * @param index
     * @returns {Promise<void>}
     */
    async hitReport(search_text,index){
      // 上报过不再上报
      if(this.associate_history[search_text][index].is_click){
        return
      }
      this.associate_history[search_text][index].is_click = true
      let hit_text = this.associate_history[search_text][index].keyword_text
      try{
        let res = await this.$api.sendRequest({
          url: apiurls.searchHitUrl,
          data: {
            search_text,
            hit_text
          },
          async: false
        })
      }catch (e) {

      }
    },
    /**
     * 搜索上报
     * @param source  来源：search直接搜索，guess联想点击搜索，history历史搜索
     * @param search_text  搜索词
     * @param source_search_text  原搜索词，联想点击前的搜索词
     * @returns {Promise<void>}
     */
    async searchReport(source="",search_text, source_search_text){
      let key = `${source}-${search_text}`
      if(this.search_report_list.includes(key)){
        return
      }
      this.search_report_list.push(key)
      try {
        let res = await this.$api.sendRequest({
          url: apiurls.searchReportUrl,
          data:{
            source,
            search_text,
            source_search_text
          },
          async: false
        })
      }catch (e) {

      }
    }
  }
}
</script>

<style scoped lang="scss">
.search-top-wrap{
  &-input{
    border-radius: 40rpx 0 0 40rpx;
    background: rgba(247, 247, 247, 1);
    padding-left: 24rpx;
    font-size: 32rpx;
    font-weight: 400;
    line-height: 37.26rpx;
    color: rgba(0, 0, 0, 1);
    z-index: 1;
    &-placeholder{
      font-size: 26rpx;
      font-weight: 400;
      line-height: 30.26rpx;
      color: rgba(166, 166, 166, 1);
    }
  }
  &-wrapper{
    position: relative;
    border-radius: 40rpx;
    background: rgba(247, 247, 247, 1);
    .iconguanbi{
      color: rgba(196, 196, 196, 1);
      font-size: 32rpx;
      position: absolute;
      right: 100rpx;
      top: 50%;
      transform: translateY(-50%);
      z-index: 10;
    }
    .iconsousuo{
      margin: 0;
      font-size: 32rpx;
      color: white;
      background: var(--custom-brand-color);
      width: 80rpx;
      height: 52rpx;
      border-radius: 40rpx;
      display: flex;
      justify-content: center;
      align-items: center;
      position: absolute;
      right: 6rpx;
      top: 50%;
      transform: translateY(-50%);
      z-index: 10;
    }
  }
  &-associate{
    position: fixed;
    left: 0;
    width: 100%;
    background-color: #fff;
    padding: 0 20rpx;
    box-sizing: border-box;
    padding-bottom: 40rpx;
    overflow-y: scroll;
    &-one{
      display: flex;
      justify-content: space-between;
      align-items: center;
      height: 88rpx;
      border-bottom: 2rpx solid rgba(250, 250, 250, 1);
      box-sizing: border-box;
      &-left{
        font-size: 28rpx;
        font-weight: 400;
        line-height: 88rpx;
        color: rgba(56, 56, 56, 1);
        &-select{
          color: var(--custom-brand-color);
        }
      }
      &-right{
        &-icon{
          display: block;
          transform: rotate(-45deg);
        }
      }
    }
  }
}
</style>
