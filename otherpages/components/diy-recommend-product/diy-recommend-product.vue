<template>
	<view class="recommend" v-if="recommendGoods.length>0">
		<view class="recommend--title"><text></text>店主推荐</view>
		<view class="recommend--products">
			<view v-for="(item,index) in recommendGoods" v-bind:key="index"
				class="recommend--products--one" @click="$util.toProductDetail(item)"
				:style="(index+1)%3==2 ? 'margin:0 24rpx;':''">
				<view class="recommend--products--one--img">
					<image :src="$util.img(item.goods_image)"
						@error="imageError(recommendGoods,index)"></image>
					<!--                <image :src="$util.img('public/static/youpin/product-over.png')" class="over"></image>-->
				</view>
				<view class="recommend--products--one--name">{{item.goods_name}}</view>

			</view>
		</view>
	</view>
</template>

<script>
	export default {
		props:{
			recommendGoods:{
				type:Array,
				default:[]
			},
      imageError:Function
		},
		data() {
			return {
			}
		},
		methods: {
		}
	}
</script>

<style lang="scss">
	.recommend {
		background: #FFFFFF;
		border-radius: 20rpx;
		padding: 30rpx 24rpx 0 24rpx;
		box-sizing: border-box;
		margin-top: 24rpx;

		&--title {
			height: 36rpx;
			font-size: 30rpx;
			font-weight: bold;
			color: #343434;
			display: flex;
			align-items: center;

			text {
				width: 6rpx;
				height: 36rpx;
				background: var(--custom-brand-color);
				border-radius: 3rpx;
				margin-right: 17rpx;
				display: inline-block;
			}
		}

		&--products {
			display: flex;
			align-items: flex-start;
			flex-wrap: wrap;
			margin-top: 33rpx;

			&--one {
				width: 202rpx;
				margin-bottom: 30rpx;

				&--img {
					width: 100%;
					height: 202rpx;
					position: relative;

					image {
						width: 100%;
						height: 100%;
						border-radius: 8rpx;

						&.over {
							width: 120rpx;
							height: 120rpx;
							position: absolute;
							left: 50%;
							top: 50%;
							transform: translate(-50%, -50%);
						}
					}
				}

				&--name {
					font-size: 26rpx;
					font-weight: 500;
					color: #343434;
					word-break: break-all;
					text-overflow: ellipsis; //显示为省略号
					display: -webkit-box; //对象作为伸缩盒子模型显示
					-webkit-box-orient: vertical; //设置或检索伸缩盒对象的子元素的排列方式
					-webkit-line-clamp: 2; //显示行数## 标题文字 ##
					overflow: hidden;
					margin-top: 20rpx;
					line-height: 1.4;
				}

				&--price {
					margin-top: 24rpx;
					font-size: 36rpx;
					font-weight: bold;
					color: var(--custom-brand-color);

					text {
						font-size: 26rpx;
					}
				}

				&--primary {
					margin-top: 16rpx;
					font-size: 24rpx;
					font-weight: 500;
					text-decoration: line-through;
					color: #9A9A9A;
				}
			}
		}
	}
</style>
