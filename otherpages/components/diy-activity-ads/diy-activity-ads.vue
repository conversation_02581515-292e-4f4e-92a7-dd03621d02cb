<template>
	<view class="diy-activity-ads" :class="{'diy-activity-ads-not-padding': diyActivityAdsNotPadding}">
		<view class="select_01" :class="{'select_vw':value.currentSelectType==4}" v-if="value.currentSelect == 1">
			<view class="select_01_box" :style="{width:image_size_list[0].width,height:image_size_list[0].height}" :class="{'marginRight': value.currentSelectType!=4 }">
				<image :src="$util.img(value.list[0].imageUrl)" alt="" @click="toAd(value.list[0])" class="imgs" mode="widthFix"/>
			</view>
			<view class="select_01_box" :style="{width:image_size_list[1].width,height:image_size_list[1].height}">
				<image :src="$util.img(value.list[1].imageUrl)" alt="" @click="toAd(value.list[1])" class="imgs" mode="widthFix"/>
			</view>
		</view>
		<view class="select_02" :class="{'select_vw':value.currentSelectType==4}" v-else-if="value.currentSelect == 2">
			<view class="select_02_box " :style="{width:image_size_list[0].width,height:image_size_list[0].height}" :class="{'marginRight': value.currentSelectType!=4 }">
				<image :src="$util.img(value.list[0].imageUrl)" alt="" @click="toAd(value.list[0])" class="imgs" mode="widthFix"/>
			</view>
			<view class="select_02_box " :style="{width:image_size_list[1].width,height:image_size_list[1].height}" :class="{'marginRight': value.currentSelectType!=4 }">
				<image :src="$util.img(value.list[1].imageUrl)" alt="" @click="toAd(value.list[1])" class="imgs" mode="widthFix"/>
			</view>
			<view class="select_02_box " :style="{width:image_size_list[2].width,height:image_size_list[2].height}">
				<image :src="$util.img(value.list[2].imageUrl)" alt="" @click="toAd(value.list[2])" class="imgs" mode="widthFix"/>
			</view>
		</view>
		<view class="select_03" :class="{'select_vw' :value.currentSelectType==4 }" v-else-if="value.currentSelect == 3">
			<view class="select_03_box " :style="{width:image_size_list[0].width,height:image_size_list[0].height}" :class="{'marginRight': value.currentSelectType!=4 }">
				<image :src="$util.img(value.list[0].imageUrl)" alt="" @click="toAd(value.list[0])" class="imgs" mode="widthFix"/>
			</view>
			<view class="select_03_box " :style="{width:image_size_list[1].width,height:image_size_list[1].height}" :class="{'marginRight': value.currentSelectType!=4 }">
				<image :src="$util.img(value.list[1].imageUrl)" alt="" @click="toAd(value.list[1])" class="imgs" mode="widthFix"/>
			</view>
			<view class="select_03_box " :style="{width:image_size_list[2].width,height:image_size_list[2].height}" :class="{'marginRight': value.currentSelectType!=4 }">
				<image :src="$util.img(value.list[2].imageUrl)" alt="" @click="toAd(value.list[2])" class="imgs" mode="widthFix"/>
			</view>
			<view class="select_03_box " :style="{width:image_size_list[3].width,height:image_size_list[3].height}">
				<image :src="$util.img(value.list[3].imageUrl)" alt="" @click="toAd(value.list[3])" class="imgs" mode="widthFix"/>
			</view>
		</view>
		<view class="select_04" :class="{'select_vw' :value.currentSelectType==4 }" v-else-if="value.currentSelect == 4">
			<view class="select_04_layer">
				<view class="select_04_box" :style="{width:image_size_list[0].width,height:image_size_list[0].height}" :class="{'marginRight': value.currentSelectType!=4 }">
					<image :src="$util.img(value.list[0].imageUrl)" alt="" @click="toAd(value.list[0])" class="imgs" mode="widthFix">
				</view>
				<view class="select_04_box" :style="{width:image_size_list[1].width,height:image_size_list[1].height}">
					<image :src="$util.img(value.list[1].imageUrl)" alt="" @click="toAd(value.list[1])" class="imgs" mode="widthFix"/>
				</view>
			</view>
			<view class="select_04_layer" :class="{'marginTop': value.currentSelectType!=4 }">
				<view class="select_04_box" :style="{width:image_size_list[2].width,height:image_size_list[2].height}" :class="{'marginRight': value.currentSelectType!=4 }">
					<image :src="$util.img(value.list[2].imageUrl)" alt="" @click="toAd(value.list[2])" class="imgs" mode="widthFix"/>
				</view>
				<view class="select_04_box" :style="{width:image_size_list[3].width,height:image_size_list[3].height}">
					<image :src="$util.img(value.list[3].imageUrl)" alt="" @click="toAd(value.list[3])" class="imgs" mode="widthFix"/>
				</view>
			</view>
		</view>
		<view class="select_05" :class="{'select_vw' :value.currentSelectType==2 }" v-else-if="value.currentSelect == 5">
			<view class="select_05_layer" :class="{'marginRight': value.currentSelectType!=2 }">
				<view class="select_05_box " :style="{width:image_size_list[0].width,height:image_size_list[0].height}">
					<image :src="$util.img(value.list[0].imageUrl)" alt="" @click="toAd(value.list[0])" class="imgs" mode="widthFix"/>
				</view>
			</view>
			<view class="select_05_layer">
				<view class="select_05_box" :style="{width:image_size_list[1].width,height:image_size_list[1].height}">
					<image :src="$util.img(value.list[1].imageUrl)" alt="" @click="toAd(value.list[1])" class="imgs" mode="widthFix"/>
				</view>
				<view class="select_05_box" :style="{width:image_size_list[2].width,height:image_size_list[2].height}" :class="{'marginTop': value.currentSelectType!=2 }">
					<image :src="$util.img(value.list[2].imageUrl)" alt="" @click="toAd(value.list[2])" class="imgs" mode="widthFix"/>
				</view>
			</view>
		</view>
		<view class="select_06" :class="{'select_vw' :value.currentSelectType==4 }" v-else-if="value.currentSelect == 6">
			<view class="select_06_layer">
				<view class="select_06_box" :style="{width:image_size_list[0].width,height:image_size_list[0].height}">
					<image :src="$util.img(value.list[0].imageUrl)" alt="" @click="toAd(value.list[0])" class="imgs" mode="widthFix"/>
				</view>
			</view>
			<view class="select_06_layer" :class="{'marginTop': value.currentSelectType!=4 }">
				<view class="select_06_box" :style="{width:image_size_list[1].width,height:image_size_list[1].height}" :class="{'marginRight': value.currentSelectType!=4 }">
					<image :src="$util.img(value.list[1].imageUrl)" alt="" @click="toAd(value.list[1])" class="imgs" mode="widthFix"/>
				</view>
				<view class="select_06_box" :style="{width:image_size_list[2].width,height:image_size_list[2].height}">
					<image :src="$util.img(value.list[2].imageUrl)" alt="" @click="toAd(value.list[2])" class="imgs" mode="widthFix"/>
				</view>
			</view>
		</view>
		<view class="select_07" :class="{'select_vw' :value.currentSelectType==3 }" v-else-if="value.currentSelect == 7">
			<view class="select_07_layer" :class="{'marginRight': value.currentSelectType!=3 }">
				<view class="select_07_box" :style="{width:image_size_list[0].width,height:image_size_list[0].height}">
					<image :src="$util.img(value.list[0].imageUrl)" alt="" @click="toAd(value.list[0])" class="imgs" mode="widthFix"/>
				</view>
			</view>
			<view class="select_07_layer ">
				<view class="select_07_box " :style="{width:image_size_list[1].width,height:image_size_list[1].height}">
					<image :src="$util.img(value.list[1].imageUrl)" alt="" @click="toAd(value.list[1])" class="imgs" mode="widthFix"/>
				</view>
				<view class="deeplayer_box " :class="{'marginTop': value.currentSelectType!=3 }">
					<view class="deeplayer_item" :style="{width:image_size_list[2].width,height:image_size_list[2].height}" :class="{'marginRight': value.currentSelectType!=3 }">
						<image :src="$util.img(value.list[2].imageUrl)" alt="" @click="toAd(value.list[2])"
							class="imgs" mode="widthFix"/>
					</view>
					<view class="deeplayer_item" :style="{width:image_size_list[3].width,height:image_size_list[3].height}">
						<image :src="$util.img(value.list[3].imageUrl)" alt="" @click="toAd(value.list[3])"
							class="imgs" mode="widthFix"/>
					</view>
				</view>
			</view>
		</view>
		<view class="select_08" :class="{'select_08_2' :value.currentSelectType==2 }" v-else-if="value.currentSelect == 8" :style="{width:image_size_list[0].width,height:image_size_list[0].height}">
			<image :src="$util.img(value.list[0].imageUrl)" alt="" @click="toAd(value.list[0])" mode="widthFix"/>
		</view>
    <view class="select_09" :class="{'select_vw' :value.currentSelectType==2 }" v-else-if="value.currentSelect == 9">
      <view class="select_09_box " :style="{width:image_size_list[0].width,height:image_size_list[0].height}" :class="{'marginRight': value.currentSelectType!=2 }">
        <image :src="$util.img(value.list[0].imageUrl)" alt="" @click="toAd(value.list[0])" class="imgs" mode="widthFix"/>
      </view>
      <view class="select_09_box " :style="{width:image_size_list[1].width,height:image_size_list[1].height}" :class="{'marginRight': value.currentSelectType!=2 }">
        <image :src="$util.img(value.list[1].imageUrl)" alt="" @click="toAd(value.list[1])" class="imgs" mode="widthFix"/>
      </view>
      <view class="select_09_box " :style="{width:image_size_list[2].width,height:image_size_list[2].height}" :class="{'marginRight': value.currentSelectType!=2 }">
        <image :src="$util.img(value.list[2].imageUrl)" alt="" @click="toAd(value.list[2])" class="imgs" mode="widthFix"/>
      </view>
      <view class="select_09_box " :style="{width:image_size_list[3].width,height:image_size_list[3].height}" :class="{'marginRight': value.currentSelectType!=2 }">
        <image :src="$util.img(value.list[3].imageUrl)" alt="" @click="toAd(value.list[3])" class="imgs" mode="widthFix"/>
      </view>
      <view class="select_09_box " :style="{width:image_size_list[4].width,height:image_size_list[4].height}">
        <image :src="$util.img(value.list[4].imageUrl)" alt="" @click="toAd(value.list[4])" class="imgs" mode="widthFix"/>
      </view>
    </view>
	</view>
</template>

<script>
	// 搜索

  export default {
		name: 'diy-search',
		props: {
			value: {
				type: Object,
				default: () => {
					return { };
				}
			},
			city: {
				type: String,
				value: ''
			},
			siteId: {
				type: [Number, String],
				default: 0
			}
		},
    computed:{
      diyActivityAdsNotPadding(){
        if(
            (this.value.currentSelect==1&&this.value.currentSelectType==4)||
            (this.value.currentSelect==2&&this.value.currentSelectType==4)||
            (this.value.currentSelect==3&&this.value.currentSelectType==4)||
            (this.value.currentSelect==4&&this.value.currentSelectType==4)||
            (this.value.currentSelect==5&&this.value.currentSelectType==2)||
            (this.value.currentSelect==6&&this.value.currentSelectType==4)||
            (this.value.currentSelect==7&&this.value.currentSelectType==3)||
            (this.value.currentSelect==8&&(this.value.currentSelectType==2 || this.value.currentSelectType==3)) ||
            (this.value.currentSelect==9&&this.value.currentSelectType==2)
        ){
          return true
        }else{
          return false
        }
      }
    },
		data() {
			return {
        image_size_list:[]
      };
		},
    created(){
      this.transitionSize()
    },
		methods: {
      transitionSize(){
        let imgSizeList = this.value.imgSizeList
        for (let i = 0; i < imgSizeList.length; i++) {
          if(imgSizeList[i].select == this.value.currentSelect){
            let typeList = imgSizeList[i].typeList
            for (let j = 0; j < typeList.length; j++) {
              if(typeList[j].type == this.value.currentSelectType){
                let imageObj = typeList[j].list
                this.image_size_list = imageObj.map(item=>{
                  let width =''
                  let height = ''
                  let tmp_w = parseFloat(item.width)
                  if(isNaN(tmp_w)){
                    width = 'auto'
                  }else{
                    width = `${tmp_w}rpx`
                  }
                  let tmp_h = parseFloat(item.height)
                  if(isNaN(tmp_h)){
                    height = 'auto'
                  }else{
                    height = `${tmp_h}rpx`
                  }
                  return {width,height}
                })
                break;
              }
            }
            break;
          }
        }
      },
			toAd(item) {
        this.$buriedPoint.diyReportAdEvent(
            {diy_material_path:item.imageUrl,diy_ad_type:'image',diy_target_page:item.link,diy_ad_id:item.id,diy_action_type:'click'})
        this.$buriedPoint.diyReportDecorationComponentInteractionEvent({diy_template_name:this.value.controller,diy_text:item.title,diy_link:item.link,diy_image:item.imageUrl})
        if (item.link) {
					this.$util.diyCompateRedirectTo({
						wap_url: item.link
					})
				}
			},
		}
	};
</script>

<style lang="scss" scoped>
	/*活动广告组件*/
	.diy-activity-ads {
		width: 100%;
		margin-top: 20rpx;
	}
  .diy-activity-ads-not-padding{
    margin-top: 0!important;
  }
  .diy-activity-ads .select_vw{
    width: 100vw!important;
    margin-left: -20rpx;
  }
	.diy-activity-ads .select_01 {
		width: 100%;
		text-align: center;
		display: flex;
		font-size: 18rpx;
		justify-content: space-between;
	}

	.imgs {
		width: 100%;
		height: 100%;
    display: block;
	}

	/* ----------------------------选择1------------------------------ */

	.diy-activity-ads .select_01 .select_01_box {
		text-align: center;
		flex: 1;
	}

	.diy-activity-ads .select_01 .marginRight {
		margin-right: 20rpx;
	}

	.diy-activity-ads .select_01 .height_type_1 {
		height: 162rpx;
		line-height: 162rpx;
	}

	.diy-activity-ads .select_01 .height_type_2 {
		height: 266rpx;
		line-height: 266rpx;
	}

	.diy-activity-ads .select_01 .height_type_3 {
		height: 342rpx;
		line-height: 342rpx;
	}


	/* ----------------------------选择2------------------------------ */

	.diy-activity-ads .select_02 {
		width: 100%;
		display: flex;
		text-align: center;
		font-size: 18rpx;
		justify-content: space-between;
	}

	.diy-activity-ads .select_02 .select_02_box {
		flex: 1;
	}

	.diy-activity-ads .select_02 .marginRight {
		margin-right: 20rpx;
	}

	.diy-activity-ads .select_02 .height_type_1 {
		height: 140rpx;
		line-height: 140rpx;
	}

	.diy-activity-ads .select_02 .height_type_2 {
		height: 222rpx;
		line-height: 222rpx;
	}

	.diy-activity-ads .select_02 .height_type_3 {
		height: 296rpx;
		line-height: 296rpx;
	}

	/* ----------------------------选择3------------------------------ */

	.diy-activity-ads .select_03 {
		width: 100%;
		display: flex;
		text-align: center;
		font-size: 18rpx;
		justify-content: space-between;
	}

	.diy-activity-ads .select_03 .select_03_box {
		flex: 1;
	}

	.diy-activity-ads .select_03 .marginRight {
		margin-right: 20rpx;
	}

	.diy-activity-ads .select_03 .height_type_1 {
		height: 162rpx;
		line-height: 162rpx;
	}

	.diy-activity-ads .select_03 .height_type_2 {
		height: 128rpx;
		line-height: 128rpx;
	}

	.diy-activity-ads .select_03 .height_type_3 {
		height: 200rpx;
		line-height: 200rpx;
	}


	/* ----------------------------选择4------------------------------ */

	.diy-activity-ads .select_04 {
		width: 100%;
		text-align: center;
		font-size: 18rpx;
	}

	.diy-activity-ads .select_04 .select_04_layer {
		width: 100%;
		display: flex;
		justify-content: space-between;
	}

	.diy-activity-ads .select_04 .marginTop {
		margin-top: 18rpx;
	}

	.diy-activity-ads .select_04 .select_04_box {
		flex: 1;
	}

	.diy-activity-ads .select_04 .marginRight {
		margin-right: 20rpx;
	}

	.diy-activity-ads .select_04 .height_type_1 {
		height: 194rpx;
		line-height: 194rpx;
	}

	.diy-activity-ads .select_04 .height_type_2 {
		height: 266rpx;
		line-height: 266rpx;
	}

	.diy-activity-ads .select_04 .height_type_3 {
		height: 342rpx;
		line-height: 342rpx;
	}


	/* ----------------------------选择5------------------------------ */

	.diy-activity-ads .select_05 {
		width: 100%;
		display: flex;
		justify-content: space-between;
		text-align: center;
		font-size: 18rpx;
	}

	.diy-activity-ads .select_05 .select_05_layer {
		flex: 1;
	}

	.diy-activity-ads .select_05 .marginTop {
		margin-top: 18rpx;
	}

	.diy-activity-ads .select_05 .marginRight {
		margin-right: 20rpx;
	}

	.diy-activity-ads .select_05 .select_05_box {
		//background: #E8F7FD;
	}

	.diy-activity-ads .select_05 .height_type_1 {
		height: 342rpx;
		line-height: 342rpx;
	}

	.diy-activity-ads .select_05 .height_type_2 {
		height: 162rpx;
		line-height: 162rpx;
	}


	/* ----------------------------选择6------------------------------ */

	.diy-activity-ads .select_06 {
		width: 100%;
		text-align: center;
		font-size: 18rpx;
	}

	.diy-activity-ads .select_06 .select_06_layer {
		display: flex;
		justify-content: space-between;
	}

	.diy-activity-ads .select_06 .marginTop {
		margin-top: 18rpx;
	}

	.diy-activity-ads .select_06 .marginRight {
		margin-right: 20rpx;
	}

	.diy-activity-ads .select_06 .select_06_box {
		flex: 1;
		// background: #E8F7FD;
	}

	.diy-activity-ads .select_06 .height_fixed {
		height: 246rpx;
		line-height: 246rpx;
	}

	.diy-activity-ads .select_06 .height_type_1 {
		height: 194rpx;
		line-height: 194rpx;
	}

	.diy-activity-ads .select_06 .height_type_2 {
		height: 266rpx;
		line-height: 266rpx;
	}

	.diy-activity-ads .select_06 .height_type_3 {
		height: 342rpx;
		line-height: 342rpx;
	}


	/* ----------------------------选择7------------------------------ */

	.diy-activity-ads .select_07 {
		width: 100%;
		display: flex;
		justify-content: space-between;
		text-align: center;
		font-size: 18rpx;
	}

	.diy-activity-ads .select_07 .select_07_layer {
		flex: 1;
	}

	.diy-activity-ads .select_07 .marginTop {
		margin-top: 18rpx;
	}

	.diy-activity-ads .select_07 .marginRight {
		margin-right: 20rpx;
	}

	.diy-activity-ads .select_07 .select_07_box {
		flex: 1;
	}

	.diy-activity-ads .select_07 .deeplayer_box {
		flex: 1;
		display: flex;
	}

	.diy-activity-ads .select_07 .deeplayer_box .deeplayer_item {
		flex: 1;
	}

	.diy-activity-ads .select_07 .height_left_box_1 {
		height: 342rpx;
		line-height: 342rpx;
	}

	.diy-activity-ads .select_07 .height_left_box_2 {
		height: 218rpx;
		line-height: 218rpx;
	}

	.diy-activity-ads .select_07 .height_type_1 {
		height: 162rpx;
		line-height: 162rpx;
	}

	.diy-activity-ads .select_07 .height_type_2 {
		height: 100rpx;
		line-height: 100rpx;
	}
  .diy-activity-ads .select_08 {
    image{
      width: 100%;
      display: block;
    }
  }
  .diy-activity-ads .select_08_2{
    margin-left: -20rpx;
  }

  /* ----------------------------选择9------------------------------ */

  .diy-activity-ads .select_09 {
    width: 100%;
    display: flex;
    text-align: center;
    font-size: 18rpx;
    justify-content: space-between;
  }

  .diy-activity-ads .select_09 .select_09_box {
    flex: 1;
  }

  .diy-activity-ads .select_09 .marginRight {
    margin-right: 20rpx;
  }
</style>
