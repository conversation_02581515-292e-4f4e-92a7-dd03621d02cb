<template>
  <view v-if="is_fulfil">
    <!--全部商品 start-->
    <view class="all" v-if="all_products.length>0">
      <view class="all--title" v-if="false"><text></text>推荐商品</view>
      <view class="all--products">
        <uv-waterfall ref="fallsFlow" v-model="all_products" columnGap="20rpx" @changeList="changeList" v-if="all_products.length">
          <template v-slot:list1>
            <view class="all--products--one" v-for="(item,index) in list1" v-bind:key="index"
                  @click="toProductDetail(item)">
              <view class="all--products--one--img">
                <image :src="$util.img(item.goods_image)" @error="imageError(index,list1)" mode='widthFix'>
                </image>
                <!-- 商品显示已抢光，其他显示已售罄 -->
                <image :src="$util.img('public/static/youpin/product-over.png')" class="over"
                       v-if="item.goods_stock==0 && item.is_seckill == 1"></image>
                <image :src="$util.img('public/static/youpin/product-sell-out.png')" class="over"
                       v-if="item.goods_stock==0 && item.is_seckill == 0"></image>
              </view>
              <view class="all--products--one--row">
                <view class="all--products--one--name">
                  <text class="tag-cross-border" v-if="item.cbec_origin_name">跨境
                    <image :src="$util.img(item.cbec_origin_icon)" mode="aspectFit" class="tag-cross-border--flag" v-if="item.cbec_origin_icon"></image>
                    <text class="tag-cross-border--full" v-else>{{item.cbec_origin_name[0]}}</text>
                  </text>
                  <text v-if="item.is_recommend" class="all--products--one--name--tag">推荐</text>
                  <template v-else-if="(item.tags && item.tags.length>0)">
                    <text v-for="(sub,u) in item.tags" v-bind:key="u" class="all--products--one--name--tag">{{sub.tag_name}}</text>
                  </template>
                  {{item.goods_name}}
                </view>
                <view class="all--products--one--price">
                  <view><text>￥</text>{{item.retail_price}}</view>
                  <view>￥{{item.market_price}}</view>
                </view>
                <view class="all--products--one--benefit" v-if="item.forecast_price && parseFloat(item.forecast_price)">
                  <view><text class="benefit-color">预估收益</text><text class="all--products--one--benefit--price">￥{{item.forecast_price}}</text></view>
                </view>
              </view>
            </view>
          </template>
          <template v-slot:list2>
            <view class="all--products--one" v-for="(item,index) in list2" v-bind:key="index"
                  @click="toProductDetail(item)">
              <view class="all--products--one--img">
                <image :src="$util.img(item.goods_image)" @error="imageError(index,list2)" mode='widthFix'>
                </image>
                <!-- 商品显示已抢光，其他显示已售罄 -->
                <image :src="$util.img('public/static/youpin/product-over.png')" class="over"
                       v-if="item.goods_stock==0 && item.is_seckill == 1"></image>
                <image :src="$util.img('public/static/youpin/product-sell-out.png')" class="over"
                       v-if="item.goods_stock==0 && item.is_seckill == 0"></image>
              </view>
              <view class="all--products--one--row">
                <view class="all--products--one--name">
                  <text class="tag-cross-border" v-if="item.cbec_origin_name">跨境
                    <image :src="$util.img(item.cbec_origin_icon)" mode="aspectFit" class="tag-cross-border--flag" v-if="item.cbec_origin_icon"></image>
                    <text class="tag-cross-border--full" v-else>{{item.cbec_origin_name[0]}}</text>
                  </text>
                  <text v-if="item.is_recommend" class="all--products--one--name--tag">推荐</text>
                  <template v-else-if="(item.tags && item.tags.length>0)">
                    <text v-for="(sub,u) in item.tags" v-bind:key="u" class="all--products--one--name--tag">{{sub.tag_name}}</text>
                  </template>
                  {{item.goods_name}}
                </view>
                <view class="all--products--one--price">
                  <view><text>￥</text>{{item.retail_price}}</view>
                  <view>￥{{item.market_price}}</view>
                </view>
                <view class="all--products--one--benefit" v-if="item.forecast_price && parseFloat(item.forecast_price)">
                  <view><text class="benefit-color">预估收益</text><text class="all--products--one--benefit--price">￥{{item.forecast_price}}</text></view>
                </view>
              </view>
            </view>
          </template>
        </uv-waterfall>
        <!--			<view class="all&#45;&#45;products&#45;&#45;one" v-for="(item,index) in products" v-bind:key="index"-->
        <!--				@click="$util.toProductDetail(item)">-->
        <!--				<view class="all&#45;&#45;products&#45;&#45;one&#45;&#45;img">-->
        <!--					<image :src="$util.img(item.goods_image)" @error="imageError(index)" mode='aspectFit'>-->
        <!--					</image>-->
        <!--				</view>-->
        <!--				-->
        <!--			</view>-->
      </view>
    </view>
    <!--全部商品 end-->
    <ns-empty :isIndex="!1" v-else></ns-empty>
  </view>

</template>

<script>
	import apiurls from "@/common/js/apiurls"
	// 商品列表
	export default {
		name: 'diy-goods-list',

		props: {
			products:{
				type:Array,
				default:[]
			},
		},
		data() {
			return {
        all_products: [],
        list1:[],
        list2: [],
        is_fulfil : false
			};
		},
    watch:{
      products:{
        handler(val){
          this.all_products = val.map(item=>{
            item.goods_image = this.$util.imageCdnResize(item.goods_image)
            return item;
          })
          this.is_fulfil = true
        },
      }
    },
		created() {

		},
		methods: {
      // 这点非常重要：e.name在这里返回是list1或list2，要手动将数据追加到相应列
      changeList(e){
        this[e.name].push(e.value);
      },
			imageError(index,dataList) {

				dataList[index] && (dataList[index].goods_image = this.$util.getDefaultImage().default_goods_img);

				this.$forceUpdate();
			},
      toProductDetail(item){
        this.$util.toProductDetail(item,(wap_url)=>{
          this.$buriedPoint.diyReportDecorationComponentInteractionEvent({diy_template_name:'GoodsList',diy_text:item.goods_name,diy_link:wap_url,diy_image:item.goods_image})
        })
      }
		}
	};
</script>

<style lang="scss">
	.all {
		background: transparent;
		border-radius: 20rpx;
		box-sizing: border-box;
		margin-top: 20rpx;
		margin-bottom: 20rpx;
		margin-bottom: calc(20rpx + constant(safe-area-inset-bottom));
		margin-bottom: calc(20rpx + env(safe-area-inset-bottom));

		&--title {
			height: 36rpx;
			font-size: 30rpx;
			font-weight: bold;
			color: #343434;
			display: flex;
			align-items: center;

			text {
				width: 6rpx;
				height: 36rpx;
				background: var(--custom-brand-color);
				border-radius: 3rpx;
				margin-right: 17rpx;
				display: inline-block;
			}
		}

		&--products {
			//display: flex;
			//justify-content: space-between;
			//flex-wrap: wrap;
			margin-top: 34rpx;
      .over {
        width: 120rpx;
        height: 120rpx;
        position: absolute;
        left: 50%;
        top: 50%;
        transform: translate(-50%, -50%);
      }
			&--one {
				width: 344rpx;
				//margin-bottom: 30rpx;
        //padding: 0 20rpx;
        padding-bottom: 20rpx;
        border-radius: 20rpx;
        box-sizing: border-box;
        background-color: white;
        margin-bottom: 20rpx;
				&--img {
					width: 100%;
					position: relative;

					image {
						width: 100%;
						height: 100%;
						border-radius: 20rpx;

					}
				}
        &--row{
          padding: 0 20rpx;
          box-sizing: border-box;
        }
				&--name {
					font-size: 26rpx;
					font-weight: 500;
					color: #343434;
					word-break: break-all;
					text-overflow: ellipsis;
					display: -webkit-box;
					-webkit-box-orient: vertical;
					-webkit-line-clamp: 2;
					overflow: hidden;
					margin-top: 20rpx;
					//min-height: 74rpx;
					box-sizing: border-box;
					position: relative;
					line-height: 1.4;

					&--tag {
						vertical-align: text-bottom;
						padding: 2rpx 10rpx;
						margin-right: 5rpx;
						box-sizing: border-box;
						display: inline-block;
						text-align: center;
						font-size: 20rpx;
						font-weight: 500;
						color: #FFFFFF;
						background: linear-gradient(55deg, var(--custom-brand-color-80), var(--custom-brand-color));
						border-radius: 4rpx;

					}
				}

				&--price {
					display: flex;
					justify-content: flex-start;
					align-items: baseline;

					view:first-child {
						font-size: 36rpx;
						font-family: PingFang SC;
						font-weight: bold;
						color: var(--custom-brand-color);

						text {
							font-size: 26rpx;
						}
					}

					view:last-child {
						font-size: 24rpx;
						font-weight: 500;
						text-decoration: line-through;
						color: #9A9A9A;
						margin-left: 8rpx;
					}
				}
        &--benefit {
          background: var(--custom-brand-color-10);
          font-size: 22rpx;
          border-radius: 8rpx;
          display: inline-block;
          margin-top: 10rpx;
          padding: 0 10rpx;
          &--price{
            color: var(--custom-brand-color);
          }

          .benefit-color {
            color: #333333;
          }
        }
			}
		}
	}
</style>
