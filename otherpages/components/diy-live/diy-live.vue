<template>
	<view class="live-box" v-if="value.room_id != undefined && liveInfo.roomid">
		<view class="live-wrap" :style="{backgroundColor: value.backgroundColor,backgroundImage: `url(${value.backgroundImg})`}">
      <view class="live-wrap-left" @click="roomFun">
        <video class="live-wrap-left-img" :src="liveInfo.media_url" autoplay :muted="true" :controls="false" :show-loading="true" object-fit="cover" :loop="true" :poster="value.coverImg" v-if="liveInfo.live_status==103"></video>
        <image :src="value.coverImg" class="live-wrap-left-img" v-else></image>
        <image :src="$util.img('public/static/youpin/live/playback.png')" class="live-wrap-left-icon" mode="widthFix" v-if="liveInfo.live_status==103"></image>
        <image :src="$util.img('public/static/youpin/live/subscribe.png')" class="live-wrap-left-icon" mode="widthFix" v-else-if="liveInfo.live_status==102"></image>
        <image :src="$util.img('public/static/youpin/live/live.png')" class="live-wrap-left-icon" mode="widthFix" v-else></image>
      </view>
      <view class="live-wrap-right">
        <view class="live-wrap-right-title" :style="{color: value.textColor}">{{value.title}}</view>
        <view class="live-wrap-right-name" :style="{color: value.textColor}">主播：{{liveInfo.anchor_name}}</view>
        <view class="live-wrap-right-list">
			<!-- #ifdef MP-WEIXIN -->
				<image :src="item.cover_img" class="live-wrap-right-list-one" v-for="(item,index) in liveInfo.goods.slice(0,3)" :key="index"></image>
			<!-- #endif -->
        </view>
        <div class="live-wrap-right-op">
          <button class="live-wrap-right-op-share" @click="openSharePopup(liveInfo)"><image :src="$util.img('public/static/youpin/live/share.png')" class="live-wrap-right-op-share-icon"></image>分享</button>
          <view class="live-wrap-right-op-play" @click="roomFun" v-if="liveInfo.live_status==103"><image :src="$util.img('public/static/youpin/live/play.png')" class="live-wrap-right-op-play-icon"></image>回看</view>
          <template v-else-if="liveInfo.live_status==102">
            <view class="live-wrap-right-op-play">
              <image :src="$util.img('public/static/youpin/live/subscribe-icon.png')" class="live-wrap-right-op-play-icon"></image>{{is_subscribe ? '取消预约' : '预约' }}
              <view class="live-wrap-right-op-play-slot"><slot name="liveSubscribe"></slot></view>
            </view>
            </template>
          <view class="live-wrap-right-op-play" @click="roomFun" v-else><image :src="$util.img('public/static/youpin/live/play.png')" class="live-wrap-right-op-play-icon"></image>观看</view>
        </div>
      </view>
		</view>

    <!-- h5分享 -->
	<!-- #ifdef H5 -->
	<diy-share-navigate-h5 ref="shareNavigateH5"></diy-share-navigate-h5>
	<!-- #endif -->

	<!-- #ifdef MP-WEIXIN -->
	<share-popup
	    v-if="isShowCanvas"
	    :canvasOptions="canvasOptions"
	    ref="sharePopup"
	    :sharePopupOptions="sharePopupOptions"
      share-type="liveRoom"
	></share-popup>
	<!-- #endif -->

	</view>
</template>

<script>
  import sharePopup from "@/components/share-popup/share-popup.vue";
  import DiyShareNavigateH5 from "@/components/diy-share-navigate-h5/diy-share-navigate-h5.vue";
  import { query_to_scene } from "@/common/js/scene_handle";
	export default {
		components: {
      DiyShareNavigateH5,
      sharePopup
    },
		name: 'diy-live',
		props: {
			value: {
				type: Object,
				default: () => {
					return {};
				}
			},
			siteId: {
				type: [Number, String]
			}
		},
		data() {
			return {
        is_subscribe:false,
				liveInfo: {
          goods:[],
          live_status: 101,  //直播间状态。101：直播中，102：未开始，103已结束，104禁播，105：暂停，106：异常，107：已过期
        },
        isShowCanvas: false,
        canvasOptions: {
          width: "634",
          height: "832",
          borderRadius: "20rpx",
        },
        sharePopupOptions: [],
			};
		},
		async created() {
			await this.getLiveInfo();
		},
		watch:{
			siteId(val,nal){
				// this.getLiveInfo();
			}
		},
		methods: {
      changeSubscribe(roomid) {
        let livePlayer = requirePlugin("live-player-plugin");
        // 获取直播间单次订阅状态
        const roomId = roomid; // 房间 id
        let that = this;
        livePlayer
            .getSubscribeStatus({ room_id: roomId })
            .then((res) => {
              // console.log("房间号：", res.room_id);
              // console.log("订阅用户openid", res.openid);
              // console.log("是否订阅", res.is_subscribe);
              that.is_subscribe = res.is_subscribe
            })
            .catch((err) => {
              console.log("get subscribe status", err);
            });
      },
			async getLiveInfo(){
				let data={
          roomid: this.value.room_id
        };
        try {
          let res = await this.$api.sendRequest({
            // url: '/live/api/live/info',
            url: this.$apiUrl.getLiveInfoUrl,
            data:data,
            async: false,
          })
          if (res.code == 0 && res.data) {
            this.liveInfo = res.data;
            if(this.liveInfo.live_status == 102){
              this.changeSubscribe(this.liveInfo.roomid)
            }
          }
        }catch (e) {

        }
			},
      // 点击列表跳转
      roomFun() {
        if (this.liveInfo.live_status == 103) {
          this.$buriedPoint.diyReportDecorationComponentInteractionEvent({diy_template_name:'LiveInfo',diy_text:'',diy_link:`/otherpages/live/end/end?room_id=${this.liveInfo.roomid}`,diy_image:''})
          this.$util.redirectTo(`/otherpages/live/end/end?room_id=${this.liveInfo.roomid}`);
        } else {
          this.playerLive(this.liveInfo.roomid);
        }
      },
      // 跳转直播间
      playerLive(roomid) {
        let path = this.$util.livePlayerPageUrl(roomid,false)
        this.$buriedPoint.diyReportDecorationComponentInteractionEvent({diy_template_name:'LiveInfo',diy_text:'',diy_link:path,diy_image:''})
        this.$util.redirectTo(path);
      },
			getLiveStatus(){
				// #ifdef MP-WEIXIN
				let livePlayer = requirePlugin('live-player-plugin');

				livePlayer.getLiveStatus({ room_id: this.liveInfo.roomid })
					.then(res => {
						const liveStatus = res.liveStatus;
						if (liveStatus && liveStatus != this.liveInfo.live_status) {
							this.changeLiveStatus(liveStatus)
						}
					})
					.catch(err => {
						console.log('get live status', err)
					})

				// 往后间隔1分钟或更慢的频率去轮询获取直播状态
				var timer = setInterval(() => {
					livePlayer.getLiveStatus({ room_id: this.liveInfo.roomid })
						.then(res => {
							const liveStatus = res.liveStatus;
							if (liveStatus && liveStatus != this.liveInfo.live_status) {
								this.changeLiveStatus(liveStatus)
							}
							if (this.$util.inArray(liveStatus, [103, 104, 106, 107])) {
								clearInterval(timer);
							}
						})
						.catch(err => {
							console.log('get live status', err)
						})
				}, 60000)
				// #endif
			},
			changeLiveStatus(status){
				this.$api.sendRequest({
					url: '/live/api/live/modifyLiveStatus',
					data: {
						room_id: this.liveInfo.roomid,
						status: status
					},
					success: res => {
						if (res.code == 0) {
							this.getLiveInfo();
						}
					},
				})
			},
      // 打开分享弹出层
      openSharePopup(data) {
        this.$buriedPoint.diyReportDecorationComponentInteractionEvent({diy_template_name:'LiveInfo',diy_text:'分享',diy_link:'',diy_image:''})
        // #ifdef MP-WEIXIN
        // if(this.$refs.sharePopup) this.$refs.sharePopup.open();
        this.newCommQrcode(data);
        // #endif
        // #ifdef H5
        this.$util.showToast({
          title:'请在小程序使用此功能！'
        })
        // #endif
      },
      // 获取二维码；调取海报
      newCommQrcode(data) {
        // 模拟二维码参数 scene=rd%3D10%26s%3D206%26r%3D343
        let querys = {
          room_id: data.roomid,
          shop_id: uni.getStorageSync("shop_id"),
        };
        if (uni.getStorageSync("member_id")) {
          querys.recommend_member_id = uni.getStorageSync("member_id");
        }
        let scene = query_to_scene(querys);
        let userInfo = uni.getStorageSync("userInfo");
        if (uni.getStorageSync("token") == "") {
          userInfo = "";
        }
        this.isShowCanvas = false;
        this.$api.sendRequest({
          url: "/api/Website/newCommQrcode",
          data: {
            // path: "pages/index/index/index", // 测试用
            path: 'pages/live-player-plugin',
            scene,
          },
          success: (res) => {
            if (res.code == 0) {
              if (userInfo == "") {
                let user = {};
                this.$api.sendRequest({
                  url: "/api/member/info",
                  success: (info) => {
                    if (info.code == 0 && uni.getStorageSync("token") != "") {
                      user = {
                        headimg: info.data.headimg,
                        nickname: info.data.nickname,
                      };
                    } else {
                      user = {
                        headimg:
                            this.$util.img("/upload/default/default_img/head.png"),
                        nickname: "请登录",
                      };
                    }
                    this.drawCanvas(res.data.qrcodeUrl, data, user);
                    setTimeout(() => {
                      if (this.$refs.sharePopup) this.$refs.sharePopup.open();
                    }, 0);
                  },
                });
              } else {
                this.drawCanvas(res.data.qrcodeUrl, data, userInfo);
                setTimeout(() => {
                  if (this.$refs.sharePopup) this.$refs.sharePopup.open();
                }, 0);
              }
            } else {
              this.$util.showToast({
                title: res.message,
              });
            }
          },
        });
      },
      drawCanvas(qrcodeUrl, data, user) {
        this.sharePopupOptions = [
          {
            background: "#fff",
            x: 0,
            y: 0,
            width: 634,
            height: 832,
            type: "image",
          },
          {
            // 头图
            path: this.$util.img(data.share_img),
            x: 0,
            y: 0,
            width: 634,
            height: 507,
            type: "image",
          },
          {
            // 头像
            path: user.headimg,
            radius: 36,
            x: 40,
            y: 567,
            width: 56,
            height: 56,
            type: "image",
          },
          {
            text: user.nickname,
            size: 28,
            color: "#333",
            fontWeight: "bold",
            x: 130,
            y: 582,
            type: "text",
          },
          {
            text: data.name,
            size: 26,
            color: "#999",
            x: 130,
            y: 630,
            width: 310,
            lineNum: 2,
            lineHeight: 34,
            type: "text",
          },
          {
            path: qrcodeUrl,
            x: 466,
            y: 536,
            width: 128,
            height: 128,
            type: "image",
          },
          {
            background: "#F8F8F8",
            x: 0,
            y: 692,
            width: 634,
            height: 140,
            type: "image",
          },
          {
            path: this.$util.img("public/static/youpin/qrcodetips.png"),
            x: 40,
            y: 710,
            width: 554,
            height: 106,
            type: "image",
          },
        ];
        this.isShowCanvas = true;
      },
      /*提供分享的数据*/
      toShareAppMessage(res){
        let share_data = this.$util.unifySharePageParams("/pages/live-player-plugin","先迈商城",'',
            {room_id:this.liveInfo.roomid},this.$util.img(this.liveInfo.share_img))
        return share_data
      }
		}
	};
</script>

<style lang="scss">
.live-box{
  margin-top: 20rpx;
}
	.live-wrap{
    width: 100%;
    border-radius: 20rpx;
    padding: 20rpx;
    box-sizing: border-box;
		overflow: hidden;
    background-size: 100% 100%;
    background-repeat: no-repeat;
    display: flex;
    &-left{
      width: 264rpx;
      height: 352rpx;
      border-radius: 20rpx;
      position: relative;
      &-img{
        width: 100%;
        height: 100%;
        border-radius: 20rpx;
      }
      &-icon{
        width: 92rpx;
        //height: 28rpx;
        position: absolute;
        left: 8rpx;
        top: 8rpx;
      }
    }
    &-right{
      width: 424rpx;
      padding-left: 28rpx;
      box-sizing: border-box;
      padding-top: 12rpx;
      &-title{
        font-size: 32rpx;
        font-weight: 700;
        line-height: 43.9rpx;
      }
      &-name{
        font-size: 24rpx;
        font-weight: 400;
        line-height: 27.94rpx;
        margin-top: 8rpx;
      }
      &-list{
        height: 112rpx;
        margin-top: 30rpx;
        &-one{
          width: 112rpx;
          height: 112rpx;
          border-radius: 10rpx;
          &:not(:first-child){
            margin-left: 16rpx;
          }
        }
      }
      &-op{
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-top: 44rpx;
        &-share{
          margin: 0;
          width: 172rpx;
          height: 64rpx;
          border-radius: 40rpx;
          background: rgba(242, 242, 242, 1);
          display: flex;
          justify-content: center;
          align-items: center;
          font-size: 28rpx;
          font-weight: 400;
          line-height: 32.6rpx;
          color: rgba(56, 56, 56, 1);
          &-icon{
            width: 28rpx;
            height: 28rpx;
            margin-right: 4rpx;
          }
        }
        &-play{
          width: 172rpx;
          height: 64rpx;
          border-radius: 40rpx;
          background: linear-gradient(225deg, rgba(246, 166, 186, 1) 0%, rgba(199, 194, 252, 1) 100%);
          display: flex;
          justify-content: center;
          align-items: center;
          font-size: 28rpx;
          font-weight: 400;
          line-height: 32.6rpx;
          color: rgba(255, 255, 255, 1);
          position: relative;
          &-icon{
            width: 28rpx;
            height: 28rpx;
            margin-right: 4rpx;
          }
          &-slot{
            position: absolute;
            left: 0;
            top: 0;
          }
        }
      }
    }
	}
</style>
<style scoped>
	.coupon-all>>>.uni-scroll-view::-webkit-scrollbar {
		display: none;
	}
</style>
