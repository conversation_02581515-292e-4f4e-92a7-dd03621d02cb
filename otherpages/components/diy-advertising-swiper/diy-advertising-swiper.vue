<template>
	<view class="swiper" :class="{'swiper--not' : config.selectedTemplate == 'carousel-posters-2'}" v-if="ads.length>0">
		<swiper :class="{'swiper--list' : config.selectedTemplate == 'carousel-posters', 'swiper--list--two' : config.selectedTemplate == 'carousel-posters-2'}"
            :indicator-dots="false" indicator-active-color="rgba(86, 98, 130, 1)"
            :current="current" @change="change" @animationfinish="animationfinishChange"
			:autoplay="autoplay" :circular="true">
			<swiper-item v-for="(ad,index) in ads" v-bind:key="index"
                   :class="{'swiper--list--item' : config.selectedTemplate == 'carousel-posters', 'swiper--list--two--item' : config.selectedTemplate == 'carousel-posters-2'}"
				v-on:click="toAd(ad)">
				<image v-bind:src="$util.img(ad.image_url)" @error="imageError(ads,index)"></image>
			</swiper-item>
		</swiper>
    <view class="swiper-dots" v-if="ads.length > 1">
      <text class="swiper-dots-one" v-for="(item,index) in ads" :key="index" :class="{'swiper-dots-one-active': index == current}"></text>
    </view>
	</view>
</template>

<script>
	import apiurls from "@/common/js/apiurls";

  export default {
		props:{
			ads:Array,
      imageError:Function,
      config:{
        type:Object,
        default(){
          return {}
        }
      },
      topHeight:{
        type:Number,
        default:0
      },
      scrollTop:{
        type:Number,
        default:0,
      },
      autoplay:{
        type:Boolean,
        default() {
          return true;
        },
      }
		},
		data() {
			return {
        current:0,
        colorList:[],
        scrollOverImg: false
			}
		},
    watch:{
      ads:{
        async handler(newVal,oldVal){
          if(JSON.stringify(newVal) != JSON.stringify(oldVal)){
            this.resetCurrentIndex()
            await this.generateColorList(true)
          }
        },
        deep: true, // 深度监听
        immediate: false  // 第一次改变就执行
      },
      scrollTop(newVal,oldVal){
        if(this.config.selectedTemplate == 'carousel-posters-2'){
          this.getPosition()
        }
      }
    },
		methods: {
			toAd(ad) {
        this.$buriedPoint.diyReportAdEvent(
            {diy_material_path:ad.image_url,diy_ad_type:'image',diy_target_page:ad.banner_url,diy_ad_id:ad.id,diy_action_type:'click'})
        this.$buriedPoint.diyReportDecorationComponentInteractionEvent({diy_template_name:'ImageAds',diy_text:ad.banner_name,diy_link:ad.banner_url,diy_image:ad.image_url})
        this.$emit('clickBanner',ad.id)
				if (ad.banner_url) {
					this.$util.diyCompateRedirectTo({
						wap_url: ad.banner_url
					})
				}
			},
      async getDominantColor(imagePath){
        let res = await this.$api.sendRequest({
          url:apiurls.getDominantColorUrl,
          data:{
            image_url:imagePath,
            image_crop: 1
          },
          async: false,
        })
        if(res.code == 0){
          return this.$util.generateRGBAColors(res.data.dominantColor)[7]
        }else{
          return ''
        }
      },
      async generateColorList(force = false){
        if(this.config.selectedTemplate != 'carousel-posters-2'){
          return
        }
        if(!force && this.colorList.length){
          return
        }
        if(this.ads && this.ads.length > 0){
          this.colorList = [];
          for (let i = 0; i < this.ads.length; i++) {
            let color = await this.getDominantColor(this.$util.img(this.ads[i].image_url));
            this.colorList.push(color)
          }
          this.toEmit(this.current);
        }
      },
      getPosition() {
        const query = uni.createSelectorQuery().in(this);
        query.select('.swiper--not').boundingClientRect(data => {
          if (data) {
            if((data.height - this.topHeight + data.top) <= 0 ){
              if(!this.scrollOverImg){
                this.scrollOverImg = true
                this.$emit('headerColorChange','var(--custom-brand-color)')
              }
            }else{
              if(this.scrollOverImg){
                this.scrollOverImg = false
                this.toEmit(this.current);
              }
            }
            // this.stickyTop = data.top;
            // if(data.top == this.topHeight){
            //   this.is_scroll_to_top = true
            // }else{
            //   this.is_scroll_to_top = false
            // }
          }
        }).exec();
      },
      change(item){
        if(this.config.selectedTemplate != 'carousel-posters-2'){
          return
        }
        this.toEmit(item.detail.current);
      },
      animationfinishChange(item){
        this.current = item.detail.current;
      },
      toEmit(index){
        if(!this.scrollOverImg){
          if(this.colorList[index]){
            this.$emit('headerColorChange',this.colorList[index]);
          }
        }
      },
      resetCurrentIndex(){
        this.current = 0
      }
		}
	}
</script>

<style lang="scss">
	.swiper {
		margin-top: 20rpx;
    position: relative;
    &--not{
      margin-top: 0;
    }
		&--list {
			height: 228rpx;
			&--item {
				image {
					width: 100%;
					height: 100%;
					border-radius: 20rpx;
				}
			}
		}
    &--list--two{
      height: 1000rpx;
      width: 100vw;
      margin-left: -20rpx;
      &--item {
        image {
          width: 100%;
          height: 100%;
        }
      }
    }
	}
  /deep/ .uni-swiper-dot{
    width: 16rpx;
    height: 6rpx;
    border-radius: 20rpx;
    background: rgba(255, 255, 255, 0.5);
  }
  .swiper-dots{
    width: 100%;
    position: absolute;
    left: 0;
    bottom: 30rpx;
    display: flex;
    justify-content: center;
    align-items: center;
    &-one{
      width: 20rpx;
      height: 6rpx;
      border-radius: 20rpx;
      background: rgba(255, 255, 255, 0.5);
      &-active{
        background: var(--custom-brand-color);
      }
      &:not(:last-child){
        margin-right: 10rpx;
      }
    }
  }
</style>
