<!--幻灯片组件-->
<template>
  <view class="slide-show" :style="{width: data.backgroundImgSize.width, height: data.backgroundImgSize.height, backgroundImage: `url(${data.backgroundImg})`}">
    <swiper class="swiper"
            :indicator-dots="false"
            :autoplay="autoplay"
            :interval="data.playTime * 1000"
            :previous-margin="previousMargin"
            :next-margin="nextMargin"
            :current="current"
            :circular="true"
            :style="{height: data.imgList[0].height}"
            @change="onSwiperChange"
            @animationfinish="animationFinish"
            @transition="onTransition"
    >
      <swiper-item v-for="(item, index) in data.imgList" :key="index" class="swiper-item">
        <view class="swiper-one"
              :class="{'active': index === current, 'inactive': index !== current}"
              :style="{
                width: (index != current ? `calc(${item.width} * ${scale})` : item.width),
                height: (index != current ? `calc(${item.height} * ${scale})` : item.height),
                transform: getSwiperItemTransform(index)
              }"
              @click="toAd(item)"
        >
          <image :src="item.imgUrl" mode="aspectFit" class="slide-image"></image>
        </view>
      </swiper-item>
    </swiper>
  </view>
</template>

<script>
export default {
  name: "diy-slide-show",
  props: {
    value: {
      type: Object,
      default: () => {
        return {};
      }
    }
  },
  data(){
    return{
      data: {},
      current: 0,
      scale: 0.8, // 增加缩放比例使两侧图片更大
      previousMargin: '200rpx', // 显著减小外边距，让两侧图片显示更多
      nextMargin: '200rpx', // 显著减小外边距，让两侧图片显示更多
      autoplay: true, // 默认开启自动播放
      animating: false, // 控制动画状态
      dx: 0, // 用于滑动过渡动画
      animationData: null, // 动画数据
    }
  },
  created() {
    this.handlePx();
  },
  methods:{
    handlePx(){
      let tmp = JSON.parse(JSON.stringify(this.value));
      tmp.backgroundImgSize.width = tmp.backgroundImgSize.width.replace('px', 'rpx');
      tmp.backgroundImgSize.height = tmp.backgroundImgSize.height.replace('px', 'rpx');
      tmp.imgList.forEach(item => {
        item.width = item.width.replace('px', 'rpx');
        item.height = item.height.replace('px', 'rpx');
      });
      this.data = tmp;
    },
    onSwiperChange(e) {
      this.animating = true;
      this.current = e.detail.current;
    },
    animationFinish() {
      this.animating = false;
      this.dx = 0;
    },
    onTransition(e) {
      // 获取滑动的偏移量，用于实现过渡动画
      this.dx = e.detail.dx;
    },
    getSwiperItemTransform(index) {
      // 根据滑动的偏移量计算每个item的变换
      if (this.animating && this.dx !== 0) {
        // 当前显示的item
        if (index === this.current) {
          return `translateY(${Math.abs(this.dx) * 0.05}rpx) scale(${1 - Math.abs(this.dx) * 0.0003})`;
        }
        // 其他item
        return `scale(${this.scale + Math.abs(this.dx) * 0.0003})`;
      }

      // 默认状态
      if (index === this.current) {
        return 'translateY(-5rpx) scale(1)';
      }
      return `scale(${this.scale})`;
    },
    toAd(ad) {
      this.$buriedPoint.diyReportDecorationComponentInteractionEvent({
        diy_template_name: 'SlideShow',
        diy_text: '',
        diy_link: ad.link,
        diy_image: ad.imgUrl
      })
      if (ad.link) {
        this.$util.diyCompateRedirectTo({
          wap_url: ad.link
        })
      }
    }
  }
}
</script>

<style scoped lang="scss">
.slide-show{
  margin-left: -20rpx;
  background-size: 100% 100%;
  background-repeat: no-repeat;
  display: flex;
  align-items: center;
  position: relative;
  overflow: hidden;
}
.swiper{
  width: 100%;
}
.swiper-item{
  display: flex;
  justify-content: center;
  align-items: center;
}
.swiper-one {
  transition: all 0.5s cubic-bezier(0.215, 0.610, 0.355, 1.000);
  transform-origin: center center;
  border-radius: 12rpx;
  overflow: hidden;
  will-change: transform, opacity, box-shadow;

  &.active {
    box-shadow: 0 10rpx 25rpx rgba(0, 0, 0, 0);
  }

  &.inactive {
    filter: brightness(0.92);
  }

  .slide-image {
    width: 100%;
    height: 100%;
    transition: all 0.3s ease;
    will-change: transform;
  }
}
</style>
