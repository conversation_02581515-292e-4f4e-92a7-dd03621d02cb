<template>
	<view class="active-ads">
		<view class="active-ads--one" v-if="activeAds.top1 || activeAds.top2">
			<image v-if="activeAds.top1" v-bind:src="$util.img(activeAds.top1.image_url)"
				@error="imageError(activeAds,'top1')" v-on:click="toAd(activeAds.top1.banner_url)">
			</image>
			<image v-if="activeAds.top2" v-bind:src="$util.img(activeAds.top2.image_url)"
				@error="imageError(activeAds,'top2')" v-on:click="toAd(activeAds.top2.banner_url)">
			</image>
		</view>
		<view class="active-ads--two"
			v-if="activeAds.bottom1 || activeAds.bottom2 || activeAds.bottom3">
			<view class="active-ads--two--left" v-if="activeAds.bottom1">
				<image v-bind:src="$util.img(activeAds.bottom1.image_url)"
					@error="imageError(activeAds,'bottom1')"
					v-on:click="toAd(activeAds.bottom1.banner_url)"></image>
			</view>
			<view class="active-ads--two--right">
				<image v-if="activeAds.bottom2" v-bind:src="$util.img(activeAds.bottom2.image_url)"
					@error="imageError(activeAds,'bottom2')"
					v-on:click="toAd(activeAds.bottom2.banner_url)"></image>
				<image v-if="activeAds.bottom3" v-bind:src="$util.img(activeAds.bottom3.image_url)"
					@error="imageError(activeAds,'bottom3')"
					v-on:click="toAd(activeAds.bottom3.banner_url)"></image>
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		props:{
			activeAds:Object
		},
		data() {
			return {
				
			}
		},
		methods: {
			toAd(url) {
				if (url) {
					this.$util.diyRedirectTo({
						wap_url: url
					})
				}
			},
		}
	}
</script>

<style lang="scss" scoped>
	.active-ads {
		margin-top: 20rpx;
	
		&--one {
			display: flex;
			justify-content: space-between;
			align-items: center;
			margin-bottom: 14rpx;
	
			image {
				width: 344rpx;
				height: 165rpx;
				background: #FFFFFF;
				border-radius: 10rpx;
				display: block;
			}
		}
	
		&--two {
			display: flex;
			justify-content: space-between;
			align-items: center;
	
			&--left {
				width: 344rpx;
	
				image {
					width: 344rpx;
					height: 344rpx;
					border-radius: 10rpx;
					display: block;
				}
			}
	
			&--right {
				width: 344rpx;
				height: 344rpx;
				display: flex;
				flex-direction: column;
				justify-content: space-between;
	
				image {
					width: 344rpx;
					height: 165rpx;
					border-radius: 10rpx;
					display: block;
				}
			}
		}
	}
</style>
