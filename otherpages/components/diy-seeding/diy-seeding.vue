<template>
  <view class="diy-seeding" v-if="dataList && dataList.length" :style="{backgroundColor: value.item.backgroundColor,backgroundImage: `url(${value.item.backgroundImg})`}">
    <view class="diy-seeding-top">
      <view class="seeding-title">
        <text class="seeding-title-name" :style="{color: value.item.textColor}">{{name}}</text>
      </view>
      <view class="seeding-more" :style="{color: value.item.textColor}" @click="toMore()">{{moreText}}
        <uni-icons type="forward" size="24rpx" :color="value.item.textColor" class="seeding-more-icon"></uni-icons>
      </view>
    </view>
    <scroll-view class="diy-seeding-box" scroll-x="true">
      <view class="seeding-box-item" v-for="(item, i) in dataList" :key="i" @click="toDetail(item)">
        <view class="seeding-item">
          <view class="seeding-item-image">
            <image :src="$util.img(item.image || $util.getDefaultImage().default_goods_img)" @error="imageError(i)" mode='aspectFill' class="seeding-item-image-bg"></image>
            <view class="seeding-item-image-star">
              <image :src="$util.img('public/static/youpin/like-star.png')" class="seeding-item-image-star-like"></image>
              <text>{{item.like_num}}</text>
              <canvas :id="`seeding_revenue_id_${i}`" type="2d" class="seeding-item-image-star-revenue" v-if="playIndex==i"></canvas>
            </view>
          </view>
        </view>
      </view>
    </scroll-view>
  </view>
</template>

<script>
import UniIcons from "@/components/uni-icons/uni-icons.vue";
// #ifdef MP-WEIXIN
import lottie from 'lottie-miniprogram';
// #endif
// #ifdef H5
import lottie from 'lottie-web';
// #endif
let ani = null; // 必须放在外面，uni里不要挂在this上,否则会触发循环引用的报错

export default {
  components: {
    UniIcons

  },
  name: 'diy-seeding',
  props: {
    value: {
      type: Object
    },
  },
  filters:{
    getInteger(value){
      return String(parseFloat(value)).split('.')[0]
    },
    getDecimals(value){
      return '.'+String(parseFloat(value).toFixed(2)).split('.')[1]
    }
  },
  data() {
    return {
      name: '',
      moreText: '',
      dataList:[],
      animationData:{},
      playIndex:0,
      playObj:null,
    };
  },
  async created() {
    if (this.value.item) {
      let it = this.value.item
      it.name && (this.name = it.name)
      it.moreText && (this.moreText = it.moreText)
    }
  },
  async mounted(){
    await this.getAnimationData('/public/static/youpin/seeding_like.json')
    await this.getData()
    this.playAnimation()
  },
  beforeDestroy(){
    clearInterval(this.playObj)
  },
  methods: {
    async getData() {
      try{
        let res = await this.$api.sendRequest({
          url: this.$apiUrl.usershareexperienceListUrl,
          async: false,
          data: {
            page: 1,
            page_size: 4,
          },
        });
        this.dataList = res.data.list
      }catch (e) {

      }
    },
    toMore() {
      this.$buriedPoint.diyReportDecorationComponentInteractionEvent({diy_template_name:'Seeding',diy_text:this.moreText,diy_link:this.value.item.moreUrl ? this.value.item.moreUrl:'/promotionpages/seeding/seeding-list/seeding-list',diy_image:''})
      this.$util.diyCompateRedirectTo({
        wap_url: this.value.item.moreUrl ? this.value.item.moreUrl:'/promotionpages/seeding/seeding-list/seeding-list'
      });
    },
    toDetail(res) {
      this.$buriedPoint.diyReportDecorationComponentInteractionEvent({diy_template_name:'Seeding',diy_text:res.title,diy_link:`/promotionpages/seeding/seeding_detail/seeding_detail?id=${res.id}`,diy_image:this.$util.img(res.image)})
      this.$util.diyCompateRedirectTo({
        wap_url: `/promotionpages/seeding/seeding_detail/seeding_detail?id=${res.id}`
      });
    },
    imageError(index) {
      this.dataList[index].image = this.$util.getDefaultImage().default_goods_img;
      this.$forceUpdate();
    },
    onFinish() {
      this.$emit('finish')
    },
    async getAnimationData(animationDataUrl){
      try {
        let res = await this.$api.sendRequest({
          url: animationDataUrl,
          async: false
        })
        this.animationData = res
      }catch (e) {

      }
    },
    playAnimation(){
      if(this.dataList.length){
        this.playObj = setInterval(()=>{
          this.revenueAnimation()
        },3000)
      }
    },
    revenueAnimation(){
      setTimeout(()=>{
        // #ifdef MP-WEIXIN
        uni.createSelectorQuery().in(this).select(`#seeding_revenue_id_${this.playIndex}`).node(res => {
          // console.log(res); // 节点对应的 Canvas 实例。
          const canvas = res.node;
          const context = canvas.getContext('2d');
          canvas.width = 105;
          canvas.height = 140;
          lottie.setup(canvas);
          ani = lottie.loadAnimation({
            loop: false,
            autoplay: false,
            animationData: this.animationData,
            rendererSettings: {
              context
            }
          });
        }).exec(()=>{
          ani.play();
          ani.addEventListener('complete', () => {
            ani.destroy()
            if(this.playIndex<this.dataList.length-1) {
              this.playIndex++
            }else{
              this.playIndex=0
            }
          })
        });

        // #endif
        // #ifdef H5
        const canvas = document.querySelector(`#seeding_revenue_id_${this.playIndex} canvas`);
        if(canvas && canvas.style){
          canvas.width = 105;
          canvas.height = 140;
          ani = lottie.loadAnimation({
            loop: false,
            autoplay: false,
            container: canvas,
            renderer: 'canvas',
            animationData: this.animationData,
            // assetsPath: '/mini-h5/static/images/'
          });
          ani.play();
          ani.addEventListener('complete', () => {
            ani.destroy()
            if(this.playIndex<this.dataList.length-1) {
              this.playIndex++
            }else{
              this.playIndex=0
            }
          })
        }
        // #endif
      },300)
    }
  }
};
</script>

<style lang="scss" scoped>
.diy-seeding {
  margin-top: 20rpx;
  border-radius: 20rpx;
  width: 100%;
  padding: $ns-padding 0 $ns-padding $ns-padding;
  box-sizing: border-box;
  background-size: 100% 100%;
  background-repeat: no-repeat;
}

.diy-seeding-top {
  width: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-right: 20rpx;
  box-sizing: border-box;

  .seeding-title {
    display: flex;
    align-items: center;

    .seeding-title-name {
      font-size: 36rpx;
      font-weight: bolder;
      margin-right: $ns-margin;
    }

  }

  .seeding-more {
    font-size: $ns-font-size-xm;
    display: flex;
    align-items: center;
    &-icon{
      border: 1px solid;
      border-radius: 50%;
      margin-top: 2rpx;
      height: 26rpx;
      width: 26rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-left: 6rpx;
    }
  }
}

.diy-seeding-box {
  width: 100%;
  /*white-space 不能丢  */
  white-space: nowrap;
  box-sizing: border-box;
  margin-top: 28rpx;
}

.seeding-box-item {
  width: 210rpx;
  height: 100%;
  vertical-align: top;
  display: inline-block;
  background: #ffffff;
  border-radius: 20rpx;
  margin-right: 12rpx;
  box-sizing: border-box;

  .seeding-item {
    width: 100%;
    height: 100%;
  }

  .seeding-item-image {
    width: 100%;
    height: 280rpx;
    border-radius: 20rpx;
    overflow: hidden;
    display: flex;
    justify-content: center;
    align-items: center;
    position: relative;
    &-bg {
      width: 100%;
      height: 100%;
      padding: 0;
      margin: 0;
      display: block;
      border-radius: 20rpx;
    }
    &-star{
      height: 56rpx;
      border-radius: 100rpx;
      background: rgba(0, 0, 0, 0.4);
      position: absolute;
      right: 12rpx;
      bottom: 12rpx;
      display: flex;
      justify-content: center;
      align-items: center;
      padding: 0 16rpx;
      box-sizing: border-box;
      &-like{
        width: 38rpx;
        height: 34rpx;
        margin-right: 4rpx;
      }
      text{
        font-size: 26rpx;
        font-weight: 400;
        line-height: 18.62rpx;
        color: rgba(255, 255, 255, 1);
        padding-top: 2rpx;
        box-sizing: border-box;
      }
      &-revenue{
        width: 105rpx;
        height: 140rpx;
        position: absolute;
        left: -18rpx;
        bottom:-2rpx;
      }
    }

  }
  .seeding-item-bottom{
    display: flex;
    justify-content: space-between;
    margin-top: 14rpx;
    &-left{
    }
    &-right{
      display: flex;
      align-items: flex-end;
      line-height: 40rpx;
      padding-bottom: 6rpx;
      box-sizing: border-box;
    }
  }
  .seeding-item-original-price{
    font-size: 18rpx;
    font-weight: 400;
    line-height: 20rpx;
    text-decoration-line: line-through;
    color: rgba(166, 166, 166, 1);
  }
  .seeding-item-new-price {
    font-size: 24rpx;
    font-weight: 700;
    line-height: 36rpx;
    color: var(--custom-brand-color);


    text:first-child {
      font-size: 20rpx;
    }
    &-integer{
      font-size: 36rpx;
    }
  }

}
</style>
