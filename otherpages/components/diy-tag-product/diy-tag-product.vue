<template>
	<view>
    <view class="tagproducts" v-if="categoryGoodsList.length>0">
      <view class="tagproducts--one" v-for="(item,index) in categoryGoodsList" v-bind:key="index"
            @click="$util.toProductDetail(item)">
        <view class="tagproducts--one--img">
          <image :src="$util.img(item.goods_image)"
                 @error="imageError(categoryGoodsList,index)" mode='aspectFit'></image>
          <!-- 秒杀商品显示已抢光，其他显示已售罄 -->
          <image :src="$util.img('public/static/youpin/product-over.png')" class="over"
                 v-if="item.goods_stock==0 && item.is_seckill == 1"></image>
          <image :src="$util.img('public/static/youpin/product-sell-out.png')" class="over"
                 v-if="item.goods_stock==0 && item.is_seckill == 0"></image>
        </view>
        <view class="tagproducts--one--name"
              :style="item.is_recommend ? 'padding-left: 60rpx;' :''"><text
            v-if="item.is_recommend">推荐</text>{{item.goods_name}}</view>
        <view class="tagproducts--one--price">
          <view><text>￥</text>{{item.retail_price}}</view>
          <view>￥{{item.market_price}}</view>
        </view>
      </view>
    </view>
    <template v-else>
      <ns-empty :isIndex="!1" v-if="!isLoading"></ns-empty>
    </template>
  </view>
</template>

<script>
	import apiurls from "@/common/js/apiurls";

	export default {
		name:'diy-tag-product',
		prop: {

		},
		data() {
			return {
				categoryGoodsList:[],
        isLoading: true
			}
		},
		methods: {
			async getCategoryGoodsList(mescroll,data) {

				let cid = data.category_id;
				let category_level = data.level;

				if (!cid) {
					return
				}
				let res = await this.$api.sendRequest({
					url: apiurls.goodsListUrl,
					async: false,
					data: {
						shop_id: this.shop_id,
						category_id: cid,
						category_level,
						page_size: mescroll.size,
						page: mescroll.num,
					},
				});
				if (res.code != 0) {
					uni.showToast({
						title: res.message,
						mask: true,
						icon: "none",
						duration: 3000
					});
					return
				}
				let newArr = res.data.list;
				mescroll.endSuccess(newArr.length);
				//设置列表数据
				if (mescroll.num == 1) this.categoryGoodsList = []; //如果是第一页需手动制空列表
				this.categoryGoodsList = this.categoryGoodsList.concat(newArr); //追加新数据
        this.isLoading = false;
				this.$buriedPoint.exposeGoods(newArr, 'sku_id')
			},
			imageError(data, index) {
				if (data instanceof Object && data[index] && data[index].goods_image) {
					data[index].goods_image = this.$util.getDefaultImage().default_goods_img;
				}
				this.$forceUpdate();
			},
		}
	}
</script>

<style lang="scss">
	.tagproducts {
		margin-top: 24rpx;
		display: flex;
		flex-wrap: wrap;
		justify-content: space-between;
		margin-bottom: 20rpx;
		margin-bottom: calc(20rpx + constant(safe-area-inset-bottom));
		margin-bottom: calc(20rpx + env(safe-area-inset-bottom));

		&--one {
			width: 343rpx;
			background: #FFFFFF;
			border-radius: 8rpx;
			margin-bottom: 24rpx;

			&--img {
				width: 100%;
				height: 343rpx;
				position: relative;

				image {
					width: 100%;
					height: 100%;
					border-radius: 8rpx 8rpx 0px 0px;

					&.over {
						width: 120rpx;
						height: 120rpx;
						position: absolute;
						left: 50%;
						top: 50%;
						transform: translate(-50%, -50%);
					}
				}
			}

			&--name {
				padding: 0 16rpx;
				box-sizing: border-box;
				font-size: 26rpx;
				font-weight: 500;
				color: #343434;
				word-break: break-all;
				text-overflow: ellipsis; //显示为省略号
				display: -webkit-box; //对象作为伸缩盒子模型显示
				-webkit-box-orient: vertical; //设置或检索伸缩盒对象的子元素的排列方式
				-webkit-line-clamp: 2; //显示行数## 标题文字 ##
				overflow: hidden;
				margin-top: 20rpx;
				position: relative;
				line-height: 1.4;
				min-height: 74rpx;

				text {
					position: absolute;
					left: 0;
					top: 0rpx;
					display: inline-block;
					width: 48rpx;
					height: 32rpx;
					line-height: 32rpx;
					text-align: center;
					font-size: 20rpx;
					font-weight: 500;
					color: #FFFFFF;
					background: linear-gradient(55deg, var(--custom-brand-color-80), var(--custom-brand-color));
					border-radius: 4rpx;
				}
			}

			&--price {
				padding: 0 16rpx;
				box-sizing: border-box;
				display: flex;
				align-items: center;
				margin-top: 30rpx;

				view:first-child {
					font-size: 36rpx;
					font-family: PingFang SC;
					font-weight: bold;
					color: var(--custom-brand-color);

					text {
						font-size: 26rpx;
					}
				}

				view:last-child {
					font-size: 24rpx;
					font-weight: 500;
					text-decoration: line-through;
					color: #9A9A9A;
					margin-left: 8rpx;
				}
			}
		}
	}
</style>
