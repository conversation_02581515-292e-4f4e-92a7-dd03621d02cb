<template>
	<view class="diy-seckill" v-if="dataList.list && dataList.list.length">
		<view class="diy-seckill-top">
			<view class="seckill-title">
				<text></text>
				<text class="seckill-title-name">{{name}}</text>
			</view>
			<view class="seckill-more ns-text-color" @click="toMore()">{{moreText}}</view>
		</view>
		<scroll-view class="diy-seckill-box" scroll-x="true">
			<view class="seckill-box-item" v-for="(item, i) in dataList.list" :key="i" @click="toDetail(item)">
				<view class="seckill-item">
					<view class="seckill-item-image">
							<view class="goods_img">
							<image :src="$util.img(item.goods_image)" @error="imageError(i)" mode='aspectFit'></image>
				 			 <image :src="$util.img('public/static/youpin/product-sell-out.png')" class="goods_img-over"  v-if="item.goods_stock == 0"></image>
							</view>

					</view>
					<view class="seckill-item-new-name">{{item.goods_name }}</view>
					<view class="seckill-item-new-price ns-text-color">
						<text>￥</text>
						{{ item.retail_price }}
					</view>
					<text class="seckill-item-old-price">￥{{ item.market_price }}</text>
					<view class="song_maidou">
						<view class="huanhang">
							<text class="song">送迈豆</text>
							<text class="maidounum">{{item.send_maidou}}</text>
						</view>
					</view>
				</view>
			</view>
		</scroll-view>
	</view>
</template>

<script>
	export default {
		components: {

		},
		name: 'diy-maidou',
		props: {
			value: {
				type: Object
			},
			dataList:{
				type: Object,
				default: () => {
					return {};
				}
			},
		},
		data() {
			return {
				name: '迈豆专区',
				moreText: '查看更多'
			};
		},
		created() {
			if (this.value.item) {
				let it = this.value.item
				it.name && (this.name = it.name)
				it.moreText && (this.moreText = it.moreText)
			}
		},
		methods: {
			toMore() {
        this.$buriedPoint.diyReportDecorationComponentInteractionEvent({diy_template_name:'Maidou',diy_text:this.moreText,diy_link:this.value.item.moreUrl ? this.value.item.moreUrl:'/promotionpages/maidou/list/list',diy_image:''})
				this.$util.diyCompateRedirectTo({
					wap_url: this.value.item.moreUrl ? this.value.item.moreUrl:'/promotionpages/maidou/list/list'
				});
			},
			toDetail(res) {
        this.$buriedPoint.diyReportDecorationComponentInteractionEvent({diy_template_name:'Maidou',diy_text:res.goods_name,diy_link:`/pages/goods/detail/detail?sku_id=${res.sku_id}`,diy_image:this.$util.img(res.sku_image)})
				this.$util.diyCompateRedirectTo({
					wap_url: `/pages/goods/detail/detail?sku_id=${res.sku_id}`
				});
			},
			imageError(index) {
				this.dataList.list[index].goods_image = this.$util.getDefaultImage().default_goods_img;
				this.$forceUpdate();
			},
			onFinish(){
				this.$emit('finish')
			},
		}
	};
</script>

<style lang="scss" scoped>
	.diy-seckill {
		margin-top:20rpx;
		border-radius: 20rpx;
		width: 100%;
		padding: $ns-padding;
		box-sizing: border-box;
		background-color: #ffffff;
	}

	.diy-seckill-top {
		width: 100%;
		display: flex;
		justify-content: space-between;
		align-items: center;

		.seckill-title {
			display: flex;
			align-items: center;

			text:first-child{
				width: 6rpx;
				height: 36rpx;
				background: var(--custom-brand-color);
				border-radius: 3rpx;
				margin-right: 17rpx;
				display: inline-block;
			}

			.seckill-title-name {
				font-size: 30rpx;
				font-weight: bold;
				margin-right: $ns-margin;
			}

			/* .seckill-titie-tit{
				border-radius: 50rpx;
				background-color:$base-color;
				color:#ffffff;
				font-size: 22rpx;
				padding: 0 15rpx;
				line-height: 1.6;
				font-size: $ns-font-size-sm;
			} */

		}

		.seckill-more {
			font-size: $ns-font-size-sm;
		}

		.seckill-more::after {
			font-family: 'iconfont';
			content: '\eb93';
			font-size: $ns-font-size-base;
			line-height: 1;
			position: relative;
			top: 2rpx;
			margin-left: 4rpx;
		}
	}

	.diy-seckill-box {
		width: 100%;
		/*white-space 不能丢  */
		white-space: nowrap;
		box-sizing: border-box;
		margin-top: 30rpx;
	}

	.seckill-box-item {
		width: 205rpx;
		height: 100%;
		vertical-align: top;
		display: inline-block;
		background: #ffffff;
		margin-right: $ns-margin;

		.seckill-item {
			width: 100%;
			height: 100%;
		}
		.seckill-item-image {
			width: 100%;
			height: 205rpx;
			border-radius: 20rpx;
			overflow:hidden;
			display: flex;
			justify-content: center;
			align-items: center;
			position: relative;
			image {
				width: 205rpx;
				height: 205rpx;
				padding: 0;
				margin: 0;
				display: block;
			}
	.goods_img-over {
        width: 100rpx;
        height: 100rpx;
        position: absolute;
        left: 50%;
        top: 50%;
        transform: translate(-50%,-50%);
	}
		}

		.seckill-item-new-name{
			white-space:normal;
			margin:30rpx 0 20rpx 0;
			height: 60rpx;
			font-size: $ns-font-size-xm;
			color: #333333;
			line-height: 1.3;
			// height: 60rpx;
			word-break:break-all;
			text-overflow:ellipsis;//显示为省略号
			display:-webkit-box;//对象作为伸缩盒子模型显示
			-webkit-box-orient:vertical;//设置或检索伸缩盒对象的子元素的排列方式
			-webkit-line-clamp:2;//显示行数## 标题文字 ##
			overflow:hidden;
		}

		.seckill-item-new-price {
			font-size: 36rpx;
			line-height:1;
			font-weight: bold;
			text:first-child{
				font-size:$ns-font-size-xm;
			}
		}

		.seckill-item-old-price {
			font-size: $ns-font-size-sm;
			color: $ns-text-color-gray;
			text-decoration: line-through;
			line-height: 1;
		}
		.song_maidou{
			width: 100%;
			.huanhang{
				background: var(--custom-brand-color-10);
				font-size: 22rpx;
				border-radius: 8rpx;
				// width: 70%;
				display: inline-block;
				margin-top: 10rpx;
				padding:0 10rpx;
					.song{
						color: #333333;
						// margin-left: 10rpx;
					}
					.maidounum{
						color: var(--custom-brand-color);
					}
			}

		}
	}
</style>
