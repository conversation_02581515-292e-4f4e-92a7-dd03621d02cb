<!--生物认证 指纹验证-->
<template>
	<view class="main" :style="[themeColorVar]">
    <view class="main-content">
      <image :src="$util.img('public/static/youpin/safe.svg')" mode="widthFix" class="main-content-img"></image>
      <view class="main-content-tip">{{ d_type ? tip_list[d_type-1] : ''}}</view>
      <template v-if="isSuccess">
        <button class="main-content-success"open-type="launchApp" app-parameter="wechat" binderror="launchAppError">验证成功，点击返回APP</button>
      </template>
      <template v-else>
        <button class="main-content-op main-content-op-loading" v-if="isLoading" :loading="true"></button>
        <button class="main-content-op" @click="startCertification" v-else>验证失败,点击重新认证</button>
      </template>
    </view>
    <uni-popup ref="popup">
      <view class="box">
        <view class="pay-text">认证成功，是否返回到app中</view>
        <button class="btn-app" open-type="launchApp" app-parameter="wechat" binderror="launchAppError">点击返回App</button>
      </view>
    </uni-popup>
	</view>
</template>

<script>
	import golbalConfig from "../../../common/mixins/golbalConfig";

  export default {
    mixins:[golbalConfig],
		data() {
			return {
        AUTH_MODE: [],
        type: 'attestation',
        d_type: null, // 操作类型 1 新增加盟 2退出加盟 3开通生物验证
        soter_key: null, // 生物验证唯一key, 接口提供，自动生成
        league_id: null, // 加盟id
        user_league_id: null, // 用户加盟id
        xm_token: null,
        league_type: null, // 加盟类型，1分类，2商品 ，d_type=1时必填 ， 接口提供
        plans_id: null, // 加盟方案id ，d_type=1时必填 ， app提供
        tip_list:[
          '您正在进行新增加盟操作，该操作已开启指纹密码保护，请先完成指纹密码验证 。',
          '您正在申请提前退回加盟操作，该操作已开启指纹密码保护，请先完成指纹密码验证 。',
          '您正在开启指纹密码保护功能，请使用本机开锁指纹完成首次指纹密码验证校验操作 。',
        ],
        isSuccess: false,
        isLoading: true
			}
		},
    onLoad(options){
      this.d_type = options.d_type || null
      this.soter_key = options.soter_key || null
      this.league_id = options.league_id || null
      this.user_league_id = options.user_league_id || null
      this.xm_token = options.xm_token || null
      this.league_type = options.league_type || null
      this.plans_id = options.plans_id || null
      this.startCertification()
    },
    onShow(){
      this.$langConfig.refresh();
      uni.setNavigationBarTitle({
        title:"先迈指纹识别"
      })
    },
		methods: {
      async sendRecordResult(params={}){
        try{
          let res = await this.$api.sendRequest({
            url: this.$apiUrl.recrodSoterCheckUrl,
            async: false,
            data:params
          });
        }catch (e) {

        }
      },
      startCertification(){
        this.isLoading = true
        this.startAuth({
          xm_token: this.xm_token,
          soter_key: this.soter_key,
          d_type: this.d_type,
          league_id: this.league_id,
          league_type: this.league_type,
          plans_id: this.plans_id,
          user_league_id: this.user_league_id
        })
      },
      startSoterAuthentication(params){
        // #ifdef MP-WEIXIN
        params.wx_type = 3
        uni.startSoterAuthentication({
          requestAuthModes: this.AUTH_MODE,
          challenge: this.type,
          authContent: '先迈指纹识别',
          success: async (res) => {
            params.is_succ = 1
            params.content = JSON.stringify(res)
            await this.sendRecordResult(params)
            // this.$refs.popup.open()
            this.isLoading = false
            this.isSuccess = true
            // this.$util.showToast({
            //   title: '认证成功',
            //   mask: true,
            // })
          },
          fail: (err) => {
            params.is_succ = 0
            params.content = `指纹认证失败 ${JSON.stringify(err)}`
            this.sendRecordResult(params)
            if(err.errCode == 90010){
              uni.showModal({
                title: '失败',
                content: '认证失败,重试次数过多,请稍后再试',
                showCancel: false
              })
            } else{
              uni.showModal({
                title: '失败',
                content: '认证失败',
                showCancel: false
              })
            }
            this.isLoading = false
          }
        })
        // #endif
      },
      checkIsEnrolled(params){
        // #ifdef MP-WEIXIN
        params.wx_type = 2
        uni.checkIsSoterEnrolledInDevice({
          checkAuthMode: this.AUTH_MODE[0],
          success: (res) => {
            if (res.isEnrolled) {
              this.startSoterAuthentication(params);
            }else{
              params.is_succ = 0
              params.content = '您暂未录入指纹信息，请录入后重试'
              this.sendRecordResult(params)
              uni.showModal({
                title: '错误',
                content: '您暂未录入指纹信息，请录入后重试',
                showCancel: false
              })
              this.isLoading = false
            }
          },
          fail: (err) => {
            params.is_succ = 0
            params.content = `您暂未录入指纹信息，请录入后重试 ${JSON.stringify(err)}`
            this.sendRecordResult(params)
            uni.showModal({
              title: '错误',
              content: '您暂未录入指纹信息，请录入后重试',
              showCancel: false
            })
            this.isLoading = false
          }
        })
        // #endif
      },
      startAuth(params) {
        // #ifdef MP-WEIXIN
        params.wx_type = 1
        uni.checkIsSupportSoterAuthentication({
          success: (res) => {
            if(res.supportMode.includes('fingerPrint')){
              this.AUTH_MODE = ['fingerPrint']
              this.checkIsEnrolled(params)
            }else{
              params.is_succ = 0
              params.content = '您的设备不支持指纹识别'
              this.sendRecordResult(params)
              uni.showModal({
                title: '错误',
                content: '您的设备不支持指纹识别',
                showCancel: false
              })
              this.isLoading = false
            }
          },
          fail: (err) => {
            params.is_succ = 0
            params.content = `您的设备不支持指纹识别 ${JSON.stringify(err)}`
            this.sendRecordResult(params)
            uni.showModal({
              title: '错误',
              content: '您的设备不支持指纹识别',
              showCancel: false
            })
            this.isLoading = false
          }
        })
        // #endif
      },
      /**
       *分享参数组装
       */
      getSharePageParams() {
        return this.$util.unifySharePageParams('/otherpages/shop/home/<USER>', this.shareTitle || '先迈商城',
            '',{}, this.$util.img(this.shareImg) || this.$util.img('public/static/youpin/home_share.jpg'))
      }
		},
    /**
     * 自定义分享内容
     * @param {Object} res
     */
    onShareAppMessage(res) {
      let { title, link, imageUrl, query } = this.getSharePageParams()
      return this.$buriedPoint.pageShare(link , imageUrl, title);
    },
	}
</script>

<style scoped lang="scss">
.main{
  background: white;
  width: 100%;
  height: 100vh;
  overflow-y: scroll;
  -webkit-overflow-scrolling: touch;
  &-content{
    display: flex;
    flex-direction: column;
    align-items: center;
    padding-top: 396rpx;
    box-sizing: border-box;
    &-img{
      width: 74rpx;
      height: 91rpx;
    }
    &-tip{
      width: 587rpx;
      font-size: 28rpx;
      text-align: center;
      margin-top: 50rpx;
    }
    &-success{
      min-width: 173rpx;
      height: 59rpx;
      display: flex;
      justify-content: center;
      align-items: center;
      background-color: #00CD77;
      color: white;
      margin-top: 70rpx;
      &-loading{
        background-color: #eee;
      }
    }
    &-op{
      min-width: 173rpx;
      height: 59rpx;
      display: flex;
      justify-content: center;
      align-items: center;
      background-color: var(--custom-brand-color);
      color: white;
      margin-top: 70rpx;
      &-loading{
        background-color: #eee;
      }
    }
  }
}
.btn-app {
  height: 100rpx;
  line-height: 100rpx;
  color: #1AAD19;
  font-size: 36rpx;
  border: 1rpx solid #EEEEEE;
  background-color: transparent;
}
.box {
  width: 580rpx;
  padding-top: 60rpx;
  background-color: #fff;
  text-align: center;
  border-radius: 20rpx;
  padding-bottom: 60rpx;
}
.pay-text {
  color: #000000;
  font-size: 40rpx;
  margin-bottom: 60rpx;
}
</style>
