<template>
	<view :class="themeStyle" :style="[themeColorVar]">
		<view class="top-tips">注意：请绑定本人的储蓄卡，不支持绑定信用卡</view>
		<view class="form">
			<view class="form-item">
				<view class="label">持卡人</view>
				<view class="item">
					<input type="text" v-model="formObj.realname" disabled placeholder="请填写持卡人" placeholder-class="placeholder" />
				</view>
			</view>
			<view class="form-item">
				<view class="label">证件号码</view>
				<view class="item">
					<input type="text" v-model="formObj.IdCard" disabled placeholder="请填写证件号码" placeholder-class="placeholder" />
				</view>
			</view>
			<view class="form-item">
				<view class="label">银行名称</view>
				<view @click="selectBank" class="item">
					<view class="bank-name" v-if="formObj.branch_bank_name">{{formObj.branch_bank_name}}<text class="iconfont iconright placeholder"></text></view>
					<view class="picker" v-else>请选择<text class="iconfont iconright placeholder"></text></view>
				</view>
			</view>
			<view class="form-item">
				<view class="label">银行卡号</view>
				<view class="item">
					<view class="input" v-if="formObj.cardNoFormat && !isFocus" @click="isFocus=true">{{formObj.cardNoFormat}}</view>
					<input v-else type="number" :focus="isFocus" v-model="formObj.bank_account" @input="inputChange" @focus="isFocus=true" @blur="cardBlur" placeholder="请填写银行卡号" placeholder-class="placeholder" />
				</view>
			</view>
			<view class="form-item">
				<view class="label">开户省市</view>
				<!-- <view @click="" class="item">
					<view class="bank-name" v-if="formObj.city">{{formObj.city}}<text class="iconfont iconright placeholder"></text></view>
					<view class="picker" v-else>请选择<text class="iconfont iconright placeholder"></text></view>
				</view> -->
				<pick-regions :default-regions="defaultRegions" @getRegions="handleGetRegions" class="picker">
					<view class="item">
						<view class="bank-name" v-if="formObj.full_address">{{formObj.full_address}}<text class="iconfont iconright placeholder"></text></view>
						<view class="picker" v-else>请选择<text class="iconfont iconright placeholder"></text></view>
					</view>
				</pick-regions>
			</view>
			<view class="form-item">
				<view class="label">开户行支行</view>
				<view class="item">
					<input type="text" v-model="formObj.bankname" placeholder="请填写开户行支行名称" placeholder-class="placeholder" />
				</view>
			</view>
			<view class="form-item">
				<view class="label">手机号</view>
				<view class="item">
					<input type="number" v-model="formObj.mobile" placeholder="请填写银行预留手机号" placeholder-class="placeholder" />
				</view>
			</view>
			<view class="form-item">
				<view class="label">验证码</view>
				<view class="item">
					<input type="text" v-model="formObj.code" placeholder="请输入验证码" placeholder-class="placeholder" maxlength="4"/>
					<view class="verification-code" :class="{'disabled':countDownNum>0}" @click="countDownNum>0?'':getCode()">{{countDownNum>0?countDownNum+'后重发':'获取验证码'}}</view>
				</view>
			</view>
		</view>
		<button class="submit" type="primary" @click="submit">立即绑定</button>
	</view>
</template>

<script>
	import pickRegions from '@/components/pick-regions/pick-regions.vue';
	import validate from 'common/js/validate.js';
	import apiurls from '@/common/js/apiurls'
  import golbalConfig from "../../../common/mixins/golbalConfig";
	export default{
		components: {
			pickRegions
		},
    mixins:[golbalConfig],
		data(){
			return{
				countDownNum:0,
				bank_account_id:0,
				formObj:{
					realname:'', //真实姓名
					mobile:'', //手机号码
					bank_account:'', //银行卡号
					cardNoFormat:'',
					branch_bank_name:'',
					code:'', //手机验证码
					full_address:'',
					bankname:'',
					IdCard:'',
				},
				isFocus:false,
				addressValue:'',
        defaultRegions:[],
        bank_card_list_code: {}
			}
		},
		computed: {
			//vueX页面主题
			themeStyle() {
				return 'theme-' + this.$store.state.themeStyle
			}
		},
		async onLoad(options){
			if(options.id) {
				this.bank_account_id = options.id;
				await this.getBankCard()
			}

      // 接收银行卡列表页面的短信的code和倒计时
      const bank_card_list_code = uni.getStorageSync('bank_card_list_code')
      if (bank_card_list_code && Object.keys(bank_card_list_code).length>0 && bank_card_list_code.countDownNum > 0){
        this.bank_card_list_code = bank_card_list_code
        uni.removeStorageSync('bank_card_list_code')
        if(this.formObj.mobile == bank_card_list_code.mobile){
          this.formObj.code = bank_card_list_code.code
          this.countDownNum = this.bank_card_list_code.countDownNum
          let timer = setInterval(()=>{
            this.countDownNum -= 1
            if(!this.countDownNum){
              clearInterval(timer)
            }
          },1000)
        }
      }

			uni.removeStorageSync('bank')
			let memberAuth = uni.getStorageSync('memberAuth')
			if(memberAuth){
				this.formObj.realname = memberAuth.auth_card_name;
				this.formObj.IdCard =  this.$util.addStar(memberAuth.auth_card_no,4,4);
			}
			await this.getMemberAuthenticationInfo()
		},
		onShow() {
			// 刷新多语言
			this.$langConfig.refresh();
			if(uni.getStorageSync('bank')){
				this.formObj.branch_bank_name = uni.getStorageSync('bank').bank_name
			}
			if(this.bank_account_id){
				uni.setNavigationBarTitle({
					title:'编辑银行卡'
				})
			}
		},
		methods:{
			// 获取银行卡详情
			async getBankCard(){
				const res = await this.$api.sendRequest({
					url:apiurls.memberBankUrl,
          async: false,
				})
        if(res.data) {
          this.formObj = Object.assign(this.formObj,res.data)
          this.formObj.bankname = res.data.branch_branch_bank_name
          this.formObj.full_address = res.data.province+" "+res.data.city
          this.inputChange()
        }
			},

			// 获取实名信息
			async getMemberAuthenticationInfo(){
				const res = await this.$api.sendRequest({
					url:apiurls.memberAuthenticationInfo,
          async: false
				})
        if(res.data){
          this.formObj.realname = res.data.auth_card_name
          this.formObj.IdCard = this.$util.addStar(res.data.auth_card_no,4,4)
        }
			},
			inputChange(e){
				this.formObj.bank_account = e?e.detail.value:this.formObj.bank_account
				this.formObj.cardNoFormat = String(e?e.detail.value:this.formObj.bank_account).replace(/\D/g,'').replace(/....(?!$)/g,'$& ');
			},
      async cardBlur(){
        this.isFocus=false
        if(this.formObj.bank_account){
            try{
              let res = await this.$api.sendRequest({
                url:apiurls.bankCardInfoUrl,
                data:{
                  card_no:this.formObj.bank_account,
                },
                async: false
              })
              if(res.code == 0){
                if(res.data && Object.keys(res.data).length>0){
                  uni.setStorageSync('bank',res.data)
                  this.formObj.branch_bank_name = res.data.bank_name
                }
              }
            }catch (e) {

            }
        }
      },
			selectBank(){
				this.$util.redirectTo('/otherpages/member/bank_list/bank_list', {
					'back': '/otherpages/member/bank_card_detail/bank_card_detail'
				});
			},
			// 获取选择的地区
			handleGetRegions(regions) {
				this.formObj.full_address = '';
				this.formObj.full_address += regions[0] != undefined ? regions[0].label : '';
				this.formObj.full_address += regions[1] != undefined ? ' ' + regions[1].label : '';
				this.formObj.full_address += regions[2] != undefined ? ' ' + regions[2].label : '';
				this.addressValue = '';
				this.addressValue += regions[0] != undefined ? regions[0].value : '';
				this.addressValue += regions[1] != undefined ? ' ' + regions[1].value : '';
				this.addressValue += regions[2] != undefined ? ' ' + regions[2].value : '';
			},
			vertify(name) {
				this.formObj.branch_bank_name = this.formObj.branch_bank_name.trim();
				this.formObj.bank_account = this.formObj.bank_account.trim();
				this.formObj.mobile = this.formObj.mobile.trim();
				var rulelist = []
				var rule = [
					{
						name: 'branch_bank_name',
						checkType: 'required',
						errorMsg: '请选择银行名称'
					},
					{
						name: 'bank_account',
						checkType: 'required',
						errorMsg: '请输入提现账号'
					},
					{
						name: 'bank_account',
						checkType: 'bank_account',
						errorMsg: '请输入正确的提现账号'
					},
					{
						name: 'full_address',
						checkType: 'required',
						errorMsg: '请选择开户省市'
					},
					{
						name: 'bankname',
						checkType: 'required',
						errorMsg: '请输入开户支行名称'
					},
					{
						name: 'mobile',
						checkType: 'required',
						errorMsg: '请输入手机号'
					},
					{
						name: 'mobile',
						checkType: 'phoneno',
						errorMsg: '请输入正确的手机号'
					},
					{
						name: 'code',
						checkType: 'required',
						errorMsg: '请输入验证码'
					}
				]
				if(!name){
					rulelist = rule
				}else{
					name.forEach(e=>{
						rulelist = rulelist.concat(rule.filter(item=>item.name == e))
					})

				}
				var checkRes = validate.check(this.formObj, rulelist);
				if (checkRes) {
					return true;
				} else {
					this.$util.showToast({
						title: validate.error
					});
					this.flag = false;
					return false;
				}
			},
			getCode(){
				const checkResult = this.vertify(['mobile']);
				if(!checkResult) return
				this.$api.sendRequest({
					url:apiurls.sendMobileCodeUrl,
					data:{
						mobile:this.formObj.mobile
					},
					success:(res)=>{
						if(res.code==0){
							this.countDownNum = 120
							let timer = setInterval(()=>{
								this.countDownNum -= 1
								if(!this.countDownNum){
									clearInterval(timer)
								}
							},1000)
						}else{
							this.$util.showToast({title:res.message})
						}
					}
				})
			},
			submit(){
				const checkResult = this.vertify();
				if(!checkResult) return
				let api = !this.bank_account_id ? apiurls.addBankUrl : apiurls.editBankUrl
				let addressValueArr = this.addressValue.split(' ');
				var datas = {};
				datas = {
					realname: this.formObj.realname,
					mobile: this.formObj.mobile,
					branch_bank_name:this.formObj.branch_bank_name,
					bank_account:this.formObj.bank_account,
					code:this.formObj.code,
					province_id: addressValueArr[0] ? addressValueArr[0] :this.formObj.province_id,
					city_id: addressValueArr[1] ? addressValueArr[1] : this.formObj.city_id,
					branch_branch_bank_name:this.formObj.bankname
				};
				if(this.bank_account_id){
					datas.bank_account_id = this.bank_account_id
				}
				this.$api.sendRequest({
					url:api,
					data:datas,
					success:(res)=>{
						if(res.code==0){
							this.$util.showToast({title:'绑定成功',icon:'success'})
							setTimeout(()=>{
								this.$util.redirectTo('/otherpages/member/bank_card_list/bank_card_list', {}, 'redirectTo')
							},1000)

						}else{
							this.$util.showToast({title:res.message})
						}
					}
				})
			},
      /**
       *分享参数组装
       */
      getSharePageParams() {
        return this.$util.unifySharePageParams('/otherpages/shop/home/<USER>', this.shareTitle || '先迈商城',
            '',{}, this.$util.img(this.shareImg) || this.$util.img('public/static/youpin/home_share.jpg'))
      }
		},
    /**
     * 自定义分享内容
     * @param {Object} res
     */
    onShareAppMessage(res) {
      let { title, link, imageUrl, query } = this.getSharePageParams()
      return this.$buriedPoint.pageShare(link , imageUrl, title);
    },
	}
</script>

<style lang="scss">
$height:116rpx; // 行高度
$width:190rpx; //左标题宽度
.top-tips{
	background: var(--custom-brand-color-10);
	padding:0 25rpx;
	line-height: 64rpx;
	font-size: $ns-font-size-sm;
	color: var(--custom-brand-color);
}
.form{
	background: #fff;
  width: 710rpx;
  margin: 0 auto;
  margin-top: 20rpx;
  border-radius: 20rpx;
  padding: 0 30rpx;
  box-sizing: border-box;

	.form-item{
		display: flex;
		align-items: center;
		justify-content: space-between;
		height: $height;
		position: relative;
    box-sizing: border-box;
    &:not(:first-child){
      border-top: 2rpx solid rgba(245, 245, 245, 1);
    }
		.label{
			width: $width;
      font-size: 30rpx;
      font-weight: 400;
      line-height: 52rpx;
      color: rgba(56, 56, 56, 1);
		}
		.item{
			width: calc(100vw - #{ $width } - 100rpx);
			position: absolute;
			line-height: $height;
			top: 0;
			right: 26rpx;
			font-size: $ns-font-size-base + 2rpx;
			input{
				line-height: $height;
				height: $height;
				position: absolute;
				width: 100%;
				top: 0;
				left: 0;
				z-index: 2;
				font-size: $ns-font-size-base + 2rpx;
			}
			.cardNo{
				opacity: 0;
			}
			.input{
				width:100%;
				line-height: $height;
				height: $height;
			}
			.picker{
				color: #9a9a9a;
				text-align: right;
				line-height:100rpx
			}
			.bank-name{
				display: flex;
				justify-content: space-between;
				height: 100rpx;
				line-height: 100rpx;
				font-size: $ns-font-size-base + 2rpx;
			}
			.verification-code{
				position: absolute;
				right: 0;
				top: 26rpx;
				z-index:999;
				width: 148rpx;
				height: 48rpx;
				border: 1px solid var(--custom-brand-color);
				border-radius: 24px;
				line-height: 48rpx;
				text-align: center;
				color: var(--custom-brand-color);
				font-size:$ns-font-size-sm;
				&.disabled{
					border: 1px solid #eee;
					color:#BCBCBC
				}
			}
		}
	}
	.placeholder{
		font-size: 30rpx;
		color:#ccc
	}
}
.submit{
	margin-top: 80rpx;
	line-height:80rpx
}
/deep/.pick-regions  div:last-child{
  height: 100rpx;
  .bank-name{
    margin-left: 24rpx;
  }
}
</style>
