<template>
  <view class="authentication" :class="themeStyle" :style="[themeColorVar]">
    <view class="authentication-title">实名认证</view>
    <view class="authentication-desc">根据海关总署要求，购买跨境商品需要进行实名认证，请填写以下信息</view>
    <view class="authentication-row">
      <view class="authentication-row-label">姓名</view>
      <input type="text" placeholder="请输入真实姓名" class="authentication-row-input" v-model="form.auth_card_name"/>
    </view>
    <view class="authentication-row">
      <view class="authentication-row-label">身份证号码</view>
      <input type="number" placeholder="请输入18位身份证号码" class="authentication-row-input" v-model="form.auth_card_no" maxlength="18"/>
    </view>
    <view class="authentication-row">
      <view class="authentication-row-label">身份证正面照片</view>
      <view class="authentication-upload" @click="chooseImage('auth_card_front')">
        <view class="authentication-upload-tip" v-if="!form.auth_card_front">
          <uni-icons type="camera" size="22" color="#000"></uni-icons>
          <view class="authentication-upload-tip-text">点击上传身份证证明照片</view>
          <view class="authentication-upload-tip-desc">请确保照片清晰，信息完整</view>
        </view>
        <image :src="form.auth_card_front" mode="aspectFit" class="authentication-upload-img" v-else></image>
      </view>
    </view>
    <view class="authentication-row">
      <view class="authentication-row-label">身份证反面照片</view>
      <view class="authentication-upload" @click="chooseImage('auth_card_back')">
        <view class="authentication-upload-tip" v-if="!form.auth_card_back">
          <uni-icons type="camera" size="22" color="#000"></uni-icons>
          <view class="authentication-upload-tip-text">点击上传身份证证明照片</view>
          <view class="authentication-upload-tip-desc">请确保照片清晰，信息完整</view>
        </view>
        <image :src="form.auth_card_back" mode="aspectFit" class="authentication-upload-img" v-else></image>
      </view>
    </view>
    <view class="authentication-agreement">
      <view class="authentication-agreement-text">
        <checkbox-group @change="protocolChange" class="authentication-agreement-text-group">
          <checkbox value="cb" :checked="isProtocol" style="transform:scale(0.7)"/>
        </checkbox-group>
          我已阅读并同意
        <text class="authentication-agreement-text-link">《先迈网用户协议》和《隐私政策》</text>
        ，并授权平台将我的身份信息提交给海关进行清关申报
      </view>
    </view>
    <view class="authentication-submit">
      <button type="primary" class="authentication-submit-btn" @click="submit">提交</button>
    </view>
    <loading-cover ref="loadingCover"></loading-cover>
    <!-- 授权登录弹窗 -->
    <yd-auth-popup ref="ydauth"></yd-auth-popup>
  </view>
</template>

<script>
import globalConfig from "@/common/mixins/golbalConfig";
import appInlineH5 from "@/common/mixins/appInlineH5";
// #ifdef H5
import {isOnXianMaiApp} from "@/common/js/h5/appOP";
import system from "@/common/js/system";
// #endif

export default {
  mixins:[globalConfig,appInlineH5],
  name: "cross_border_real_name_authentication",
  components: {
  },
  data() {
    return {
      form:{
        auth_card_name: '',
        auth_card_no: '',
        auth_card_front: '',
        auth_card_back: ''
      },
      isProtocol: false,
      // #ifdef H5
      isOnXianMaiApp:isOnXianMaiApp,
      // #endif
    };
  },
  onLoad(){
    // this.form.id_card_front = this.$util.img('public/static/youpin/member/contribution/beijing.png')
  },
  async onShow(){
    // 刷新多语言
    this.$langConfig.refresh();
    await system.wait_staticLogin_success();
    await this.getCbecAuthInfo()
  },
  methods:{
    protocolChange(event){
      let value = event.detail.value
      if(value.length>0){
        this.isProtocol = true
      }else{
        this.isProtocol = false
      }
    },
    chooseImage( type){
      this.$util.upload(
        1, {
          path: 'cbecAuthCardImage'
        },
        res => {
          this.form[type] = res[0];
        },
          ['album','camera']
      );
    },
    async getCbecAuthInfo() {
      try{
        let res = await this.$api.sendRequest({
          url: this.$apiUrl.cbecRealNameAuthInfoUrl,
          async: false
        })
        if (this.$refs.loadingCover) this.$refs.loadingCover.hide();
        if(res.code == 0){
          this.form = Object.assign(this.form,res.data)
        }else{
          this.$util.showToast({
            title: res.message
          });
        }
      }catch (e) {
        if (this.$refs.loadingCover) this.$refs.loadingCover.hide();
        this.$util.showToast({
          title: e.message
        });
      }
    },
    async submit(){
      if(!this.form.auth_card_name || !this.form.auth_card_no || !this.form.auth_card_front || !this.form.auth_card_no){
        this.$util.showToast({
          title: '请填写完整信息'
        });
        return
      }
      if(!this.isProtocol){
        this.$util.showToast({
          title: '请阅读并同意协议'
        });
        return
      }
      uni.showLoading({
        title: '提交中',
        mask: true
      })
      try{
        let res = await this.$api.sendRequest({
          url: this.$apiUrl.cbecRealNameAuthEditUrl,
          data: this.form,
          async: false
        })
        uni.hideLoading()
        if(res.code == 0){
          this.$util.showToast({
            title: '提交成功',
            success: res => {
              setTimeout(() => {
                this.toBack()
              }, 2000);
            }
          });
        }else{
          this.$util.showToast({
            title: res.message
          });
        }
      }catch (e) {
        uni.hideLoading()
        this.$util.showToast({
          title: e.message
        });
      }
    },
    async toBack(){
      // #ifdef H5
      this.appGoBack();
      // #endif
      // #ifdef MP-WEIXIN
      this.$util.goBack()
      // #endif
    }
  }
}
</script>
<style lang="scss">
page{
  background-color: white;
}
</style>
<style lang="scss" scoped>
.authentication{
  padding: 24rpx 24rpx 0 24rpx;
  box-sizing: border-box;
  &-title{
    font-size: 36rpx;
    font-weight: 700;
    line-height: 52rpx;
    color: rgba(56, 56, 56, 1);
  }
  &-desc{
    font-size: 28rpx;
    font-weight: 400;
    line-height: 32.82rpx;
    color: rgba(166, 166, 166, 1);
    margin-top: 16rpx;
  }
  &-row{
    margin-top: 40rpx;
    &-label{
      font-size: 30rpx;
      font-weight: 400;
      line-height: 52rpx;
      color: rgba(128, 128, 128, 1);
    }
    &-input{
      width: 100%;
      height: 80rpx;
      border-radius: 10rpx;
      background: rgba(246, 246, 246, 1);
      padding: 0 20rpx;
      box-sizing: border-box;
      font-size: 30rpx;
      font-weight: 400;
      color: rgba(0, 0, 0, 1);
      border: 1px solid #F1F1F4;
    }
  }
  &-upload{
    height: 200rpx;
    border-radius: 20rpx;
    background: rgba(246, 246, 246, 1);
    display: flex;
    align-items: center;
    justify-content: center;
    &-tip{
      display: flex;
      flex-direction: column;
      align-items: center;
      &-text{
        font-size: 28rpx;
        font-weight: bold;
        line-height: 32.82rpx;
        color: #333;
      }
      &-desc{
        font-size: 24rpx;
        font-weight: 400;
        line-height: 32.82rpx;
        color: rgba(166, 166, 166, 1);
        margin-top: 10rpx;
      }
    }
    &-img{
      width: 100%;
      height: 100%;
    }
  }
  &-agreement{
    margin-top: 40rpx;
    &-text{
      font-size: 24rpx;
      font-weight: 400;
      line-height: 32.82rpx;
      color: rgba(166, 166, 166, 1);
      &-group{
        display: inline-block;
      }
      &-link{
        color: var(--custom-brand-color);
      }
    }
  }
  &-submit{
    width: 100%;
    position: fixed;
    left: 0;
    bottom: env(safe-area-inset-bottom);
    padding: 24rpx 0;
    box-sizing: border-box;
    border-top: 1px solid #F1F1F4;
    display: flex;
    justify-content: center;
    &-btn{
      width: 540rpx;
      height: 80rpx;
      line-height: 80rpx;
      border-radius: 40rpx;
      font-size: 30rpx;
      font-weight: 400;
      color: rgba(255, 255, 255, 1);
    }
  }
}
</style>
