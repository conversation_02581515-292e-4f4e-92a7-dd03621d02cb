<template>
	<view :class="themeStyle" :style="[themeColorVar]">
<!--		<mescroll-uni @getData="getData" ref="mescroll">-->
<!--			<block slot="list">-->
<!--				&lt;!&ndash; 明细列表 &ndash;&gt;-->
<!--				<view class="balances" v-if="dataList.length">-->
<!--					<view v-for="(item, index) in dataList" :key="index" class="ns-border-color-gray balances-item">-->
<!--						<view class="balance-img">-->
<!--							<image v-if="item.account_data>0" :src="$util.img('upload/uniapp/member/balance_detail/income.png')" mode="widthFix"></image>-->
<!--							<image v-else :src="$util.img('upload/uniapp/member/balance_detail/pay.png')" mode="widthFix"></image>-->
<!--						</view>-->
<!--						<view class="balance-head-wrap">-->
<!--							<view class="ns-margin-bottom balance-head">-->
<!--								<text class="ns-font-size-base balance-head-title">{{ item.type_name }}</text>-->
<!--								<text class="ns-text-color ns-font-size-sm num">{{ item.account_data ? item.account_data : 0 }}</text>-->
<!--							</view>-->
<!--							<text class="balance-text ns-text-color-gray ns-font-size-sm balance-desc">{{ item.remark }}</text>-->
<!--							<text class="ns-text-color-gray balance-time ns-font-size-sm">{{ $util.timeStampTurnTime(item.create_time) }}</text>-->
<!--						</view>-->
<!--					</view>-->
<!--				</view>-->
<!--				&lt;!&ndash; 无明细列表 &ndash;&gt;-->
<!--				<view v-else>-->
<!--					<ns-empty></ns-empty>-->
<!--				</view>-->
<!--			</block>-->
<!--		</mescroll-uni>-->
    <view class="balances-detail">
      <view class="balances-detail-name">{{detail.type_name}}</view>
      <view class="balances-detail-order">{{detail.account_no}}</view>
      <view class="balances-detail-price"><text v-if="parseFloat(detail.account_data)>=0">+</text>{{detail.account_data}}</view>
      <view class="empty-box"></view>
      <view class="balances-detail-list">
        <view v-if="detail.type_name=='提现'"><text>交易手续费：</text><text>{{detail.charge_fee}}</text></view>
        <view><text>交易类型：</text><text>{{detail.type_name}}</text></view>
        <view><text>交易时间：</text><text>{{detail.create_time}}</text></view>
        <view v-if="detail.memo"><text>备注：</text><text>{{detail.memo}}</text></view>
      </view>
    </view>
		<loading-cover ref="loadingCover"></loading-cover>
	</view>
</template>

<script>
	import apiurls from "../../../common/js/apiurls";
  import golbalConfig from "../../../common/mixins/golbalConfig";

  export default {
    mixins:[golbalConfig],
		data() {
			return {
				dataList: [],
				statusList: [{
						name: '全部',
						id: '0'
					},
					{
						name: '收入',
						id: '1'
					},
					{
						name: '支出',
						id: '2'
					}
				],
				scrollInto: '',
				orderStatus: '0',
        id: 0,
        detail:{}
			};
		},
		onLoad(option) {
			if (option.status) this.orderStatus = option.status;
      this.id = option.id || 0;
		},
		onShow() {
			// 刷新多语言
			this.$langConfig.refresh();
			if (!uni.getStorageSync('token')) {
        this.$util.redirectTo('/pages/member/index/index', {},'redirectTo');
			}else{
			  this.getDetail();
      }

		},
		computed: {
			themeStyle() {
				return 'theme-' + this.$store.state.themeStyle
			}
		},
		methods: {
			ontabtap(e) {
				let index = e.currentTarget.dataset.current;
				this.orderStatus = this.statusList[index].id;
				this.$refs.mescroll.refresh();
			},
			getData(mescroll) {
				this.$api.sendRequest({
					url: '/api/memberaccount/page',
					data: {
						page_size: mescroll.size,
						page: mescroll.num,
						account_type: 'balance'
					},
					success: res => {
						let newArr = [];
						let msg = res.message;
						if (res.code == 0 && res.data) {
							newArr = res.data.list;
						} else {
							this.$util.showToast({
								title: msg
							});
						}
						mescroll.endSuccess(newArr.length);
						//设置列表数据
						if (mescroll.num == 1) this.dataList = []; //如果是第一页需手动制空列表
						this.dataList = this.dataList.concat(newArr); //追加新数据
						if (this.$refs.loadingCover) this.$refs.loadingCover.hide();
					},
					fail: res => {
						mescroll.endErr();
						if (this.$refs.loadingCover) this.$refs.loadingCover.hide();
					}
				});
			},
      getDetail() {
        this.$api.sendRequest({
          url: apiurls.memberaccountDetailUrl,
          data: {
            id: this.id
          },
          success: res => {
            if (res.data) {
              this.detail = res.data;
            }
            if (this.$refs.loadingCover) this.$refs.loadingCover.hide();
          },
          fail: res => {
            if (this.$refs.loadingCover) this.$refs.loadingCover.hide();
          }
        });
      },
      /**
       *分享参数组装
       */
      getSharePageParams() {
        return this.$util.unifySharePageParams('/otherpages/shop/home/<USER>', this.shareTitle || '先迈商城',
            '',{}, this.$util.img(this.shareImg) || this.$util.img('public/static/youpin/home_share.jpg'))
      }
		},
    /**
     * 自定义分享内容
     * @param {Object} res
     */
    onShareAppMessage(res) {
      let { title, link, imageUrl, query } = this.getSharePageParams()
      return this.$buriedPoint.pageShare(link , imageUrl, title);
    },
	};
</script>

<style lang="scss">
	// [data-theme] {
		.balances {
			background: #fff;

			.balances-item {
				display: flex;
				padding: 30rpx 30rpx;
				border-bottom: 1px solid $ns-border-color-gray;
				font-size: $ns-font-size-lg + 2rpx;

				.balance-img {
					padding-right: 20rpx;
					felx: 2;
					width: 70rpx;
					height: 70rpx;
					border-radius: 50%;
					margin-left: 20rpx;

					image {
						width: 100%;
						height: 100%;
					}
				}

				.balance-head-wrap {
					flex: 5;

					.balance-head {
						display: flex;
						justify-content: space-between;
						line-height: 1;

						.balance-head-title {
							color: #000000;
							font-size: $ns-font-size-lg;
							font-weight: 600;
							overflow: hidden;
							text-overflow: ellipsis;
							white-space: nowrap;
							width: 460rpx;
						}

						.num {
							font-size: $ns-font-size-lg;
						}
					}

					.balance-time {
						// align-self: flex-end;
					}

					.balance-text {
						overflow: hidden;
						text-overflow: ellipsis;
						display: -webkit-box;
						-webkit-line-clamp: 2;
						-webkit-box-orient: vertical;
						line-height: 1.5;
					}
				}
			}

		}

		.empty {
			width: 100%;
			height: 500rpx;
			display: flex;
			justify-content: center;
			flex-direction: column;
			align-items: center;

			.iconfont {
				font-size: 50rpx;
				margin-bottom: $ns-margin;
			}
		}

    .balances-detail{
      margin-top: 20rpx;
      background: #FFFFFF;
      border-radius: 20rpx;
      padding: 78rpx 24rpx;
      box-sizing: border-box;
      &-name{
        font-size: 32rpx;
        font-weight: 500;
        color: #333333;
        text-align: center;
        margin-bottom: 19rpx;
      }
      &-order{
        font-size: 32rpx;
        font-weight: 500;
        color: #333333;
        text-align: center;
        margin-bottom: 53rpx;
      }
      &-price{
        font-size: 52rpx;
        font-weight: bold;
        color: #333333;
        margin-bottom: 78rpx;
        text-align: center;
        text{
          font-size: 40rpx;
        }
      }
      &-list{
        view{
          &:not(:first-child){
            margin-top: 30rpx;
          }
          text:first-child{
            width: 207rpx;
            font-size: 28rpx;
            font-weight: 500;
            color: #999999;
            display: inline-block;
          }
          text:last-child{
            font-size: 28rpx;
            font-weight: 500;
            color: #333333;
          };
        }
      }
    }
    .empty-box{
      height: 2rpx;
      background-color: #eeeeee;
      margin-bottom: 48rpx;
    }
	// }
</style>
