<template>
	<view :class="themeStyle">
		<view class="banner">
			<image :src="$util.img('public/static/youpin/maidou/shoperbanner.png')" mode=""></image>
			<view class="sjtj">
				<view class="title">
					升级条件
				</view>
				<view class="smalltitle">
					满足1个条件可申请
				</view>
			</view>
			<view class="tuijian">
				<view class="tj_left">
					<text class="wuqian">累计消费{{applyData.condition.consumption}}元</text>
					<text class="qianer">当前￥{{applyData.member_condition.consumption}}</text>
				</view>
				<view class="tj_right">
					<image :src="$util.img('public/static/youpin/maidou/get.png')" mode="" v-if="ispay==1"></image>
					<image :src="$util.img('public/static/youpin/maidou/noneget.png')" mode="" v-else></image>
				</view>
			</view>
		</view>
		<view class="xiadan">
			<view class="tj_left">
				<text class="wuqian">推荐用户注册并成功下单{{applyData.condition.recommend}}人</text>
				<text class="qianer">当前{{applyData.member_condition.recommend}}人</text>
			</view>
			<view class="tj_right">
				<image :src="$util.img('public/static/youpin/maidou/get.png')" mode="" v-if="isxiadan==1"></image>
				<image :src="$util.img('public/static/youpin/maidou/noneget.png')" mode="" v-else></image>
			</view>
		</view>
		<view class="dianzhushuoming">
			<view class="dzsm_title">
				店主收益说明
			</view>
			<view class="dzsm_fuwenben" v-html="shopdata.content">

			</view>
		</view>
		<view class="tuijianimg" v-if="tuijianList.length>0">
			<image :src="$util.img('public/static/youpin/maidou/tuijan.png')"></image>
		</view>
		<view class="tuijianlist">
			<view class="seckill-box-item" v-for="(item, key) in tuijianList" :key="key" @click="toDetail(item)">
				<view class="seckill-item">
					<view class="seckill-item-image">
						<image :src="$util.img(item.goods_image)"></image>
					</view>
					<view class="seckill-item-new-name">{{item.goods_name}}</view>
					<view class="seckill-item-new-price ns-text-color">
						<text>￥</text>
						{{item.retail_price}}
					</view>
					<text class="seckill-item-old-price">￥{{item.market_price}}</text>
					<view class="song_maidou">
						<text class="song">送迈豆</text>
						<text class="mdnum">{{item.send_maidou}}</text>
					</view>
				</view>
			</view>
		</view>
		<view class="seemore" @click="toindex">
			<text>查看更多</text>
			<image :src="$util.img('public/static/youpin/maidou/maidou_seemore.png')" mode=""></image>
		</view>
		<view class="page-bottom">
			<view class="comfirm_three"@click="$refs.sharePopup.open()">
				分享给好友
			</view>
			<view class="comfirm_one" @click="toshenqing()" v-if="tip==1">
				申请成为店主
			</view>
			<view class="comfirm_two" v-else>
				申请成为店主
			</view>
		</view>
		<share-popups v-if="isShowCanvas" :canvasOptions="canvasOptions" ref="sharePopup" :sharePopupOptions="sharePopupOptions"></share-popups>
		 <yd-auth-popup ref="ydauth"></yd-auth-popup>
    <ns-login ref="login"></ns-login>
    <uni-coupon-pop ref="couponPop"></uni-coupon-pop>
    <loading-cover ref="loadingCover"></loading-cover>
	</view>
</template>
<script>
	import sharePopups from '@/components/share-popup/share-popup.vue';
  import system from "@/common/js/system.js";
  import {query_to_scene} from '../../../common/js/scene_handle'
	export default {
		components: {
			sharePopups
		},
		data() {
			return {
				phonenum: ',',
				tip: 0, //0不满足申请条件1满足
				applyData: {
          condition:{
            consumption:0,
            recommend:0
          },
          member_condition:{
            consumption:0,
            recommend:0
      }
        },
				ispay: 0, //0不满足推荐累计消费1满足
				isxiadan: 0, //0不满足下单1满足
				shopdata: '',
				canvasImg:'',
				tuijianList: '',
				isShowCanvas:false,
				canvasOptions:{
					width:674,
					height:784
				},
				sharePopupOptions:[],
				bgimg:'',
			};
		},
		onLoad(data) {
			this.phonenum = data.phonenum;

			this.getshopDoc()
			this.getTuijian();
			this.getAdvList()
		},
		onShow() {
			// this.mescroll.resetUpScroll(false);
			// 刷新多语言
			this.$langConfig.refresh();
			uni.setNavigationBarTitle({
				title: "升级成为店主"
			})
      system.checkToken().then(res=>{
        if(!uni.getStorageSync('token')){
          this.$util.toShowLoginPopup(this,null,'/otherpages/member/up_to_shopkeeper/up_to_shopkeeper');
          if (this.$refs.loadingCover) this.$refs.loadingCover.hide();
          return
        }
        this.getData()
      })
			if (uni.getStorageSync('is_register')) {
				this.$util.toShowCouponPopup(this)
				uni.removeStorageSync('is_register');
			}
			// this.getbanner()
		},
		onShareAppMessage() {
			let shop_id=uni.getStorageSync('shop_id');
			var path = `/otherpages/member/up_to_shopkeeper/up_to_shopkeeper?shop_id=${shop_id}`;
			console.log('分享的商品详情路径：'+path)
			let recommend_member_id=uni.getStorageSync('member_id');
			if(recommend_member_id){
				path+=`&recommend_member_id=${recommend_member_id}`;
			}
			return {
				title: `升级成为店主`,
				imageUrl: this.$util.img(this.bgimg),
				path: path,
				success: res => {},
				fail: res => {}
			};
		},
		computed: {
			themeStyle() {
				return 'theme-' + this.$store.state.themeStyle
			},
		},
		watch: {},

		methods: {
			toshenqing() {
			  this.$util.redirectTo(`/otherpages/member/up_to_shopkeeper/form_up_to_shopkeeper?phonenum=${phonenum}`)
			},
			toindex() {
			  this.$util.redirectTo(`/otherpages/shop/home/<USER>
			},
			toDetail(res) {
				this.$util.redirectTo('/pages/goods/detail/detail', {
					sku_id: res.sku_id
				});
			},
			getData() {
				this.$api.sendRequest({
					url: this.$apiUrl.upgradeShop,
					data: {

					},
					success: res => {
            if (this.$refs.loadingCover) this.$refs.loadingCover.hide();
						let msg = res.message;
						if (res.code == 0 && res.data) {
							this.applyData = res.data
							console.log(res.data)
							if (res.data.condition.consumption <= res.data.member_condition.consumption) {
								this.tip = 1;
								this.ispay = 1
							}
							if (res.data.condition.recommend <= res.data.member_condition.recommend) {
								this.tip = 1
								this.isxiadan = 1
							}
						  let is_shopper=uni.getStorageSync('is_shopper');  //是否为店主，1：是，0：否
						  if(is_shopper){
                this.tip = 0;
                this.$util.showToast({
                  title: "您已经是店主了！"
                })
              }
						} else {
							this.$util.showToast({
								title: msg
							})
						}
					},
					fail() {

					},
				});
			},
			getshopDoc() {
				this.$api.sendRequest({
					url: this.$apiUrl.shopDoc,
					data: {
						type: 1
					},
					success: res => {
						let msg = res.message;
						if (res.code == 0 && res.data) {
							this.shopdata = res.data
						} else {
							this.$util.showToast({
								title: msg
							})
						}
					},
					fail() {

					}
				});
			},
			getTuijian() {
				console.log("jinlail")
				this.$api.sendRequest({
					url: this.$apiUrl.recommandGoodList,
					data: {

					},
					success: res => {
						let msg = res.message;
						if (res.code == 0 && res.data) {
							this.tuijianList = res.data.list
						} else {
							this.$util.showToast({
								title: msg
							})
						}
					},
					fail() {

					}
				});
			},
			getAdvList() {
					this.$api.sendRequest({
						url: apiurls.specialBannerUrl,
						data: {
							sign: "shopUpgrade-1",
						},
						success: res => {
							if (res.code == 0 && res.data) {
								this.bgimg = res.data.list[0].image_url;
								this.getqrcode()
							}
						},
						fail() {}
					});
			},
			getqrcode() {
					var shopid = uni.getStorageSync('shop_id');
					var menberid = uni.getStorageSync('member_id');
          let scene=query_to_scene({
            shop_id:shopid,
            recommend_member_id:menberid
          })
					this.$api.sendRequest({
						url: '/api/Website/newCommQrcode',
						data: {
							// path: 'otherpages/shop/home/<USER>'+ shopid +'&recommend_member_id='+menberid,
							path: 'otherpages/shop/home/<USER>',
              scene
						},
						success: res => {
							if (res.code == 0 && res.data) {
								this.drawCanvas(res.data.qrcodeUrl)
							}
						},
						fail() {}
					});
			},
			drawCanvas(qrcodeUrl){
				console.log("333")
				this.sharePopupOptions = [
					{
						path:this.$util.img(this.bgimg),
						x: 0,
						y: 0,
						width: 674,
						height: 784,
						type: 'image',
					},
					{
						path:qrcodeUrl,
						x: 232,
						y: 480,
						width: 210,
						height: 210,
						type: 'image',
					}
				]
				this.isShowCanvas = true
			},

		},

	};
</script>

<style lang="scss">
	.banner {
		position: relative;

		image {
			width: 100%;
			height: 264rpx;
		}

		.sjtj {
			position: absolute;
			top: 0;
			left: 0;
			display: flex;
			align-items: center;
			flex-direction: column;
			margin-top: 30rpx;
			width: 100%;
			z-index: 9;
			.title {
				font-size: 40rpx;
				color: #FFFFFF;
			}

			.smalltitle {
				font-size: 28rpx;
				color: #FFFFFF;
			}
		}

		.tuijian {
			position: absolute;
			background: #FFFFFF;
			border-radius: 15rpx;
			display: flex;
			align-items: center;
			justify-content: space-between;
			width: 702rpx;
			top: 200rpx;
			left: 24rpx;
			height: 120rpx;
			z-index: 9;
			.tj_left {
				display: flex;
				flex-direction: column;
				margin-left: 20rpx;

				.wuqian {
					font-size: 28rpx;
					color: #333333;
					font-weight: bold;
				}

				.qianer {
					font-size: 26rpx;
					color: #F2270C;
				}
			}

			.tj_right {
				margin-right: 20rpx;

				image {
					width: 40rpx;
					height: 40rpx;
					margin-top: 15rpx;
				}
			}
		}
	}

	.xiadan {
		background: #FFFFFF;
		border-radius: 15rpx;
		display: flex;
		align-items: center;
		justify-content: space-between;
		width: 702rpx;
		height: 120rpx;
		margin: 0 auto;
		margin-top: 80rpx;

		.tj_left {
			display: flex;
			flex-direction: column;
			margin-left: 20rpx;

			.wuqian {
				font-size: 28rpx;
				color: #333333;
				font-weight: bold;
			}

			.qianer {
				font-size: 26rpx;
				color: #F2270C;
			}
		}

		.tj_right {
			margin-right: 20rpx;

			image {
				width: 40rpx;
				height: 40rpx;
				margin-top: 15rpx;
			}
		}
	}

	.dianzhushuoming {
		margin-top: 70rpx;

		.dzsm_title {
			margin-left: 24rpx;
			font-size: 28rpx;
			font-weight: bold;
			color: #333333;
		}
	}

	.page-bottom {
		width: 100%;
		height: 100rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		background: #FFFFFF;
		position: fixed;
		bottom: 0;

		.comfirm_one {
			width: 342rpx;
			height: 80rpx;
			text-align: center;
			border-radius: 50rpx;
			line-height: 80rpx;
			background: #F2270C;
			color: #FFFFFF;
			font-size: 32rpx;
		}

		.comfirm_two {
			width: 342rpx;
			height: 80rpx;
			text-align: center;
			border-radius: 50rpx;
			line-height: 80rpx;
			background: #CCCCCC;
			color: #FFFFFF;
			font-size: 32rpx;
		}
		.comfirm_three{
			width: 342rpx;
			height: 80rpx;
			text-align: center;
			border-radius: 50rpx;
			line-height: 80rpx;
			// background: #CCCCCC;
			color: #F2270C;
			font-size: 32rpx;
			border: 1rpx solid #F2270C;
			margin-right: 20rpx;
		}
	}

	.dzsm_fuwenben {
		padding: 24rpx;
		// padding-bottom: 120rpx;
	}
	.share-popup,
	.uni-popup__wrapper-box {
		.share-title {
			line-height: 60rpx;
			font-size: $ns-font-size-lg;
			padding: 15rpx 0;
			text-align: center;
		}

		.share-content {
			display: flex;
			display: -webkit-flex;
			-webkit-flex-wrap: wrap;
			-moz-flex-wrap: wrap;
			-ms-flex-wrap: wrap;
			-o-flex-wrap: wrap;
			flex-wrap: wrap;
			padding: 15rpx;

			.share-box {
				flex: 1;
				text-align: center;

				.share-btn {
					margin: 0;
					padding: 0;
					border: none;
					line-height: 1;
					height: auto;
					text {
						margin-top: 20rpx;
						font-size: 24rpx;
						display: block;
						color: $ns-text-color-black;
					}
				}

				.iconfont {
					font-size: 80rpx;
					line-height: initial;
				}
				.iconpengyouquan,
				.iconiconfenxianggeihaoyou {
					color: #07c160;
				}
			}
		}

		.share-footer {
			height: 90rpx;
			line-height: 90rpx;
			border-top: 2rpx #f5f5f5 solid;
			text-align: center;
			color: #666;
		}
	}
	.tuijianimg {
		padding: 40rpx 0 10rpx 0;
		text-align: center;

		image {
			width: 292rpx;
			height: 32rpx;
		}
	}
	.tuijianlist {
		margin: 20rpx;
		background: #FFFFFF;
		border-radius: 20rpx !important;
		display: flex;
		flex-wrap: wrap;
	}
	.seckill-box-item {
		width: 30%;
		height: 100%;
		display: flex;
		flex-direction: column;
		align-items: center;
		background: #ffffff;
		padding: 10rpx;
		margin-left: 3rpx;
		border-radius: 20rpx;

		.seckill-item {
			width: 100%;
			padding: 20rpx;
		}

		.seckill-item-image {
			width: 100%;
			height: 205rpx;
			border-radius: 20rpx;
			overflow: hidden;
			display: flex;
			justify-content: center;
			align-items: center;

			image {
				width: 100%;
				height: 205rpx;
				padding: 0;
				margin: 0;
			}
		}

		.seckill-item-new-name {
			white-space: normal;
			margin: 30rpx 0 20rpx 0;
			font-size: $ns-font-size-xm;
			color: #333333;
			line-height: 1.3;
			height: 64rpx;
			// height: 60rpx;
			word-break: break-all;
			text-overflow: ellipsis; //显示为省略号
			display: -webkit-box; //对象作为伸缩盒子模型显示
			-webkit-box-orient: vertical; //设置或检索伸缩盒对象的子元素的排列方式
			-webkit-line-clamp: 2; //显示行数## 标题文字 ##
			overflow: hidden;
		}

		.seckill-item-new-price {
			font-size: 36rpx;
			line-height: 1;

			text:first-child {
				font-size: $ns-font-size-xm;
			}
		}

		.seckill-item-old-price {
			font-size: $ns-font-size-sm;
			color: $ns-text-color-gray;
			text-decoration: line-through;
			line-height: 1;
		}

		.song_maidou {
			background: #FFEFEF;
			font-size: 22rpx;
			border-radius: 8rpx;
			width: 70%;
			margin-top: 10rpx;

			.song {
				color: #333333;
				margin-left: 10rpx;
			}

			.mdnum {
				color: #FC3533;
			}

		}
	}
	.postter{
		position: fixed;
		z-index: 1000;
		top: 20rpx;
		background: red;
		image{
			width: 200rpx;
			height: 200rpx;
		}
	}
	.seemore{
		display: flex;
		// align-items: center;
		justify-content: center;
		padding-bottom: 120rpx;
		text{
			font-size: 28rpx;
			color: #999999;
		}
		image{
			width: 24rpx;
			height: 24rpx;
			margin-left: 10rpx;
			margin-top: 12rpx;
		}
	}
</style>
