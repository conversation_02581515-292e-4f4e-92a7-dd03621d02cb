<template>
  <view class="balances" :data-theme="themeStyle" :style="[themeColorVar]">
    <!-- #ifdef MP-WEIXIN -->
    <uni-nav-bar left-icon="back" :border="false" @clickLeft="goBack" :fixed="true" :statusBar="true" backgroundColor="transparent" leftText="钱包明细" color="#fff">
      <template>
<!--        <view class="page-title">钱包明细</view>-->
      </template>
    </uni-nav-bar>
    <!-- #endif -->
    <view class="balances-header">
      <image :src="$util.img('public/static/youpin/member/balance-heder-bg.png')" mode="widthFix" class="balances-header-bg"></image>
      <view class="balances-header-info">
        <view class="balances-header-info-left">
          <view class="balances-header-info-name">账户余额（元）</view>
          <view class="balances-header-info-money"><count-to :startVal="0" :endVal="balance_money" :decimals="2"></count-to></view>
        </view>
        <view class="balances-header-info-op" @click="redirectToLink('/otherpages/member/apply_withdrawal/apply_withdrawal')">提现</view>
      </view>
      <view class="balances-header-bottom">
        <view class="balances-header-bottom-left">
          <view class="balances-header-bottom-left-one" :class="{'balances-header-bottom-left-one-action':billType=='all'}" @click="changeBillType('all')">全部<view class="line"></view></view>
          <view class="balances-header-bottom-left-one" :class="{'balances-header-bottom-left-one-action':billType=='pay'}" @click="changeBillType('pay')">支出<view class="line"></view></view>
          <view class="balances-header-bottom-left-one" :class="{'balances-header-bottom-left-one-action':billType=='reward'}" @click="changeBillType('reward')">奖励<view class="line"></view></view>
          <view class="balances-header-bottom-left-one" :class="{'balances-header-bottom-left-one-action':billType=='other'}" @click="changeBillType('other')">其他<view class="line"></view></view>
        </view>
        <view class="balances-header-bottom-right">
          <view class="balances-header-bottom-right-one" @click="toFiltrate">
            <image :src="orderFilterBg" class="balances-header-bottom-right-one-icon"></image>筛选</view>
        </view>
      </view>
    </view>
    <mescroll-uni @getData="getData" ref="mescroll" top="500rpx">
      <block slot="list">
        <view class="balances-info" v-if="dataList.length">
          <view class="balances-info-statistics">
            <view class="balances-info-statistics-title">交易占比</view>
            <view class="balances-info-statistics-list" v-if="billType=='all' || billType=='pay'">
              <view class="balances-info-statistics-list-left">
                <image :src="$util.img('public/static/youpin/member/expend.png')" class="balances-info-statistics-list-left-img"></image>
                <text class="balances-info-statistics-list-left-name">支出</text>
              </view>
              <view class="balances-info-statistics-list-center">
                <progress :percent="statistics_data.pay_statistics.pro*100" stroke-width="8" border-radius="30" active activeColor="rgb(88, 165, 253)" :duration="10"/>
              </view>
              <view class="balances-info-statistics-list-right">
                <text class="balances-info-statistics-list-right-money" @click="changeBillType('pay')">￥{{statistics_data.pay_statistics.money}}<text class="iconfont iconright"></text></text>
              </view>
            </view>
            <view class="balances-info-statistics-list" v-if="billType=='all' || billType=='reward'">
              <view class="balances-info-statistics-list-left">
                <image :src="$util.img('public/static/youpin/member/reward.png')" class="balances-info-statistics-list-left-img"></image>
                <text class="balances-info-statistics-list-left-name">奖励</text>
              </view>
              <view class="balances-info-statistics-list-center">
                <progress :percent="statistics_data.reward_statistics.pro*100" stroke-width="8" border-radius="30" active activeColor="rgb(255, 186, 83)" :duration="10"/>
              </view>
              <view class="balances-info-statistics-list-right">
                <text class="balances-info-statistics-list-right-money" @click="changeBillType('reward')">￥{{statistics_data.reward_statistics.money}}<text class="iconfont iconright"></text></text>
              </view>
            </view>
            <view class="balances-info-statistics-list" v-if="billType=='all' || billType=='other'">
              <view class="balances-info-statistics-list-left">
                <image :src="$util.img('public/static/youpin/member/reimburse.png')" class="balances-info-statistics-list-left-img"></image>
                <text class="balances-info-statistics-list-left-name">其他</text>
              </view>
              <view class="balances-info-statistics-list-center">
                <progress :percent="statistics_data.other_statistics.pro*100" stroke-width="8" border-radius="30" active activeColor="rgb(96, 213, 101)" :duration="10"/>
              </view>
              <view class="balances-info-statistics-list-right">
                <text class="balances-info-statistics-list-right-money" @click="changeBillType('other')">￥{{statistics_data.other_statistics.money}}<text class="iconfont iconright"></text></text>
              </view>
            </view>
          </view>
          <view class="balances-info-bill">
            <view class="balances-info-bill-title">账单明细</view>
            <!-- 明细列表 -->
            <view class="balances-info-bill-list">
              <view v-for="(row, i) in dataList" :key="i" class="balances-info-bill-list-item">
                <view class="balances-info-bill-list-item-header">
                  <view class="balances-info-bill-list-item-header-left">{{row.monthStr}}</view>
                  <view class="balances-info-bill-list-item-header-right">支出{{row.pay_money}}，收入{{row.income_money}}</view>
                </view>
                <view class="balances-info-bill-list-item-wrap" v-for="(item, index) in row.record_data" :key="index">
                  <view class="balances-info-bill-list-item-wrap-left">
                    <text class="balances-info-bill-list-item-wrap-left-name">{{ item.type_name }}</text>
                    <text class="balances-info-bill-list-item-wrap-left-time">{{item.create_time}}</text>
                  </view>
                  <view class="balances-info-bill-list-item-wrap-right">
                    <text class="balances-info-bill-list-item-wrap-right-add" v-if="item.account_data && parseFloat(item.account_data)>=0"><text>+</text>{{ item.account_data ? item.account_data : 0 }}</text>
                    <text class="balances-info-bill-list-item-wrap-right-subtract" v-if="item.account_data && parseFloat(item.account_data)<0">{{ item.account_data ? item.account_data : 0 }}</text>
                    <text class="balances-info-bill-list-item-wrap-right-balance">余额{{ item.balance }}</text>
                  </view>
                </view>
              </view>
            </view>
          </view>
        </view>
        <!-- 无明细列表 -->
        <view v-else>
          <ns-empty :fixed="false" :isIndex="false"></ns-empty>
        </view>
      </block>
    </mescroll-uni>
    <uni-popup ref="filtratePopup" type="bottom" :mask-click="false" >
      <view class="filtrate">
        <view class="filtrate-title">选择筛选项：</view>
        <view class="filtrate-row">
          <view class="filtrate-row-title">统计时段：</view>
          <view class="filtrate-row-list">
            <text class="filtrate-row-list-one" v-for="(item,index) in time_types" :key="index" :class="{'filtrate-row-list-one-action': tmpPeriod==index}" @click="changePeriod(index)">{{item}}</text>
          </view>
        </view>
        <view class="filtrate-row">
          <view class="filtrate-row-title">交易类型：</view>
          <view class="filtrate-row-list">
            <text class="filtrate-row-list-one" v-for="(item,index) in trade_types" :key="index" :class="{'filtrate-row-list-one-action': tmpTransactionType==index}" @click="changeTransactionType(index)">{{item}}</text>
          </view>
        </view>
        <view class="filtrate-op">
          <view class="filtrate-op-cancel" @click="filtrateCancel">取消</view>
          <view class="filtrate-op-confirm" @click="filtrateConfirm">确认</view>
        </view>
      </view>
    </uni-popup>
    <loading-cover ref="loadingCover"></loading-cover>
  </view>
</template>

<script>
import uniPopup from '@/components/uni-popup/uni-popup.vue';
import countTo from '@/components/dash-countTo/dash-countTo.vue'
import uniNavBar from '@/components/uni-nav-bar/uni-nav-bar.vue';
import golbalConfig from "../../../common/mixins/golbalConfig";

export default {
  components: {uniPopup,countTo,uniNavBar},
  mixins:[golbalConfig],
  data() {
    return {
      balanceInfo: {
        balance: 0,
        balance_money: 0
      },
      balance_money: 0,
      billType: 'all',
      statistics_data:{},
      time_types:{},
      trade_types:{},
      tmpPeriod: 'all',
      period: 'all',
      tmpTransactionType: 'all',
      transactionType: 'all',
      withdrawConfig: null,
      memberrechargeConfig: null,
      timeList:[],
      dataList: [],
      orderFilterBg: ''
    };
  },
  computed: {
    Development() {
      return this.$store.state.Development
    },
    themeStyle() {
      return 'theme-' + this.$store.state.themeStyle
    },
    addonIsExit(){
      return this.$store.state.addonIsExit
    }
  },
  async onLoad(){
    uni.setNavigationBarColor({
      backgroundColor: '#ffffff',
      frontColor: '#ffffff'
    })
    let color = this.$util.colorToHex('rgba(128, 128, 128, 1)').slice(1);
    this.orderFilterBg = encodeURI(this.$util.img( `api/website/svgChangeFillColor?svg_name=order-filter&color=${color}`))
    if (this.$refs.loadingCover) this.$refs.loadingCover.show();
    await this.getMemberaccountSelectTypes()
  },
  async onShow() {
    // 刷新多语言
    this.$langConfig.refresh();
    if (!uni.getStorageSync('token')) {
      this.$util.redirectTo('/pages/member/index/index', {},'redirectTo');
    }
    // this.getUserInfo();
    // this.getWithdrawConfig();
    // this.getMemberrechargeConfig();

  },
  onReady(){
    this.$store.dispatch('writeIncomeInfo',{income_money: 0, income_nums: 0})
  },
  methods: {
    goBack(){
      this.$util.goBack()
    },
    //获取余额信息
    getUserInfo() {
      this.$api.sendRequest({
        url: '/api/memberaccount/info',
        data: {
          account_type: 'balance,balance_money'
        },
        success: res => {
          if (res.data) {
            this.balanceInfo = res.data;
          } else {
            this.$util.showToast({ title: res.message });
          }
          if (this.$refs.loadingCover) this.$refs.loadingCover.hide();
        },
        fail: res => {
          mescroll.endErr();
          if (this.$refs.loadingCover) this.$refs.loadingCover.hide();
        }
      });
    },
    /**
     * 获取余额提现配置
     */
    getWithdrawConfig() {
      this.$api.sendRequest({
        url: '/api/memberwithdraw/config',
        success: res => {
          if (res.code >= 0 && res.data) {
            this.withdrawConfig = res.data;
          }
        }
      });
    },
    /**
     * 获取充值提现配置
     */
    getMemberrechargeConfig() {
      this.$api.sendRequest({
        url: '/memberrecharge/api/memberrecharge/config',
        success: res => {
          if (res.code >= 0 && res.data) {
            this.memberrechargeConfig = res.data;
          }
        }
      });
    },

    refreshData(){
      this.$refs.mescroll.refresh()
    },
    redirectToLink(url) {
      this.$util.redirectTo(url);
    },

    changeBillType(val){
      if(this.billType!=val){
        this.billType = val
        // if (this.$refs.loadingCover) this.$refs.loadingCover.show();
        this.refreshData()
      }
    },

    toFiltrate(){
      this.tmpPeriod = this.period
      this.tmpTransactionType = this.transactionType
      this.$refs.filtratePopup.open()
    },
    changePeriod(period){
      this.tmpPeriod = period
    },
    changeTransactionType(transactionType){
      this.tmpTransactionType = transactionType
    },
    filtrateCancel(){
      this.$refs.filtratePopup.close()
    },
    filtrateConfirm(){
      this.period = this.tmpPeriod
      this.transactionType = this.tmpTransactionType
      this.$refs.filtratePopup.close()
      if (this.$refs.loadingCover) this.$refs.loadingCover.show();
      this.refreshData()
    },
    async getMemberaccountSelectTypes(){
      try{
        let res = await this.$api.sendRequest({
          url: this.$apiUrl.memberaccountSelectTypesUrl,
          async: false,
          data: {}
        })
        if(res.code==0){
          this.trade_types = res.data.trade_types
          this.time_types = res.data.time_types
        }
      }catch (e) {

      }
    },
    getData(mescroll) {
      try{
        this.$api.sendRequest({
          // url: '/api/memberaccount/page',
          url: this.$apiUrl.memberaccountWalletInfoUrl,
          data: {
            page_size: mescroll.size,
            page: mescroll.num,
            period_time: this.period,
            trade_type: this.transactionType,
            category: this.billType
          },
          success: res => {
            this.statistics_data = res.data.statistics_data
            this.balance_money = parseFloat(res.data.balance_money)
            let newArr = [];
            let msg = res.message;
            if (res.code == 0 && res.data) {
              newArr = res.data.list;
            } else {
              this.$util.showToast({
                title: msg
              });
            }
            mescroll.endSuccess(newArr.length);
            //设置列表数据
            if (mescroll.num == 1) this.dataList = []; //如果是第一页需手动制空列表
            let add_list = []
            for (let i = 0; i < newArr.length; i++) {
              let has_index = null
              let has_item = this.dataList.find((item,j)=>{
                if (item.monthStr == newArr[i].monthStr){
                  has_index = j
                  return true
                }else{
                  return false
                }
              })
              if(has_item){
                let one = this.dataList[has_index]
                one.record_data = one.record_data.concat(newArr[i].record_data)
                this.$set(this.dataList, has_index, one)
              }else{
                add_list.push(newArr[i])
              }
            }
            this.dataList = this.dataList.concat(add_list); //追加新数据
            if (this.$refs.loadingCover) this.$refs.loadingCover.hide();
          },
          fail: res => {
            mescroll.endErr();
            if (this.$refs.loadingCover) this.$refs.loadingCover.hide();
          }
        });
      } catch (e) {

      }
    },
    toDetail(id) {
      this.$util.redirectTo('/otherpages/member/balance_detail/balance_detail', {
        id: id
      });
    },
    /**
     *分享参数组装
     */
    getSharePageParams() {
      return this.$util.unifySharePageParams('/otherpages/shop/home/<USER>', this.shareTitle || '先迈商城',
          '',{}, this.$util.img(this.shareImg) || this.$util.img('public/static/youpin/home_share.jpg'))
    }
  },
  /**
   * 自定义分享内容
   * @param {Object} res
   */
  onShareAppMessage(res) {
    let { title, link, imageUrl, query } = this.getSharePageParams()
    return this.$buriedPoint.pageShare(link , imageUrl, title);
  },
};
</script>

<style lang="scss">
page{
background-color: rgb(250, 250, 250);
}
 /deep/ .mescroll-uni{
   background-color: transparent;
   border-radius: 30rpx;
   height: calc(100vh - 420rpx)!important;
   width: 710rpx!important;
   box-sizing: border-box;
   margin: 0 auto;
 }

.balances {
  box-sizing: border-box;
  &-header{
    width: 100%;
    height: 420rpx;
    background-color: var(--custom-brand-color);
    position: relative;
    border-radius: 0 0 40rpx 40rpx;
    &-bg{
      width: 100%;
      height: 480rpx;
      position: absolute;
      left: 0;
      bottom: 0;
    }
    &-info{
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 0 30rpx;
      box-sizing: border-box;
      width: 100%;
      position: absolute;
      left: 0;
      bottom: 98rpx;
      &-left{
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: flex-start;
      }
      &-name{
        font-size: 28rpx;
        font-weight: 400;
        line-height: 32.82rpx;
        color: rgba(255, 255, 255, 1);
      }
      &-money{
        font-size: 60rpx;
        font-weight: 700;
        line-height: 70.32rpx;
        color: rgba(255, 255, 255, 1);
        display: inline-flex;
        justify-content: center;
        align-items: center;
        margin-top: 12rpx;
      }
      &-op{
        width: 168rpx;
        height: 80rpx;
        border-radius: 40rpx;
        background: rgba(255, 255, 255, 1);
        font-size: 32rpx;
        font-weight: 400;
        line-height: 32rpx;
        color: rgba(246, 93, 114, 1);
        margin: 0;
        margin-top: 10rpx;
        display: flex;
        justify-content: center;
        align-items: center;
      }
    }
    &-bottom{
      position: absolute;
      left: 50%;
      bottom: -60rpx;
      transform: translateX(-50%);
      width: 710rpx;
      height: 120rpx;
      border-radius: 20px;
      background-color: white;
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 0 32rpx;
      box-sizing: border-box;
      &-left{
        display: flex;
        align-items: center;
        &-one{
          width: 64rpx;
          height: 38rpx;
          font-size: 32rpx;
          font-weight: 400;
          line-height: 37.5rpx;
          color: rgba(56, 56, 56, 1);
          box-sizing: border-box;
          position: relative;
          &:not(:first-child){
            margin-left: 60rpx;
          }
          .line{
            width: 36rpx;
            height: 6rpx;
            background: transparent;
            border-radius: 3rpx;
            margin: 0 auto;
          }
          &-action{
            font-weight: bolder;
            color: var(--custom-brand-color);
            position: relative;
            .line {
              width: 24rpx;
              height: 24rpx;
              overflow: hidden;
              background: transparent;
              border-bottom-left-radius: 28rpx;
              border-bottom-right-radius: 0rpx;
              border-left: 6rpx solid var(--custom-brand-color);
              border-bottom: 6rpx solid var(--custom-brand-color);
              position: absolute;
              left: 50%;
              bottom: -20rpx;
              transform: translateX(-50%) rotate(-45deg);
            }
          }
        }
      }
      &-right{
        margin-top: -8rpx;
        &-one{
          font-size: 32rpx;
          font-weight: 400;
          line-height: 46.34rpx;
          color: rgba(128, 128, 128, 1);
          box-sizing: border-box;
          display: flex;
          align-items: center;
          &-icon{
            width: 36rpx;
            height: 36rpx;
            vertical-align: middle;
            margin-right: 4rpx;
          }
        }
      }
    }
  }
  &-info{
    //border-radius: 30rpx;
    width: 702rpx;
    margin: 0 auto;
    box-sizing: border-box;
    &-statistics{
      background-color: white;
      border-radius: 20rpx;
      padding: 24rpx;
      box-sizing: border-box;
      margin-bottom: 20rpx;
      &-title{
        font-weight: bolder;
        font-size: 28rpx;
        color: rgb(51, 51, 51);
        margin-bottom: 20rpx;
      }
      &-list{
        display: flex;
        align-items: center;
        margin-bottom: 20rpx;
        &-left{
          display: flex;
          align-items: center;
          margin-right: 10rpx;
          &-img{
            width: 39rpx;
            height: 39rpx;
          }
          &-name{
            font-size: 28rpx;
            font-weight: 400;
            color: rgb(121, 121, 121);
            margin-left: 10rpx;
          }
        }
        &-center{
          width: 320rpx;
          /* #ifdef H5 */
          /deep/ .uni-progress-bar{
            border-radius: 30rpx;
          }
          /deep/ .uni-progress-inner-bar{
            border-radius: 30rpx;
          }
          /* #endif */
		  /* #ifdef MP-WEIXIN */
		  /deep/ .wx-progress-inner-bar{
		    border-radius: 30rpx;
		  }
		  /* #endif */
        }
        &-right{
          flex-grow: 1;
          text-align: right;
          //max-width: 180rpx;
          overflow-x: hidden;
          &-money{
            font-size: 28rpx;
            font-weight: 400;
            color: rgb(51, 51, 51);
            .iconfont{
              font-size: 22rpx;
              color: rgb(121, 121, 121);
            }
          }
        }
      }
    }
    &-bill{
      background-color: white;
      border-radius: 20rpx;
      padding: 24rpx;
      box-sizing: border-box;
      &-title{
        font-size: 28rpx;
        font-weight: bolder;
        color: rgb(51, 51, 51);
        margin-bottom: 24rpx;
      }
      &-list{
        &-item{
          &-header{
            display: flex;
            justify-content: space-between;
            align-items: center;
            background-color: rgb(242, 242, 242);
            border-radius: 10rpx;
            padding: 0 20rpx;
            height: 64rpx;
            line-height: 64rpx;
            box-sizing: border-box;
            position: sticky;
            top: 0rpx;
            &-left{
              color: rgba(0, 0, 0, 0.314);
              font-size: 24rpx;
              line-height: 64rpx;
              font-weight: 400;
            }
            &-right{
              color: rgba(0, 0, 0, 0.314);
              font-size: 24rpx;
              font-weight: 400;
              line-height: 64rpx;
            }
          }
          &-wrap{
            display: flex;
            justify-content: space-between;
            padding: 24rpx;
            box-sizing: border-box;
            background: rgba(250, 250, 250, 1);
            border-radius: 20rpx;
            margin-top: 20rpx;
            &-left{
              display: flex;
              flex-direction: column;
              &-name{
                font-size: 28rpx;
                font-weight: bolder;
                color: rgb(51, 51, 51);
              }
              &-time{
                font-size: 24rpx;
                color: rgba(0, 0, 0, 0.314);
                font-weight: 400;
              }
            }
            &-right{
              display: flex;
              flex-direction: column;
              align-items: flex-end;
              &-add{
                color: var(--custom-brand-color);
                font-size: 28rpx;
                font-weight: 400;
              }
              &-subtract{
                font-size: 28rpx;
                font-weight: 400;
                color: rgb(51, 51, 51);
              }
              &-balance{
                font-size: 28rpx;
                font-weight: 400;
                color: rgba(0, 0, 0, 0.314);
              }
            }
          }
        }
      }
    }
  }
}

.empty {
  width: 100%;
  height: 500rpx;
  display: flex;
  justify-content: center;
  flex-direction: column;
  align-items: center;

  .iconfont {
    font-size: 50rpx;
    margin-bottom: $ns-margin;
  }
}


.balance-wrap {
  height: 336rpx;
  position: relative;
  display: flex;
  margin: 24rpx;
  flex-direction: column;
  border-radius: 20rpx;
  overflow: hidden;
  padding: 44rpx;
  box-sizing: border-box;
  .balance-top {
    display: flex;
    justify-content: space-between;
    .balance-num {
      display: flex;
      flex-direction: column;
    }
    .balance-tips {
      font-size: $ns-font-size-sm;
      margin-bottom: 20rpx;
    }
    .withdraw-btn {
      height: 44rpx;
      background-color: #fff;
      border-radius: 50px;
      padding: 2rpx 40rpx;
      line-height: 44rpx;
      font-size: 20rpx;
    }
    .all-alance {
      line-height: 1;
      font-size: 54rpx;
      font-weight: 400;
      color: #ffffff;
    }
  }

  .balance-bottom {
    flex: 2;
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-right: 104rpx;

    .balance-bottom-item {
      display: flex;
      flex-direction: column;
      justify-content: center;
      .balance-title {
        font-size: $ns-font-size-base;
        margin-bottom: 10rpx;
      }

      .balance {
        line-height: 1;
        font-size: 36rpx;
        color: #ffffff;
      }
    }
  }
}
.operation {
  width: 100%;
  top: 500rpx;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  margin-top: 100rpx;
  .recharge {
    color: #ffffff;
    width: 80%;
    border-radius: 50rpx;
    text-align: center;
    padding: 20rpx 0;
  }

  .withdraw {
    margin-top: 50rpx;
    text-align: center;
    width: 534rpx;
    height: 78rpx;
    line-height: 78rpx;
    box-sizing: border-box;
    border-radius: 50rpx;
    color: #fff;
    font-size: $ns-font-size-base;
  }
}
.balance-other-info {
  margin-top: 50rpx;
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  top: 750rpx;
  margin-top: 50rpx;
  .balance-detail {
    line-height: 1;
    padding: 0 20rpx;
    font-size: $ns-font-size-base;
  }
  .recharge-record {
    line-height: 1;
    padding: 0 20rpx;
    font-size: $ns-font-size-base;
  }
}
.balance-xian {
  width: 2rpx;
  height: 20rpx;
  background-color: #383838;
}
.balance-title {
  color: rgba(255, 255, 255, 0.7);
}
.filtrate{
  width: 100%;
  min-height: 568rpx;
  padding: 32rpx;
  box-sizing: border-box;
  &-title{
    color: #333333;
    font-size: 28rpx;
    font-weight: 400;
  }
  &-row{
    margin-top: 24rpx;
    &-title{
      color: #797979;
      font-size: 24rpx;
      font-weight: 400;
      margin-bottom: 20rpx;
    }
    &-list{
      display: flex;
      flex-wrap: wrap;
      &-one{
        width: 162rpx;
        height: 75rpx;
        line-height: 75rpx;
        background-color: rgba(242, 242, 242, 1);
        border-radius: 40rpx;
        color: #333333;
        font-size: 24rpx;
        display: flex;
        justify-content: center;
        align-items: center;
        box-sizing: border-box;
        margin-bottom: 24rpx;
        margin-right: 8rpx;
        &-action{
          color: white;
          background-color: var(--custom-brand-color);
          border: 1px solid var(--custom-brand-color);
        }
      }
    }
  }
  &-op{
    display: flex;
    justify-content: center;
    align-items: center;
    margin-top: 24rpx;
    &-cancel{
      width: 320rpx;
      height: 80rpx;
      line-height: 80rpx;
      background-color: var(--custom-brand-color-10);
      border: 1px solid var(--custom-brand-color-10);
      border-radius: 40rpx;
      color: var(--custom-brand-color);
      font-size: 24rpx;
      margin: 0;
      display: flex;
      justify-content: center;
      align-items: center;
      margin-right: 80rpx;
    }
    &-confirm{
      width: 320rpx;
      height: 80rpx;
      line-height: 80rpx;
      background-color: var(--custom-brand-color);
      //border: 1px solid rgba(161, 115, 47, 1);
      border-radius: 40rpx;
      color: white;
      font-size: 24rpx;
      margin: 0;
      display: flex;
      justify-content: center;
      align-items: center;
    }
  }
}
/deep/ .uni-navbar{
  position: fixed;
  z-index: 999;
}
/deep/ .uni-navbar__header-btns-left{
  width: 200rpx!important;
}
/deep/ .uni-popup__wrapper{
  border-radius: 30rpx 30rpx 0 0;
}
.page-title{
  width: 360rpx;
  overflow:hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  -o-text-overflow:ellipsis;
  text-align: center;
}
</style>
