<template>
	<view class="coupon" :style="[themeColorVar]">
		<view class="status-box">
			<view class="status-list" :class="selectIndex == item.status ? 'active':''" v-for="(item, index) in statusList" :key="index" @click="selectIndex = item.status; reset()">{{ item.name }}</view>
		</view>
		<template v-if="list.length">
			<view class="coupon-box">
				<view class="coupon-list" v-for="(item, index) in list" :key="index" :style="{'background-image':`url(${item.state == 1 ? $util.img('public/static/youpin/coupon_ysy.png'):$util.img('public/static/youpin/coupon_no.png')})`}">
					<view class="coupon-left">
						<view>
							<text :style="{color: item.state == 1 ? 'var(--custom-brand-color)':'#fff'}">￥</text>
							<text :style="{color: item.state == 1 ? 'var(--custom-brand-color)':'#fff'}">{{ parseFloat(item.money) }}</text>
						</view>
					</view>
					<view class="coupon-right">
						<view class="coupon-name">{{ item.desc }}</view>
						<view class="coupon-time">有效期：{{ $util.timeStampTurnTime(item.end_time) }}</view>
						<view class="use-btn" :class ="item.state == 1 ? '':'disabled'" @click="toGoodList(item)">{{statusList[parseInt(item.state) - 1].btn}}</view>
					</view>
				</view>
				<view class="empty-list-text" v-if="scrollLoading">没有更多内容</view>
				<!-- 下拉加载动画 -->
				<ns-loading v-if="showLoading"></ns-loading>
			</view>
		</template>
		<view v-else class="empty-box">
			<image :src="$util.img('public/static/youpin/empty_coupon.png')" mode="" />
			<view class="empty-info">暂无可用优惠券</view>
		</view>
	</view>
</template>

<script>
import scrollLoading from '@/common/mixins/scroll-loading.js'
import golbalConfig from "../../../common/mixins/golbalConfig";
export default {
	mixins: [scrollLoading,golbalConfig],
	data() {
		return {
			list: [],
			statusList: [{
				status: '1',
				name: '已领取',
				btn: '去使用'
			},{
        status: '2',
				name: '已使用',
				btn: '已使用'
			},{
				status: '3',
				name: '已过期',
				btn: '已过期'
			}],
			selectIndex: '1',
			barHeight: 0
		};
	},
  onLoad(options){
    this.selectIndex = options.selectIndex || '1'
  },
	onShow() {
		let that = this
		that.reset()
	},
	methods: {
		// 初始化参数
		reset() {
			uni.showLoading({
				title: '加载中'
			});
			this.page = 1
			this.list = []
			this.scrollLoading = false
			this.getInit()
		},
		//获取优惠券列表
		getInit() {
			this.$api.sendRequest({
				url:this.$apiUrl.memberpage,
				data: {
					page: this.page,
					page_size: this.page_size,
					state: parseInt(this.selectIndex),
					token: uni.getStorageSync('token')
				},
				success: res => {
					uni.hideLoading();
          if(res.code==0){
            let list = res.data.list
            if(list.length) {
              this.page ++;
              this.list = this.list.concat(list);
              if(list.length < this.page_size) {
                this.scrollLoading = true
              }
            }else{
              this.scrollLoading = true
            }
          }
					this.hideLoading()
				},
				fail: res => {
					uni.hideLoading();
				}
			});
		},
		//去使用优惠券
		toGoodList(item) {
			if(item.state == 3) {
				return false
			}
			this.$util.redirectTo('/otherpages/goods/coupon_goods_list/coupon_goods_list', {
				goodscoupon_type_id: item.goodscoupon_type_id
			})
		},
    /**
     *分享参数组装
     */
    getSharePageParams() {
      return this.$util.unifySharePageParams('/otherpages/shop/home/<USER>', this.shareTitle || '先迈商城',
          '',{}, this.$util.img(this.shareImg) || this.$util.img('public/static/youpin/home_share.jpg'))
    }
	},
  /**
   * 自定义分享内容
   * @param {Object} res
   */
  onShareAppMessage(res) {
    let { title, link, imageUrl, query } = this.getSharePageParams()
    return this.$buriedPoint.pageShare(link , imageUrl, title);
  },
};
</script>

<style lang="scss" scoped>
.coupoon{
	position: relative;
}
.status-box{
	position: fixed;
	left: 0;
	right: 0;
	top: 0;
	display: flex;
	height: 100rpx;
	background-color: #fff;
	color: #333333;
	font-weight: 400;
	z-index: 1;
	.status-list{
		position: relative;
		display: flex;
		align-items: center;
		justify-content: center;
		flex: 1;
	}
	.active{
		font-weight: bold;
		&::after{
			position: absolute;
			bottom: 20rpx;
			left: 50%;
			transform: translateX(-50%);
			content: '';
			width: 36rpx;
			height: 8rpx;
			background-color: var(--custom-brand-color);
			border-radius: 6rpx;
		}
	}
}
.coupon-box{
	padding: 120rpx 20rpx 110rpx;
	.coupon-list{
		display: flex;
		height: 202rpx;
		// background-size: cover;
		background-size: 702rpx 200rpx;
		background-position: center;
		background-repeat: no-repeat;
		margin-bottom: 20rpx;
		.coupon-left{
			display: flex;
			justify-content: center;
			align-items: center;
			flex-shrink: 0;
			width: 218rpx;
			&>view{
				&>text:first-child{
					font-size: 28rpx;
					// color: #F2270C;
					font-weight: bold;
				}
				&>text:last-child{
					font-size: 52rpx;
					// color: #F2270C;
					font-weight: bold;
				}
			}
		}
		.coupon-right{
			position: relative;
			flex: 1;
			padding: 22rpx 24rpx 28rpx;
			.coupon-name{
				font-size: 30rpx;
				line-height: 42rpx;
				color: #333;
				font-weight: bold;
			}
			.coupon-time{
				position: absolute;
				bottom: 64rpx;
				color: #999;
				font-size: 22rpx;
				line-height: 42rpx;
			}
      .use-btn{
        background: var(--custom-brand-color);
        text-align: center;
        position: absolute;
        width: 120rpx;
        height: 48rpx;
        color: #fff;
        font-size: 24rpx;
        line-height: 48rpx;
        border-radius: 24rpx;
        right: 24rpx;
        bottom: 16rpx;
      }
      .disabled{
        border: 1px solid #CCCCCC;
        background-color: #fff;
        color: #999;
      }
			image{
				position: absolute;
				right: 16rpx;
				top: 50%;
				transform: translateY(-50%);
				width: 40rpx;
				height: 40rpx;
			}
		}
	}
}
.empty-box{
	display: flex;
	flex-direction: column;
	align-items: center;
	image{
		width: 402rpx;
		height: 282rpx;
		margin: 208rpx 0 42rpx;
	}
	.empty-info{
		display: flex;
		align-items: center;
		justify-content: center;
		height: 100%;
		font-size: 32rpx;
		line-height: 44rpx;
	}
}

</style>
