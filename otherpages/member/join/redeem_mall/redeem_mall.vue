<template>
    <view class="redeem-mall" :data-theme="themeStyle" :style="[themeColorVar]">
      <!-- #ifdef MP-WEIXIN -->
      <uni-nav-bar left-icon="back" :border="false" @clickLeft="$util.goBack" :fixed="true" :statusBar="true" backgroundColor="transparent" :leftText="title" color="#fff">
        <template #default>
          <view class="nav-points" @click="toContributionRecords">
            贡献值:<text class="nav-points-text">{{member_points || 0}}</text>
          </view>
        </template>
      </uni-nav-bar>
      <!-- #endif -->
      <!-- #ifdef H5 -->
      <uni-nav-bar left-icon="back" :border="false" @clickLeft="$util.goBack" :fixed="true" :statusBar="true" backgroundColor="transparent" :leftText="title" color="#fff">
        <template #right>
          <view class="nav-points" @click="toContributionRecords">
            贡献值:<text class="nav-points-text">{{member_points || 0}}</text>
          </view>
        </template>
      </uni-nav-bar>
      <!-- #endif -->
      <view class="bg-img" :style="{backgroundImage: `url(${$util.img('public/static/youpin/member/contribution/beijing.png')})`}"></view>
      <view class="container">
        <view class="container-list">
          <view class="container-list-one" v-for="(item,index) in datalist" :key="index">
            <view class="container-list-one-info" @click="toGoodsCoupon(item)">
              <view class="container-list-one-info-top">
                <view class="container-list-one-info-top-title">满{{Number(item.at_least)}}减{{Number(item.money)}}</view>
                <view class="container-list-one-info-top-desc">{{item.use_scenario_text}}</view>
              </view>
              <view class="container-list-one-info-bottom">
                <text class="container-list-one-info-bottom-left-circle"></text>
                <text class="container-list-one-info-bottom-right-circle"></text>
                <image :src="$util.img(goods.goods_image)" @error="imageError(goods,j)"
                       mode='aspectFill' class="container-list-one-info-bottom-img" v-for="(goods,j) in item.use_goods" :key="j">
                </image>
              </view>
            </view>
            <view class="container-list-one-op">
              <text class="container-list-one-op-num">{{item.points}} 贡献值</text>
              <button size="mini" class="container-list-one-op-button" @click="toExchange(item)">兑换</button>
            </view>
          </view>
        </view>
      </view>

      <uni-popup ref="exchangePopupRef" type="center" :maskClick="false">
        <view class="exchange-popup">
          <view class="exchange-popup-top">
            <view class="exchange-popup-top-title">满{{Number(one_data.at_least)}}减{{Number(one_data.money)}}</view>
            <view class="exchange-popup-top-desc">{{one_data.use_scenario_text}}</view>
          </view>
          <view class="exchange-popup-bottom">
            <view class="exchange-popup-bottom-desc">{{ one_data.is_allow ? `当前你的余额为${member_points}，确定要消耗${one_data.points}贡献值兑换该商品？` : '当前你的贡献值余额不足，无法兑换该物品'}}</view>
            <view class="exchange-popup-bottom-op">
              <template v-if="one_data.is_allow">
                <button class="exchange-popup-bottom-op-button exchange-popup-bottom-op-cancel" @click="closeExchangePopup">取消</button>
                <button class="exchange-popup-bottom-op-button exchange-popup-bottom-op-submit" @click="confirmExchange">兑换</button>
              </template>
              <button class="exchange-popup-bottom-op-button exchange-popup-bottom-op-cancel" v-else @click="closeExchangePopup">返回</button>
            </view>
          </view>
        </view>
      </uni-popup>

      <uni-popup ref="goodsCouponPopupRef" type="center" :maskClick="false">
        <view class="to-popup">
          <view class="to-popup-title">兑换成功，请去先迈商城小程序查看并使用该优惠券</view>
          <view class="to-popup-bottom-op">
            <button class="to-popup-bottom-op-button to-popup-bottom-op-cancel" @click="$refs.goodsCouponPopupRef.close()">返回</button>
            <button class="to-popup-bottom-op-button to-popup-bottom-op-submit" @click="toUse">去使用</button>
          </view>
        </view>
      </uni-popup>

      <loading-cover ref="loadingCover"></loading-cover>
    </view>
</template>

<script>
// #ifdef H5
import {isOnXianMaiApp, launchMiniProgram} from "@/common/js/h5/appOP";
// #endif
import UniNavBar from "@/components/uni-nav-bar/uni-nav-bar.vue";
import golbalConfig from "@/common/mixins/golbalConfig";
import system from "@/common/js/system";
    export default {
      name: "redeem_mall",
      components:{
        UniNavBar,
      },
      mixins: [golbalConfig],
      data() {
          return {
            title: '贡献值兑换',
            member_points: 0,
            page:1,
            page_size: 10,
            loading: false,
            finished: false,
            datalist: [],
            one_data:{},
            // #ifdef H5
            isOnXianMaiApp:isOnXianMaiApp,
            // #endif
          };
      },
      async onLoad(){
        // 刷新多语言
        this.$langConfig.refresh();
        await system.wait_staticLogin_success();
        await this.getDataList()
      },
      async onReachBottom(){
        if(this.finished || this.loading){
          return
        }
        await this.getDataList()
      },
      methods: {
        async getDataList(){
          if(this.finished || this.loading){
            return
          }
          this.loading = true
          try{
            let res = await this.$api.sendRequest({
              url: this.$apiUrl.exchangeListUrl,
              async: false,
              data: {
                page: this.page,
                page_size: this.page_size,
              },
            })
            if (this.$refs.loadingCover) this.$refs.loadingCover.hide();
            if(res.code !=0){
              this.loading = false
              this.finished = true
              this.$util.showToast({
                title: res.message
              })
            }else if(res.code == 0){
              this.member_points = res.data.member_points
              for (let i = 0; i <res.data.list.length ; i++) {
                this.$set(this.datalist,this.datalist.length,res.data.list[i])
              }
              // 加载状态结束
              this.loading = false;
              if(this.datalist.length >= res.data.count){
                this.finished=true;
              }
              this.page=this.page+1
            }
          }catch (e) {
            if (this.$refs.loadingCover) this.$refs.loadingCover.hide();
            this.loading = false
            this.finished = true
            this.$util.showToast({
              title: e.message
            })
          }
        },
        imageError(item, j) {
          item.goods_image = this.$util.getDefaultImage().default_goods_img;
          this.$forceUpdate();
        },
        toContributionRecords() {
          console.log('aaaaa')
          this.$util.redirectTo('/otherpages/member/join/contribution_records/contribution_records')
        },
        toGoodsCoupon(item) {
          this.$util.redirectTo('/otherpages/goods/coupon_goods_list/coupon_goods_list', {
            goodscoupon_type_id: item.goodscoupon_type_id
          })
        },
        toExchange(data) {
          this.one_data = JSON.parse(JSON.stringify(data))
          this.one_data.is_allow = this.member_points >= this.one_data.points
          this.$refs.exchangePopupRef.open();
        },
        closeExchangePopup() {
          this.$refs.exchangePopupRef.close();
        },
        async resetData() {
          this.loading = false;
          this.finished = false;
          this.page = 1;
          this.datalist = [];
          await this.getDataList()
        },
        async confirmExchange() {
          if (!this.one_data.is_allow) {
            this.$util.showToast({
              title: '当前你的贡献值余额不足，无法兑换该物品'
            });
            return
          }
          uni.showLoading({
            title: '兑换中',
            mask: true
          })
          try{
           let res = await this.$api.sendRequest({
              url: this.$apiUrl.exchangeUrl,
              async: false,
              data: {
                exchange_goods_id: this.one_data.exchange_goods_id
              },
            })
            uni.hideLoading();
            if(res.code !=0){
              this.$util.showToast({
                title: res.message
              })
            }else{
              this.$refs.exchangePopupRef.close();
              this.$refs.goodsCouponPopupRef.open()
              await this.resetData()
            }
          }catch (e) {
            uni.hideLoading();
            this.$util.showToast({
              title: e.message
            })
          }
        },
        toUse() {
          // #ifdef H5
          if (this.isOnXianMaiApp) {
            launchMiniProgram(this.one_data.wx_url)
          } else {
            window.location.href = this.one_data.wx_url
          }
          // #endif
          // #ifdef MP
          this.$util.redirectTo(this.one_data.wx_url)
          // #endif
        },
      }
    }
</script>

<style lang="scss">
$nav-height: 44px;
/deep/.uni-navbar__header-btns-left{
  width: 200rpx!important;
}
/deep/.uni-navbar__header-btns{
  width: 200rpx!important;
}
.redeem-mall{
  position: relative;
  min-height: 100vh;
  background: linear-gradient(180deg, var(--custom-brand-color) 0%, var(--custom-brand-color-20) 100%);
  box-sizing: border-box;
  .bg-img{
    width: 750rpx;
    height: 422rpx;
    background-repeat: no-repeat;
    background-size: 100% 100%;
    position: absolute;
    top: 0;
    left: 0;
  }
  .nav-points {
    font-size: 24rpx;
    height: #{$nav-height};
    line-height: #{$nav-height};
    padding-top: 2rpx;
    box-sizing: border-box;
    color: #fff;
    &-text {
      font-weight: bold;
    }
  }
  .container{
    width: 702rpx;
    margin: 0 auto;
    position: relative;
    &-list{
      width: 100%;
      display: flex;
      justify-content: space-between;
      flex-wrap: wrap;
      &-one{
        width: 343rpx;
        background-color: white;
        border-radius: 12rpx;
        padding: 12rpx;
        box-sizing: border-box;
        margin-bottom: 20rpx;
        &-info{
          background: linear-gradient(158.431575225186deg, var(--custom-brand-color-70) -7%, var(--custom-brand-color) 60%);
          border-radius: 10rpx;
          padding-top: 32rpx;
          padding-bottom: 20rpx;
          box-sizing: border-box;
          &-top{
            box-sizing: border-box;
            padding-bottom: 20rpx;
            position: relative;
            &-title{
              font-size: 36rpx;
              font-weight: 700;
              color: #FFEC25;
              margin: 0;
              text-align: center;
            }
            &-desc{
              font-size: 28rpx;
              font-weight: 400;
              color: rgba(255, 255, 255, 1);
              margin: 0;
              text-align: center;
              margin-top: 5rpx;
            }
          }
          &-bottom{
            display: flex;
            justify-content: center;
            align-items: center;
            padding-top: 20rpx;
            box-sizing: border-box;
            border-top: 2rpx dashed rgba(255, 255, 255, 0.6);
            position: relative;
            height: 100rpx;
            &-left-circle{
              width: 12rpx;
              height: 20rpx;
              background: rgba(255, 255, 255, 1);
              border-radius: 0 10rpx 10rpx 0;
              position: absolute;
              left: 0;
              top: -12rpx;
            }
            &-right-circle{
              width: 12rpx;
              height: 20rpx;
              background: rgba(255, 255, 255, 1);
              border-radius: 12rpx 0 0 12rpx;
              position: absolute;
              right: 0;
              top: -12rpx;
            }
            &-img{
              width: 80rpx;
              height: 80rpx;
              border-radius: 10rpx;
              &:not(:first-child){
                margin-left: 10rpx;
              }
            }
          }
        }
        &-op{
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-top: 20rpx;
          padding-left: 20rpx;
          box-sizing: border-box;
          &-num{
            font-size: 28rpx;
            font-weight: 400;
            color: var(--custom-brand-color);
          }
          &-button{
            width: 100rpx;
            color: white;
            font-size: 26rpx;
            font-weight: 400;
            background: var(--custom-brand-color);
            border: none;
            height: 50rpx;
            line-height: 50rpx;
            padding: 0;
            box-sizing: border-box;
          }
        }
      }
    }
  }
  .exchange-popup{
    width: 450rpx;
    box-sizing: border-box;
    &-father{
      background: transparent;
    }
    &-top{
      background: var(--custom-brand-color);
      padding-top: 32rpx;
      padding-bottom: 16rpx;
      box-sizing: border-box;
      &-title{
        font-size: 32rpx;
        font-weight: 700;
        color: #FFEC25;
        margin: 0;
        text-align: center;
      }
      &-desc{
        font-size: 28rpx;
        font-weight: 400;
        color: #fff;
        text-align: center;
        margin: 0;
        margin-top: 10rpx;
      }
    }
    &-bottom{
      background-color: white;
      padding: 24rpx;
      box-sizing: border-box;
      &-desc{
        font-size: 28rpx;
        font-weight: 400;
        color: rgba(56, 56, 56, 1);
        margin: 0;
        text-align: center;
      }
      &-op{
        display: flex;
        justify-content: center;
        margin-top: 40rpx;
        &-button{
          display: flex;
          justify-content: center;
          align-items: center;
          width: 190rpx;
          height: 64rpx;
          margin: 0;
          padding: 0;
          background: linear-gradient(141.63deg, var(--custom-brand-color) 0%, var(--custom-brand-color-70) 100%);
          border-radius: 28rpx;
          font-size: 24rpx;
          font-weight: 500;
          color: #FFFFFF;
          border: none;
        }
        &-cancel{
          background: var(--custom-brand-color-20);
          color: var(--custom-brand-color);
        }
        &-submit{
          margin-left: 10rpx;
        }
      }
    }
  }
  .to-popup{
    width: 450rpx;
    box-sizing: border-box;
    padding: 32rpx;
    &-title{
      font-size: 28rpx;
      font-weight: 400;
      color: #333333;
      text-align: center;
    }
    &-bottom{
      background-color: white;
      padding: 24rpx;
      box-sizing: border-box;
      &-desc{
        font-size: 28rpx;
        font-weight: 400;
        color: rgba(56, 56, 56, 1);
        margin: 0;
        text-align: center;
      }
      &-op{
        display: flex;
        justify-content: center;
        margin-top: 40rpx;
        &-button{
          display: flex;
          justify-content: center;
          align-items: center;
          margin: 0;
          padding: 0;
          width: 190rpx;
          height: 64rpx;
          background: linear-gradient(141.63deg, var(--custom-brand-color) 0%, var(--custom-brand-color-70) 100%);
          border-radius: 28rpx;
          font-size: 24rpx;
          font-weight: 500;
          color: #FFFFFF;
          border: none;
        }
        &-cancel{
          background: var(--custom-brand-color-20);
          color: var(--custom-brand-color);
        }
        &-submit{
          margin-left: 10rpx;
        }
      }
    }
  }
}
</style>
