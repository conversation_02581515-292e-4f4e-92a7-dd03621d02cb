<template>
    <view class="contribution-records" :data-theme="themeStyle" :style="[themeColorVar]">
      <view class="container">
        <view class="header" id="headerFilter">
          <view class="header-left">
            <view class="header-left-get">获得：<text class="header-left-get-text">{{point_get}}</text></view>
            <view class="header-left-use">已使用：<text class="header-left-use-text">{{point_use}}</text></view>
          </view>
          <view class="header-right" @click="showFilter">
            <image :src="$util.img('public/static/youpin/member/contribution/filter.png')" alt="" class="header-right-icon"/>
            <text class="header-right-text">筛选</text>
          </view>
        </view>
        <view class="records" v-if="datalist.length">
          <view class="records-one" v-for="(item,index) in datalist" :key="index">
            <view class="records-one-left">
              <view class="records-one-left-info" v-if="item.point_type=='task_complete'">{{item.league_name}}<span>({{item.point_type_text}})</span></view>
              <view class="records-one-left-info" v-else>{{item.point_type_text}}</view>
              <view class="records-one-left-time">{{item.create_time}}</view>
            </view>
            <view class="records-one-right">
              <text class="records-one-right-text" :class="{'records-one-right-text-add' : item.point>0}">{{item.point>0 ? `+${item.point}` : item.point}}</text>
            </view>
          </view>
        </view>
        <view class="group-default" v-if="datalist.length < 1 && finished">
          <image :src="$util.img('public/static/youpin/empty_data.png')" class="group-default-img"/>
          <view class="group-default-text">暂无数据</view>
        </view>
      </view>

      <uni-popup ref="filterPopup" type="top" top="88rpx">
        <view class="filter-pop">
          <view class="filter-pop-group">
            <view class="filter-pop-group-title">时间段:<text class="filter-pop-group-title-text">(单选)</text></view>
            <view class="filter-pop-group-options">
              <text class="filter-pop-group-options-one" :class="{'filter-pop-group-options-one-selected': time_index=='current_month'}" @click="timeChoose('current_month')">本月</text>
              <text class="filter-pop-group-options-one filter-pop-group-options-one-middle" :class="{'filter-pop-group-options-one-selected': time_index=='last_month'}" @click="timeChoose('last_month')">上月</text>
              <text class="filter-pop-group-options-one" :class="{'filter-pop-group-options-one-selected': time_index=='current_year'}" @click="timeChoose('current_year')">今年</text>
              <text class="filter-pop-group-options-one" :class="{'filter-pop-group-options-one-selected': time_index=='custom'}" @click="timeChoose('custom')">{{ time_index=='custom' && date_text ? date_text : '自定义'}}</text>
            </view>
          </view>
          <view class="filter-pop-group">
            <p class="filter-pop-group-title">类型:<text class="filter-pop-group-title-text">(多选)</text></p>
            <view class="filter-pop-group-options">
              <text class="filter-pop-group-options-one" :class="{'filter-pop-group-options-one-selected':type_list.includes('')}" @click="typeChoose('')">全部</text>
              <text class="filter-pop-group-options-one filter-pop-group-options-one-middle" :class="{'filter-pop-group-options-one-selected':type_list.includes('task_complete')}" @click="typeChoose('task_complete')">完成加盟任务</text>
              <text class="filter-pop-group-options-one" :class="{'filter-pop-group-options-one-selected':type_list.includes('recommend_register')}" @click="typeChoose('recommend_register')">邀请好友</text>
              <text class="filter-pop-group-options-one" :class="{'filter-pop-group-options-one-selected':type_list.includes('add_shop_fans')}" @click="typeChoose('add_shop_fans')">粉丝增长</text>
              <text class="filter-pop-group-options-one filter-pop-group-options-one-middle" :class="{'filter-pop-group-options-one-selected':type_list.includes('recommend_browse')}" @click="typeChoose('recommend_browse')">好友浏览</text>
              <text class="filter-pop-group-options-one" :class="{'filter-pop-group-options-one-selected':type_list.includes('shop_fans_browse')}" @click="typeChoose('shop_fans_browse')">粉丝浏览</text>
              <text class="filter-pop-group-options-one" :class="{'filter-pop-group-options-one-selected':type_list.includes('recommend_shop')}" @click="typeChoose('recommend_shop')">升级店主</text>
              <text class="filter-pop-group-options-one filter-pop-group-options-one-middle" :class="{'filter-pop-group-options-one-selected':type_list.includes('month_empty')}" @click="typeChoose('month_empty')">月末清零</text>
              <text class="filter-pop-group-options-one filter-pop-group-options-one-middle" :class="{'filter-pop-group-options-one-selected':type_list.includes('goods_task_complete')}" @click="typeChoose('goods_task_complete')">推广任务</text>
              <text class="filter-pop-group-options-one filter-pop-group-options-one-middle" :class="{'filter-pop-group-options-one-selected':type_list.includes('sale_task_complete')}" @click="typeChoose('sale_task_complete')">销售任务</text>
              <text class="filter-pop-group-options-one filter-pop-group-options-one-middle" :class="{'filter-pop-group-options-one-selected':type_list.includes('exchange')}" @click="typeChoose('exchange')">兑换</text>
              <text class="filter-pop-group-options-one filter-pop-group-options-one-middle" :class="{'filter-pop-group-options-one-selected':type_list.includes('other')}" @click="typeChoose('other')">其他</text>
            </view>
          </view>
          <view class="filter-pop-op">
            <text @click="filterReset" class="filter-pop-op-text">重置</text>
            <text @click="filterConfirm" class="filter-pop-op-text">确定</text>
          </view>
        </view>
      </uni-popup>
      <uni-calender
          ref="datePopup"
          :insert="false"
          :lunar="false"
          :range="true"
          @confirm="onConfirm"
      />
      <loading-cover ref="loadingCover"></loading-cover>
    </view>
</template>

<script>
// #ifdef H5
import {isOnXianMaiApp} from "@/common/js/h5/appOP";
// #endif
import golbalConfig from "@/common/mixins/golbalConfig";
import uniCalender from '@/components/uni-calendar/uni-calendar.vue';
import system from "@/common/js/system";
export default {
    name: "contribution_records",
  mixins: [golbalConfig],
  components:{
    uniCalender
  },
    data() {
        return {
          point_get:0,
          point_use:0,
          time_index: 'current_month',
          type_list:[''],
          date_list: [],
          is_date_show: false,
          date_text:"",
          page:1,
          page_size: 10,
          loading: false,
          finished: false,
          datalist: [],

          // #ifdef H5
          isOnXianMaiApp:isOnXianMaiApp,
          // #endif
        };
    },
  async onLoad() {
    // 刷新多语言
    // this.$langConfig.refresh();
    await system.wait_staticLogin_success();
    await this.getDataList();
  },
  async onReachBottom(){
    if(this.finished || this.loading){
      return
    }
    await this.getDataList()
  },
  methods:{
    showFilter(){
      this.$refs.filterPopup.open()
    },
    timeChoose(index){
      if(index=='custom'){
        this.$refs.datePopup.open()
      }else{
        this.time_index = index;
      }
    },
    typeChoose(index){
      if(this.type_list.includes(index)){
        this.type_list = this.type_list.filter(item=>item!=index)
      }else{
        this.type_list.push(index)
      }
    },
    filterReset(){
      this.time_index = 'current_month';
      this.type_list = [''];
      this.date_list = [];
      this.date_text = "";
    },
    filterConfirm(){
      this.$refs.filterPopup.close()
      this.page = 1;
      this.datalist = [];
      this.loading = false;
      this.finished = false;
      this.getDataList();
    },
    onConfirm(e){
      this.date_list = e.range.data.length > 1? [e.range.data[0],e.range.data[e.range.data.length-1]] : [e.range.data[0],e.range.data[0]];
      this.date_text = `${this.date_list[0]} - ${this.date_list[1]}`;
      this.time_index = 'custom';
      this.$refs.datePopup.close()
    },
    async getDataList(){
      if(this.finished || this.loading) return;
      this.loading = true;
      let data = {
        time_period_type: this.time_index,
        point_type: JSON.stringify(this.type_list),
        league_task_key: 'league_1',
        page: this.page,
        page_size: this.page_size,
      }
      if(this.time_index == 'custom'){
        data.custom_start_date = this.date_list[0];
        data.custom_end_date = this.date_list[1];
      }
      try{
        let res = await this.$api.sendRequest({
          url: this.$apiUrl.xmInOutRecordsUrl,
          async: false,
          data,
        })
        if (this.$refs.loadingCover) this.$refs.loadingCover.hide();
        if (res.code == 0) {
          this.point_get = res.data.point_get;
          this.point_use = res.data.point_use;
          for (let i = 0; i <res.data.list.length ; i++) {
            this.$set(this.datalist,this.datalist.length,res.data.list[i])
          }
          // 加载状态结束
          this.loading = false;
          if(this.datalist.length >= res.data.count){
            this.finished=true;
          }
          this.page=this.page+1
        }else{
          this.loading = false;
          this.finished = true;
          this.$util.showToast({
            title: res.message
          })
        }
      }catch (e) {
        if (this.$refs.loadingCover) this.$refs.loadingCover.hide();
        this.loading = false;
        this.finished = true;
        this.$util.showToast({
          title: e.message
        })
      }
    }
  }
}
</script>

<style lang="scss">
$headerH:88rpx;
/deep/.uni-popup__wrapper.uni-custom .uni-popup__wrapper-box{
  border-radius: 0!important;
}
/deep/.uni-calendar__mask{
  z-index: 1000!important;
}
/deep/.uni-calendar--fixed{
  z-index: 1000!important;
}
.contribution-records{
  min-height: 100vh;
  background: #f5f5f5;
  padding-bottom: 60rpx;
  .container {
    box-sizing: border-box;

    .header {
      z-index: 2004;
      background-color: white;
      width: 100vw;
      height: $headerH;
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 0 0.8125rem 0 1.0625rem;
      box-sizing: border-box;
      position: fixed;
      left: 0;
      top: 0;
      border-bottom: 1px solid rgba(245, 245, 245, 1);

      &-left {
        display: flex;
        align-items: center;

        &-get {
          margin: 0;
          font-size: 0.9375rem;
          font-weight: 400;
          color: rgba(128, 128, 128, 1);

          &-text {
            color: rgba(0, 186, 173, 1);
          }
        }

        &-use {
          margin: 0;
          margin-left: 0.9375rem;
          font-size: 0.9375rem;
          font-weight: 400;
          color: rgba(128, 128, 128, 1);

          &-text {
            color: rgba(255, 97, 118, 1);
          }
        }
      }

      &-right {
        display: flex;
        align-items: center;

        &-icon {
          width: 1rem;
          height: 1rem;
        }

        &-text {
          font-size: 0.9375rem;
          font-weight: 400;
          color: rgba(56, 56, 56, 1);
          margin-left: 0.125rem;
        }
      }
    }
    .records{
      padding: 0 20rpx;
      box-sizing: border-box;
      margin-top: calc(#{$headerH} + 20rpx);
      &-one{
        margin-top: 20rpx;
        display: flex;
        justify-content: space-between;
        align-items: center;
        border-radius: 20rpx;
        background: rgba(255, 255, 255, 1);
        height: 140rpx;
        padding: 0 20rpx;
        box-sizing: border-box;
        &-left{
          &-info{
            font-size: 32rpx;
            font-weight: 400;
            color: rgba(56, 56, 56, 1);
            margin: 0;
            span{
              color: rgba(128, 128, 128, 1);
            }
          }
          &-time{
            font-size: 28rpx;
            font-weight: 400;
            color: rgba(166, 166, 166, 1);
            margin: 0;
            margin-top: 14rpx;
          }
        }
        &-right{
          &-text{
            font-size: 32rpx;
            font-weight: 400;
            color: var(--custom-brand-color);
            &-add{
              color: rgba(0, 186, 173, 1);
            }
          }
        }
      }
    }
    .group-default {
      margin-top: calc(#{$headerH} * 2);
      display: flex;
      align-items: center;
      justify-content: center;
      flex-direction: column;
      &-img {
        width: 400rpx;
        height: 280rpx;
      }
      &-text {
        margin-top: 24rpx;
        color: #999999;
        font-size: 28rpx;
      }
    }
  }
  .filter-pop{
    &-group{
      margin-top: 50rpx;
      padding: 0 30rpx;
      box-sizing: border-box;
      &-title{
        font-size: 32rpx;
        font-weight: 400;
        color: rgba(56, 56, 56, 1);
        margin: 0;
        &-text{
          color: rgba(166, 166, 166, 1);
        }
      }
      &-options{
        display: flex;
        justify-content: space-between;
        flex-wrap: wrap;
        &-one{
          font-size: 28rpx;
          font-weight: 400;
          color: rgba(102, 102, 102, 1);
          min-width: 216rpx;
          height: 72rpx;
          padding: 0 20rpx;
          border-radius: 100rpx;
          background: rgba(255, 255, 255, 1);
          border: 2rpx solid rgba(229, 229, 229, 1);
          box-sizing: border-box;
          display: flex;
          justify-content: center;
          align-items: center;
          margin-top: 32rpx;
          &-middle{
            //margin-left: 0.3125rem;
            //margin-right: 0.3125rem;
          }
          &-selected{
            background: linear-gradient(180deg, var(--custom-brand-color) 0%, var(--custom-brand-color-70) 100%);
            border: none;
            color: white;
          }
        }
      }
    }
    &-op{
      display: flex;
      margin-top: 48rpx;
      &-text{
        width: 50%;
        height: 96rpx;
        background: rgba(250, 250, 250, 1);
        font-size: 32rpx;
        font-weight: 400;
        color: rgba(56, 56, 56, 1);
        display: flex;
        justify-content: center;
        align-items: center;
        &:first-child{
          border-left: 2rpx solid rgba(229, 229, 229, 1);
        }
      }
    }
  }
}

</style>
