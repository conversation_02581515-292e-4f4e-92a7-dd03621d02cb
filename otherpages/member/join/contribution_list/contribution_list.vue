<template>
  <view class="contribution-list" :data-theme="themeStyle" :style="[themeColorVar]">
    <!-- #ifdef MP-WEIXIN -->
    <uni-nav-bar left-icon="back" :border="false" @clickLeft="$util.goBack" :fixed="true" :statusBar="true" backgroundColor="transparent" :leftText="title" color="#fff">
    </uni-nav-bar>
    <!-- #endif -->
    <!-- #ifdef H5 -->
    <uni-nav-bar left-icon="back" :border="false" @clickLeft="$util.goBack" :fixed="true" :statusBar="true" backgroundColor="transparent" :leftText="title" color="#fff">
    </uni-nav-bar>
    <!-- #endif -->
    <view class="bg-img" :style="{backgroundImage: `url(${$util.img('public/static/youpin/member/contribution/beijing.png')})`}"></view>
    <template v-if="is_show_page">
      <view class="container">
        <view class="month-value">
          <view class="month-value-left">
            <view class="month-value-left-title">本月贡献值<uni-icons type="info-filled" size="16" color="rgba(0,0,0,0.3)" @click="to_tip"></uni-icons></view>
            <view class="month-value-left-value">{{league_1.point}}<text class="month-value-left-value-unit">分</text></view>
          </view>
          <view class="month-value-right">
            <text class="month-value-right-detail" @click="toDetail">查看明细</text>
            <text class="month-value-right-redeem" @click="toRedeemMall">兑换商城</text>
          </view>
        </view>
        <template v-if="is_permissions">
          <view class="earning-method" v-if="league_1.rules && league_1.rules.length>0">
            <view class="earning-method-header">
              <image :src="$util.img('public/static/youpin/member/contribution/point-shop.png')" alt="" class="earning-method-header-icon"/>
              <text class="earning-method-header-title">赚取贡献值方式</text>
            </view>
            <view class="earning-method-list">
              <template v-for="(item,index) in league_1.rules">
                <view class="earning-method-list-one" v-if="item.rule_key=='recommend_register' && item.enable == 1">
                  <view class="earning-method-list-one-left">
                    <view class="earning-method-list-one-left-title">邀请新好友注册</view>
                    <view class="earning-method-list-one-left-desc">每邀请一位新用户即可获得<text>{{item.rule_val}}</text>贡献值</view>
                  </view>
                  <view class="earning-method-list-one-right">
                    <button class="earning-method-list-one-right-op" open-type="share" v-if="$util.getPlatform() == 'weapp'">邀请</button>
                    <button class="earning-method-list-one-right-op" v-if="$util.getPlatform() == 'h5'" @click="openSharePopup">邀请</button>
                  </view>
                </view>
                <view class="earning-method-list-one" v-if="item.rule_key=='add_shop_fans' && item.enable == 1">
                  <view class="earning-method-list-one-left">
                    <view class="earning-method-list-one-left-title">店铺绑定粉丝增长</view>
                    <view class="earning-method-list-one-left-desc">店铺每新增一位粉丝可获得<text>{{item.rule_val}}</text>贡献值</view>
                  </view>
                  <view class="earning-method-list-one-right">
                    <button class="earning-method-list-one-right-op" open-type="share" v-if="$util.getPlatform() == 'weapp'">分享</button>
                    <button class="earning-method-list-one-right-op" v-if="$util.getPlatform() == 'h5'" @click="openSharePopup">分享</button>
                  </view>
                </view>
                <view class="earning-method-list-one" v-if="item.rule_key=='recommend_browse' && item.enable == 1">
                  <view class="earning-method-list-one-left">
                    <view class="earning-method-list-one-left-title">好友商品浏览数增长</view>
                    <view class="earning-method-list-one-left-desc">好友每<text>{{item.rule_nums}}</text>人浏览可得<text>{{item.rule_val}}</text>贡献值</view>
                  </view>
                  <view class="earning-method-list-one-right">
                    <button class="earning-method-list-one-right-op" open-type="share" v-if="$util.getPlatform() == 'weapp'">分享</button>
                    <button class="earning-method-list-one-right-op" v-if="$util.getPlatform() == 'h5'" @click="openSharePopup">分享</button>
                  </view>
                </view>
                <view class="earning-method-list-one" v-if="item.rule_key=='shop_fans_browse' && item.enable == 1">
                  <view class="earning-method-list-one-left">
                    <view class="earning-method-list-one-left-title">粉丝商品浏览数增长</view>
                    <view class="earning-method-list-one-left-desc">店铺粉丝每<text>{{item.rule_nums}}</text>人浏览可得<text>{{item.rule_val}}</text>贡献值</view>
                  </view>
                  <view class="earning-method-list-one-right">
                    <button class="earning-method-list-one-right-op" open-type="share" v-if="$util.getPlatform() == 'weapp'">分享</button>
                    <button class="earning-method-list-one-right-op" v-if="$util.getPlatform() == 'h5'" @click="openSharePopup">分享</button>
                  </view>
                </view>
                <view class="earning-method-list-one" v-if="item.rule_key=='recommend_shop' && item.enable == 1">
                  <view class="earning-method-list-one-left">
                    <view class="earning-method-list-one-left-title">好友升级店主</view>
                    <view class="earning-method-list-one-left-desc">每位直推好友升级可获得<text>{{item.rule_val}}</text>贡献值</view>
                  </view>
                  <view class="earning-method-list-one-right">
                    <button class="earning-method-list-one-right-op" open-type="share" v-if="$util.getPlatform() == 'weapp'">邀请</button>
                    <button class="earning-method-list-one-right-op" v-if="$util.getPlatform() == 'h5'" @click="openSharePopup">邀请</button>
                  </view>
                </view>
              </template>
            </view>
          </view>
          <view class="not-tip" v-else>
            <image :src="$util.img('public/static/youpin/member/contribution/not_config.png')" alt="" class="not-tip-icon"/>
            <view class="not-tip-text">暂未配置贡献值方式</view>
          </view>
        </template>
        <template v-else>
          <view class="not-tip">
            <image :src="$util.img('public/static/youpin/member/contribution/not_permissions.png')" alt="" class="not-tip-icon"/>
            <view class="not-tip-text">您的活动权限已关闭无法获得新的贡献值</view>
          </view>
        </template>
      </view>
      <view class="rule" @click="showRuleDetail">
        <view class="rule-left">赚贡献值，自动完成加盟任务</view>
        <view class="rule-right">查看详情<image :src="$util.img('public/static/youpin/member/contribution/rule-more.png')" alt="" class="rule-right-img"/></view>
      </view>
    </template>
    <uni-popup ref="rulePopupRef" type="bottom" :bottom-radius="true">
      <mp-html :content="rule_html" :preview-img="true" class="rule-detail"/>
      <image :src="$util.img('public/static/youpin/clase-x.png')" alt="" class="rule-close" @click="$refs.rulePopupRef.close()"/>
    </uni-popup>
    <uni-popup ref="tipPopup" type="center" class="tip-popup">
      <view class="tip-popup-content">
        <view class="tip-popup-content-desc">用户获得贡献值时，将优先用于完成任意加盟单任务一。如未使用，也可以用于兑换优惠券等其他活动。</view>
        <view class="tip-popup-content-desc">贡献值本月有效，下月1号零点之前贡献值自动失效。</view>
        <button class="tip-popup-content-op" @click="$refs.tipPopup.close()">我知道了</button>
      </view>
    </uni-popup>
    <diy-share-navigate-h5 ref="shareNavigateH5"></diy-share-navigate-h5>
    <loading-cover ref="loadingCover"></loading-cover>
  </view>
</template>

<script>
// #ifdef H5
import {isOnXianMaiApp} from "@/common/js/h5/appOP";
// #endif
import UniNavBar from "@/components/uni-nav-bar/uni-nav-bar.vue";
import golbalConfig from "@/common/mixins/golbalConfig";
import UniIcons from "@/components/uni-icons/uni-icons.vue";
import diyShareNavigateH5 from '@/components/diy-share-navigate-h5/diy-share-navigate-h5'
import mpHtml from "@/components/mp-html/mp-html.vue"
import system from "@/common/js/system";
export default {
name: "contribution_list",
  components: {
    UniNavBar,
    UniIcons,
    diyShareNavigateH5,
    mpHtml
  },
  mixins: [golbalConfig],
  data() {
    return {
      title: '我的贡献值',
      is_show_page: false,
      shop_id: null,
      shareTitle: '',
      shareImg:'',
      is_permissions: true,
      rule_html:'',
      league_1:{
        point: 0,
        auto_complete_nums: 0,
        wait_complete_task:[],
      },
      // #ifdef H5
      isOnXianMaiApp:isOnXianMaiApp,
      // #endif
    };
  },
  onLoad() {
  },
  async onShow() {
    // 刷新多语言
    this.$langConfig.refresh();
    await system.wait_staticLogin_success();
    this.shop_id = uni.getStorageSync('shop_id');
    await this.getDiyInfo();
    await this.getData()
    // #ifdef H5
    let share_data = this.$util.deepClone(this.getSharePageParams());
    let link = window.location.origin + this.$router.options.base + share_data.link.slice(1);
    share_data.link = link;
    await this.$util.publicShare(share_data);
    // #endif
  },
  methods:{
    async getData(){
      try{
        let res = await this.$api.sendRequest({
          url: this.$apiUrl.xmLeaguePointsUrl,
          async: false,
          data: {}
        })
        if (this.$refs.loadingCover) this.$refs.loadingCover.hide();
        if(res.code == -20001){
          uni.showModal({
            content: '该活动仅限受邀内测用户参与，暂未对外开放。',
            showCancel: false,
            confirmText: '知道了',
            confirmColor:"var(--custom-brand-color)",
            success(res){
              if(res.confirm){
                this.$util.goBack()
              }
            }
          })
        }else if(res.code == 0){
          this.league_1 = Object.assign(this.league_1,res.data.league_1);
          if(this.league_1.enable){
            this.is_permissions = true;
          }
          this.is_show_page = true
        }else{
          this.$util.showToast(res.message)
        }
      }catch (e) {
        if (this.$refs.loadingCover) this.$refs.loadingCover.hide();
        this.$util.showToast(e.message)
      }

    },
    async getDiyInfo() {
      let res = await this.$api.sendRequest({
        url: '/api/diyview/info',
        async: false,
        data: {
          name: 'DIYVIEW_SHOP_INDEX',
          site_id: this.shop_id || 0
        }
      });
      if (res.data) {
        let data = JSON.parse(res.data.value)
        this.shareTitle = data.global.shareTitle
        this.shareImg = data.global.shareImg

      }
    },
    /**
     *分享参数组装
     */
    getSharePageParams() {
      return this.$util.unifySharePageParams('/otherpages/shop/home/<USER>', this.shareTitle || '先迈商城',
          '',{}, this.$util.img(this.shareImg) || this.$util.img('public/static/youpin/home_share.jpg'))
    },
    // 打开分享弹出层
    openSharePopup() {
      // #ifdef H5
      let share_data=this.getSharePageParams();
      if(this.$refs.shareNavigateH5) this.$refs.shareNavigateH5.open(share_data);
      // #endif
    },
    showRuleDetail(){
      this.rule_html = this.league_1.rule_content;
      this.$refs.rulePopupRef.open();
    },
    to_tip(){
      this.$refs.tipPopup.open()
    },
    toDetail(){
      this.$util.redirectTo('/otherpages/member/join/contribution_records/contribution_records')
    },
    toRedeemMall(){
      this.$util.redirectTo('/otherpages/member/join/redeem_mall/redeem_mall')
    }
  },
  onShareAppMessage(res){
    let { title, link, imageUrl, query } = this.getSharePageParams()
    return this.$buriedPoint.pageShare(link , imageUrl, title);
  },
  onShareTimeline(res) {
    let { title, imageUrl, query } = this.getSharePageParams()
    return {
      title,
      imageUrl,
      query,
      success: res => {},
      fail: res => {}
    };
  }
}
</script>

<style scoped lang="scss">
$nav-height: 44px;
/deep/.uni-navbar__header-btns-left{
  width: 200rpx!important;
}
.contribution-list{
  position: relative;
  min-height: 100vh;
  background: linear-gradient(180deg, var(--custom-brand-color) 0%, var(--custom-brand-color-20) 100%);
  box-sizing: border-box;
  .bg-img{
    width: 750rpx;
    height: 422rpx;
    background-repeat: no-repeat;
    background-size: 100% 100%;
    position: absolute;
    top: 0;
    left: 0;
  }
  .container{
    position: relative;
    .month-value{
      width: 702rpx;
      height: 250rpx;
      background: #FFFFFF;
      border-radius: 16rpx;
      margin: 0 auto;
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 0 64rpx;
      box-sizing: border-box;
      &-left{
        &-title{
          font-size: 32rpx;
          font-weight: 400;
          line-height: 40rpx;
          color: rgba(56, 56, 56, 0.8);
          display: flex;
          align-items: center;
          /deep/ .uni-icons{
            margin-left: 12rpx;
          }
        }
        &-value{
          font-size: 70rpx;
          font-weight: bold;
          line-height: 50rpx;
          color: var(--custom-brand-color);
          margin-top: 32rpx;
          &-unit{
            font-size: 32rpx;
            font-weight: 400;
            line-height: 40rpx;
            margin-left: 10rpx;
          }
        }
      }
      &-right{
        display: flex;
        flex-direction: column;
        align-items: center;
        &-detail{
          display: block;
          font-size: 28rpx;
          font-weight: 400;
          height: 60rpx;
          line-height: 60rpx;
          color: var(--custom-brand-color);
          width: 160rpx;
          text-align: center;
          background-color: var(--custom-brand-color-10);
          border-radius: 30rpx;

        }
        &-redeem{
          display: block;
          font-size: 28rpx;
          font-weight: 400;
          width: 160rpx;
          height: 60rpx;
          line-height: 60rpx;
          color: white;
          background: linear-gradient(335deg, var(--custom-brand-color) 0%, var(--custom-brand-color-60) 100%);
          border-radius: 30rpx;
          text-align: center;
          margin-top: 24rpx;
        }
      }
    }
    .earning-method{
      width: 702rpx;
      background: #FFFFFF;
      border-radius: 16rpx;
      margin: 0 auto;
      margin-top: 24rpx;
      padding: 0 24rpx;
      box-sizing: border-box;
      &-header{
        display: flex;
        align-items: center;
        box-sizing: border-box;
        height: 88rpx;
        &-icon{
          width: 40rpx;
          height: 40rpx;
          margin-right: 12rpx;
        }
        &-title{
          font-size: 32rpx;
          font-weight: 400;
          line-height: 40rpx;
          color: rgba(56, 56, 56, 0.8);
        }
      }
      &-list{
        padding-bottom: 54rpx;
        box-sizing: border-box;
        &-one{
          display: flex;
          justify-content: space-between;
          align-items: center;
          &:not(:first-child){
            margin-top: 54rpx;
          }
          &-left{
            &-title{
              font-size: 32rpx;
              font-weight: 700;
              color: rgba(56, 56, 56, 1);
              margin: 0;
            }
            &-desc{
              margin: 0;
              margin-top: 14rpx;
              font-size: 24rpx;
              font-weight: 400;
              color: rgba(128, 128, 128, 1);
              text{
                color: var(--custom-brand-color);
              }
            }
          }
          &-right{
            &-op{
              width: 164rpx;
              height: 72rpx;
              border-radius: 100rpx;
              background: linear-gradient(180deg, var(--custom-brand-color) 0%, var(--custom-brand-color-70) 100%);
              font-size: 30rpx;
              font-weight: 400;
              letter-spacing: 4rpx;
              color: rgba(255, 255, 255, 1);
              display: flex;
              justify-content: center;
              align-items: center;
              margin: 0;
            }
          }
        }
      }
    }
    .not-tip{
      margin: 0 auto;
      width: 702rpx;
      min-height: 364rpx;
      background-color: white;
      border-radius: 20rpx;
      padding: 100rpx 24rpx 60rpx 24rpx;
      box-sizing: border-box;
      margin-top: 20rpx;
      display: flex;
      flex-direction: column;
      align-items: center;
      position: relative;
      &-icon{
        width: 240rpx;
        height: 240rpx;
      }
      &-text{
        font-size: 32rpx;
        font-weight: 400;
        color: rgba(128, 128, 128, 1);
        width: 310rpx;
        text-align: center;
        margin-top: 24rpx;
      }
    }
  }
  .rule{
    width: 100vw;
    position: fixed;
    left: 0;
    bottom: 0;
    background-color: white;
    height: calc(76rpx + env(safe-area-inset-bottom));
    padding: 0 24rpx;
    padding-bottom: env(safe-area-inset-bottom);
    box-sizing: border-box;
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 30rpx;
    font-weight: 400;
    &-left{
      color: var(--custom-brand-color);
    }
    &-right{
      display: flex;
      justify-content: center;
      align-items: center;
      color: var(--custom-brand-color);
      &-img{
        width: 28rpx;
        height: 28rpx;
        margin-left: 10rpx;
      }
    }
  }
  .rule-detail {
    padding: 60rpx 24rpx 0 24rpx;
    box-sizing: border-box;
    white-space: pre-line;
  }
  .rule-close{
    width: 24rpx;
    height: 24rpx;
    position: absolute;
    right: 32rpx;
    top: 24rpx;
  }
  .tip-popup{
    &-content{
      padding: 40rpx 24rpx 60rpx 24rpx;
      box-sizing: border-box;
      display: flex;
      flex-direction: column;
      align-items: center;
      &-title{
        font-size: 32rpx;
        font-weight: 700;
        color: rgba(56, 56, 56, 1);
        margin-bottom: 24rpx;
        text-align: center;
      }
      &-desc{
        font-size: 28rpx;
        font-weight: 400;
        color: rgba(56, 56, 56, 1);
        line-height: 40rpx;
        margin-bottom: 30rpx;
        text-align: center;
      }
      &-op{
        width: 400rpx;
        height: 80rpx;
        border-radius: 40rpx;
        background: var(--custom-brand-color);
        font-size: 32rpx;
        font-weight: 400;
        color: rgba(255, 255, 255, 1);
        display: flex;
        justify-content: center;
        align-items: center;
      }
    }
  }
}
</style>
