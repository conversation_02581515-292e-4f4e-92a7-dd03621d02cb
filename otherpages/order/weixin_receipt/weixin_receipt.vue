<!--公众号 微信支付成功后显示微信小票页面-->
<template>
  <view class="main" :style="[themeColorVar]">
    <view class="header">
      <image :src="$util.img('public/static/youpin/order/pay-right.png')" class="header-logo"></image>
      <text class="header-title">{{resultData.title}}</text>
    </view>
    <view class="row">
      <view class="row-one">商品名称</view>
      <view class="row-two">{{resultData.goods_name}}</view>
    </view>
    <view class="row">
      <view class="row-one">支付金额</view>
      <view class="row-two row-two-price">￥{{resultData.pay_money}}</view>
    </view>
    <view class="row">
      <view class="row-one">订单号</view>
      <view class="row-two">{{resultData.order_no}}</view>
    </view>
    <button class="op" @click="toOrder">查看订单</button>
    <loading-cover ref="loadingCover"></loading-cover>
  </view>
</template>

<script>
import apiurls from "../../../common/js/apiurls";
import globalConfig from "../../../common/mixins/golbalConfig";

export default {
  name: "weixin_receipt",
  mixins:[globalConfig],
  data(){
    return{
      sub_mch_id:'',
      out_trade_no:'',
      check_code:'',
      resultData:{},
    }
  },
  onLoad(data){
    this.sub_mch_id=data.sub_mch_id || data.cusid;  // cusid是通联支付的字段名
    this.out_trade_no=data.out_trade_no || data.reqsn; //reqsn是通联支付的字段名
    this.check_code=data.check_code;
  },
  async onReady(){
    if(this.$util.isWeiXin()){
      window.onload=function (){
        //初始化小票
        let initData = {
          action: 'onIframeReady',
          displayStyle: 'SHOW_CUSTOM_PAGE'
        }
        let initPostData = JSON.stringify(initData)
        parent.postMessage(initPostData, 'https://payapp.weixin.qq.com')
      }
    }
    await this.getData();
  },
  methods:{
    async getData(){
      let res=await this.$api.sendRequest({
        url: apiurls.wechatCustomPageUrl,
        async:false,
        data: {
          transaction_id:this.out_trade_no
        },
      });
      if (this.$refs.loadingCover) this.$refs.loadingCover.hide();
      if(res.code==0){
        this.resultData=res.data;
      }else{
        this.$util.showToast({
          title: res.message,
          mask:true,
          icon:"none",
          duration: 3000
        })
      }
    },
    toOrder(){
      let orderListUrl=this.resultData.redirect_url;
      let mchData = {
        action: 'jumpOut',
        jumpOutUrl: orderListUrl //跳转的页面
      }
      let postData = JSON.stringify(mchData)
      parent.postMessage(postData, 'https://payapp.weixin.qq.com')
    },
    /**
     *分享参数组装
     */
    getSharePageParams() {
      return this.$util.unifySharePageParams('/otherpages/shop/home/<USER>', this.shareTitle || '先迈商城',
          '',{}, this.$util.img(this.shareImg) || this.$util.img('public/static/youpin/home_share.jpg'))
    }
  },
  /**
   * 自定义分享内容
   * @param {Object} res
   */
  onShareAppMessage(res) {
    let { title, link, imageUrl, query } = this.getSharePageParams()
    return this.$buriedPoint.pageShare(link , imageUrl, title);
  },
}
</script>

<style scoped lang="scss">
  .main{
    width: 620rpx;
    height: 600rpx;
    background: #F6F5F5;
    margin: 0 auto;
    padding-top: 20rpx;
    box-sizing: border-box;
    .header{
      width: 100%;
      display: flex;
      justify-content: center;
      align-items: center;
      &-logo{
        width: 48rpx;
        height: 48rpx;
      }
      &-title{
        font-size: 36rpx;
        font-weight: 500;
        color: #000000;
        margin-left: 19rpx;
      }
    }
    .row{
      width: 100%;
      margin-top: 30rpx;
      display: flex;
      justify-content: center;
      &-one{
        font-size: 30rpx;
        font-weight: 500;
        color: #666666;
        width: 120rpx;
        text-align: left;
      }
      &-two{
        margin-left: 36rpx;
        font-size: 30rpx;
        font-weight: 500;
        color: #333333;
        overflow: hidden;
        text-overflow: ellipsis;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        text-align: left;
        width: 360rpx;
        &-price{
          color: var(--custom-brand-color);
        }
      }
    }
    .op{
      width: 320rpx;
      height: 68rpx;
      line-height: 68rpx;
      background: var(--custom-brand-color);
      border-radius: 34rpx;
      display: block;
      font-size: 32rpx;
      font-weight: 500;
      color: #FFFFFF;
      margin: 0 auto;
      margin-top: 60rpx;
    }
  }
</style>
