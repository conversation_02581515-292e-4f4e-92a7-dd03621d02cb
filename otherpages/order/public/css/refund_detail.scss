.detail-container {
	width: 100vw;
	height: 100vh;
}
.container {
	transition: all 0.3s;

	.hide {
		transform: translateX(-100%);
	}
}

.status-wrap {
	height: 240rpx;
	box-sizing: border-box;
	background-color: var(--custom-brand-color);
	.status-name {
		display: block;
		font-size: 28rpx;
		font-weight: normal;
	}
	.status-name-box {
		.status-name {
			font-size: 32rpx;
			font-weight: bold;
		}

	}

	.refund-explain {
		border-top: 1px dashed #eee;
		padding-top: 20rpx;
	}
}

.refund-address-wrap {
	margin: 20rpx;
	background: #fff;
	padding: 20rpx;
	border-radius: $ns-border-radius;

	.copy {
		font-size: 20rpx;
		display: inline-block;
		color: #666;
		background: #fff;
		line-height: 1;
		padding: 6rpx 10rpx;
		margin-left: 10rpx;
		border-radius: 4rpx;
		border: 1px solid #ddd;
	}
}

.history-wrap {
	margin: 20rpx;
	background: #fff;
	padding: 20rpx;
	display: flex;
	position: relative;
	border-radius: $ns-border-radius;

	view {
		flex: 1;
	}

	.iconright {
		position: absolute;
		top: 50%;
		transform: translateY(-50%);
		color: #ddd;
		right: 20rpx;
	}
}

.refund-info {
	padding-top: 20rpx;
	border-top: 1px solid #EEEEEE;
	.cell {
		display: flex;
		align-items: flex-start;
		margin-bottom: 20rpx;
		.name {
			//width: 212rpx;
			font-size: 26rpx;
			min-width: 184rpx;
		}
	}
}

.operation {
	position: fixed;
	z-index: 5;
	left: 0;
	bottom: 0;
	width: 100vw;
	height: 100rpx;
	background: #fff;
	box-shadow: 0 0px 10px rgba(0, 0, 0, 0.1);
	text-align: right;
	line-height: 100rpx;

	&.bottom-safe-area {
		padding-bottom: 68rpx !important;
	}
	.order-box-btn{
		margin-right: $ns-margin;
		margin-left: 0;
	}

	.operation-btn {
		height: 70rpx;
		line-height: 70rpx;
		color: #fff;
		padding: 0 40rpx;
		display: inline-block;
		text-align: center;
		margin: 16rpx 20rpx 16rpx 0;
		border-radius: 40rpx;

		&.white {
			height: 68rpx;
			line-height: 68rpx;
			color: #333;
			border: 0.5px solid #999;
			background: #fff;
		}
	}
}

.form-wrap {
	background: #fff;

	.item {
		margin: 0 20rpx;
		display: flex;
		border-bottom: 1px solid #eee;

		&:last-child {
			border-bottom: none;
		}

		.label {
			width: 140rpx;
			line-height: 90rpx;
		}

		.cont {
			flex: 1;
			line-height: 90rpx;

			.input,
			.input-placeholder {
				height: 90rpx;
				line-height: 90rpx;
				font-size: 28rpx;
			}

			.textarea {
				width: 100%;
				padding: 26rpx 0;
				line-height: 1.3;
				font-size: 28rpx;
			}
		}
	}
}

.sub-btn {
	margin-top: 20rpx;
}

.record-wrap {
	padding: 0 20rpx;

	.record-item {
		display: flex;

		.cont {
			flex: 1;
			margin-top: 40rpx;
			box-shadow: 0 1.5px 3px 0 rgba(0, 0, 0, 0.06), 0 1.5px 3px 0 rgba(0, 0, 0, 0.08);
			border-radius: 20rpx;
			padding: 20rpx;

			.head {
				line-height: 1;

				.time {
					margin-left: 40rpx;
					color: #999;
					font-size: 24rpx;
					float: right;
				}
			}

			.body {
				padding-top: 20rpx;
				color: #999;
			}
		}

		&.buyer {
			.cont {
				margin-left: 10%;
				background: #fff;
			}
		}

		&.seller {
			.cont {
				margin-right: 10%;
				background: #ffe48c;
			}
		}
	}

	.empty-box{
		height: 168rpx;
	}
}

.history-bottom {
	position: fixed;
	z-index: 5;
	left: 0;
	bottom: 0;
	width: 100vw;
	height: 100rpx;
	background: #fff;
	box-shadow: 0 0px 10px rgba(0, 0, 0, 0.1);
	text-align: right;
	display: flex;

	&.bottom-safe-area {
		padding-bottom: 68rpx !important;
	}

	view {
		flex: 1;
		text-align: center;
		line-height: 100rpx;

		&:first-child {
			border-right: 1px solid #eee;
		}
		.iconfont {
			font-weight: bold;
			margin-right: 10rpx;
			font-size: 28rpx;
			line-height: 1;
		}
	}
	button {
		width: 50%;
		height: 100%;
		position: absolute;
		border: none;
		z-index: 1;
		padding: 0;
		margin: 0;
		background: none;

		&::after {
			border: none !important;
		}
	}
}
.site-wrap {
	.site-body {
		.goods-wrap {
			margin-bottom: 35rpx;
		}
	}
	padding-bottom: 1rpx;
	margin-bottom: 36rpx;
	border-radius: 20rpx;
}
.refund-apply-money {
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 0 24rpx;
	margin: 0 24rpx;
	margin-bottom: 20rpx;
	height: 100rpx;
	border-radius: 20rpx;
	background-color: #FFFFFF;
	.name {
		font-weight: bold;
		color: #333333;
		font-size: 28rpx;
	}
	.money {
		.text {
			font-weight: normal;
			font-size: 26rpx;
		}
		font-weight: bold;
		color: var(--custom-brand-color);
		font-size: 36rpx;
	}
}
