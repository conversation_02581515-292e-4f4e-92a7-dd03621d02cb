.refund-container {
	width: 100vw;
	height: 100vh;
}

.align-right {
	text-align: right;
}

.goods-wrap {
	margin: $ns-margin;
	padding: $ns-padding;
	border-radius: $ns-border-radius;
	background: #fff;
	display: flex;
	position: relative;

	.goods-img {
		width: 170rpx;
		height: 170rpx;
		margin-right: 20rpx;

		image {
			width: 100%;
			height: 100%;
		}
	}

	.goods-info {
		flex: 1;
		position: relative;
		max-width: calc(100% - 200rpx);

		.goods-name {
			display: -webkit-box;
			-webkit-box-orient: vertical;
			-webkit-line-clamp: 2;
			overflow: hidden;
			line-height: 1.5;
			font-size: 28rpx;
		}
	}
}

.refund-option {
	margin: $ns-margin;
	border-radius: $ns-border-radius;
	background: #fff;

	.option-item {
		padding: $ns-padding;
		display: flex;
		position: relative;

		view {
			flex: 1;

			text {
				display: block;
			}
		}

		.iconright {
			position: absolute;
			top: 50%;
			transform: translateY(-50%);
			color: #ddd;
			right: 20rpx;
		}

		&:last-of-type {
			border-top: 1px solid #f2f2f2;
		}
	}
}

.refund-form {
	margin: $ns-margin;
	padding: $ns-padding;
	border-radius: $ns-border-radius;
	background: #fff;

	.item-wrap {
		display: flex;
		position: relative;
		line-height: 80rpx;

		.label {
			width: 142rpx;
			padding-right: 5rpx;
			line-height: 80rpx;
		}

		.cont {
			flex: 1;
			line-height: 80rpx;
			.refund-desc {
				font-size: 28rpx;
				width: 100%;
				line-height: 1;
				min-height: 80rpx;
			}
		}
		.label.active{
			width: 100%;
		}

		.iconright {
			position: absolute;
			top: 50%;
			transform: translateY(-50%);
			color: #ddd;
			right: 0;
		}
	}
}
.textarea-box{
	position: relative;
	.mark{
		width: 100%;
		height: 100%;
		position: absolute;
		left: 0;
		bottom: 0;
		background: red;
	}
}
.newText{
	width: 100%;
	min-height: 130px;
	border-radius: $ns-border-radius;
	border: 1rpx solid $ns-border-color-gray;
	padding: 10rpx;
	box-sizing: border-box;
	margin-top: 10rpx;
}

.sub-btn {
	position: fixed;
	width: 100%;
	height: 120rpx;
	line-height: 120rpx;
	text-align: center;
	color: #fff;
	bottom: 0;
	&.safe-area {
		margin-bottom: 68rpx !important;
	}
}

.popup {
	width: 100vw;
	background: #fff;
	border-top-left-radius: 24rpx;
	border-top-right-radius: 24rpx;

	.popup-header {
		height: 90rpx;
		display: flex;
		align-items: center;
		padding: 0 30rpx;

		& > view {
			flex: 1;
			line-height: 1;
		}

		.tit {
			font-size: 32rpx;
			font-weight: 600;
		}

		.vice-tit {
			margin-right: 20rpx;
		}
	}

	.popup-body {
		height: calc(100% - 210rpx);

		&.safe-area {
			height: calc(100% - 278rpx);
		}
	}

	.popup-footer {
		height: 120rpx;

		.confirm-btn {
			height: 80rpx;
			line-height: 80rpx;
			color: #fff;
			text-align: center;
			margin: 20rpx;
			border-radius: 40rpx;
		}

		&.bottom-safe-area {
			padding-bottom: 68rpx !important;
		}
	}
}

.refund-reason-popup {
	height: 65vh;

	.popup-body {
		.item {
			display: flex;
			padding: 0 30rpx;
			position: relative;
			height: 70rpx;
			line-height: 70rpx;

			.reason {
				flex: 1;
				height: 70rpx;
				line-height: 70rpx;
			}

			& > .iconfont {
				font-size: 40rpx;
				position: absolute;
				top: 50%;
				right: 30rpx;
				transform: translateY(-50%);
			}

			& > .iconyuan_checkbox {
				color: $ns-text-color-gray;
			}
		}
	}
}
