<template>
  <view class="parcel" :style="[themeColorVar]">
    <view class="title">该订单已拆成{{parcelList.length}}个包裹</view>
    <view class="parcel-box">
      <view class="parcel-list" v-for="(item, index) in parcelList" :key="index">
        <view class="name">
          <image :src="$util.img('public/static/youpin/parcel.png')" alt="" />
          <view>{{item.package_name}}</view>
        </view>
        <view class="info-box">
          <view class="info">{{item.express_company_name}}：{{item.delivery_no}}</view>
          <view class="btn" @click="detailFun(item)" v-if="orderStatus == 3">查看详情</view>
          <view class="btn" @click="$util.copy(item.delivery_no)" v-else>复制</view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
  import globalConfig from "../../../common/mixins/golbalConfig";

  export default {
    mixins:[globalConfig],
    data() {
      return {
        parcelList: [],
        order_id: '',
        orderStatus: ''
      }
    },
    onLoad({order_id}) {
      this.order_id = order_id
    },
    onShow() {
      uni.showLoading({
        title: '加载中',
        mask:true
      });
      this.$api.sendRequest({
				url: this.$apiUrl.getPackageList,
				data: {
					order_id: this.order_id
				},
				success: (res) => {
          uni.hideLoading();
          if(res.code == 0){
            this.parcelList = res.data.list
            this.orderStatus = res.data.order_status
          }else{
            this.$util.showToast({
							title: res.message
						});
          }
        },
        fali: (res) => {
          uni.hideLoading();
        }
      })
    },
    methods: {
      detailFun(e) {
        //是否是使用物流插件
        // #ifdef MP-WEIXIN
        if(e.out_delivery_type == 'wx_mini'){
          this.$util.redirectTo(
            '/pluginspages/logisticsPlugin/logisticsPlugin',
            { waybillToken: e.out_delivery_no },
          );
        }else{
          this.$util.redirectTo('/pages/order/logistics/logistics', {
            order_id: this.order_id,
            delivery_no: e.delivery_no
          });
        }
        // #endif
        // #ifdef H5
        this.$util.redirectTo('/pages/order/logistics/logistics', {
          order_id: this.order_id,
          delivery_no: e.delivery_no
        });
        // #endif
      },
      /**
       *分享参数组装
       */
      getSharePageParams() {
        return this.$util.unifySharePageParams('/otherpages/shop/home/<USER>', this.shareTitle || '先迈商城',
            '',{}, this.$util.img(this.shareImg) || this.$util.img('public/static/youpin/home_share.jpg'))
      }
    },
    /**
     * 自定义分享内容
     * @param {Object} res
     */
    onShareAppMessage(res) {
      let { title, link, imageUrl, query } = this.getSharePageParams()
      return this.$buriedPoint.pageShare(link , imageUrl, title);
    },
  }
</script>

<style lang="scss" scoped>
.parcel{
  .title{
    display: flex;
    align-items: center;
    font-size: 22rpx;
    color: var(--custom-brand-color);
    height: 56rpx;
    background-color: var(--custom-brand-color-10);
    padding-left: 24rpx;
  }
  .parcel-box{
    .parcel-list{
      position: relative;
      background-color: #fff;
      margin-bottom: 20rpx;
      padding: 34rpx 24rpx 26rpx;
      .name{
        display: flex;
        align-items: center;
        image{
          width: 28rpx;
          height: 28rpx;
        }
        view{
          font-size: 24rpx;
          color: #333;
          line-height: 34rpx;
          padding-left: 8rpx;
        }
      }
      .info-box{
        display: flex;
        align-items: center;
        padding: 16rpx 0 0;
        .info{
          font-size: 24rpx;
          color: #999;
          line-height: 34rpx;
        }
        .copy{
          font-size: 24rpx;
          color: var(--custom-brand-color);
          line-height: 34rpx;
          margin-left: 24rpx;
        }
      }
      .btn{
        position: absolute;
        bottom: 30rpx;
        right: 24rpx;
        display: flex;
        justify-content: center;
        align-items: center;
        width: 136rpx;
        height: 48rpx;
        border: 1px solid var(--custom-brand-color);
        border-radius: 48rpx;
        font-size: 24rpx;
        color: var(--custom-brand-color);
      }
    }
  }
}
</style>
