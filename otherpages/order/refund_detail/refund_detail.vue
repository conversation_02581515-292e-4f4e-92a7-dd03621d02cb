<template>
	<view :class="themeStyle" :style="[themeColorVar]">
		<scroll-view scroll-y="true" class="detail-container" :class="{ 'safe-area': isIphoneX }">
			<!-- 订单状态 -->
			<view class="status-wrap">
				<view class="status-box">
					<view class="status-name-box">
						<image v-if="detail.refund_name!='退款成功'" class="icon-pay-clock" :src="$util.img('public/static/youpin/order/icon-pay-clock.png')"/>
						<image v-if="detail.refund_name==='退款成功'" class="icon-pay-clock" :src="$util.img('public/static/youpin/order/icon-finished.png')"/>
						<text class="status-name">{{ detail.refund_name }}</text>
					</view>
					<view class="status-name" v-if="detail.refund_status==1">订单已支付，申请退款中</view>
					<!-- 1=买家申请退款, -1=卖家拒绝，2=卖家确认退款，3=维权完成 -->
				</view>
			</view>

			<!-- 地址信息 -->
			<view class="address-wrap active">
				<view class="icon">
					<image class="icon-pay-clock" :src="$util.img('public/static/youpin/order/icon-no-pay-address.png')"/>
				</view>
				<view class="address-info">
					<view class="info">
						<view class="info-name">{{ detail.order_info.name }}</view>
						<view>{{ detail.order_info.mobile }}</view>
					</view>
					<view class="detail">
						<text>{{ detail.order_info.full_address }} {{ detail.order_info.address }}</text>
					</view>
				</view>
			</view>
			<view class="refund-apply-money" v-if="detail.refund_apply_money">
				<view class="name">退款总金额</view>
				<view class="money"><text class="unit" v-if="detail.is_maidou_pay!=1">{{ $lang('common.currencySymbol') }}</text>{{ detail.refund_apply_money }}<text class="unit" v-if="detail.is_maidou_pay==1">迈豆</text></view>
			</view>
			<view class="site-wrap">
				<view class="site-header">
					<text class="site-name">退款信息</text>
				</view>
				<view class="site-body">
					<view class="goods-wrap">
						<view class="goods-img">
							<image :src="$util.img(detail.sku_image)" @error="imageError(goodsIndex)" mode="aspectFill" :lazy-load="true"></image>
						</view>
						<view class="goods-info">
							<view class="goods-name">{{ detail.goods_name }}</view>
							<view class="goods-sub-section">
								<view>{{ detail.spec_name }}</view>
							</view>
						</view>
					</view>
					<!-- 退款信息 -->
					<view class="refund-info">
						<view class="cell" v-if="detail.refund_time">
							<view class="name">{{ detail.refund_type == 1 ? '退款' : detail.refund_type == 2 ? '退款退货' : '换货' }}时间：</view>{{ $util.timeStampTurnTime(detail.refund_time) }}
						</view>
						<view class="cell">
							<view class="name">{{ detail.refund_type == 1 || detail.refund_type == 2 ? '退款' : detail.refund_type == 3 ? '换货' : '' }}单号：</view>{{ detail.refund_no }}
						</view>
						<view class="cell" v-if="detail.refund_type != 2">
							<view class="name">退款金额：</view>
              <text class="unit" v-if="detail.is_maidou_pay!=1">{{ $lang('common.currencySymbol') }}</text>
              {{detail.refund_apply_money}}<text v-if="parseFloat(detail.refund_delivery_money)>0" style="color: #F2270C;">（含运费{{detail.refund_delivery_money}}）</text>
              <text class="unit" v-if="detail.is_maidou_pay==1">迈豆</text>
						</view>
						<view class="cell">
							<view class="name">申请件数：</view>{{ detail.num }}
						</view>
						<view class="cell">
							<view class="name">{{ detail.refund_type == 1 ? '退款' : detail.refund_type == 2 ? '退款退货' : '换货' }}理由：</view>{{ detail.refund_remark }}
						</view>
            <view class="cell" v-if="detail.admin_remark">
              <view class="name">审核备注：</view>{{ detail.admin_remark }}
            </view>
					</view>
				</view>
			</view>

		</scroll-view>
		<loading-cover ref="loadingCover"></loading-cover>
	</view>
</template>

<script>
import refundMethod from '../public/js/refundMethod.js';
import validate from 'common/js/validate.js';
import globalConfig from 'common/mixins/golbalConfig.js'

export default {
	data() {
		return {
			order_goods_id: '',
			detail: {
				refund_action: [],
        order_info:{
				  name:''
        }
			},
			isIphoneX: false,
			action: '',
			formData: {
				refund_delivery_name: '',
				refund_delivery_no: '',
				refund_delivery_remark: ''
			},
			isSub: false
		};
	},
	mixins: [refundMethod,globalConfig],
	onLoad(option) {
		if (option.order_goods_id) this.order_goods_id = option.order_goods_id;
		if (option.action) this.action = option.action;
		this.isIphoneX=this.$util.uniappIsIPhoneX()
	},
	computed: {
		// 使用对象展开运算符将此对象混入到外部对象中
		themeStyle(){
			return 'theme-'+this.$store.state.themeStyle
		},
		addonIsExit(){
			return this.$store.state.addonIsExit
		}
	},
	onShow() {
		// 刷新多语言
		this.$langConfig.refresh();

		if (uni.getStorageSync('token')) {
			this.getRefundDetail();
		} else {
			this.$util.redirectTo('/pages/login/login/login', { back: '/otherpages/order/refund_detail/refund_detail?order_goods_id=' + this.order_goods_id });
		}

	},
	methods: {
		//联系客服
		goConnect(){
			let that=this;
			if (uni.getStorageSync('token')) {
				that.$api.sendRequest({
					url:'/servicer/api/chat/hasServicers',
					data:{
						site_id:that.detail.site_id
					},
					success(res){
						if(res.code>=0){
							let data={
								siteId:that.detail.site_id,
								orderId:that.detail.order_id
							}
							that.$util.redirectTo('/otherpages/chat/room/room',data)
						}else{
							that.$util.showToast({'title': '客服未上线'})
						}
					}
				})
			}
		},
		getRefundDetail() {
			this.$api.sendRequest({
				url: '/api/orderrefund/detail',
				data: {
					order_goods_id: this.order_goods_id
				},
				success: res => {
					if (res.code >= 0) {
						this.detail = res.data;
						if (this.$refs.loadingCover) this.$refs.loadingCover.hide();
					} else {
						this.$util.showToast({ title: '未获取到该订单项退款信息' });
						setTimeout(() => {
							this.$util.redirectTo('/pages/order/list/list');
						}, 1000);
					}
				},
				fail: res => {
					if (this.$refs.loadingCover) this.$refs.loadingCover.hide();
				}
			});
		},
		refundAction(event) {
			switch (event) {
				case 'orderRefundCancel':
					this.cancleRefund(this.detail.order_goods_id, res => {
						if (res.code >= 0) {
							this.$util.showToast({ title: '撤销成功' });
							setTimeout(() => {
								this.$util.redirectTo('/pages/order/list/list');
							}, 1000);
						}
					});
					break;
				case 'orderRefundDelivery':
					this.action = 'returngoods';
					break;
				case 'orderRefundAsk':
					this.$util.redirectTo('/otherpages/order/refund/refund', { order_goods_id: this.detail.order_goods_id });
					break;
			}
		},
		refurnGoods() {
			var rule = [
				{ name: 'refund_delivery_name', checkType: 'required', errorMsg: '请输入物流公司' },
				{ name: 'refund_delivery_no', checkType: 'required', errorMsg: '请输入物流单号' }
			];
			this.formData.order_goods_id = this.order_goods_id;
			var checkRes = validate.check(this.formData, rule);
			if (checkRes) {
				if (this.isSub) return;
				this.isSub = true;
				this.$api.sendRequest({
					url: '/api/orderrefund/delivery',
					data: this.formData,
					success: res => {
						if (res.code == 0) {
							this.action = '';
							this.getRefundDetail();
						} else {
							this.$util.showToast({ title: res.message });
						}
					}
				});
			} else {
				this.$util.showToast({ title: validate.error });
				return false;
			}
		},
		/**
		 * 切换操作
		 */
		switchAction(action){
			this.action = action;
		},
		imageError() {
			this.detail.sku_image = this.$util.getDefaultImage().default_goods_img;
			this.$forceUpdate();
		},
    /**
     *分享参数组装
     */
    getSharePageParams() {
      return this.$util.unifySharePageParams('/otherpages/shop/home/<USER>', this.shareTitle || '先迈商城',
          '',{}, this.$util.img(this.shareImg) || this.$util.img('public/static/youpin/home_share.jpg'))
    }
	},
  /**
   * 自定义分享内容
   * @param {Object} res
   */
  onShareAppMessage(res) {
    let { title, link, imageUrl, query } = this.getSharePageParams()
    return this.$buriedPoint.pageShare(link , imageUrl, title);
  },
};
</script>

<style lang="scss">
@import '../public/css/detial_old.scss';
@import '../public/css/refund_detail.scss';
</style>
