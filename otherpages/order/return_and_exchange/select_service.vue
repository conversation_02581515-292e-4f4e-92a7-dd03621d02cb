<template>
  <!-- 选择服务 -->
  <view class="select-service" :style="[themeColorVar]">
    <view class="goods-info" >
      <view class="goods-info-title">售后商品</view>
      <view class="goods-info-content">
        <view class="goods-info-content-img">
          <img :src="$util.img(info.sku_image)" alt="" />
        </view>
        <view class="goods-info-content-text">
          <view class="content-text-info">{{ info.goods_name }}</view>
          <view class="content-text-tips">
            <text>{{ info.spec_name }}</text>
            <text>×{{ info.num || 1 }}</text>
          </view>
          <view class="content-text-price">{{ info.single_price_unit_str }}</view>
        </view>
      </view>
    </view>
    <view class="type-select">
      <view class="type-select-title">选择售后服务类型</view>
      <view class="type-select-content">
        <view class="content-types" @click="handleToSelectService(4)">
          <view class="types-icon"
            ><img :src="$util.img(jintuikuan)" alt=""/></view>
          <view class="types-info">
            <view class="info-title">仅退款（无需退货）</view>
            <view class="info-text">没收到货，或与厂家协商同意不需要退货</view>
          </view>
          <view class="types-icon-right">
            <view class="iconfont iconright"></view>
          </view>
        </view>
        <view class="content-types" @click="handleToSelectService(2)">
          <view class="types-icon"
            ><img :src="$util.img(tuihuotuikuan)" alt=""
          /></view>
          <view class="types-info">
            <view class="info-title">我要退货退款</view>
            <view class="info-text">已收到货，需要退还收到的货物</view>
          </view>
          <view class="types-icon-right">
            <view class="iconfont iconright"></view>
          </view>
        </view>
        <view class="content-types" @click="handleToSelectService(3)">
          <view class="types-icon"
            ><img :src="$util.img(huanhuo)" alt=""
          /></view>
          <view class="types-info">
            <view class="info-title">我要换货</view>
            <view class="info-text">已收到货，需要更换已收到的货物</view>
          </view>
          <view class="types-icon-right">
            <view class="iconfont iconright"></view>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>
<script>
import golbalConfig from "../../../common/mixins/golbalConfig";

export default {
  mixins:[golbalConfig],
  data() {
    return {
      info: {},
      num: 0,
      order_goods_id: "",
      jintuikuan:'',
      tuihuotuikuan:'',
      huanhuo:''
    };
  },
  onLoad({ order_goods_id, num }) {
    this.order_goods_id = order_goods_id || "4807";
    this.num = num || "";

    let color = this.$util.colorToHex(this.$store.state.themeColorVar['--custom-brand-color']).slice(1);
    this.jintuikuan = encodeURI(this.$util.img( `api/website/svgChangeFillColor?svg_name=jintuikuan&color=${color}`))
    this.tuihuotuikuan = encodeURI(this.$util.img( `api/website/svgChangeFillColor?svg_name=tuihuotuikuan&color=${color}`))
    this.huanhuo = encodeURI(this.$util.img( `api/website/svgChangeFillColor?svg_name=huanhuo&color=${color}`))
  },
  onShow() {
    this.orderRefund();
  },
  methods: {
    orderRefund() {
      uni.showLoading({
        title: "加载中",
      });
      this.$api.sendRequest({
        url: this.$apiUrl.orderrefund,
        data: {
          order_goods_id: this.order_goods_id,
          // num: this.num,
          token: uni.getStorageSync("token"),
        },
        success: (res) => {
          let data = res.data;
          this.info = data.order_goods;
          uni.hideLoading();
        },
      });
    },
    returnFun() {
      this.$util.showToast({
        title: "该功能暂未开放",
      });
    },
    handleToSelectService(type) {
      this.$util.redirectTo(
        `/otherpages/order/return_and_exchange/refund_form?type=${type}&order_goods_id=${this.order_goods_id}&num=${this.num}`,
        {},
        "redirectTo"
      );
    },
    /**
     *分享参数组装
     */
    getSharePageParams() {
      return this.$util.unifySharePageParams('/otherpages/shop/home/<USER>', this.shareTitle || '先迈商城',
          '',{}, this.$util.img(this.shareImg) || this.$util.img('public/static/youpin/home_share.jpg'))
    }
  },
  /**
   * 自定义分享内容
   * @param {Object} res
   */
  onShareAppMessage(res) {
    let { title, link, imageUrl, query } = this.getSharePageParams()
    return this.$buriedPoint.pageShare(link , imageUrl, title);
  },
};
</script>
<style lang="scss" scoped>
.select-service {
  width: 100%;
  box-sizing: border-box;
  padding: 20rpx 24rpx;
  .goods-info {
    width: 100%;
    background: #ffffff;
    box-sizing: border-box;
    padding: 20rpx;
    border-radius: 20rpx;
    .goods-info-title {
      width: 100%;
      color: #333333;
      font-weight: bold;
      font-size: 32rpx;
    }
    .goods-info-content {
      width: 100%;
      display: flex;
      margin-top: 15rpx;
      .goods-info-content-img {
        width: 180rpx;
        height: 180rpx;
        margin-right: 20rpx;
        img {
          width: 180rpx;
          height: 180rpx;
          display: block;
          border: 1px solid #f2f2f2;
          border-radius: 20px;
        }
      }
      .goods-info-content-text {
        flex: 1;
        .content-text-info {
          font-size: 28rpx;
          font-weight: 400;
          color: #333333;
        }
        .content-text-tips {
          display: flex;
          justify-content: space-between;
          font-size: 24rpx;
          font-weight: 400;
          color: #999999;
        }
        .content-text-price {
          width: 100%;
          text-align: end;
          font-size: 28rpx;
          font-weight: 400;
          color: #333333;
        }
      }
    }
  }
  .type-select {
    width: 100%;
    background: #ffffff;
    box-sizing: border-box;
    padding: 20rpx;
    border-radius: 20rpx;
    margin-top: 20rpx;
    .type-select-title {
      width: 100%;
      color: #333333;
      font-weight: bold;
      font-size: 32rpx;
    }
    .type-select-content {
      .content-types {
        width: 100%;
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-top: 59rpx;
        .types-icon {
          flex: 1;
          display: flex;
          justify-content: flex-start;
          img {
            width: 48rpx;
            height: 48rpx;
            display: block;
          }
        }
        .types-info {
          flex: 7;
          .info-title {
            font-size: 32rpx;
            font-weight: 400;
            color: #333333;
          }
          .info-text {
            font-size: 28rpx;
            font-weight: 400;
            color: #999999;
          }
        }

        .types-icon-right {
          flex: 1;
          text-align: end;
          .iconright {
            font-weight: 400;
            color: #999999;
          }
        }
      }
    }
  }
}
</style>
