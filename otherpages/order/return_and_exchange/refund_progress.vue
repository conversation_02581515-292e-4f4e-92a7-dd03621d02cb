<template>
  <!-- 退换进度 -->
  <view class="refund_progress" :class="overHiden ? $util.getPlatform() == 'weapp' ? 'overHiden' : 'overHidenH5' : ''" :style="[themeColorVar]">
    <template v-if="loadFinsh">
      <!-- 头部进度 -->
      <view class="refund_progress-header">
        <view class="header__progress">
          <view class="header__progress-title">{{ dataInfo.steps.title }}</view>
          <view class="header__progress-step">
            <uni-step :dataList="stepList"></uni-step>
          </view>
        </view>
      </view>
      <!-- 中间内容 -->
      <view class="refund_progress-content">
        <view
            class="refund_progress-situation"
            v-if="!(STATUS_6 && REFUND_TYPE_3) && !STATUS_1 && !STATUS_10"
        >
          <view class="situation__box" :class="STATUS_3 || STATUS_00 ||  STATUS_6 ? 'bottom_unset' :'' ">
            <view class="situation__box-title" v-if="STATUS_4"
            >商家已同意退货，请寄回商品并填写物流单号</view
            >
            <view class="situation__box-title" v-if="STATUS_9"
            >商家未收到退货,请检查物流信息重新提交</view
            >
            <view
                class="situation__box-title"
                v-if="(STATUS_8 || STATUS_3) && REFUND_TYPE_3"
            >商家已重新发货，请注意接收快递</view
            >
            <view class="situation__box-content">
              <!-- 物流信息 -->
              <view
                  class="content-receiving"
                  v-if="(STATUS_8 || STATUS_3) && REFUND_TYPE_3"
              >
                <view class="receiving__info-box">
                  <!-- 卖家重发物流公司 -->
                  <view class="info__box-text">物流公司</view>
                  <view class="info__box-value">{{
                      dataInfo.order_goods_aftersale.refund_recept_delivery_name
                    }}</view>
                </view>
                <!-- 卖家重发物流编号 -->
                <view class="receiving__info-box">
                  <view class="info__box-text">快递单号</view>
                  <view class="info__box-value progress-info"
                  >{{
                      dataInfo.order_goods_aftersale.refund_recept_delivery_no
                    }}
                    <view class="progress-btn" @click="progressDetailFun(dataInfo.order_goods_aftersale.refund_recept_delivery_company_id,dataInfo.order_goods_aftersale.refund_recept_delivery_no)"
                    >查看物流</view
                    >
                  </view>
                </view>
              </view>
              <!-- 申请退货退款 / 换货 -->
              <view
                  class="content__receiving-info"
                  v-if="STATUS_9 || STATUS_4 || STATUS_5"
              >
                <view class="receiving__info-box">
                  <view class="info__box-text">收件人</view>
                  <view class="info__box-value">{{ dataInfo.refund_name }}</view>
                </view>
                <view class="receiving__info-box">
                  <view class="info__box-text">联系电话</view>
                  <view class="info__box-value">{{ dataInfo.refund_phone }}</view>
                </view>
                <view class="receiving__info-box">
                  <view class="info__box-text">退货地址</view>
                  <view class="info__box-value address-box"
                  ><view class="value__address" style="flex: 1">{{
                      dataInfo.refund_address
                    }}</view>
                    <view class="value__copy" @click="copyText(dataInfo.refund_name +',' +dataInfo.refund_phone +',' +dataInfo.refund_address)">复制</view>
                  </view>
                </view>
                <!-- 填写物流表单 -->
                <view
                    class="receiving__info-form"
                    v-if="STATUS_9 || STATUS_4 || isCompanyInfoEdit"
                >
                  <view class="info__form-input">
                    <input
                        class="form__input"
                        type="number"
                        v-model="form.refund_delivery_no"
                        placeholder="请填写快递单号"
                        placeholder-style="color: #cccccc"
                    />
                  </view>
                  <uni-select
                      @openModel="openModel"
                      @closeModel="closeModel"
                      :refund_delivery_company_id.sync="
                    form.refund_delivery_company_id
                  "
                      @companySelect="companySelect"
                      :express_company_list="express_company_list"
                  />
                </view>

                <!-- 回显物流表单 -->
                <template v-if="STATUS_5 && !isCompanyInfoEdit">
                  <view class="receiving__info-box">
                    <view class="info__box-text">快递单号</view>
                    <view class="info__box-value progress-info">
                      <view>{{ dataInfo.refund_delivery_no }}</view>
                      <view class="progress-btn" @click="progressDetailFun(dataInfo.refund_delivery_company_id, dataInfo.refund_delivery_no)"
                      >查看物流</view
                      >
                    </view>
                  </view>
                  <view class="receiving__info-box">
                    <view class="info__box-text">物流公司</view>
                    <view class="info__box-value">{{ COMPANY_NAME }}</view>
                  </view>
                </template>
              </view>
            </view>
          </view>
          <!-- 等待退款 状态6 -->
          <view class="situation__box" v-if="STATUS_6">
            <view class="situation__box-title">商家已同意，待平台审核退款</view>
            <view class="situation__box-content">
              <view class="content-receiving">
                平台会在2个工作日内审核退款，退款金额将按原路
                退回支付账号，如有疑问也可通过客服咨询。
              </view>
            </view>
          </view>
          <!-- 商家备注 为空则不显示-->
          <view
              class="situation__box"
              v-if="(STATUS_6 || STATUS_7) && String(dataInfo.admin_remark).length"
          >
            <view class="situation__box-title">商家备注</view>
            <view class="situation__box-content">
              <view class="content-receiving">
                {{ dataInfo.admin_remark }}
              </view>
            </view>
          </view>
          <!-- 驳回原因 -->
          <view class="situation__box" v-if="STATUS_00">
            <view class="situation__box-title"
            >以下是驳回原因，您可重新申请或请平台介入</view
            >
            <view class="situation__box-content">
              <view class="content-receiving">
                {{ dataInfo.refund_refuse_reason }}
              </view>
            </view>
          </view>
          <!-- 退款成功 订单已完成-->
          <view class="situation__box" v-if="STATUS_3 && REFUND_TYPE_2">
            <view class="situation__box-title">平台已退款，请留意退款信息</view>
            <view class="situation__box-content">
              <view class="content-receiving">
                退款金额已按原路退回支付账号，请留意退款信息，
                如有疑问也可通过客服咨询。
              </view>
            </view>
          </view>
        </view>
        <!-- 退款信息 -->
        <view class="refund_progress-info">
          <view class="info__box-title"
          >{{
              REFUND_TYPE_1 ? "退款" : REFUND_TYPE_2 ? "退货" : "换货"
            }}信息</view
          >
          <view class="info__box-goods">
            <view class="goods__img">
              <img :src="$util.img(dataInfo.sku_image)" alt="" />
            </view>
            <view class="goods__info">
              <view class="goods__info-text">{{ dataInfo.sku_name }}</view>
              <view class="goods__info-tips">{{ dataInfo.spec_name }}</view>
            </view>
          </view>
          <view class="info__box-content">
            <view class="content__box">
              <view class="content__box-text">{{
                  REFUND_TYPE_1 ? "退款" : REFUND_TYPE_2 ? "退款" : "换货"
                }}编号</view>
              <view class="content__box-value">{{
                  dataInfo.refund_no || "无"
                }}</view>
            </view>
            <view class="content__box">
              <view class="content__box-text">申请原因</view>
              <view class="content__box-value">{{
                  dataInfo.refund_reason || "无"
                }}</view>
            </view>
            <view class="content__box" v-if="REFUND_TYPE_1">
              <view class="content__box-text">货物状态</view>
              <view class="content__box-value">{{
                  dataInfo.goods_status_name || "无"
                }}</view>
            </view>
            <view class="content__box">
              <view class="content__box-text">购买数量</view>
              <view class="content__box-value">{{ dataInfo.num || "无" }}</view>
            </view>
            <view class="content__box">
              <view class="content__box-text">申请数量</view>
              <view class="content__box-value">{{
                  dataInfo.order_goods_aftersale.refund_num || "无"
                }}</view>
            </view>
            <!-- 换货时隐藏 -->
            <view class="content__box" v-if="!REFUND_TYPE_3">
              <view class="content__box-text">{{
                  String(dataInfo.is_maidou_pay) == "0" ? "退款金额" : "退回迈豆"
                }}</view>
              <view class="content__box-value isred">{{
                  String(dataInfo.is_maidou_pay) == "0"
                      ? "￥" + dataInfo.refund_apply_money
                      : dataInfo.refund_apply_money + "迈豆"
                }}{{String(dataInfo.is_maidou_pay) == "0" ? parseFloat(dataInfo.refund_delivery_money)>0 ? `(包含运费${dataInfo.refund_delivery_money})` : '（不包含运费）' :'' }}</view>
            </view>
            <view class="content__box">
              <view class="content__box-text">申请时间</view>
              <view class="content__box-value">{{
                  dataInfo.apply_time || "无"
                }}</view>
            </view>
            <view class="content__box" v-if="REFUND_TYPE_3">
              <view class="content__box-text">收货地址</view>
              <view class="content__box-value">
                <view class="value_name">{{
                    dataInfo.order_goods_aftersale.refund_recept_name
                  }}</view>
                <view class="value_iphone">{{
                    dataInfo.order_goods_aftersale.refund_recept_phone
                  }}</view>
                <view class="value_address">{{
                    dataInfo.order_goods_aftersale.refund_recept_address
                  }}</view>
              </view>
            </view>
            <view class="content__box">
              <view class="content__box-text">补充描述</view>
              <view class="content__box-value">{{
                  dataInfo.refund_remark || "无"
                }}</view>
            </view>
            <view class="content__box">
              <view class="content__box-text">上传凭证</view>
              <view
                  class="content__box-value imgbox"
                  v-if="dataInfo.order_goods_aftersale.upload_vouchers.length"
              >
                <img
                    @click="
                  preViewImage(
                    dataInfo.order_goods_aftersale.upload_vouchers,
                    index
                  )
                "
                    :src="item"
                    alt=""
                    v-for="(item, index) in dataInfo.order_goods_aftersale
                  .upload_vouchers"
                    :key="index"
                />
              </view>
              <view class="content__box-value" v-else>无</view>
            </view>
          </view>
        </view>
      </view>
      <!-- 按钮操作 -->
      <view class="refund_progress-btns">
        <view class="btns default" @click="$util.getCustomerService()">客服介入</view>
        <view class="btns default" v-if="STATUS_1" @click="checkstatusFunc(handleCanleApply, 'cancel')"
        >撤销申请</view
        >
        <view class="btns submit" v-if="STATUS_1" @click="checkstatusFunc(handleEditApply, 'apply')"
        >修改申请</view
        >
        <view class="btns submit" v-if="STATUS_10 || STATUS_00" @click="afreshApply"
        >重新申请</view
        >
        <view
            class="btns default"
            v-if="isCompanyInfoEdit"
            @click="isCompanyInfoEdit = false"
        >取消修改</view
        >
        <view
            class="btns submit"
            v-if="STATUS_4 || (STATUS_5 && isCompanyInfoEdit) || STATUS_9"
            @click="submitCompanyInfo"
        >提交物流信息</view
        >
        <view
            class="btns submit"
            v-if="STATUS_5 && !isCompanyInfoEdit"
            @click="checkstatusFunc(handleCanleEdit, 'edit_delivery')"
        >修改物流信息</view
        >
        <view class="btns submit" v-if="STATUS_8" @click="handleConfirm"
        >确认收货</view
        >
      </view>
    </template>
    <!-- 撤销/确认收货弹框 -->
    <uni-popup ref="popup-enter-upoff" type="center" class="uni-popup-father">
      <view class="uni-popup-popup">
        <view class="uni-popup-box">
          <view class="uni-popup-title">提示</view>
          <view class="uni-popup-content">{{ popupTitle }}</view>
        </view>
        <view class="uni-popup-btns">
          <view class="btn right" @click="close">取消</view>
          <view class="btn" @click="enter">确认</view>
        </view>
      </view>
    </uni-popup>
    <loading-cover ref="loadingCover"></loading-cover>
  </view>
</template>
<script>
import golbalConfig from "../../../common/mixins/golbalConfig";

export default {
  mixins:[golbalConfig],
  data() {
    return {
      dataInfo: {}, // data
      popupType: 1, // 弹窗类型
      popupTitle: "", //弹窗提示内容
      order_goods_id: "", // 商品id
      form: {
        // 物流表单提交
        refund_delivery_no: "",
        refund_delivery_company_id: "",
        refund_delivery_name: "",
      },
      express_company_list: [], // 物流公司
      stepList: [], // 步骤条
      isCompanyInfoEdit: false, // 是否修改
      overHiden: false, // 是否弹窗状态
      loadFinsh: false
    };
  },
  onLoad(option) {
    this.order_goods_id = option.order_goods_id;
  },
  onShow() {
    if (this.order_goods_id) {
      this.orderDetail();
    }
  },
  computed: {
    // 订单状态码
    // 已申请退款
    STATUS_1() {
      return Number(this.dataInfo.refund_status) === 1;
    },
    // 已确认
    STATUS_2() {
      return Number(this.dataInfo.refund_status) === 2;
    },
    // 已完成
    STATUS_3() {
      return Number(this.dataInfo.refund_status) === 3;
    },
    // 等待买家发货
    STATUS_4() {
      return Number(this.dataInfo.refund_status) === 4;
    },
    // 等待卖家收货
    STATUS_5() {
      return Number(this.dataInfo.refund_status) === 5;
    },
    // 卖家确认收货
    STATUS_6() {
      return Number(this.dataInfo.refund_status) === 6;
    },
    // 确认等待转账中(为处理第三方转账异步回调)
    STATUS_7() {
      return Number(this.dataInfo.refund_status) === 7;
    },
    // 等待买家收货
    STATUS_8() {
      return Number(this.dataInfo.refund_status) === 8;
    },
    // 卖家未收到货
    STATUS_9() {
      return Number(this.dataInfo.refund_status) === 9;
    },
    // 买家超时未退货
    STATUS_10() {
      return Number(this.dataInfo.refund_status) === 10;
    },
    // 卖家拒绝退款(驳回)
    STATUS_00() {
      return Number(this.dataInfo.refund_status) === -1;
    },
    // 退换方式
    // 仅退款
    REFUND_TYPE_1() {
      return Number(this.dataInfo.refund_type === 4);
    },
    // 退货退款
    REFUND_TYPE_2() {
      return Number(this.dataInfo.refund_type === 2);
    },
    // 换货
    REFUND_TYPE_3() {
      return Number(this.dataInfo.refund_type === 3);
    },
    // 物流公司名称
    COMPANY_NAME() {
      let company_name;
      this.express_company_list.map((item) => {
        if (item.company_id == this.dataInfo.refund_delivery_company_id) {
          company_name = item.company_name;
        }
      });
      return company_name;
    },
  },
  methods: {
    open() {
      if (this.popupType == 1) {
        this.popupTitle = "确认撤销申请吗";
      } else {
        this.popupTitle = "确认收货吗";
      }
      this.$refs["popup-enter-upoff"].open();
    },
    close() {
      this.$refs["popup-enter-upoff"].close();
    },
    enter() {
      switch (this.popupType) {
        case 1:
          this.canleApply();
          break;
        case 2:
          this.confirmReceipt();
          break;
      }
    },
    handleCanleEdit() {
      this.isCompanyInfoEdit = true;
      this.form.refund_delivery_company_id =
        this.dataInfo.refund_delivery_company_id;
      this.form.refund_delivery_no = this.dataInfo.refund_delivery_no;
    },
    // 修改申请
    handleEditApply() {
      this.$util.redirectTo(
        "/otherpages/order/return_and_exchange/refund_form",
        {
          order_goods_id: this.dataInfo.order_goods_id,
          num: this.dataInfo.num,
          type: this.dataInfo.refund_type,
        },
        "redirectTo"
      );
    },
    // 重新申请
    afreshApply() {
      this.$util.redirectTo(
        "/otherpages/order/return_and_exchange/select_service",
        {
          order_goods_id: this.dataInfo.order_goods_id,
          num: this.dataInfo.num,
        },
        "redirectTo"
      );
    },
    // 显示确认收货弹窗
    handleConfirm() {
      this.popupType = 2;
      this.open();
    },
    // 确认收货
    confirmReceipt() {
      this.close();
      uni.showLoading({
        title: "加载中",
        mask: true,
      });
      this.$api.sendRequest({
        url: this.$apiUrl.confirm,
        data: {
          order_goods_id: this.order_goods_id,
        },
        success: (res) => {
          if (res.code === 0) {
            this.$util.showToast({
              title: "已确认收货",
              mask: true,
              success: () => {
                setTimeout(() => {
                  // 重新获取详情数据
                  this.$nextTick(() => {
                    this.orderDetail();
                  });
                }, 1500);
              },
            });
          } else {
            this.$util.showToast({
              title: res.message,
            });
          }
        },
        complete: () => {
          uni.hideLoading();
        },
      });
    },
    // 显示确认撤销申请弹窗
    handleCanleApply() {
      this.popupType = 1;
      this.open();
    },
    // 撤销申请
    canleApply() {
      this.close();
      uni.showLoading({
        title: "加载中",
        mask: true,
      });
      this.$api.sendRequest({
        url: this.$apiUrl.cancel,
        data: {
          order_goods_id: this.order_goods_id,
        },
        success: (res) => {
          uni.hideLoading();
          if (res.code === 0) {
            this.$util.showToast({
              title: "已提交",
              mask: true,
              success: () => {
                setTimeout(() => {
                  // 重新获取详情数据
                  this.$util.redirectTo(
                    "/pages/order/detail/detail",
                    {
                      order_id: this.dataInfo.order_id,
                    },
                    "reLaunch"
                  );
                }, 1500);
              },
            });
          } else {
            this.$util.showToast({
              title: res.message,
            });
          }
        },
        complete: () => {
          uni.hideLoading();
        },
      });
    },
    // 获取订单详情
    orderDetail() {
      this.$api.sendRequest({
        url: this.$apiUrl.detail,
        data: {
          order_goods_id: this.order_goods_id,
        },
        success: (res) => {
          if (res.code === 0) {
            this.dataInfo = res.data;
            this.express_company_list = res.data.express_company_list;
            this.stepList = res.data.steps.content;
            if (
              res.data.refund_status == 9 &&
              res.data.refund_delivery_company_id
            ) {
              this.form.refund_delivery_no = res.data.refund_delivery_no;
              this.form.refund_delivery_name = res.data.refund_delivery_name;
              this.form.refund_delivery_company_id =
                res.data.refund_delivery_company_id;
            }
            uni.setNavigationBarTitle({
              title:
                this.dataInfo.refund_type == 4
                  ? "申请仅退款"
                  : this.dataInfo.refund_type == 2
                  ? "申请退货退款"
                  : "申请换货",
            });
          } else {
            this.$util.showToast({
              title: res.message,
            });
          }
          this.loadFinsh = true
        },
        complete: () => {
          if (this.$refs.loadingCover) this.$refs.loadingCover.hide();
        },
      });
    },
    // 提交物流信息
    submitCompanyInfo() {
      if (this.form.refund_delivery_no == "") {
        this.$util.showToast({
          title: "请填写快递单号",
        });
        return false;
      }
      if (!this.form.refund_delivery_company_id) {
        this.$util.showToast({
          title: "请选择物流公司",
        });
        return false;
      }
      uni.showLoading({
        title: "加载中",
        mask: true,
      });
      this.$api.sendRequest({
        url: this.$apiUrl.delivery,
        data: {
          order_goods_id: this.order_goods_id,
          refund_delivery_no: this.form.refund_delivery_no,
          refund_delivery_company_id: this.form.refund_delivery_company_id,
          refund_delivery_name: this.form.refund_delivery_name,
        },
        success: (res) => {
          uni.hideLoading();
          if (res.code === 0) {
            this.$util.showToast({
              title: "已提交",
              mask: true,
              success: (res) => {
                setTimeout(() => {
                  // 重新获取详情数据
                  this.$nextTick(() => {
                    this.orderDetail();
                    this.isCompanyInfoEdit = false;
                  });
                }, 1500);
              },
            });
          } else if (res.code === -10001) {
            this.$util.showToast({
              title: res.message,
              mask: true,
            });
            setTimeout(() => {
              // 重新获取详情数据
              this.$nextTick(() => {
                this.orderDetail();
                this.isCompanyInfoEdit = false;
              });
            }, 2000);
          }
          // 订单状态已经被刷新 重新加载页面 暂时用-10001状态
          // else if (res.code == -10083) {
          //   this.$util.showToast({
          //     title: res.message,
          //   });
          //   setTimeout(() => {
          //     // 重新获取详情数据
          //     this.$nextTick(() => {
          //       this.orderDetail();
          //       this.isCompanyInfoEdit = false;
          //     });
          //   }, 1500);
          // }
        },
      });
    },
    // 选择物流公司信息
    companySelect(data) {
      this.form.refund_delivery_name = data.company_name;
      this.form.refund_delivery_company_id = data.company_id;
      //   console.log("this.form -->", this.form);
    },
    // 查看物流
    progressDetailFun(company_id, delivery_no) {
      const params = {
        company_id: company_id,
        delivery_no: delivery_no,
        refund: 1,
      };
      // // 寄回商品时物流查看
      // if (this.STATUS_5) {
      //   params.delivery_no = this.dataInfo.refund_delivery_no;
      //   params.company_id = this.dataInfo.refund_delivery_company_id;
      // }
      // // 获取商家商品时物流查看
      // if (this.STATUS_8 || this.STATUS_3) {
      //   params.delivery_no =
      //     this.dataInfo.order_goods_aftersale.refund_recept_delivery_no;
      //   params.company_id =
      //     this.dataInfo.order_goods_aftersale.refund_recept_delivery_company_id;
      // }
      this.$util.redirectTo("/pages/order/logistics/logistics", params);
    },
    // 图片预览
    preViewImage(imgArr, index) {
      if (!imgArr.length) return;
      uni.previewImage({
        urls: imgArr,
        current: index,
      });
    },
    // 检查当前订单状态是否已经改变
    checkstatusFunc(callback, action) {
      uni.showLoading();
      this.$api.sendRequest({
        url: this.$apiUrl.checkstatus,
        data: {
          order_goods_id: this.order_goods_id,
          action: action,
        },
        success: (res) => {
          uni.hideLoading();
          if (res.code === 0) {
            callback();
          } else {
            this.$util.showToast({
              title: res.message,
              mask: true,
            });
            setTimeout(() => {
              // 重新获取详情数据
              this.$nextTick(() => {
                this.orderDetail();
              });
            }, 2000);
          }
        },
        // complete: () => {
        //   uni.hideLoading();
        // },
      });
    },
    copyText(text){
      this.$util.copy(text)
    },
    /**
     *分享参数组装
     */
    getSharePageParams() {
      return this.$util.unifySharePageParams('/otherpages/shop/home/<USER>', this.shareTitle || '先迈商城',
          '',{}, this.$util.img(this.shareImg) || this.$util.img('public/static/youpin/home_share.jpg'))
    },
    openModel(){
      this.overHiden = true
    },
    closeModel(){
      this.overHiden = false
    }
  },
  /**
   * 自定义分享内容
   * @param {Object} res
   */
  onShareAppMessage(res) {
    let { title, link, imageUrl, query } = this.getSharePageParams()
    return this.$buriedPoint.pageShare(link , imageUrl, title);
  },
  watch: {
    "form.refund_delivery_no": {
      handler: function (e) {
        this.$nextTick(() => {
          this.form.refund_delivery_no = e.replace(/[\W]/g, "");
        });
      },
    },
  },
};
</script>
<style lang="scss" scoped>
.uni-input-placeholder,
.placeholderByweixin-input {
  font-size: 28rpx;
  font-weight: 400;
  color: #cccccc;
}
/deep/.uni-input-input {
  font-size: 28rpx;
  font-weight: 400;
  color: #666666;
}
.refund_progress {
  width: 100%;
  box-sizing: border-box;
  padding-bottom: 100rpx;
  .refund_progress-header {
    height: 264rpx;
    background: linear-gradient(-90deg, var(--custom-brand-color) 0%, var(--custom-brand-color-80) 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    .header__progress {
      width: 90%;
      height: 200rpx;
      background: #ffffff;
      border-radius: 20rpx;
      box-sizing: border-box;
      padding: 30rpx 31rpx;
      .header__progress-title {
        font-size: 32rpx;
        font-weight: bold;
        color: #333333;
      }
    }
  }
  .refund_progress-content {
    width: 100%;
    box-sizing: border-box;
    padding: 20rpx 24rpx;
    .refund_progress-situation {
      width: 100%;
      background: #ffffff;
      border-radius: 20rpx;
      box-sizing: border-box;
      padding: 29rpx 22rpx;
      .situation__box {
        margin-bottom: 61rpx;
        &:last-of-type {
          margin-bottom: unset;
        }
      }
      .bottom_unset {
        margin-bottom: unset;
      }
      .situation__box-title {
        font-size: 28rpx;
        font-weight: bold;
        color: #333333;
        margin-bottom: 31rpx;
      }
      .situation__box-content {
        width: 100%;
        .receiving__info-box {
          width: 100%;
          display: flex;
          .info__box-text {
            width: 159rpx;
            font-size: 28rpx;
            font-weight: 400;
            color: #666666;
          }
          .info__box-value {
            flex: 1;
            font-size: 28rpx;
            font-weight: 400;
            color: #333333;
          }
          .address-box {
            display: flex;
            align-items: flex-end;
            .value__copy {
              width: 90rpx;
              height: 44rpx;
              font-size: 22rpx;
              font-weight: 400;
              color: var(--custom-brand-color);
              text-align: center;
              line-height: 44rpx;
            }
          }
        }
        .content__receiving-info {
          width: 100%;
          .receiving__info-form {
            width: 100%;
            border-top: 1px solid #eeeeee;
            box-sizing: border-box;
            padding-top: 30rpx;
            .info__form-input {
              width: 100%;
              height: 68rpx;
              background: #ffffff;
              border: 1px solid #cccccc;
              border-radius: 4px;
              display: flex;
              align-items: center;
              //   box-sizing: border-box;
              //   padding: 0 19rpx;
              margin-bottom: 20rpx;
              .form__input {
                width: 100%;
                height: 100%;
                box-sizing: border-box;
                padding: 0 19rpx;
              }
            }
          }
        }
      }
    }
    .refund_progress-info {
      width: 100%;
      margin-top: 20rpx;
      background: #ffffff;
      border-radius: 20rpx;
      box-sizing: border-box;
      padding: 30rpx 21rpx;
      .info__box-title {
        font-size: 32rpx;
        font-weight: bold;
        color: #333333;
        margin-bottom: 16rpx;
      }
      .info__box-goods {
        width: 100%;
        display: flex;
        .goods__img {
          width: 180rpx;
          height: 180rpx;
          img {
            width: 180rpx;
            height: 180rpx;
            display: block;
            border-radius: 20rpx;
            border: 1px solid #f2f2f2;
          }
        }
        .goods__info {
          margin-left: 20rpx;
          .goods_info-text {
            font-size: 28rpx;
            font-weight: 400;
            color: #333333;
            line-height: 42px;
          }
          .goods__info-tips {
            font-size: 24rpx;
            font-weight: 400;
            color: #999999;
          }
        }
      }
      .info__box-content {
        width: 100%;
        margin-top: 63rpx;
        .content__box {
          width: 100%;
          display: flex;
          margin-bottom: 19rpx;
          &:last-of-type {
            margin-bottom: unset;
          }
          .content__box-text {
            // flex: 1;
            width: 191rpx;
            font-size: 28rpx;
            font-weight: 400;
            color: #666666;
            // min-width: 120rpx;
          }
          .content__box-value {
            flex: 4;
            font-size: 28rpx;
            font-weight: 400;
            color: #333333;
            // margin-left: 82rpx;
            // box-sizing: border-box;
            // padding-left: 60rpx;
          }
          .isred {
            font-size: 28rpx;
            font-weight: 400;
            color: var(--custom-brand-color);
          }
          .imgbox {
            display: flex;
            img {
              width: 130rpx;
              height: 130rpx;
              display: block;
              border: 1px solid #f2f2f2;
              margin-right: 20rpx;
              &:last-of-type {
                margin-right: unset;
              }
            }
          }
        }
      }
    }
  }
  .refund_progress-btns {
    width: 100%;
    height: 98rpx;
    background: #ffffff;
    display: flex;
    align-items: center;
    justify-content: flex-end;
    box-sizing: border-box;
    padding-right: 24rpx;
    position: fixed;
    bottom: 0;
    .default {
      border: 1px solid #cccccc;
      border-radius: 32rpx;
      padding: 10rpx 28rpx;
      font-size: 26rpx;
      font-weight: 400;
      color: #666666;
    }
    .submit {
      padding: 10rpx 28rpx;
      font-size: 26rpx;
      font-weight: 400;
      color: #ffffff;
      background: linear-gradient(0deg, var(--custom-brand-color) 0%, var(--custom-brand-color-80) 100%);
      border-radius: 32rpx;
    }
    .btns {
      margin-right: 20rpx;
      &:last-of-type {
        margin-right: unset;
      }
    }
  }
}

.uni-popup-popup {
  width: 455rpx;
  height: 315rpx;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  align-items: center;
  border-radius: 15rpx;
  .uni-popup-box {
    width: 100%;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
    flex: 1;
    justify-content: center;
    .uni-popup-title {
      width: 100%;
      text-align: center;
      font-size: 32rpx;
      font-weight: bold;
      color: #333333;
      margin-bottom: 30rpx;
    }
    .uni-popup-content {
      width: 100%;
      text-align: center;
      font-size: 28rpx;
      font-weight: 400;
      color: #666666;
    }
  }
  .uni-popup-btns {
    width: 100%;
    display: flex;
    border-top: 1px solid #f2f2f2;
    .btn {
      flex: 1;
      text-align: center;
      padding: 15rpx 0;
    }
    .right {
      border-right: 1px solid #f2f2f2;
    }
  }
}

.overHiden {
  position: fixed;
  top: 0;
  bottom: 0;
  width: 100%;
  height: 100%;
  overflow: hidden;
}
.overHidenH5 {
  position: fixed;
  top: 0rpx;
  bottom: 0;
  width: 100%;
  height: 100%;
  overflow: hidden;
}

.progress-info {
  display: flex;
  .progress-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 120rpx;
    height: 48rpx;
    font-size: 24rpx;
    color: #666;
    border: 1px solid #ccc;
    border-radius: 24rpx;
    margin-left: 12rpx;
  }
}
</style>
