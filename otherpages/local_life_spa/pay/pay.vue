<template>
    <div>
      <loading-cover ref="loadingCover"></loading-cover>
    </div>
</template>

<script>
import AdaPay from "../../../common/js/adaPay";

export default {
  data(){
    return{
      pay_info:"",
      success_redirect_to_url:"",
      error_redirect_to_url:"",
    }
  },
  methods: {
    toPay() {
      //   AdaPay.doPay(this.pay_info, (result)=> {
      //     console.log('result',result)
      //     if (result.result_status == 'succeeded') {
      //       // 跳转支付成功页面
      //       // this.$util.redirectTo('/pages/pay/result/result?order_ids=' + order_ids, {
      //       //   code: out_trade_no
      //       // }, 'redirectTo');
      //       this.$util.diyRedirectTo({wap_url:this.success_redirect_to_url});
      //     } else if (result.result_status == 'failed' || result.result_status == 'cancel') {
      //       this.$util.diyRedirectTo({wap_url:this.error_redirect_to_url});
      //     } else {
      //       this.$util.diyRedirectTo({wap_url:this.error_redirect_to_url});
      //     }
      //
      // })
      let info={
        provider: 'wxpay',
        timeStamp: '',
        nonceStr: '',
        package: '',
        signType: '',
        paySign: '',
        success: (res)=> {
          this.$util.diyRedirectTo({wap_url:this.success_redirect_to_url},'redirectTo');
        },
        fail: (err) => {
          this.$util.diyRedirectTo({wap_url:this.error_redirect_to_url},'redirectTo');
        }
      }
      info=Object.assign(info,JSON.parse(this.pay_info))
      uni.requestPayment(info);
    },
    /**
     *分享参数组装
     */
    getSharePageParams() {
      return this.$util.unifySharePageParams('/otherpages/shop/home/<USER>', this.shareTitle || '先迈商城',
          '',{}, this.$util.img(this.shareImg) || this.$util.img('public/static/youpin/home_share.jpg'))
    }
  },
  /**
   * 自定义分享内容
   * @param {Object} res
   */
  onShareAppMessage(res) {
    let { title, link, imageUrl, query } = this.getSharePageParams()
    return this.$buriedPoint.pageShare(link , imageUrl, title);
  },
  onLoad(options) {
    console.log("options.pay_info",options);
    this.pay_info = options.pay_info ? decodeURIComponent(options.pay_info) : "";
    this.success_redirect_to_url = options.success_redirect_to_url ? options.success_redirect_to_url : '';
    this.error_redirect_to_url = options.error_redirect_to_url ? options.error_redirect_to_url : '';
    this.toPay();
  },

}
</script>

<style scoped>

</style>
