@mixin flex-row-center {
	display: flex;
	justify-content: center;
	align-items: center;
}

@mixin wrap {
	margin: $ns-margin;
	padding: $ns-padding;
	border-radius: $ns-border-radius;
	background: #fff;
	position: relative;
}

.align-right {
	text-align: right;
}

.container {
	width: 100vw;
	height: 100vh;
}

.site-wrap {
	@include wrap;

	.site-header {
		display: flex;
		align-items: center;

		.icondianpu {
			display: inline-block;
			line-height: 1;
			margin-right: 12rpx;
			font-size: 30rpx;
		}
	}

	.site-body {
		background: #f5f5f5;
		padding: 0 20rpx 20rpx 20rpx; 
		margin-top: 20rpx;

		.goods-wrap {
			margin-bottom: 20rpx;
			display: flex;
			position: relative;

			&:last-of-type {
				margin-bottom: 0;
			}

			.goods-img {
				width: 180rpx;
				height: 180rpx;
				padding: 20rpx 0 0 0;
				margin-right: 20rpx;

				image {
					width: 100%;
					height: 100%;
					border-radius: $ns-border-radius;
				}
			}

			.goods-info {
				flex: 1;
				position: relative;
				padding: 20rpx 0 0 0;
				max-width: calc(100% - 200rpx);

				.goods-name {
					display: -webkit-box;
					-webkit-box-orient: vertical;
					-webkit-line-clamp: 2;
					overflow: hidden;
					line-height: 1.5;
					font-size: 28rpx;
				}

				.goods-sub-section {
					width: 100%;
					line-height: 1.3;
					display: flex;

					.goods-price {
						font-weight: 700;
						font-size: 15px;
					}

					.unit {
						font-weight: normal;
						font-size: 24rpx;
						margin-right: 2rpx;
					}

					view {
						flex: 1;
						line-height: 1.3;
						&:last-of-type {
							text-align: right;

							.iconfont {
								line-height: 1;
								font-size: 26rpx;
							}
						}
					}
				}
			}
		}
	}
}

.order-cell {
	display: flex;
	margin: 20rpx 0;
	align-items: center;
	background: #fff;
	line-height: 40rpx;

	.tit {
		text-align: left;
	}

	.box {
		flex: 1;
		padding: 0 20rpx;
		line-height: inherit;

		.textarea {
			height: 40rpx;
		}
	}

	.iconfont {
		color: #bbb;
		font-size: 28rpx;
	}

	.order-pay {
		padding: 0;

		text {
			display: inline-block;
			margin-left: 6rpx;
		}
	}
}

.order-summary {
	@include wrap;

	.order-cell {
		.tit {
			color: #999;
		}

		.box {
			display: flex;
			align-items: center;
			color: #999;
		}
	}
}

.verify-btn {
	margin-top: $ns-margin;
}
