<template>
  <div class="end-box">
    <video class="video" :src="url" object-fit="fill" :autoplay="true"></video>
  </div>
</template>

<script>
  export default {
    data() {
      return {
        url: ''
      }
    },
    onLoad({room_id}) {
      this.$api.sendRequest({
        url: this.$apiUrl.getPlaybacks,
        data: {
          room_id
        },
        success: res => {
          if(res.code == 0) {
            this.url = res.data.live_replay[res.data.live_replay.length - 1].media_url
          }
        }
      })
    }
  }
</script>

<style lang="scss" scoped>
.end-box{
  position: fixed;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
}
.video{
  width: 100%;
  height: 100%;
}
</style>