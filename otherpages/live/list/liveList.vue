<template>
  <view class="live-view">
    <view class="live-list-tag">
      <view class="tag-item" @click="tagChangeFunc(0)">
        <view>精彩直播</view>
        <view
          class="tag-bottom-line"
          :class="tagActive === 0 ? 'tag-bottom-line-active' : ''"
        ></view>
      </view>
      <view class="tag-item" @click="tagChangeFunc(1)">
        <view>直播预告</view>
        <view
          class="tag-bottom-line"
          :class="tagActive === 1 ? 'tag-bottom-line-active' : ''"
        ></view>
      </view>
    </view>
    <view class="live-content">
      <!-- #ifdef MP-WEIXIN -->
      <mescroll-uni
        top="110rpx"
        height="90%"
        ref="mescroll"
        @getData="getListData"
        v-if="tagActive === 0"
      >
        <block slot="list">
          <block v-if="roomInfo.length">
            <view class="live-list-wonderful">
              <view
                class="live-list"
                v-for="(item, index) in roomInfo"
                :key="index"
                @click="roomFun(item)"
              >
                <view class="left">
                  <image
                    class="live-img"
                    :src="$util.img(item.feeds_img)"
                    @error="imageError(index)"
                    alt=""
                    mode="aspectFill"
                  />
                  <view class="info">
                    <view
                      ><img
                        :src="
                          $util.img(
                            `/public/static/youpin/${
                              liveStatusBgc[item.live_status]
                            }.png`
                          )
                        "
                        alt=""
                      />{{ liveStatus[item.live_status] }}</view
                    >
                    <!-- <view>3.43万观看</view> -->
                  </view>
                </view>
                <view class="right">
                  <view class="title">{{ item.name }}</view>
                  <view class="wonderful-info">
                    <!-- <img src="https://thirdwx.qlogo.cn/mmopen/vi_32/cxsxbxLbcob6mF6UOEuoWSbDcvYT7YD1yhBFJiadYTHc3P6j8Z2wGwAVTGI0wFG2e0OwUQYDMVw0VbA9MviewYdl2zg/132" alt=""> -->
                    <view>{{ item.anchor_name }}</view>
                  </view>
                  <view class="goods">
                    <img
                      :src="$util.img(goods.cover_img)"
                      alt=""
                      v-for="(goods, goodsIndex) in item.goods"
                      :key="goodsIndex"
                    />
                  </view>
                  <view class="btn">
                    <view @click.stop.prevent="openSharePopup(item)">
                      <img
                        :src="$util.img('public/static/youpin/forwarding.png')"
                        alt=""
                      />
                      转发
                    </view>
                  </view>
                </view>
              </view>
            </view>
          </block>
          <block v-else>
            <ns-empty
              text="暂无更多直播，敬请期待"
              :entrance="'live'"
              :isIndex="isIndex"
              style="margin-top: 40px; top: unset"
            ></ns-empty>
          </block>
        </block>
      </mescroll-uni>
      <mescroll-uni
        top="80rpx"
        height="90%"
        ref="mescroll"
        @getData="getListData"
        v-if="tagActive === 1"
      >
        <block slot="list">
          <block v-if="roomInfo.length">
            <view class="live-list-notice">
              <view
                class="live-item-notice"
                v-for="item in roomInfo"
                :key="item"
              >
                <view class="notice-box">
                  <view class="notice-status">
                    <view class="status">
                      <image
                        class="status-img"
                        :src="
                          $util.img(
                            `/public/static/youpin/${
                              liveStatusBgc[item.live_status]
                            }.png`
                          )
                        "
                        mode="aspectFill"
                      />
                      <text>{{ liveStatus[item.live_status] }}</text>
                    </view>
                    <view class="time"> {{ item.start_time }}</view>
                  </view>
                  <image
                    class="notice-cover"
                    :src="$util.img(item.feeds_img)"
                    mode="aspectFill"
                  ></image>
                </view>
                <view class="notice-info">
                  <view class="notice-title">
                    {{ item.name }}
                  </view>
                  <view class="anchor-info">
                    <!-- <image class="head-portrait" src="" mode="" /> -->
                    <view class="name-info">
                      主播 <text class="name">{{ item.anchor_name }}</text>
                    </view>
                  </view>
                  <view class="subscribe">
                    <subscribe
                      :width="62"
                      :height="24"
                      :room-id="item.roomid"
                      :font-size="12"
                      :color="'#ff3333'"
                      :background-color="'transparent'"
                      :custom-params="{}"
                      @subscribe="onSubscribe"
                    >
                    </subscribe>
                  </view>
                </view>
              </view>
            </view>
          </block>
          <block v-else>
            <ns-empty
              text="暂无更多直播，敬请期待"
              :isIndex="isIndex"
              :entrance="'live'"
              style="margin-top: 40px; top: unset"
            ></ns-empty>
          </block>
        </block>
      </mescroll-uni>
      <loading-cover ref="loadingCover"></loading-cover>

      <!-- h5分享 -->
      <diy-share-navigate-h5 ref="shareNavigateH5"></diy-share-navigate-h5>
      <!-- #endif -->
      <!-- #ifndef MP-WEIXIN -->
      <ns-empty
        text="直播仅支持在微信小程序内查看"
        :isIndex="isIndex"
        :entrance="'live'"
      ></ns-empty>
      <!-- #endif -->
    </view>
    <!--// #ifdef MP-WEIXIN -->
    <share-popup
      v-if="isShowCanvas"
      :canvasOptions="canvasOptions"
      ref="sharePopup"
      :sharePopupOptions="sharePopupOptions"
    ></share-popup>

    <view class="botton-btn" v-if="roomInfo.length">
      <button class="btn" :plain="true" open-type="share">
        <text>转发直播间</text>
      </button>
    </view>
    <!--// #endif -->
  </view>
</template>

<script>
import sharePage from "../share/share.vue";
import sharePopup from "@/components/share-popup/share-popup.vue";
import diyShare from "@/components/diy-share/diy-share.vue";
import { query_to_scene } from "../../../common/js/scene_handle";
import system from "@/common/js/system.js";
export default {
  components: {
    sharePage,
    sharePopup,
    diyShare,
  },
  data() {
    return {
      isIndex: false,
      showEmpty: false,
      siteId: 0,
      tagActive: 0,
      canvasOptions: {
        width: "634",
        height: "832",
        borderRadius: "20rpx",
      },
      sharePopupOptions: [],
      shareImgPath: [],
      isShowCanvas: false,
      roomInfo: [],
      liveStatus: {
        101: "进行中",
        102: "未开始",
        103: "回看",
        104: "禁播",
        105: "暂停",
        106: "异常",
        107: "已过期",
      },
      liveStatusBgc: {
        101: "ongoing",
        102: "begin",
        103: "back",
        104: "bear",
        105: "stop",
        106: "alarm",
        107: "",
      },
      curLiveInfo: "",
      subscribeInfo: {
        // 订阅信息
        roomid: "",
        customParams: {},
      },
      sharetype: 2, // 2： 分享列表  1 ：分享房间
    };
  },
  onLoad(options) {
    if (options.site_id) {
      this.siteId = options.site_id;
    }
  },
  async onShow() {
    // 刷新多语言
    this.$langConfig.refresh();
    system.wait_staticLogin_success();
  },
  onShareAppMessage(res) {
    let params = {};
    if (res.from == "menu" || (res.from == "button" && this.sharetype === 2)) {
      // 转发列表
      let { title, link, imageUrl, query } = this.getSharePageParams()
      return this.$buriedPoint.pageShare(link , imageUrl, title);
    } else if (res.from == "button" && this.sharetype === 1) {
      // 转发房间
      let share_data = this.getSharePageParams();
      let data = this.curLiveInfo;
      let title = data.name;
      let imageUrl = this.$util.img(data.share_img);
      let link =
        "/pages/live-player-plugin?" +
        share_data.query +
        "&room_id=" +
        data.roomid;
      this.sharetype = 2; // 初始化分享类型
      return this.$buriedPoint.pageShare(link , imageUrl, title);
    }
  },
  watch: {
    // 列表切换时刷新数据
    tagActive() {
      this.roomInfo = []
      if (this.$refs.mescroll) this.$refs.mescroll.refresh();
    },
  },
  methods: {
    // 监听订阅事件用于获取订阅状态
    onSubscribe(e) {
      console.log("房间号：", e.detail.room_id);
      console.log("订阅用户openid", e.detail.openid);
      console.log("是否订阅", e.detail.is_subscribe);
    },
    subscribe(roomid) {
      console.log("roomid", roomid);
      let livePlayer = requirePlugin("live-player-plugin");
      // 获取直播间单次订阅状态
      const roomId = roomid; // 房间 id
      livePlayer
        .getSubscribeStatus({ room_id: roomId })
        .then((res) => {
          console.log("房间号：", res.room_id);
          console.log("订阅用户openid", res.openid);
          console.log("是否订阅", res.is_subscribe);
        })
        .catch((err) => {
          console.log("get subscribe status", err);
        });
    },
    // 切换列表
    tagChangeFunc(val) {
      this.tagActive = val;
    },
    // 获取列表数据
    getListData(mescroll) {
      this.showEmpty = false;
      let data = {
        page: mescroll.num,
        page_size: mescroll.size,
        type: this.tagActive, // 精彩直播：0 直播预告：1
      };
      if (this.siteId) {
        data.site_id = this.siteId;
      }
      this.$api.sendRequest({
        url: "/broadcast/api/live/roomList",
        data: data,
        success: (res) => {
          this.showEmpty = true;
          let newArr = [];
          let msg = res.message;
          if (res.code == 0 && res.data) {
            newArr = res.data.list;
            mescroll.endSuccess(newArr.length);
            //设置列表数据
            if (mescroll.num == 1) this.roomInfo = []; //如果是第一页需手动制空列表
            this.roomInfo = this.roomInfo.concat(newArr); //追加新数据
            if (this.$refs.loadingCover) this.$refs.loadingCover.hide();
          } else {
            this.$util.showToast({
              title: msg,
            });
          }
        },
        fail: (res) => {
          mescroll.endErr();
          if (this.$refs.loadingCover) this.$refs.loadingCover.hide();
        },
      });
    },
    entryRoom(roomId) {
      // #ifdef MP-WEIXIN
      let path = this.$util.livePlayerPageUrl(roomid,false)
      this.$util.redirectTo(path);
      // #endif
    },
    imageError(index) {
      this.roomInfo[index].feeds_img =
        this.$util.getDefaultImage().default_goods_img;
    },
    /**
     *分享参数组装(注意需要分享的那一刻再调此方法)
     */
    getSharePageParams() {
      return this.$util.unifySharePageParams('/otherpages/live/list/liveList','直播间福利多多，只等你来',
          '',{}, this.$util.img("public/static/youpin/Live_fan.jpg"))
    },
    /**
     * 设置微信公众号分享
     */
    setWechatShare() {
      // 微信公众号分享
      // #ifdef H5
      let share_data = this.$util.deepClone(this.getSharePageParams());
      let link =
        window.location.origin +
        this.$router.options.base +
        share_data.link.slice(1);
      share_data.link = link;
      this.$util.publicShare(share_data);
      // #endif
    },
    // 打开分享弹出层
    openSharePopup(data) {
      this.sharetype = 1; // 分享房间
      // #ifdef MP-WEIXIN
      // if(this.$refs.sharePopup) this.$refs.sharePopup.open();
      this.newCommQrcode(data);
      // #endif
      // #ifdef H5
      let share_data = this.getSharePageParams();
      if (this.$refs.shareNavigateH5)
        this.$refs.shareNavigateH5.open(share_data);
      // #endif
    },
    // 获取二维码；调取海报
    newCommQrcode(data) {
      this.curLiveInfo = data;
      // 模拟二维码参数 scene=rd%3D10%26s%3D206%26r%3D343
      let querys = {
        room_id: data.roomid,
        shop_id: uni.getStorageSync("shop_id"),
      };
      if (uni.getStorageSync("member_id")) {
        querys.recommend_member_id = uni.getStorageSync("member_id");
      }
      let scene = query_to_scene(querys);
      let userInfo = uni.getStorageSync("userInfo");
      if (uni.getStorageSync("token") == "") {
        userInfo = "";
      }
      this.isShowCanvas = false;
      this.$api.sendRequest({
        url: "/api/Website/newCommQrcode",
        data: {
          // path: "pages/index/index/index", // 测试用
          path: 'pages/live-player-plugin',
          scene,
        },
        success: (res) => {
          if (res.code == 0) {
            if (userInfo == "") {
              let user = {};
              this.$api.sendRequest({
                url: "/api/member/info",
                success: (info) => {
                  if (info.code == 0 && uni.getStorageSync("token") != "") {
                    user = {
                      headimg: info.data.headimg,
                      nickname: info.data.nickname,
                    };
                  } else {
                    user = {
                      headimg:
                        "https://youpin-dev.jiufuwangluo.com:8443//upload/default/default_img/head.png",
                      nickname: "请登录",
                    };
                  }
                  this.drawCanvas(res.data.qrcodeUrl, data, user);
                  setTimeout(() => {
                    if (this.$refs.sharePopup) this.$refs.sharePopup.open();
                  }, 0);
                },
              });
            } else {
              this.drawCanvas(res.data.qrcodeUrl, data, userInfo);
              setTimeout(() => {
                if (this.$refs.sharePopup) this.$refs.sharePopup.open();
              }, 0);
            }
          } else {
            this.$util.showToast({
              title: res.message,
            });
          }
        },
      });
    },
    drawCanvas(qrcodeUrl, data, user) {
      this.sharePopupOptions = [
        {
          background: "#fff",
          x: 0,
          y: 0,
          width: 634,
          height: 832,
          type: "image",
        },
        {
          // 头图
          path: this.$util.img(data.share_img),
          x: 0,
          y: 0,
          width: 634,
          height: 507,
          type: "image",
        },
        {
          // 头像
          path: user.headimg,
          radius: 36,
          x: 40,
          y: 567,
          width: 56,
          height: 56,
          type: "image",
        },
        {
          text: user.nickname,
          size: 28,
          color: "#333",
          fontWeight: "bold",
          x: 130,
          y: 582,
          type: "text",
        },
        {
          text: data.name,
          size: 26,
          color: "#999",
          x: 130,
          y: 630,
          width: 310,
          lineNum: 2,
          lineHeight: 34,
          type: "text",
        },
        {
          path: qrcodeUrl,
          x: 466,
          y: 536,
          width: 128,
          height: 128,
          type: "image",
        },
        {
          background: "#F8F8F8",
          x: 0,
          y: 692,
          width: 634,
          height: 140,
          type: "image",
        },
        {
          path: this.$util.img("public/static/youpin/qrcodetips.png"),
          x: 40,
          y: 710,
          width: 554,
          height: 106,
          type: "image",
        },
      ];
      this.isShowCanvas = true;
    },
    // 点击列表跳转
    roomFun(e) {
      if (e.live_status == 103) {
        this.$util.redirectTo(`/otherpages/live/end/end?room_id=${e.roomid}`);
      } else {
        this.playerLive(e.roomid);
      }
    },
    // 跳转直播间
    playerLive(roomid) {
      let path = this.$util.livePlayerPageUrl(roomid,false)
      this.$util.redirectTo(path);
    },
  },
};
</script>

<style lang="scss" scoped>
/deep/.subscribe--live-player-subscribe__btn {
  border: 1px solid #ff3333;
  border-radius: 30rpx;
}
.live-list-tag {
  width: 100%;
  height: 88rpx;
  position: fixed;
  top: 0;
  left: 0;
  z-index: 2;
  display: flex;
  align-items: center;
  justify-content: space-around;
  font-size: 36rpx;
  font-family: PingFang SC;
  font-weight: 500;
  line-height: 50rpx;
  color: #222222;
  background: #ffffff;
  opacity: 1;
  .tag-item {
    width: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
  }
  .tag-bottom-line {
    width: 40rpx;
    height: 6rpx;
    background: #ff3333;
    opacity: 0;
    border-radius: 2px;
  }
  .tag-bottom-line-active {
    opacity: 1;
  }
}
.live-content {
  width: 100%;
  height: 500px;
}
.live-list-wonderful {
  flex: 1;
  // overflow-y: scroll;
  // margin: 0 0 30rpx;
  padding: 0 24rpx;
  background: #f5f5f5;
  //   margin-top: 40rpx;
  .live-list {
    display: flex;
    background-color: #fff;
    padding: 24rpx;
    margin-bottom: 24rpx;
    border-radius: 20rpx;
    &:last-of-type {
      margin-bottom: unset;
    }
    .left {
      position: relative;
      width: 372rpx;
      height: 372rpx;
      .live-img {
        width: 100%;
        height: 100%;
        border-radius: 20rpx;
      }
      .info {
        position: absolute;
        top: 12rpx;
        left: 12rpx;
        // width: 256rpx;
        height: 36rpx;
        display: flex;
        align-items: center;
        background-color: rgba(0, 0, 0, 0.5);
        border-radius: 18rpx;
        & > view:first-child {
          background: linear-gradient(180deg, #ec624d 0%, #ff3333 100%);
          border-radius: 18px;
          // width: 80rpx;
          // height: 36rpx;
          // min-width: 108rpx;
          font-size: 20rpx;
          color: #fff;
          padding: 0 8rpx;
          display: flex;
          align-items: center;
          // justify-content: space-between;
          box-sizing: border-box;
          padding: 2rpx 10rpx;
          img {
            width: 20rpx;
            height: 20rpx;
            margin-right: 4rpx;
          }
        }
        // &>view:last-child{
        //   font-size: 20rpx;
        //   padding: 0 20rpx 0 4rpx;
        //   color: #fff;
        // }
      }
    }
    .right {
      display: flex;
      flex-direction: column;
      flex: 1;
      padding-left: 24rpx;
      .title {
        line-height: 44rpx;
        text-overflow: ellipsis;
        display: -webkit-box;
        -webkit-box-orient: vertical;
        -webkit-line-clamp: 2;
        overflow: hidden;
        min-height: 88rpx;
      }
      .wonderful-info {
        display: flex;
        margin-top: 40rpx;
        align-items: center;
        img {
          width: 44rpx;
          height: 44rpx;
          border-radius: 50%;
          margin-right: 4rpx;
        }
        view {
          font-size: 24rpx;
          color: #666;
          line-height: 30rpx;
        }
      }
      .goods {
        display: flex;
        margin-top: 14rpx;
        width: 258rpx;
        overflow-x: scroll;
        img {
          flex-shrink: 0;
          width: 120rpx;
          height: 120rpx;
          border-radius: 10rpx;
          margin-right: 20rpx;
          &:last-child {
            margin-right: 20rpx;
          }
        }
      }
      .btn {
        display: flex;
        align-items: flex-end;
        flex-direction: row-reverse;
        flex: 1;
        view {
          display: flex;
          justify-content: center;
          align-items: center;
          width: 126rpx;
          height: 48rpx;
          background-color: #ff3333;
          border-radius: 48rpx;
          color: #fff;
          img {
            width: 32rpx;
            height: 32rpx;
            margin-right: 4rpx;
          }
        }
      }
    }
  }
}
.live-list-notice {
  width: 100%;
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  box-sizing: border-box;
  padding: 0 24rpx;
  padding-top: 10rpx;
  .live-item-notice {
    width: 342rpx;
    background: #ffffff;
    border-radius: 20rpx;
    margin-top: 20rpx;
    .notice-box {
      width: 342rpx;
      height: 342rpx;
      position: relative;
    }
    .notice-status {
      // height: 36rpx;
      background-color: rgba(0, 0, 0, 0.5);
      border-radius: 9px;
      position: absolute;
      top: 20rpx;
      left: 16rpx;
      z-index: 99;
      display: flex;
      .status {
        // width: 112rpx;
        // height: 36rpx;
        background: linear-gradient(180deg, #ec624d 0%, #ff3333 100%);
        border-radius: 18rpx;
        font-size: 20rpx;
        font-family: PingFang SC;
        font-weight: 400;
        // line-height: 38rpx;
        color: #ffffff;
        display: flex;
        align-items: center;
        box-sizing: border-box;
        padding: 2rpx 10rpx;
        justify-content: space-around;
        .status-img {
          width: 24rpx;
          height: 24rpx;
          display: block;
          margin-right: 4rpx;
        }
      }
      .time {
        font-size: 22rpx;
        font-family: PingFang SC;
        font-weight: 400;
        line-height: 38rpx;
        color: #ffffff;
        margin: 0 8rpx;
      }
    }
    .notice-cover {
      width: 342rpx;
      height: 342rpx;
      display: block;
      border-radius: 20rpx;
    }
    .notice-info {
      width: 100%;
      box-sizing: border-box;
      padding: 0 16rpx;
      padding-bottom: 20rpx;
      .notice-title {
        width: 100%;
        max-height: 74rpx;
        font-size: 26rpx;
        font-family: PingFang SC;
        font-weight: 600;
        line-height: 38rpx;
        color: #333333;
        margin-top: 8rpx;
      }
      .anchor-info {
        width: 100%;
        margin-top: 20rpx;
        display: flex;
        align-items: center;
        justify-content: space-between;
        .head-portrait {
          width: 44rpx;
          height: 44rpx;
          display: block;
          margin-right: 8rpx;
          border-radius: 50%;
          border: 1px solid black;
        }
        .name-info {
          font-size: 24rpx;
          font-family: PingFang SC;
          font-weight: 400;
          line-height: 38rpx;
          color: #666666;
          .name {
            margin-left: 8rpx;
          }
        }
      }
      .subscribe {
        width: 100%;
        display: flex;
        justify-content: flex-end;
      }
    }
  }
}
.botton-btn {
  width: 100%;
  height: 120rpx;
  background: #ffffff;
  position: fixed;
  bottom: 0;
  left: 0;
  z-index: 2;
  display: flex;
  align-items: center;
  justify-content: center;
  .btn {
    width: 642rpx;
    height: 84rpx;
    background: #ff3333;
    border-radius: 44rpx;
    text-align: center;
    line-height: 84rpx;
    font-size: 30rpx;
    font-family: PingFang SC;
    font-weight: 400;
    color: #ffffff;
    border: unset;
  }
}
</style>
