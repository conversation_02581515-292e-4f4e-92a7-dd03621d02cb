<template>
	<view>
    <view class="home--content--header">
      <!-- 搜索 start-->
      <view class="home--content--search">
        <input  maxlength="50" v-model="keyword" confirm-type="search" @confirm="toSearch" placeholder="搜索你喜欢的商品" />
        <uni-icons class="home--content--search--icon" type="search" size="20" color="#CCCCCC"></uni-icons>
      </view>
      <!-- 搜索 end-->
    </view>
    <mescroll-uni @getData="getGoodsList" ref="mescroll" top="124rpx">
      <block slot="list">
        <view class="home--content">
            <!--全部商品 start-->
            <view class="all" v-if="products.length>0">
              <view class="all--products">
                <view class="all--products--one" v-for="(item,index) in products" v-bind:key="index" :data-skuid="item.sku_id" :data-periodid="item.period_buy_id" @click="toProductDetail">
                  <view class="all--products--one--img">
                    <image :src="$util.img(item.goods_image)" @error="imageError(products,index)"></image>
                    <image :src="$util.img('public/static/youpin/product-sell-out.png')" class="over" v-if="item.goods_stock==0"></image>
                  </view>
                  <view class="all--products--one--name">
                    <text>周期购</text>
                    {{item.goods_name}}</view>
                  <view class="all--products--one--price">
                    <view><text>￥</text>{{item.buy_price}}</view>
                    <view>￥{{item.market_price}}</view>
                  </view>
                </view>
              </view>
            </view>
            <!--全部商品 end-->
            <ns-empty :isIndex="!1" v-else></ns-empty>
        </view>
      </block>
    </mescroll-uni>
    <loading-cover ref="loadingCover"></loading-cover>
	</view>
</template>

<script>
	import apiurls from "../../../common/js/apiurls";

  export default {
		data() {
			return {
        shop_id:null,
        navHeight:0,
        keyword:"",
        ads:[],
        products:[]
			};
		},
    methods:{
      async getGoodsList(mescroll){
        if (this.$refs.loadingCover) this.$refs.loadingCover.show();
        let data={
          shop_id:this.shop_id,
          page_size: mescroll.size,
          page: mescroll.num,
        };
        if(this.keyword){
          data=Object.assign(data,{keywords:this.keyword})
        }
        let res=await this.$api.sendRequest({
          url: apiurls.cyclePurchaseListUrl,
          async:false,
          data: data
        });
        if (this.$refs.loadingCover) this.$refs.loadingCover.hide();
        if(res.code!=0){
          uni.showToast({
            title: res.message,
            mask:true,
            icon:"none",
            duration: 3000
          });
          return
        }
        let newArr=res.data.list;
        mescroll.endSuccess(newArr.length);
        //设置列表数据
        if (mescroll.num == 1) this.products = []; //如果是第一页需手动制空列表
        this.products = this.products.concat(newArr); //追加新数据
      },
      toProductDetail(event){
        let sku_id=event.currentTarget.dataset.skuid;
        let period_id=event.currentTarget.dataset.periodid
        this.$util.redirectTo(`/pages/goods/periodbuy-detail/periodbuy-detail?sku_id=${sku_id}&period_id=${period_id}`)
      },
      toSearch(){
        this.$refs.mescroll.refresh();
      },
      imageError(data,index){
        if(data[index].goods_image){
          data[index].goods_image=this.$util.getDefaultImage().default_goods_img;
        }
        if(data[index].image_url){
          data[index].image_url=this.$util.getDefaultImage().default_goods_img;
        }
        this.$forceUpdate();
      },
      /**
       *分享参数组装
       */
      getSharePageParams() {
        return this.$util.unifySharePageParams('/otherpages/shop/home/<USER>', this.shareTitle || '先迈商城',
            '',{}, this.$util.img(this.shareImg) || this.$util.img('public/static/youpin/home_share.jpg'))
      }
    },
    /**
     * 自定义分享内容
     * @param {Object} res
     */
    onShareAppMessage(res) {
      let { title, link, imageUrl, query } = this.getSharePageParams()
      return this.$buriedPoint.pageShare(link , imageUrl, title);
    },
    onLoad(data){
      // 刷新多语言
      this.$langConfig.refresh();
      uni.getSystemInfo({
        success: res => {
          //导航高度
          let navHeight = res.statusBarHeight + 46;
          this.navHeight = navHeight;
        },
        fail(err) {
          console.log(err);
        }
      })
    },
    async onShow(){
      let shop_id=uni.getStorageSync('shop_id');
      this.shop_id=shop_id;
      if (this.$refs.loadingCover) this.$refs.loadingCover.hide();
    }
	}
</script>

<style lang="scss">
.home {
  width: 100%;
  height: 100vh;
  box-sizing: border-box;
  position: relative;
  &--content{
    box-sizing: border-box;
    padding: 0 24rpx;
    //padding-top: 197rpx;
    padding-top: 30rpx;
    &--header{
      position: fixed;
      top: 0rpx;
      left: 0;
      width: 100%;
      padding: 33rpx 30rpx;
      box-sizing: border-box;
      z-index: 5;
      background-color: white;
    }
    &--search{
      box-sizing: border-box;
      width: 690rpx;
      position: relative;
      input{
        background: #F5F5F5;
        border-radius: 30rpx;
        height: 60rpx;
        line-height: 60rpx;
        padding-left: 55rpx;
      }
      &--icon{
        position: absolute;
        left: 12rpx;
        top: 50%;
        transform: translateY(-50%);
      }
    }
  }
}
.all{
  background: #FFFFFF;
  border-radius: 20rpx;
  padding: 2rpx 24rpx 0 24rpx;
  box-sizing: border-box;
  margin-bottom:20rpx;
  margin-bottom: calc(20rpx + constant(safe-area-inset-bottom));
  margin-bottom: calc(20rpx + env(safe-area-inset-bottom));
  &--title {
    height: 36rpx;
    font-size: 30rpx;
    font-weight: bold;
    color: #343434;
    display: flex;
    align-items: center;
    text {
      width: 6rpx;
      height: 36rpx;
      background: #F2280C;
      border-radius: 3rpx;
      margin-right: 17rpx;
      display: inline-block;
    }
  }
  &--products{
    display: flex;
    justify-content: space-between;
    flex-wrap: wrap;
    margin-top: 34rpx;
    &--one{
      width: 319rpx;
      margin-bottom: 30rpx;
      &--img{
        width: 100%;
        height: 319rpx;
        position: relative;
        image{
          width: 100%;
          height: 100%;
          border-radius: 8rpx 8rpx 0px 0px;
          &.over{
            width: 120rpx;
            height: 120rpx;
            position: absolute;
            left: 50%;
            top: 50%;
            transform: translate(-50%,-50%);
          }
        }
      }
      &--name{
        font-size: 26rpx;
        font-weight: 500;
        color: #343434;
        word-break:break-all;
        text-overflow:ellipsis;//显示为省略号
        display:-webkit-box;//对象作为伸缩盒子模型显示
        -webkit-box-orient:vertical;//设置或检索伸缩盒对象的子元素的排列方式
        -webkit-line-clamp:2;//显示行数## 标题文字 ##
        overflow:hidden;
        margin-top: 20rpx;
        min-height: 74rpx;
        box-sizing: border-box;
        position: relative;
        line-height: 1.4;
        text{
          margin-right: 4rpx;
          display: inline-block;
          width: 88rpx;
          line-height: 34rpx;
          text-align: center;
          font-size: 20rpx;
          font-weight: 500;
          color: #FFFFFF;
          background: linear-gradient(55deg, #FE5838, #FB331D);
          border-radius: 4rpx;
        }
      }
      &--price{
        display: flex;
        justify-content: flex-start;
        align-items: center;
        view:first-child{
          font-size: 36rpx;
          font-family: PingFang SC;
          font-weight: bold;
          color: #F2280D;
          text{
            font-size: 26rpx;
          }
        }
        view:last-child{
          font-size: 24rpx;
          font-weight: 500;
          text-decoration: line-through;
          color: #9A9A9A;
          margin-left: 8rpx;
        }
      }
    }
  }
}
.to-top{
  width: 144rpx;
  height: 152rpx;
  position: fixed;
  right: 0;
  bottom: 200rpx;
}
/deep/view.empty{
  top: 40vh;
}
/deep/.mescroll-upwarp{
	padding: 0 !important;
	margin-bottom: 0 !important;
	min-height: 0 !important;
	line-height: 0 !important;
}
</style>
