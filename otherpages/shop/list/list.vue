<template>
  <diy-page-gesture :right-slide="goback">
    <view :class="themeStyle" :style="[themeColorVar]">
      <view class="content">
        <view class="head-wrap">
          <view class="search-header" :style="{ height: (navHeight+statusBarHeight) + 'px'}">
            <view class="search-header-inner" :style="{'top': statusBarHeight+'px',height: navHeight+'px', lineHeight: navHeight+'px'}">
              <text class="iconfont iconback_light" @click="$util.goBack"></text>
              <diy-associate-search :associate-top="(navHeight+statusBarHeight) + 'px'" @confirm="searchConfirm" :value="keyword" @clear="clearSearch"></diy-associate-search>
            </view>
          </view>
          <!-- 排序 -->
          <view class="sort-wrap">
            <!--					<view class="comprehensive-wrap" :class="{ 'ns-text-color': orderType === 'all' }" @click="sortTabClick('all')">-->
            <!--						<text :class="{ 'ns-text-color': orderType === 'all' }">综合</text>-->
            <!--					</view>-->
            <view class="price-wrap" @click="sortTabClick('price')">
              <text>价格</text>
              <view class="iconfont-wrap">
                <view class="iconfont iconiconangledown-copy" :class="{ 'ns-text-color': priceOrder === 'ASC' && orderType === 'price' }"></view>
                <view class="iconfont iconiconangledown" :class="{ 'ns-text-color': priceOrder === 'DESC' && orderType === 'price' }"></view>
              </view>
            </view>
<!--            <view class="price-wrap" @click="sortTabClick('sale_num')">-->
<!--              <text>销量</text>-->
<!--              <view class="iconfont-wrap">-->
<!--                <view class="iconfont iconiconangledown-copy" :class="{ 'ns-text-color': saleNumOrder === 'ASC' && orderType === 'sale_num' }"></view>-->
<!--                <view class="iconfont iconiconangledown" :class="{ 'ns-text-color': saleNumOrder === 'DESC' && orderType === 'sale_num' }"></view>-->
<!--              </view>-->
<!--            </view>-->
            <view class="price-wrap" @click="sortTabClick('is_activity_goods')">
              <text>活动</text>
              <view class="iconfont-wrap">
                <uni-icons class="iconfont iconicon-test" :class="{ 'ns-text-color': orderType === 'is_activity_goods' }" color="#999"></uni-icons>
              </view>
            </view>
<!--            <view @click="sortTabClick('screen')" class="screen-wrap">-->
<!--              <text>{{ keyword ? keyword.substring(0,3) : '搜索' }}</text>-->
<!--              <view class="iconfont-wrap">-->
<!--                <view class="iconfont iconIcon_search ns-text-color-gray"></view>-->
<!--              </view>-->
<!--            </view>-->
          </view>
        </view>


        <mescroll-uni :top="`${navHeight+statusBarHeight+top}px`" ref="mescroll" @scroll="scrollView" @getData="getGoodsList" @scrolltolower="scrolltolower">
          <block slot="list">
            <view>
              <view class="goods-list" :class="listStyle" v-if="!isLoadLike">
                <view v-for="(item, index) in goodsList" :key="index" class="goods-item" @click="$util.toProductDetail(item)">
                  <view class="image-wrap">
                    <image :src="$util.img(item.goods_image)" @error="imageError(index)" mode='aspectFit' class="expose_goods_index" :data-expose_goods_sku="item.sku_id"/>
                    <!-- 秒杀商品显示已抢光，其他显示已售罄 -->
                    <image v-if="item.goods_stock==0 && item.is_seckill == 1" :src="$util.img('public/static/youpin/product-over.png')" class="over"></image>
                    <image v-if="item.goods_stock==0 && item.is_seckill == 0" :src="$util.img('public/static/youpin/product-sell-out.png')" class="over" ></image>
                  </view>
                  <view class="goods-info">
                    <view class="goods-name">
                      <!--									<text class="recommend-icon" v-if="item.is_recommend">推荐</text>-->
                      <!--									<text class="recommend-icon" v-for="(tag, key) in item.tags" :key="key">{{tag.tag_name}}</text>-->
                      {{ item.goods_name }}
                    </view>
                    <view class="goods-tag" v-if="item.keywords && item.keywords.length">
                      <text v-for="(kk,ii) in item.keywords.filter(one=>!!one)" :key="ii" v-if="kk">{{ kk }}</text>
                    </view>
                    <view class="price-wrap">
                      <view class="price ns-text-color"> <text>￥</text>{{item.retail_price}}</view>
                      <view class="market-price">￥{{item.market_price}}</view>
                    </view>
                    <scroll-view scroll-x="true" class="twoScroll_discounts">
                      <view class="twoScroll_discounts-words">
                        <text class="twoScroll_discounts-words-one" v-if="item.is_seckill">秒杀</text>
                        <text class="twoScroll_discounts-words-one" v-if="Object.keys(item.promotion).length">拼团</text>
                        <block v-for="(tag,kk) in item.tags" :key="kk">
                          <text class="twoScroll_discounts-words-one" v-if="tag.tag_name">{{tag.tag_name}}</text>
                        </block>
                      </view>
                      <view class="twoScroll_discounts-one" v-if="item.goods_coupon">
                        <text class="twoScroll_discounts-one-name">券</text>
                        <text class="twoScroll_discounts-one-value">领{{item.goods_coupon.money}}元券</text>
                      </view>
                      <view class="twoScroll_discounts-one" v-if="item.multiple_discount">
                        <text class="twoScroll_discounts-one-name">折</text>
                        <text class="twoScroll_discounts-one-value">{{item.multiple_discount.at_least}}件{{item.multiple_discount.discount}}折</text>
                      </view>
                    </scroll-view>
                  </view>
                </view>

              </view>
              <view class="goods-empty" v-else>
                <view class="goods-empty-top">
                  <ns-empty text="没找到你要的商品哦~" :isEmptyImg=false :isIndex=false :fixed=false :siteId="siteId"></ns-empty>
                </view>
                <view>
                  <nsGoodsRecommend ref="goodrecommend"></nsGoodsRecommend>
                </view>
              </view>
            </view>
          </block>

        </mescroll-uni>

        <!-- 商品分类弹出框 -->
        <!-- <uni-drawer :visible="showCategory" mode="left" @close="showCategory = false">
          <scroll-view scroll-y class="category-list-wrap">
            <view>
              <text class="first" @click="selectedCategory(0, 0)" :class="{ selected: categoryId == 0 }">全部分类</text>
              <view v-for="(item, index) in categoryList" :key="index" class="list-wrap">
                <text class="first" @click="selectedCategory(item.category_id, item.level)" :class="{ selected: item.category_id == categoryId }">
                  {{ item.category_name }}
                </text>
                <view class="third" v-if="item.child_list != ''">
                  <view v-for="(third_item, third_index) in item.child_list" :key="third_index">
                    <uni-tag :inverted="true" :text="third_item.category_name" :type="third_item.category_id == categoryId ? 'primary' : 'default'"
                     @click="selectedCategory(third_item.category_id, third_item.level)" />
                  </view>
                </view>
              </view>
            </view>
          </scroll-view>
        </uni-drawer> -->

        <!-- 筛选弹出框 -->
        <!-- 		<uni-drawer :visible="showScreen" mode="right" @close="showScreen = false" class="screen-wrap">
              <view class="title ns-text-color-gray">筛选</view>
              <scroll-view scroll-y> -->
        <!-- 品牌筛选项 -->
        <!-- <view class="item-wrap" v-if="brandList.length > 0">
          <view class="label">
            <text>品牌</text>
            <view class="iconfont" :class="[showBrandMore ? 'iconiconangledown' : 'iconiconangledown-copy']" v-if="brandList.length > 1"
             @click="showBrandMore = !showBrandMore"></view>
          </view>
          <view class="list">
            <template v-if="showBrandMore">
              <view v-for="(item, index) in brandList" :key="index">
                <uni-tag :inverted="true" :text="item.brand_name" :type="item.brand_id == brandId ? 'primary' : 'default'"
                 @click="brandId == item.brand_id ? (brandId = 0) : (brandId = item.brand_id)" />
              </view>
            </template>
            <template v-else>
              <view v-for="(item, index) in brandList" :key="index" v-if="index < 5">
                <uni-tag :inverted="true" :text="item.brand_name" :type="item.brand_id == brandId ? 'primary' : 'default'"
                 @click="brandId == item.brand_id ? (brandId = 0) : (brandId = item.brand_id)" />
              </view>
            </template>
          </view>
        </view> -->

        <!-- 包邮 -->
        <!-- <view class="item-wrap">
          <view class="label"><text>是否包邮</text></view>
          <view class="list">
            <uni-tag :inverted="true" text="包邮" :type="isFreeShipping>0 ? 'primary' : 'default'" @click="isFreeShipping = isFreeShipping>0?0:1" />
          </view>
        </view> -->

        <!-- 属性筛选项 -->
        <!-- <view class="item-wrap" v-if="attributeList.length > 0">
          <view v-for="(item, index) in attributeList" :key="index">
            <view class="label">
              <text>{{ item.attr_name }}</text>
              <view class="iconfont iconiconangledown-copy" v-if="item.child.length > 3"></view>
            </view>
            <view class="list">
              <view v-for="(child, child_index) in item.child" :key="child_index">
                <uni-tag :inverted="true" :text="child.attr_value_name" :type="isSelectedAttr(item.attr_id, child.attr_value_id) ? 'primary' : 'default'"
                 @click="selectedAttr(item.attr_id, child.attr_value_id)" />
              </view>
            </view>
          </view>
        </view> -->

        <!-- 价格筛选项 -->
        <!-- <view class="item-wrap">
          <view class="label"><text>价格区间(元)</text></view>
          <view class="price-wrap">
            <input class="uni-input" type="digit" v-model="minPrice" placeholder="最低价" />
            <text class="ns-text-color-gray">——</text>
            <input class="uni-input" type="digit" v-model="maxPrice" placeholder="最高价" />
          </view>
        </view>
      </scroll-view>
      <view class="footer safe-area">
        <view class="footer-box" @click="showScreen = false">取消</view>
        <view class="footer-box1 ns-bg-color" @click="screenData">确定</view>
      </view>
    </uni-drawer> -->
        <image :src="$util.img('public/static/youpin/member/zhuji.png')" class="footprint" v-if="token" @click="$util.redirectTo('/otherpages/member/footprint/footprint')"></image>
        <!-- 底部tabBar -->
        <!--			<diy-bottom-nav type="shop" :site-id="siteId"></diy-bottom-nav>-->
        <loading-cover ref="loadingCover"></loading-cover>
      </view>
    </view>
  </diy-page-gesture>
</template>

<script>
	// import uniDrawer from '@/components/uni-drawer/uni-drawer.vue';
	// import uniTag from '@/components/uni-tag/uni-tag.vue';
	import diyBottomNav from '@/components/diy-bottom-nav/diy-bottom-nav.vue';
	import list from '../public/js/list.js';
	import nsGoodsRecommend from '@/components/ns-goods-recommend/ns-goods-recommend.vue';
  import wx_expose_goods from '@/common/mixins/wx_expose_goods.js';
  import UniIcons from "../../../components/uni-icons/uni-icons.vue";
  import globalConfig from "../../../common/mixins/golbalConfig";
  import diyAssociateSearch from "../../components/diy-associate-search/diy-associate-search.vue";

	export default {
		components: {
      diyAssociateSearch,
      UniIcons,
			// uniDrawer,
			// uniTag,
			diyBottomNav,
			nsGoodsRecommend
		},
		computed: {
			//vueX页面主题
			themeStyle() {
				return 'theme-' + this.$store.state.themeStyle
			},
			addonIsExit(){
				return this.$store.state.addonIsExit
			}
		},
		data() {
			return {};
		},
		mixins: [wx_expose_goods,list,globalConfig]
	};
</script>

<style lang="scss">
	@import '../public/css/list.scss';
</style>
<style lang="scss">
	/deep/ .mescroll-uni-warp scroll-view {
		//padding-bottom: 200rpx !important;
	}

	.active /deep/ .mescroll-uni-warp scroll-view {
		padding-bottom: 178rpx !important;
	}
  $gussH: 0rpx;
  .guess-goods{
    height: calc(100vh - #{$gussH});
    padding-top: #{$gussH};
  }
  .content{
    //padding: 24rpx 0;
    //box-sizing: border-box;
  }
  .footprint{
    z-index: 9990;
    width: 144rpx;
    height: 148rpx;
    position: fixed;
    right: 0;
    bottom: 140rpx;
  }
  .search-close{
    width: 42rpx!important;
    height: 42rpx!important;
    line-height: 42rpx!important;
    font-size: 20rpx;
    color: #fff;
    text-align: center;
    background: #999;
    border-radius: 50%;
    position: absolute;
    top: 50%;
    right: 20rpx;
    transform: translateY(-50%);
    z-index: 10;
  }
</style>
