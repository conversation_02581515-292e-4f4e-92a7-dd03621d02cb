<template>
	<view class="page" style="background: rgba(245, 245, 245, 1);" :style="[themeColorVar]">
		<!-- <diy-shop-info :site-id="siteId"></diy-shop-info> -->
		<!-- #ifdef MP-WEIXIN -->
		<view class="top-head" :style="{height:navHeight+44+'px'}">
			<view class="nav-bar-auto">
				<text class="iconfont iconback_light"  v-if="!showNavBar" @click="goBackHome"></text>
        <view class="top-head-search">
          <view class="top-head--content--search" v-on:click="toSearch">
            <image :src="$util.img('public/static/youpin/home-search.png')"></image>
            <text>搜索你喜欢的商品</text>
          </view>
        </view>
			</view>
		</view>
		<!-- #endif -->
    <!-- #ifdef H5 -->
		<view class="search">
			<view class="home--content--search" v-on:click="toSearch">
				<image :src="$util.img('public/static/youpin/home-search.png')"></image>
				<text>搜索你喜欢的商品</text>
			</view>
		</view>
    <!-- #endif -->
		<!-- <view v-for="(item, index) in diyData.value" :key="index">
			<template v-if="item.controller == 'GoodsCategory'">
				<view :class="iphoneX?'active' : 'no'">
					<diy-goods-level-category :value="item" :siteId="siteId" @netFinish="netFinish" :autoHeight="!1" :bottom="windowHeight"></diy-goods-level-category>
				</view>
			</template>
		</view>
		<loading-cover ref="loadingCover"></loading-cover> -->
		<view class="newpage1" :style="{boxSizing: 'border-box'}">
			<view class="category-goods">

				<scroll-view scroll-y="true" class="oneScroll">
          <block v-for="(item,index) in data" :key="index">
            <view class="oneScroll-item" :class="{'onactive':item.isChoose,'oneUnfold':item.isUnfold}">
              <text class="max-font-2 one-ji"
                    @click="chooseCategory(item.level, item.category_id_1, item.category_id_2, item.category_id_3,item.category_name)">
                {{item.category_name}}
              </text>
            </view>
          </block>
				</scroll-view>
				<view class="twoScroll">
          <view class="inner-category">
            <view class="two-level-category" v-if="category_id_1 && data.find(uu=>uu.category_id==category_id_1).child_list">
              <scroll-view scroll-x="true" class="two-level-category-scroll" :scroll-left="scrollTop">
                <text v-for="(item,index) in data.find(uu=>uu.category_id==category_id_1).child_list" class="two-level-category-scroll-item"
                      :class="{'two-level-category-scroll-item-action':item.category_id==category_id_2}" @click="chooseCategory(item.level, item.category_id_1, item.category_id_2, item.category_id_3,item.category_name)">{{item.category_name}}</text>
              </scroll-view>
<!--              <text class="iconfont iconright two-level-category-more" @click="scrollTwoCategory"></text>-->
            </view>
<!--            <swiper class="three-swiper" indicator-dots  indicator-active-color="rgba(252, 47, 186, 1)" indicator-color="rgba(242, 242, 242, 1)" v-if="category_id_2">-->
<!--              <swiper-item v-for="(three,i) in category_3_child_page_count_list" :key="i">-->
<!--                <view class="three-swiper-item">-->
<!--                  <block v-for="(one,j) in three">-->
<!--                    <view class="three-swiper-item-one"  @click="chooseCategory(one.level, one.category_id_1, one.category_id_2, one.category_id_3,one.category_name)">-->
<!--                      <image :src="one.image ? $util.img(one.image) : $util.getDefaultImage().default_goods_img" class="three-swiper-item-one-image" mode="widthFix" @error="threeCategoryImageError(category_3_child_page_count_list,i,j)"></image>-->
<!--                      <text class="three-swiper-item-one-name">{{one.category_name}}</text>-->
<!--                    </view>-->
<!--                  </block>-->
<!--                </view>-->
<!--              </swiper-item>-->
<!--            </swiper>-->
            <scroll-view scroll-x="true" class="three-swiper">
              <view class="three-swiper-item">
                <block v-for="(one,j) in category_3_child_list" :key="j">
                  <view class="three-swiper-item-one"  @click="chooseCategory(one.level, one.category_id_1, one.category_id_2, one.category_id_3,one.category_name)">
                    <image :src="one.image ? $util.img(one.image) : $util.getDefaultImage().default_goods_img" class="three-swiper-item-one-image" mode="aspectFit" @error="categoryImageError(category_3_child_list,j)"></image>
                    <text class="three-swiper-item-one-name">{{one.category_name}}</text>
                  </view>
                </block>
              </view>
            </scroll-view>
          </view>
					<scroll-view scroll-y="true" class="secondscoll"
                       :class="{'secondscoll-have-empty': !(category_id_1 && data.find(uu=>uu.category_id==category_id_1).child_list) }"
                       @scrolltolower="bottomOut" @scroll="scrollView">
            <block v-if="goodsList.length>0">
              <view class="secondscoll-category">
                <text class="secondscoll-category-name">{{ category_name }}</text>
                <text class="secondscoll-category-op" @click="toFiltrate(category_name,category_id,category_level)">筛选查看<text class="iconfont iconright secondscoll-category-op-icon"></text></text>
              </view>
              <view class="twoScroll_item" v-for="(item,index) in goodsList" :key="index"
                    @click="toDetail(item)">
                <view class="goods_img">
                  <image :src="$util.img(item.goods_image)" class="item-image expose_goods_index" :data-expose_goods_sku="item.sku_id"  mode='aspectFit' @error="imageError(goodsList,index)"></image>
                  <image :src="$util.img('public/static/youpin/product-sell-out.png')" class="goods_img-over"  v-if="item.goods_stock == 0"></image>
                </view>
                <view class="item_right">
                  <view class="twoScroll_title">
                    {{item.goods_name}}
                  </view>
                  <view class="price_box" :style="false ? 'margin-top: 35rpx;' : ''">
                    <text class="true_prcie"><text class="symbol">￥</text>{{item.retail_price}}</text>
                    <text class="market_price">￥{{item.market_price}}</text>
                  </view>
                  <view class="twoScroll_keywords">
                    <text class="twoScroll_keywords_one" v-for="(key,pp) in item.keywords.filter(one=>!!one)" :key="pp">{{key}}</text>
                  </view>
                  <scroll-view scroll-x="true" class="twoScroll_discounts">
                    <view class="twoScroll_discounts-words">
                      <text class="twoScroll_discounts-words-one" v-if="item.is_seckill">秒杀</text>
                      <text class="twoScroll_discounts-words-one" v-if="Object.keys(item.promotion).length">拼团</text>
                      <block v-for="(tag,kk) in item.tags" :key="kk">
                        <text class="twoScroll_discounts-words-one" v-if="tag.tag_name">{{tag.tag_name}}</text>
                      </block>
                    </view>
                    <view class="twoScroll_discounts-one" v-if="item.goods_coupon">
                      <text class="twoScroll_discounts-one-name">券</text>
                      <text class="twoScroll_discounts-one-value">领{{item.goods_coupon.money}}元券</text>
                    </view>
                    <view class="twoScroll_discounts-one" v-if="item.multiple_discount">
                      <text class="twoScroll_discounts-one-name">折</text>
                      <text class="twoScroll_discounts-one-value">{{item.multiple_discount.at_least}}件{{item.multiple_discount.discount}}折</text>
                    </view>
                  </scroll-view>
                </view>
              </view>
            </block>
						<block v-else>
              <view class="hr-view" v-if="goodsListgess.length>0">
<!--                <image class="title-img" :src="$util.img('public/static/youpin/goodsRecommend-title.png')" mode="widthFix">-->
<!--                </image>-->
                <view class="title-word">先迈甄选</view>
              </view>
              <view class="twoScroll_item" v-for="(item,index) in goodsListgess" :key="index"
                    @click="toDetail(item)">
                <view class="goods_img">
                  <image :src="$util.img(item.goods_image)" class="item-image" mode='aspectFit' @error="imageError(goodsListgess,index)"></image>
                  <image :src="$util.img('public/static/youpin/product-sell-out.png')" class="goods_img-over"  v-if="item.goods_stock == 0"></image>
                </view>
                <view class="item_right">
                  <view class="twoScroll_title">
                    {{item.goods_name}}
                  </view>
                  <view class="price_box">
                    <text class="true_prcie"><text class="symbol">￥</text>{{item.retail_price}}</text>
                    <text class="market_price">￥{{item.market_price}}</text>
                  </view>
                </view>
              </view>
            </block>
            <view v-if="showLoading" class="secondscoll-loading">
              <ns-loading></ns-loading>
            </view>
					</scroll-view>
<!--          <scroll-view scroll-y="true" class="secondscoll" @scrolltolower="bottomOutgess" v-else>-->
<!--            -->
<!--          </scroll-view>-->
				</view>
			</view>

		</view>

		<!-- 底部tabBar -->
		<diy-bottom-nav v-if="showNavBar" type="shop" :site-id="siteId"></diy-bottom-nav>

	</view>
</template>

<script>
	import wx_expose_goods from '@/common/mixins/wx_expose_goods.js';
	// import diyShopInfo from '@/components/diy-shop-info/diy-shop-info';
	// import diyGoodsLevelCategory from "@/components/diy-goods-level-category/diy-goods-level-category.vue";
	import diyBottomNav from '@/components/diy-bottom-nav/diy-bottom-nav.vue';
  import nsLoading from '@/components/ns-loading/ns-loading.vue'
  import golbalConfig from "../../../common/mixins/golbalConfig";
	export default {
		components: {
			diyBottomNav,
      nsLoading,
			// diyShopInfo
		},
		mixins:[wx_expose_goods,golbalConfig],
		data() {
			return {
				siteId: 0,
				diyData: [],
				isIphoneX: false, //判断手机是否是iphoneX以上,
				windowHeight: 0,
				data: [],
				goodsList: [],
				likegoodsList: [],
				category_level: null,
        category_name: '',
				category_id: null,
        category_id_1: null,
        category_id_2: null,
        category_id_3:null,
        category_3_child_list:[],
        category_3_child_page_count_list:[],
        category_3_child_page:4,
				page: 1,
				page_size: 10,
				getLikeList: false, //true 类目没商品,去获取猜你喜欢的商品
				flag: true,
				goodsListgess: [],
				paget: 1,
				page_sizet: 10,
				flagt: true,
        showLoading:false,
				navHeight: 0,
				showNavBar: true,
        scrollTop: 0
			};
		},
		onLoad(options) {
			options.showNavBar && (this.showNavBar = false)

			this.iphoneX = this.$util.uniappIsIPhoneX()
			if (!options.shop_id && !options.site_id) {
				this.$util.redirectTo('/otherpages/shop/home/<USER>', {}, 'redirectTo');
				return;
			}
			this.siteId = options.shop_id || options.site_id;

			this.getHeight();
			this.getShopGoodsCateTree();
		},
		onShow() {
			// 刷新多语言
			this.$langConfig.refresh();

		},
		methods: {
			// 滚动监听曝光
			async scrollView(e){
				await this.scrollTouch(e.detail)
			},
			// 返回上一页
			goBackHome() {
				uni.navigateBack({
					delta: 1
				});
			},
			getHeight() {
				var self = this;
				uni.getSystemInfo({
					success: function(res) {

						self.navHeight = res.statusBarHeight;

						self.windowHeight = res.windowHeight - 57 - 68;
						if (self.iphoneX) {
							self.windowHeight = self.windowHeight - 33 - 68;
						}
					}
				});
			},
			//监听子组件分类列表是否请求完成
			netFinish(e) {
				if (e) {
					if (this.$refs.loadingCover) this.$refs.loadingCover.hide();
				}
				setTimeout(() => {
					if (this.$refs.loadingCover) this.$refs.loadingCover.hide();
				}, 1000)
			},
			getDiyInfo() {
				this.$api.sendRequest({
					url: '/api/diyview/info',
					data: {
						site_id: this.siteId,
						name: 'DIYVIEW_SHOP_GOODS_CATEGORY'
					},
					success: res => {
						if (res.code != 0 || !res.data) {
							if (this.$refs.loadingCover) this.$refs.loadingCover.hide();
							return;
						}
						this.diyData = res.data;

						if (this.diyData.value) {
							this.diyData = JSON.parse(this.diyData.value);
							for (var i = 0; i < this.diyData.value.length; i++) {
								if (this.diyData.value[i].controller == 'PopWindow') {
									setTimeout(() => {
										if (uni.getStorageSync('index_wap_floating_layer') != null &&
											uni.getStorageSync(
												'index_wap_floating_layer') != '') {
											var wap_floating_cookie = JSON.parse(uni.getStorageSync(
												'index_wap_floating_layer'));
											if (wap_floating_cookie.closeNum < 3) {
												this.$refs.uniPopup[0].open();
											}
										} else {
											this.$refs.uniPopup[0].open();
										}
									}, 500);
									break;
								}
								this.diyData.value[i].template = 2;
								this.diyData.value[i].level = 3;
							}
						}
						uni.stopPullDownRefresh();
						if (this.$refs.loadingCover) this.$refs.loadingCover.hide();
					},
					fail: res => {
						if (this.$refs.loadingCover) this.$refs.loadingCover.hide();
					}
				});
			},
			toSearch() {
			  this.$util.redirectTo("/otherpages/goods/search/search")
			},
			getShopGoodsCateTree() {
        uni.showLoading({
          title:"加载中",
          mask:true
        });
				let url = "/api/goodscategory/tree"
				let shopid = uni.getStorageSync('shop_id');
				let data = {
					shop_id: shopid
				};
				this.$api.sendRequest({
					url: url,
					data: data,
					success: res => {
						if (res.code == 0) {
							this.data = this.getthreedata(res.data)
							this.getGoodslists()
						} else {
							this.$util.showToast({
								title: res.message
							});
						}
					},
				});
			},
			// 数据处理
			getthreedata(data) {
				for (let i = 0; i < data.length; i++) {
					data[i].isUnfold = false
					data[i].isChoose = false
					if (data[i].child_list) {
						for (let k = 0; k < data[i].child_list.length; k++) {
							data[i].child_list[k].isUnfold = false
							data[i].child_list[k].isChoose = false
							if (data[i].child_list[k].child_list) {
								for (let f = 0; f < data[i].child_list[k].child_list.length; f++) {
									data[i].child_list[k].child_list[f].isUnfold = false
									data[i].child_list[k].child_list[f].isChoose = false
								}
							}
						}
					}
				}
				data[0].isChoose = true;
				data[0].isUnfold = true
				this.category_id = data[0].category_id;
				this.category_id_1 = data[0].category_id;
        this.category_name = data[0].category_name;
        if(data[0].child_list && data[0].child_list.length>0){
          this.category_id_2 = data[0].child_list[0].category_id
          this.category_3_child_list = data[0].child_list[0].child_list ? data[0].child_list[0].child_list : []
          if(this.category_3_child_list.length){
            let tmp = []
            for (let i = 0; i < parseInt(this.category_3_child_list.length/this.category_3_child_page)+(this.category_3_child_list.length%this.category_3_child_page>0 ? 1 :0); i++) {
              let tmp_two = []
              for (let j = 0; j < this.category_3_child_page; j++) {
                if(i*this.category_3_child_page+j<this.category_3_child_list.length){
                  tmp_two.push(this.category_3_child_list[i*this.category_3_child_page+j])
                }
              }
              tmp.push(tmp_two)
            }
            this.category_3_child_page_count_list = tmp
          }else{
            this.category_3_child_page_count_list = []
          }
        }
				this.category_level = 1;
				return data
			},
			getGoodslists() {
				let url = "/api/goods/goodsList"
				let shopid = uni.getStorageSync('shop_id');
				let data = {
					shop_id: shopid,
					category_level: this.category_level,
					category_id: this.category_id,
					page: this.page,
					page_size: this.page_size,
				};
        this.showLoading = true
				this.$api.sendRequest({
					url: url,
					data: data,
					success: res => {
						let newArr = []
						if (res.code == 0) {
							if (res.data.page_count < this.page) {
								var reqstate = false
							} else {
								var reqstate = true
							}
              res.data.list = res.data.list.map(item=>{
                item.goods_image = this.$util.imageCdnResize(item.goods_image)
                return item;
              })
							newArr = res.data.list
							this.goodsList = this.goodsList.concat(newArr);
							// #ifdef MP-WEIXIN
							// this.$buriedPoint.exposeGoods( newArr , 'sku_id')
							// #endif
							this.page = this.page + 1
							this.flag = reqstate
							if (this.goodsList.length <= 0) {
								this.getgussyoulike();
							}else{
                this.showLoading = false
                uni.hideLoading()
              }
						} else {
              uni.hideLoading()
							this.$util.showToast({
								title: res.message
							});
						}
					},
				});
			},
      scrollTwoCategory(){
        this.scrollTop = this.scrollTop+10
      },
      // 去筛选页面
      toFiltrate(category_name,category_id,category_level){
        this.$util.redirectTo('/otherpages/shop/list/list',{
          category_name,
          category_id,
          category_level,
          site_id: uni.getStorageSync('shop_id')
        })
      },
			// 选择tab
			chooseCategory(level, category_id_1, category_id_2, category_id_3,category_name) {
        this.$buriedPoint.diyReportCategoryRetrievalInteractionEvent({diy_clicked_category_name:category_name})
				uni.showLoading({
				  title:"加载中",
				  mask:true
				});
        this.category_id_1 = category_id_1
        this.category_id_2 = category_id_2
        this.category_id_3 = category_id_3
				let data = this.data.slice();
				for (let i = 0; i < data.length; i++) {
					if (category_id_1 == data[i].category_id) {
						data[i]['isUnfold'] = true; //是否展开
						if (level == 1) {
							data[i]['isChoose'] = true; //是否选中
              this.category_name = data[i].category_name
              this.category_id_2 = data[i].child_list && data[i].child_list.length ? data[i].child_list[0].category_id : null
              this.category_3_child_list = data[i].child_list && data[i].child_list.length && data[i].child_list[0].child_list ? data[i].child_list[0].child_list : []
              if(this.category_3_child_list.length){
                let tmp = []
                for (let i = 0; i < parseInt(this.category_3_child_list.length/this.category_3_child_page)+(this.category_3_child_list.length%this.category_3_child_page>0 ? 1 :0); i++) {
                  let tmp_two = []
                  for (let j = 0; j < this.category_3_child_page; j++) {
                    if(i*this.category_3_child_page+j<this.category_3_child_list.length){
                      tmp_two.push(this.category_3_child_list[i*this.category_3_child_page+j])
                    }
                  }
                  tmp.push(tmp_two)
                }
                this.category_3_child_page_count_list = tmp
              }else{
                this.category_3_child_page_count_list = []
              }
						} else {
							// data[i]['isChoose'] = false;
						}
					} else {
						// data[i]['isUnfold'] = false;
						data[i]['isChoose'] = false;
					}
					if (data[i].child_list && data[i].child_list.length > 0) {
						for (let j = 0; j < data[i].child_list.length; j++) {
							if (category_id_2 == data[i].child_list[j].category_id) {
								data[i].child_list[j]['isUnfold'] = true; //是否展开
								if (level == 2) {
									data[i].child_list[j]['isChoose'] = true; //是否选中
                  this.category_name = data[i].child_list[j].category_name
                  this.category_id_2 = data[i].child_list[j].category_id
                  this.category_3_child_list = data[i].child_list[j].child_list ? data[i].child_list[j].child_list : []
                  if(this.category_3_child_list.length){
                    let tmp = []
                    for (let i = 0; i < parseInt(this.category_3_child_list.length/this.category_3_child_page)+(this.category_3_child_list.length%this.category_3_child_page>0 ? 1 :0); i++) {
                      let tmp_two = []
                      for (let j = 0; j < this.category_3_child_page; j++) {
                        if(i*this.category_3_child_page+j<this.category_3_child_list.length){
                          tmp_two.push(this.category_3_child_list[i*this.category_3_child_page+j])
                        }
                      }
                      tmp.push(tmp_two)
                    }
                    this.category_3_child_page_count_list = tmp
                  }else{
                    this.category_3_child_page_count_list = []
                  }
								} else {
									// data[i].child_list[j]['isChoose'] = false;
								}
							} else {
								// data[i].child_list[j]['isUnfold'] = false; //是否展开
								// data[i].child_list[j]['isChoose'] = false;
							}
							if (data[i].child_list[j].child_list && data[i].child_list[j].child_list.length > 0) {
								for (let k = 0; k < data[i].child_list[j].child_list.length; k++) {
									if (data[i].child_list[j].child_list[k].category_id == category_id_3 && level == 3) {
                    uni.hideLoading()
										data[i].child_list[j].child_list[k]['isChoose'] = true; //是否选中
                    this.toFiltrate(data[i].child_list[j].child_list[k].category_name,category_id_3,level)
                    return
									} else {
										data[i].child_list[j].child_list[k]['isChoose'] = false; //是否选中
									}
								}
							}
						}
					}
				}
				this.data = data.slice();

				let categoryArr = [category_id_1,category_id_2,category_id_3]

				if(this.category_id != categoryArr[level-1]){
					this.againDealWith(true) // 触发埋点
				}

				this.category_level = level
				this.category_id = categoryArr[level-1];
				this.resetList();

			},
			resetList() {
				this.page = 1;
				this.paget = 1;
				this.goodsList = [];
				this.goodsListgess = []
				this.getGoodslists();
			},
			getgussyoulike() {
				let url = "/api/goods/goodsGuess"
				let shopid = uni.getStorageSync('shop_id');
				let data = {
					shop_id: shopid,
					page: this.paget,
					page_size: this.page_sizet
				};
        this.showLoading = true
				this.$api.sendRequest({
					url: url,
					data: data,
					success: res => {
						var newArr = []
						if (res.code == 0) {
							if (res.data.page_count < this.paget) {
								var reqstate = false
							} else {
								var reqstate = true
							}
              res.data.list = res.data.list.map(item=>{
                item.goods_image = this.$util.imageCdnResize(item.goods_image)
                return item;
              })
							newArr = res.data.list
							this.goodsListgess = this.goodsListgess.concat(newArr);
							this.$buriedPoint.exposeGoods( newArr , 'sku_id')
							this.paget = this.paget + 1
							this.flagt = reqstate
              this.showLoading = false
              uni.hideLoading()
						} else {
              this.showLoading = false
              uni.hideLoading()
							this.$util.showToast({
								title: res.message
							});
						}
					},
				});
			},
			bottomOut() {
				if (this.flag || this.flagt) {
					this.getGoodslists();
				}

			},
			bottomOutgess() {
				if (this.flagt) {
					// this.getgussyoulike()
				}

			},
			toDetail(item) {
				if(Object.keys(item.promotion).length) {
					this.$util.redirectTo(`/promotionpages/pintuan/detail/detail`, {
						id: item.promotion.pintuan_goods_id,
						sku_id: item.sku_id,
					});
					return false
				}
				if(item.is_seckill == 1 ){
					this.$util.redirectTo(`/promotionpages/new_seckill/detail/detail?sku_id=${item.sku_id}`)
				}
				if(item.is_seckill == 0 ){
					this.$util.redirectTo(`/pages/goods/detail/detail?sku_id=${item.sku_id}`)
				}
			},
			imageError(data, index) {
        if(data && data[index] instanceof Object && data[index].hasOwnProperty('goods_image')){
          data[index].goods_image = this.$util.getDefaultImage().default_goods_img;
          this.$forceUpdate();
        }
			},
      categoryImageError(data, index) {
        if(data && data[index] instanceof Object && data[index].hasOwnProperty('image')){
          data[index].image = this.$util.getDefaultImage().default_goods_img;
          this.$forceUpdate();
        }
      },
      threeCategoryImageError(data, row, col) {
        if(data && data[row][col] instanceof Object && data[row][col].hasOwnProperty('image')){
          data[row][col].image = this.$util.getDefaultImage().default_goods_img;
          this.$forceUpdate();
        }
      },
      /**
       *分享参数组装
       */
      getSharePageParams() {
        return this.$util.unifySharePageParams('/otherpages/shop/home/<USER>', this.shareTitle || '先迈商城',
            '',{}, this.$util.img(this.shareImg) || this.$util.img('public/static/youpin/home_share.jpg'))
      }
		},
    /**
     * 自定义分享内容
     * @param {Object} res
     */
    onShareAppMessage(res) {
      let { title, link, imageUrl, query } = this.getSharePageParams()
      return this.$buriedPoint.pageShare(link , imageUrl, title);
    },
	};
</script>

<style lang="scss">
	/* #ifdef H5 || APP-PLUS */
	.active /deep/ .newpage1,
	.no /deep/ .newpage1 {
		height: calc(100vh - 92rpx) !important;
		padding-top: 92rpx;
	}

	/* #endif */

	/* #ifdef MP */
	.active /deep/ .newpage1,
	.no /deep/ .newpage1 {
		// height: calc(100vh - 144px) !important;
		height: 100vh !important;
		padding-top: 92rpx;
	}

	/* #endif */

  .top-head-search{
    height: 80rpx;
    line-height: 80rpx;
    .top-head--content--search {
      box-sizing: border-box;
      padding: 0 24rpx;
      width: 460rpx;
      height: 60rpx;
      background: rgba(245,245,245,0.2);
      border-radius: 30px;
      display: -webkit-box;
      display: -webkit-flex;
      display: flex;
      -webkit-box-pack: start;
      -webkit-justify-content: flex-start;
      justify-content: flex-start;
      -webkit-box-align: center;
      -webkit-align-items: center;
      align-items: center;
      color: #fff;

      image {
        width: 32rpx;
        height: 32rpx;
        margin-right: 10rpx;
      }
    }
  }
	.search {
    z-index: 1;
		padding: 16rpx 0;
		position: fixed;
		width: 100%;
    background-color: var(--custom-brand-color);

		.home--content--search {
			box-sizing: border-box;
			padding: 0 24rpx;
			width: 690rpx;
			height: 60rpx;
			border-radius: 15px;
			display: -webkit-box;
			display: -webkit-flex;
			display: flex;
			-webkit-box-pack: start;
			-webkit-justify-content: flex-start;
			justify-content: flex-start;
			-webkit-box-align: center;
			-webkit-align-items: center;
			align-items: center;
			margin: 0 auto;
      color: #fff;
      background: rgba(245, 245, 245, 0.2);

			image {
				width: 32rpx;
				height: 32rpx;
				margin-right: 10rpx;
			}
		}
	}

	/deep/ .fixed {
		position: relative;
		top: 0
	}

	/deep/ .empty {
		margin-top: 0 !important
	}

	.lineation-price {
		text-decoration: line-through;
	}

	.top-head {
		width: 100%;
    position: relative;
    background-color: var(--custom-brand-color);
	}
	.nav-bar-auto {
		height: 80rpx;
		line-height: 80rpx;
		text-align: center;
		box-sizing: border-box;
		padding-right: 30rpx;
    width: 100%;
    position: absolute;
    left: 0;
    bottom: -6rpx;
    z-index: 10;
    display: flex;
    align-items: center;
    padding-left: 30rpx;
	}
	.iconback_light {
		font-size: 45rpx;
		font-weight: bold;
		float: left;
    margin-top: -18rpx;
	}

	.cart-empty {
		position: relative;
		top: 50%;
		padding-top: 54px !important;
		margin: auto;
	}

	.max-font-1 {
		white-space: nowrap;
		overflow: hidden;
		text-overflow: ellipsis;
	}

	.max-font-2 {
		word-break: break-all;
		text-overflow: ellipsis;
		overflow: hidden;
		display: -webkit-box;
		-webkit-line-clamp: 2;
		-webkit-box-orient: vertical;
	}

	.newpage1 {
		width: 100%;
		/* #ifdef H5 || APP-PLUS */
		height: calc(100vh - 112rpx);
		/* #endif */
		/* #ifdef MP-WEIXIN */
		height: calc(100vh - 40rpx);
		/* #endif */
		box-sizing: border-box;
    /* #ifdef H5 */
		padding-top: 94rpx;
    /* #endif */
    position: relative;
	}

	.newpage {
		width: 100%;
		/* #ifdef H5 || APP-PLUS */
		height: calc(100vh - 44px);
		/* #endif */
		/* #ifdef MP-WEIXIN */
		height: 100vh;
		/* #endif */
		box-sizing: border-box;
	}

	.search-box {
		padding-bottom: 30rpx;
	}

	.cate-adv {
		padding-bottom: 30rpx;
	}

	.category-goods {
		width: 100%;
		height: 100%;
		display: flex;
    margin-top: 20rpx;
		.oneScroll {
			width: 188rpx;
      height: calc(100% - 40rpx);
      /* #ifdef MP-WEIXIN */
      height: calc(100% - 290rpx);
      /* #endif */
      padding-bottom: 24rpx;
      box-sizing: border-box;


			.oneScroll-item {
				width: 100%;
				// padding: 10rpx 0;
				box-sizing: border-box;
				font-size: $ns-font-size-base;
				text-align: center;
				// height: 100rpx;
				display: flex;
				justify-content: flex-end;
				align-items: center;
			}

			.oneScroll-item.active {
				background: #ffffff;
				position: relative;
			}

			.oneScroll-item.active:before {
				content: '';
				display: inline-block;
				width: 8rpx;
				height: 68rpx;
				border-radius: 8rpx;
				position: absolute;
				left: 0;
				top: 16rpx;
				border-radius: 6rpx;
			}

		}

		.twoScroll {
			width: calc(100% - 188rpx - 18rpx);
			display: flex;
			flex-direction: column;
			align-items: center;

			.twoScroll_item {
				width: 531rpx;
				height: 180rpx;
				display: flex;
				align-items: center;
				background: #FFFFFF;
				border-radius: 8rpx;
        &:not(:first-child){
          margin-top: 20rpx;
        }

				.item-image {
					width: 180rpx;
					height: 180rpx;
					display: block;
					// margin-left: 20rpx;
          border-radius: 20rpx;
				}

				.item_right {
					width: 312rpx;
					height: 180rpx;
					margin-left: 14rpx;

					.twoScroll_title {
						font-size: 28rpx;
            color: rgba(56, 56, 56, 1);
						display: -webkit-box;
						-webkit-box-orient: vertical;
						-webkit-line-clamp: 2;
						overflow: hidden;
            line-height: 32rpx;
					}
          .twoScroll_keywords{
            line-height: 1;
            margin-bottom: 9rpx;
            &_one{
              font-size: 18rpx;
              color: var(--custom-brand-color);
              height: 32rpx;
              line-height: 32rpx;
              border-radius: 4rpx;
              background: var(--custom-brand-color-10);
              padding: 0 10rpx;
              box-sizing: border-box;
              display: inline-flex;
              align-items: center;
              margin-right: 8rpx;

            }
          }
          .twoScroll_discounts{
            white-space: nowrap;
            line-height: 1;
            &-one{
              display: inline-flex;
              align-items: center;
              margin-right: 10rpx;
              &-name{
                //background: linear-gradient(127.234833981575deg, var(--custom-brand-color) 0%, var(--custom-brand-color-90) 10%, var(--custom-brand-color-70) 37%, var(--custom-brand-color-60) 48%, var(--custom-brand-color-50) 54%, rgba(243, 150, 41, 1) 100%);
                background-color: var(--custom-brand-color);
                font-size: 22rpx;
                height: 32rpx;
                line-height: 32rpx;
                border-top-left-radius: 4rpx;
                border-bottom-left-radius: 4rpx;
                color: white;
                padding: 0 6rpx;
              }
              &-value{
                color: var(--custom-brand-color);
                font-size: 18rpx;
                border-top: 1px solid var(--custom-brand-color);
                border-right: 1px solid var(--custom-brand-color);
                border-bottom: 1px solid var(--custom-brand-color);
                background-color: var(--custom-brand-color-10);
                box-sizing: border-box;
                height: 32rpx;
                line-height: 32rpx;
                padding: 0 6rpx;
                border-top-right-radius: 4rpx;
                border-bottom-right-radius: 4rpx;
              }
            }
            &-words{
              display: inline-flex;
              align-items: center;
              &-one{
                background: var(--custom-brand-color);
                box-sizing: border-box;
                font-size: 18rpx;
                height: 32rpx;
                line-height: 32rpx;
                border-radius: 4rpx;
                color: #fff;
                padding: 0 6rpx;
                display: inline-block;
                margin-right: 10rpx;
              }
            }
          }

					.price_box {
            line-height: 36rpx;
            margin-top: 6rpx;
						.true_prcie {
							font-size: 36rpx;
              line-height: 36rpx;
              color: var(--custom-brand-color);
              font-weight: 700;
              .symbol{
                font-size: 24rpx;
              }
						}

						.market_price {
							font-size: 18rpx;
              color: rgba(166, 166, 166, 1);
							text-decoration: line-through;
							margin-left: 10rpx;
						}
					}
				}
			}

		}
	}



	.isBgccc {
		background: #f1f1f1 !important;
	}

	//是否为限时折扣
	.is_discount {
		display: flex;
		justify-content: center;
		align-items: center;
		margin-right: $ns-margin;
		margin-top: 8rpx;
		padding: 0 8rpx;
		line-height: 30rpx;
		height: 30rpx;
		box-sizing: border-box;
		border: 2rpx solid;
		text-align: center;
		font-size: 20rpx;
		border-radius: 2rpx;
		width: 116rpx;

		text {
			line-height: 1;
		}
	}


	/* 上拉加载区域 */
	.mescroll-upwarp {
		margin-top: 35vh;
	}

	.no-more {
		width: 100%;
		height: 70rpx;
		display: flex;
		justify-content: center;
		align-items: center;
		color: $ns-text-color-gray;
		padding-bottom: 100rpx;
		padding-top: 40rpx;
	}

	.empty-box {
		width: 100%;
		height: 400rpx;
		display: flex;
		align-items: center;
		justify-content: center;
	}

	// sku弹出按钮
	.iconadd1 {
		padding: 6rpx;
		border-radius: 50%;
		color: #fff;
		font-size: $ns-font-size-base;
	}

	.buy-num {
		font-size: 20rpx;
	}

	.twoScroll-item {
		width: 100%;
		// padding: 10rpx 0;
		box-sizing: border-box;
		font-size: $ns-font-size-base;
		text-align: center;
		// height: 100rpx;
		display: flex;
		justify-content: center;
		flex-direction: column;
		align-items: center;

		text {
			line-height: 1.2;
		}
	}

	.threeScroll-item {
		width: 100%;
		// padding: 10rpx 0;
		box-sizing: border-box;
		font-size: $ns-font-size-base;
		text-align: center;
		// height: 100rpx;
		display: flex;
		flex-direction: column;
		justify-content: center;
		align-items: center;

		text {
			line-height: 1.2;
		}
	}

	.one-ji {
		//padding: 30rpx 0;
    height: 88rpx;
    line-height: 88rpx;
    width: 182rpx;
    border-top-left-radius: 100rpx;
    border-bottom-left-radius: 100rpx;
	}

	.two-ji {
		padding: 33rpx 0;
		background: #f7f7f7;
		width: 100%;
	}

	.three-ji {
		padding: 33rpx 0;
		background: #f7f7f7;
		width: 100%;
	}

	.twoScrollview {
		height: 100%;
	}
  .oneUnfold{
    box-sizing: border-box;
    //border-left: 6rpx solid #F2270C;
  }
  .onactive .one-ji{
    background-color: #fff;
    color: var(--custom-brand-color);
  }
	.secondscoll {
		height: calc(100% - 50rpx - 266rpx);
    /* #ifdef MP-WEIXIN */
    height: calc(100% - 50rpx - 266rpx - 248rpx);
    /* #endif */
    background-color: white;
    //border-radius: 30rpx;
    border-radius: 0 0 20rpx 20rpx;
    //margin-top: 20rpx;
    padding: 24rpx 0;
    box-sizing: border-box;
    &-have-empty{
      border-radius: 30rpx;
      height: calc(100% - 40rpx);
      /* #ifdef MP-WEIXIN */
      height: calc(100% - 290rpx);
      /* #endif */
    }
    &-category{
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 0 24rpx;
      box-sizing: border-box;
      &-name{
        font-size: 30rpx;
        color: rgba(56, 56, 56, 1);
        font-weight: bolder;
      }
      &-op{
        font-size: 26rpx;
        color: rgba(166, 166, 166, 1);
        &-icon{
          font-size: 22rpx;
        }
      }
    }
    &-secondscoll{
      width: 100%;
      display: flex;
      justify-content: center;
    }
	}
	.hr-view {
		margin-bottom: 20rpx;
		display: flex;
		justify-content: space-between;
		align-items: center;
		max-width: 100%;
		box-sizing: content-box;
		padding-top: 30rpx;
	}

	.title-img {
		width: 290rpx;
		margin: 0 auto;
		display: block;
	}
  .title-word{
    font-size: 28rpx;
    font-weight: 400;
    padding-left: 24rpx;
    box-sizing: border-box;
  }
	.goods_img {
		position: relative;
		margin-left: 20rpx;
	}
	.goods_img-over {
        width: 100rpx;
        height: 100rpx;
        position: absolute;
        left: 50%;
        top: 50%;
        transform: translate(-50%,-50%);
	}
  .inner-category{
    background-color: white;
    border-radius: 20rpx 20rpx 0 0;
    width: 100%;
    padding: 0 0 0 24rpx;
    box-sizing: border-box;
  }
  .two-level-category{
    display: flex;
    align-items: center;
    &-scroll{
      //width: 460rpx;
      white-space: nowrap;
      padding: 20rpx 0;
      box-sizing: border-box;
      &-item{
        font-size: 26rpx;
        background: rgba(250, 250, 250, 1);
        color: rgba(56, 56, 56, 1);
        font-weight: 400;
        //height: 38rpx;
        display: inline-flex;
        justify-content: center;
        align-items: center;
        width: 148rpx;
        box-sizing: border-box;
        height: 53rpx;
        border-radius: 40rpx;
        margin-right: 10rpx;
        &-action{
          color: var(--custom-brand-color);
          background: var(--custom-brand-color-10);
          border: 2rpx solid var(--custom-brand-color);
        }
      }
    }
    &-more{
      font-size: 24rpx;
      margin-left: 6rpx;
    }
  }
  .three-swiper{
    height: 184rpx;
    white-space: nowrap;
    &-item{
      &-one{
        //width: 25%;
        //padding: 12rpx 0;
        margin-right: 20rpx;
        box-sizing: border-box;
        display: inline-flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        &-image{
          width: 120rpx;
          height: 120rpx;
          border-radius: 20rpx;
        }
        &-name{
          color: rgba(128, 128, 128, 1);
          font-size: 24rpx;
          margin-top: 20rpx;
        }
      }
    }
  }
  /*  #ifdef H5 */
  /deep/.uni-swiper-dot{
    width: 24rpx;
    height: 6rpx;
    border-radius: 2rpx;
  }
  /deep/.uni-swiper-dot-active{
    background: linear-gradient(127.234833981575deg, var(--custom-brand-color) 0%, var(--custom-brand-color-90) 10%, var(--custom-brand-color-70) 37%, var(--custom-brand-color-60) 48%, var(--custom-brand-color-50) 54%, rgba(243, 150, 41, 1) 100%)!important;
  }
  /* #endif */
  /*  #ifdef MP */
  .wx-swiper-dots .wx-swiper-dot{
    width: 24rpx;
    height: 6rpx;
    border-radius: 2rpx;
  }
  .wx-swiper-dot-active{
    background: linear-gradient(127.234833981575deg, var(--custom-brand-color) 0%, var(--custom-brand-color-90) 10%, var(--custom-brand-color-70) 37%, var(--custom-brand-color-60) 48%, var(--custom-brand-color-50) 54%, rgba(243, 150, 41, 1) 100%)!important;
  }
  /* #endif */
</style>
