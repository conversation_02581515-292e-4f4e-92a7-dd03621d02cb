<template>
	<view class="home" :style="[{ background: bgColor, backgroundImage: `url(${bgUrl})`},themeColorVar]">
		<!-- 顶部标题 -->
		<view @touchmove.prevent.stop class='nav bg-white' :style="{ height: (navHeight+statusBarHeight) + 'px',backgroundColor:headerColor }">
			<view class='nav-title' :style="{'top': statusBarHeight+'px', height: navHeight+'px', lineHeight: navHeight+'px'}">
				<!-- <image  :src="$util.img('public/static/youpin/home-logo.png')" mode='aspectFit' class='home-logo'></image> -->
<!--				<view class='home-logo'>-->
<!--					<image :src="$util.img('public/static/youpin/index-logo.png')" mode="widthFix"></image>-->
<!--&lt;!&ndash;					<view>{{shop_name}}</view>&ndash;&gt;-->
<!--				</view>-->
        <view class="nav-title-right">
          <diy-search v-if="searchShow" :searchObj="searchObj" :sbgc="sbgc"></diy-search>
        </view>
			</view>
		</view>

		<!-- 类目 / 搜索框 -->
		<view @touchmove.prevent.stop class="home--content--header" :style="{ top: navHeight+statusBarHeight + 'px',backgroundColor:headerColor}" v-if="openCategoryColumn">


			<!--类目tag -->
			<view class="home--content--tags" :style="{ height: navHeight+'px' }">
				<scroll-view class="home--content--tags--list" :class="{'home--content--tags--list--has': tagsIcon}" :style="{height: navHeight+'px', lineHeight:navHeight+'px' }"
					scroll-x>
					<view v-for="(item,index) in tags" v-bind:key="index" v-bind:class="{'active':index==tagsIndex}"
						@click="changeTag(index)" :style="{color: index==tagsIndex ? ccActionFontColor : ccFontColor}">
						<text>{{item.category_name}}</text>
						<text :style="{borderColor: ccActionFontColor}"></text>
					</view>
				</scroll-view>
				<view class="home--content--tags--more" v-on:click="toCategory" v-if="tagsIcon">
<!--					<image :src="$util.img('public/static/youpin/tag-more.png?v=1')"></image>-->
            <text :style="{backgroundColor: ccFontColor}"></text>
            <text :style="{backgroundColor: ccFontColor}"></text>
            <text :style="{backgroundColor: ccFontColor}"></text>
				</view>
			</view>

		</view>

		<mescroll-uni @getData="goHome" ref="mescroll" :top="isFixedTop ?　'0px' : topHeight+'px' "
			@listenRefresh="listenRefresh" @scroll="scroll">
			<block slot="list">
				<view class="home--fill"></view>
				<view class="home--content">
					<template v-if="tagsIndex==0">
						<view v-for="(item,index) in diyDataList" :key="item.key">

							<!-- 广告 -->
							<template v-if="item.controller == 'ImageAds'">
								<diy-advertising-swiper :ads="ads" :imageError="imageError" :config="item" :top-height="topHeight" :scroll-top="scrollTop" :autoplay="diy_advertising_swiper_autoplay" @headerColorChange="headerColorChange" ref="diyAdvertisingSwiperRef" @clickBanner="clickBanner"></diy-advertising-swiper>
							</template>

							<!-- 频道模块 -->
							<template v-if="item.controller == 'GraphicNav'">
								<diy-channel-area :channels="item.list" :scrollSetting="item.scrollSetting">
								</diy-channel-area>
							</template>
              <!-- 幻灯片组件  -->
              <template v-if="item.controller == 'SlideShow'">
                <diy-slide-show :value="item"></diy-slide-show>
              </template>
							<!-- 活动广告位 -->
							<!-- <template v-if="item.controller == 'ImageAds'">
								<diy-active-advert :activeAds="activeAds" @imageError="imageError"></diy-active-advert>
							</template> -->
							<!-- 活动广告位 -->
							<template v-if="item.controller == 'ActivityAds'">
								<diy-activity-ads :value="item"></diy-activity-ads>
							</template>

							<!--  拼团专区  -->
							<template v-if="item.controller == 'Pintuan'">
								<diy-pintuan :value="{ backgroundColor:'#ffffff',item}" :dataList="pintuanList">
								</diy-pintuan>
							</template>

							<!-- 迈豆专区 -->
							<template v-if="item.controller == 'Maidou'">
								<diy-maidou :value="{item}" :dataList="maidou"
									:site-id="shop_id" @finish="listenRefresh"></diy-maidou>
							</template>

							<!-- 秒杀模块  -->
							<template v-if="item.controller == 'Seckill'">
								<diy-seckill :value="{item}" :dataList="seckill"
									:site-id="shop_id" @finish="listenRefresh"></diy-seckill>
							</template>

              <!-- 直播卡片模块  -->
              <template v-if="item.controller == 'LiveInfo'">
                <diy-live :value="item" :site-id="shop_id" ref="diyLiveRef">
                  <template slot="liveSubscribe">
                    <!-- #ifdef MP-WEIXIN -->
                    <subscribe
                        :width="86"
                        :height="32"
                        :room-id="item.room_id"
                        :font-size="12"
                        :color="'transparent'"
                        :background-color="'transparent'"
                        :custom-params="{}"
                        @subscribe="onSubscribe($event,item)"
                    >
                    </subscribe>
                    <!-- #endif -->
                  </template>
                </diy-live>
              </template>

              <!--  发现好物  -->
              <template v-if="item.controller == 'Seeding'">
                <diy-seeding :value="{ backgroundColor:'#ffffff',item}">
                </diy-seeding>
              </template>

              <!--      商品专题模块        -->
              <template v-if="item.controller == 'ProductTopic'">
                <diy-product-topic :config="item" :top-height="topHeight" :show-top="isFixedTop ? topHeight : 0" :scroll-top="scrollTop" ref="diyProductTopicRef" @scrollToPoint="scrollToY"></diy-product-topic>
              </template>

              <!--      新品专区模块        -->
              <template v-if="item.controller == 'NewProductArea'">
                <diy-new-product-area :config="item"></diy-new-product-area>
              </template>

							<!-- 店主推荐  -->
							<template v-if="item.controller == 'ShoperRecommand'">
								<diy-recommend-product :recommendGoods="recommend_goods" :imageError="imageError">
								</diy-recommend-product>
							</template>

							<!-- 推荐商品 -->
							<template v-if="item.controller == 'GoodsList'">
								<diy-goods-list :products="products"></diy-goods-list>
							</template>

              <template v-if="item.controller == 'Notice'">
                <!-- 公告 -->
                <diy-notice :value="item" :site-id="shop_id"></diy-notice>
              </template>

              <template v-if="item.controller == 'Video'">
                <!--  视频组件  -->
                <!-- #ifdef MP-WEIXIN -->
                <diy-video :value="item">
                  <template v-slot:tx_video="{vid,height,borderRadius,isMuted}">
                    <player-component :vid="vid" :height="height" :autoplay="true" :usePoster="true" :loop="true" :muted="isMuted" :border-radius="borderRadius"></player-component>
                  </template>
                </diy-video>
                <!-- #endif -->
              </template>

						</view>

					</template>
					<template v-else>
						<!-- 类目商品列表 -->
						<diy-tag-product ref="getTabProductRef"></diy-tag-product>
					</template>

				</view>
			</block>
		</mescroll-uni>
		<!-- 底部tabBar -->

		<!-- 弹出广告 -->
		<view @touchmove.prevent.stop class="PopWindow">
			<uni-popup ref="homePop" type="center" class="pop-ad" :maskClick="false">
				<view class="pop-ad-info">
					<image :src="$util.img(popData.image_url)" class="pop-ad-info-img" v-if="fullScreenMode"
						v-on:click="toPop(popData.banner_url)" :mode="fullScreenMode"></image>
<!--					<image :src="$util.img('public/static/youpin/icon-close-overlay.png')" class="pop-ad-info-close"-->
<!--						@click="$refs.homePop.close()"></image>-->
          <view class="pop-ad-time" @click="homePopClose" :style="'bottom: '+(navHeight+80)+'rpx;'">
            <text class="pop-ad-time-one">{{timing/1000}}s</text><text class="pop-ad-time-op">跳过</text>
          </view>
				</view>
			</uni-popup>
		</view>
    <diy-floating-rolling-order :top="$util.getPlatform()=='h5' ? '190rpx' : '280rpx'"></diy-floating-rolling-order>
		<diy-bottom-nav type="shop" :site-id="shop_id" v-if="openBottomNav"></diy-bottom-nav>
		<loading-cover ref="loadingCover"></loading-cover>
		<yd-auth-popup ref="ydauth"></yd-auth-popup>
		<ns-login ref="login"></ns-login>
    <uni-coupon-pop ref="couponPop"></uni-coupon-pop>
	</view>

</template>

<script>
	import apiurls from "../../../common/js/apiurls";
	import system from "@/common/js/system.js";
	import diySearch from "@/otherpages/components/diy-search/diy-search";
	import diyAdvertisingSwiper from "@/otherpages/components/diy-advertising-swiper/diy-advertising-swiper";
	import diyActivityAds from "@/otherpages/components/diy-activity-ads/diy-activity-ads";
	import diyChannelArea from "@/otherpages/components/diy-channel-area/diy-channel-area";
	import diyPintuan from "@/otherpages/components/diy-pintuan/diy-pintuan";
	import diyMaidou from "@/otherpages/components/diy-maidou/diy-maidou";
	import diySeckill from "@/otherpages/components/diy-seckill/diy-seckill";
	import diyRecommendProduct from "@/otherpages/components/diy-recommend-product/diy-recommend-product";
	import diyGoodsList from "@/otherpages/components/diy-goods-list/diy-goods-list";
	import diyTagProduct from "@/otherpages/components/diy-tag-product/diy-tag-product";
  import diyNotice from '@/otherpages/components/diy-notice/diy-notice.vue';
	import uniPopup from '@/components/uni-popup/uni-popup.vue';
	import diyFloatingRollingOrder from '@/components/diy-floating-rolling-order/diy-floating-rolling-order.vue';
	import diyProductTopic from '@/otherpages/components/diy-product-topic/diy-product-topic.vue';
  import diyNewProductArea from '@/otherpages/components/diy-new-product-area/diy-new-product-area.vue';
	import diyLive from '@/otherpages/components/diy-live/diy-live.vue';
  import diySeeding from "@/otherpages/components/diy-seeding/diy-seeding.vue";
  import diyVideo from "@/otherpages/components/diy-video/diy-video.vue";
  import diySlideShow from "@/otherpages/components/diy-slide-show/diy-slide-show.vue";
  import golbalConfig from "../../../common/mixins/golbalConfig";

	export default {
		components: {
			diySearch,
			diyAdvertisingSwiper,
			diyActivityAds,
			diyChannelArea,
			diyPintuan,
			diyMaidou,
			diySeckill,
			diyRecommendProduct,
			diyGoodsList,
			diyTagProduct,
      diyNotice,
			uniPopup,
      diyFloatingRollingOrder,
      diyProductTopic,
      diyNewProductArea,
      diyLive,
      diySeeding,
      diyVideo,
      diySlideShow
		},
    mixins:[golbalConfig],
		data() {
			return {
				searchObj: null, // 搜索框
				searchShow: false,
				shop_id: null,
				openBottomNav: true,
				isToTop: false,
				tags: [],
				tagsIndex: 0, // 类目tag索引
				tagsIcon: false,
				ads: [],
				channels: [],
				recommend_goods: [],
				pintuanList: [],
				seckill: {},
				maidou: {},
        statusBarHeight:0,
				navHeight: 40,
				shop_name: '', //店铺名称
				activeAds: { // 活动广告位
					top1: null,
					top2: null,
					bottom1: null,
					bottom2: null,
					bottom3: null
				},
				exposeStatus: true,
				exposeProducts: false,
				popData: {}, //弹窗数据
				diyData: {},
				diyDataList: [],
				bgColor: '',
        bgUrl: '',
        tagsBgColor:'',
        ccFontColor: '',
        ccActionFontColor: '',
        openCategoryColumn: false, // 是否显示分类栏
        shareTitle: '',
        shareImg: '',
				products: [], // 推荐商品列表
				sbgc:'',
        timing: 0,
        timingObj:null,
        fullScreenMode:"",
        topHeight:0,
        scrollTop: 0,
        isFixedTop: false,
        headerColor: '',
        diy_advertising_swiper_autoplay: false
      };
		},
		methods: {
      // 监听订阅事件用于获取订阅状态
      onSubscribe(e,item) {
        // console.log("房间号：", e.detail.room_id);
        // console.log("订阅用户openid", e.detail.openid);
        // console.log("是否订阅", e.detail.is_subscribe);
        this.$refs.diyLiveRef[0].changeSubscribe(e.detail.room_id)

      },
			async getDiyInfo() {
				let res = await this.$api.sendRequest({
					url: '/api/diyview/info',
					async: false,
					data: {
						name: 'DIYVIEW_SHOP_INDEX',
						site_id: this.shop_id || 0
					}
				});
				if (res.data) {

					let data = JSON.parse(res.data.value)
					this.diyData = data

					this.bgColor = data.global.bgColor || 'linear-gradient(0deg, rgba(240,240,240,1) 0%, rgba(255,255,255,1) 100%)';
					this.bgUrl = data.global.bgUrl;
          this.tagsBgColor=data.global.bgColor;
          this.openCategoryColumn = data.global.openCategoryColumn
          this.ccFontColor = data.global.ccFontColor
          this.ccActionFontColor = data.global.ccActionFontColor
          this.shareTitle = data.global.shareTitle
          this.shareImg = data.global.shareImg
					this.openBottomNav = data.global.openBottomNav
          this.topHeight = this.openCategoryColumn ? this.statusBarHeight+this.navHeight*2 :this.statusBarHeight+this.navHeight;
          data.value.length > 0 && data.value.forEach((it,index) => {
						if (it.type == 'SEARCH') {
							this.searchShow = true
							this.sbgc = it.imageUrl || it.backgroundColor
							this.searchObj = it
						}
						it.key = Math.floor(Math.random()*10) + String(index)

					})

					this.diyDataList = data.value
				}
			},
			// 监听滚动触发埋点
			scroll(e) {
        this.scrollTop = e.scrollTop;
				if (e.scrollTop >= 200 && this.exposeStatus) {
					this.exposeStatus = false
					// 拼团
					this.$buriedPoint.exposeGoods(this.pintuanList ?? [], 'goods_id')
					// 迈豆
					this.$buriedPoint.exposeGoods(this.maidou.list ?? [], 'sku_id')
					// 秒杀
					this.$buriedPoint.exposeGoods(this.seckill.list ?? [], 'sku_id')
          // 专题任务
          if(this.$refs.diyProductTopicRef){
            this.$buriedPoint.exposeGoods(this.$refs.diyProductTopicRef[0].dataList ?? [], 'sku_id')
          }
				}

				if (e.scrollTop >= 1000 && !this.exposeProducts) {
					this.$buriedPoint.exposeGoods(this.products, 'sku_id')
					this.exposeProducts = true
				}
			},

			async changeTag(index) {
				if (this.$refs.loadingCover) this.$refs.loadingCover.show();
				this.tagsIndex = index;
				this.$refs.mescroll.refresh();
				if (this.$refs.loadingCover) this.$refs.loadingCover.hide();
        this.$refs.mescroll.toTopClick();
			},

			/*
			 * 根据广告位获取广告图片
			 * @param sign 广告位置
			 * */
			async getAdInfo(sign) {
				let data = [];
				let res = await this.$api.sendRequest({
					url: apiurls.specialBannerUrl,
					async: false,
					data: {
						sign
					}
				})
				if (res.code != 0) {
					return data;
				}
				data = res.data.list;
				return data;
			},
			async getData() {
				let banners = await this.getAdInfo('index-1');
				this.ads = banners;
        if(banners && banners.length > 0 && this.diyDataList.filter(item=>item.controller=="ImageAds" && item.selectedTemplate=="carousel-posters-2").length>0){
          this.isFixedTop = true
        }
				let activeAds = await this.getAdInfo('index-2');
				for (let i = 0; i < activeAds.length; i++) {
					switch (activeAds[i].sign) {
						case 'index-2-1':
							this.activeAds.top1 = activeAds[i];
							break;
						case 'index-2-2':
							this.activeAds.top2 = activeAds[i];
							break;
						case 'index-2-3':
							this.activeAds.bottom1 = activeAds[i];
							break;
						case 'index-2-4':
							this.activeAds.bottom2 = activeAds[i];
							break;
						case 'index-2-5':
							this.activeAds.bottom3 = activeAds[i];
							break;
					}
				}
        if(this.diyDataList.filter(item=>item.controller=='Pintuan').length>0){
          await this.getPintuanGoodsList();
        }
				let res = await this.$api.sendRequest({
					url: apiurls.homeUrl,
					async: false,
					data: {},
				});
				if (res.code != 0) {
					uni.showToast({
						title: res.message,
						mask: true,
						icon: "none",
						duration: 3000
					});
					return
				}
				this.tags = res.data.cates;
				this.tags.unshift({
					category_name: "首页",
					category_id: null
				})

				this.tagsIcon = res.data.show_cate_icon ? true : false;
				let channels = res.data.channels;
				let channels_count = 10; //每页的数量
				let channels_page = 0;
				let channels_list = [];
				if (channels.length > 0) {
					channels_page = parseInt(channels.length / channels_count) + 1;
					for (let i = 0; i < channels_page; i++) {
						if ((i + 1) == channels_page) {
							let tmp = channels.slice(i * channels_count);
							if (tmp.length > 0) {
								channels_list.push(channels.slice(i * channels_count));
							}
						} else {
							channels_list.push(channels.slice(i * channels_count, (i + 1) * channels_count));
						}
					}
				}
				res.data.seckill.action_time = res.data.seckill.action_time ? Math.abs(res.data.seckill.action_time) *
					1000 : 0
				this.channels = channels_list;
				this.recommend_goods = res.data.recommend_goods;
				this.seckill = Object.prototype.toString.call(res.data.seckill).slice(8, -1) == 'Object' ? res.data
					.seckill : {};
				this.maidou = res.data.maidou;
				if (this.$refs.loadingCover) this.$refs.loadingCover.hide();
			},

			async getPintuanGoodsList() {
				let res = await this.$api.sendRequest({
					url: apiurls.pintuanGoodsList,
					async: false,
					data: {
						page_size: 5
					}
				})
				if (res.code != 0) {
					uni.showToast({
						title: res.message,
						mask: true,
						icon: "none",
						duration: 3000
					});
					return
				}
				let pintuanList = res.data.list;
				this.pintuanList = pintuanList;
			},

			toCategory() {
				this.$util.redirectTo(`/otherpages/shop/category/category?shop_id=${this.shop_id}&showNavBar=true`)
			},
			toPop(url) {
        this.homePopClose()
        this.$buriedPoint.diyReportAdEvent(
            {diy_ad_location_type:'home',diy_material_path:this.popData.image_url,diy_ad_type:'image',diy_target_page:url,diy_ad_id:this.popData.id,diy_action_type:'click'})
				this.$util.specialBannerReportByClick(this.popData.id)
        if (url) {
					this.$util.diyRedirectTo({
						wap_url: url
					})
				}
			},
      homePopClose(){
        if (this.$refs.homePop) this.$refs.homePop.close();
        this.diy_advertising_swiper_autoplay = true
      },
			async goHome(mescroll) {
        await system.wait_staticLogin_success()
				mescroll.optUp.toTop.src = this.$util.img('public/static/youpin/to-top.png');
				mescroll.optUp.toTop.width = "144rpx";

				this.$nextTick(() => {
					if (this.tagsIndex == 0) {
						this.getGoodsList(mescroll)
					} else {
						this.$refs.getTabProductRef.getCategoryGoodsList(mescroll, this.tags[this.tagsIndex])
					}
				})
			},
			// 推荐商品
			async getGoodsList(mescroll) {

				let res = await this.$api.sendRequest({
					url: apiurls.goodsListUrl,
					async: false,
					data: {
						shop_id: this.shop_id,
						page_size: mescroll.size,
						page: mescroll.num,
					},
				});
				if (res.code != 0) {
					uni.showToast({
						title: res.message,
						mask: true,
						icon: "none",
						duration: 3000
					});
					return
				}

				let newArr = res.data.list;
				mescroll.endSuccess(newArr.length);
				//设置列表数据
				if (mescroll.num == 1) this.products = []; //如果是第一页需手动制空列表
				this.products = this.products.concat(newArr); //追加新数据
				this.exposeProducts && this.$buriedPoint.exposeGoods(newArr, 'sku_id')

			},
			async listenRefresh() {
				if (this.tagsIndex == 0 ) {
          await system.wait_staticLogin_success()
          await this.getDiyInfo()
					await this.getData()
          // #ifdef H5
          let share_data = this.$util.deepClone(this.getSharePageParams());
          let link = window.location.origin + this.$router.options.base + share_data.link.slice(1);
          share_data.link = link;
          await this.$util.publicShare(share_data);
          // #endif
				}
			},
			imageError(data, index) {
				if (data instanceof Object && data[index] && data[index].goods_image) {
					data[index].goods_image = this.$util.getDefaultImage().default_goods_img;
				}
				if (data instanceof Object && data[index] && data[index].image_url) {
					data[index].image_url = this.$util.getDefaultImage().default_goods_img;
				}
				this.$forceUpdate();
			},
      // 广告关闭倒计时
      adCountDown(){
        if(this.timing<0){
          this.homePopClose()
          return;
        }
        this.timingObj = setTimeout(()=>{
          this.timing = this.timing - 1000;
          this.adCountDown();
        },1000)
      },
			/*
			 * 获取弹窗数据
			 * */
			async getPopData() {
				let res = await this.getAdInfo('index-3');
				let remind = await this.$api.sendRequest({
					url: this.$apiUrl.use_remind,
					async: false,
				});
				if(remind.data.code ==0 && remind.data.length) {
					this.$util.toShowCouponPopup(this)
          this.diy_advertising_swiper_autoplay = true
				}else{
					if (res.length > 0) {
						this.popData = res[0];
            try{
              let image_info = await this.$util.getImageInfo(this.popData.image_url)
              let window_info = await uni.getWindowInfo()
              if(parseFloat((window_info.windowHeight/window_info.windowWidth).toFixed(2))>parseFloat((image_info.height/image_info.width).toFixed(2))){
                this.fullScreenMode = 'heightFix'
              }else{
                this.fullScreenMode = 'widthFix'
              }
            }catch (e) {

            }
						if (this.$refs.homePop) this.$refs.homePop.open();
            this.$buriedPoint.diyReportAdEvent(
                {diy_ad_location_type:'home',diy_material_path:this.popData.image_url,diy_ad_type:'image',diy_ad_id:this.popData.id,diy_action_type:'display'})
            this.timing = 5000;
            this.adCountDown()
					}else{
            this.diy_advertising_swiper_autoplay = true
          }
				}
			},
			/**
			 *分享参数组装
			 */
			getSharePageParams() {
        return this.$util.unifySharePageParams('/otherpages/shop/home/<USER>', this.shareTitle || '先迈商城',
            '',{}, this.$util.img(this.shareImg) || this.$util.img('public/static/youpin/home_share.jpg'))
			},
      headerColorChange(color){
        this.headerColor = color;
      },
      scrollToY(y){
        this.$refs.mescroll.toScrollPointY(y)
      },
      clickBanner(id){
        this.$util.specialBannerReportByClick(id)
      }
		},
		onLoad(data) {
			// #ifdef H5
			this.startData = data;
			// #endif
			// #ifdef MP-WEIXIN
			uni.hideHomeButton()
      // 参考文档  https://juejin.cn/post/7076705501764911118
      // 获取微信胶囊的位置信息 width,height,top,right,left,bottom
      const custom = uni.getMenuButtonBoundingClientRect()
      let res = uni.getSystemInfoSync()
      this.statusBarHeight = res.statusBarHeight
      // 导航栏高度(标题栏高度) = 胶囊高度 + (顶部距离 - 状态栏高度) * 2
      this.navHeight = custom.height + (custom.top - this.statusBarHeight) * 2
      // #endif

		},
		async onShow(data) {
			// 刷新多语言
			this.$langConfig.refresh();
			await system.wait_staticLogin_success();
			let shop_id = uni.getStorageSync('shop_id');
			this.shop_id = shop_id;
			if (!this.shop_id) {
				this.$util.redirectTo('/otherpages/store/store_empty/store_empty', {}, 'redirectTo');
			}
      // #ifdef H5
			// this.$util.toShowLoginPopup(this, null, '/otherpages/shop/home/<USER>');
      // #endif

			setTimeout(() => {
				let shop_name = uni.getStorageSync('shop_name');
				this.shop_name = shop_name;
			}, 500)
		},
		async onReady() {
      await system.wait_staticLogin_success();
      if (uni.getStorageSync('is_register')) {
        await this.$store.dispatch('writeIsPopHomeAd', false);
        uni.removeStorageSync('is_register');
      }
			if (!this.$store.state.isPopHomeAd) {
				// #ifdef MP-WEIXIN
				await this.getPopData();
				await this.$store.dispatch('writeIsPopHomeAd', true);
				// #endif
				// #ifdef H5
				if (this.startData && Object.keys(this.startData).length > 0) {
					if (uni.getStorageSync('token')) {
						if (this.startData.hasOwnProperty('shop_id') && this.startData.hasOwnProperty(
								'recommend_member_id')) {
							await this.getPopData();
							await this.$store.dispatch('writeIsPopHomeAd', true);
						}
					} else {
						await this.getPopData();
						await this.$store.dispatch('writeIsPopHomeAd', true);
					}

				}else{
          this.diy_advertising_swiper_autoplay = true
        }
				// #endif
			}else{
        this.diy_advertising_swiper_autoplay = true
      }
		},
		/**
		 * 自定义分享内容
		 * @param {Object} res
		 */
		onShareAppMessage(res) {
      let shareType = res.target && res.target.dataset && res.target.dataset.shareType
      if(shareType && shareType=='liveRoom'){
        let { title, link, imageUrl, query } = this.$refs.diyLiveRef[0].toShareAppMessage(res)
        return this.$buriedPoint.pageShare(link , imageUrl, title);
      }else{
        let { title, link, imageUrl, query } = this.getSharePageParams()
        return this.$buriedPoint.pageShare(link , imageUrl, title);
      }
		},
		/**
		 * 自定义分享朋友圈-暂时只支持安卓
		 * @param {Object} res
		 */
		onShareTimeline(res) {
      let { title, imageUrl, query } = this.getSharePageParams()
      return {
        title,
        imageUrl,
        query,
        success: res => {},
        fail: res => {}
      };
		}
	}
</script>
<style lang="scss">
	/* #ifdef MP-WEIXIN */
	/*diy-seckill组件中计数器样式， 微信小程序这样写是因在自定义组件中做/deep/样式穿透是不生效，需要在页面上做样式样式穿透*/
	.last-time {
	  padding: 4rpx 0;
	  font-size: 20rpx !important;

	  .clockrun {
	    /deep/ .custom {
	      display: flex;
	    }

	    /deep/ .custom :nth-child(odd) {
	      background-color: var(--custom-brand-color);
	      width: 40rpx;
	      height: 40rpx;
	      line-height: 40rpx;
	      color: white;
	      border-radius: 6upx;
	      font-size: 22rpx;
	      text-align: center;
	      overflow: hidden;
	    }

	    /deep/ .custom :nth-child(even) {
	      padding: 0 6upx;
	      color: var(--custom-brand-color);
	    }
	  }


	}
  .seckill-two-item-image-area-clockrun {
    /deep/ .custom {
      display: flex;
    }
    /deep/.day {
      font-size: 26rpx;
      font-weight: 400;
      color: #fff;
    }

    /deep/.day-symbol {
      font-size: 26rpx;
      font-weight: 400;
      color: #fff;
      margin: 0 6rpx;
    }

    /deep/.hour, /deep/.minute, /deep/.second {
      font-size: 26rpx;
      font-weight: 400;
      color: #fff;
      display: flex;
      justify-content: center;
      align-items: center;
    }

    /deep/.hour-symbol, /deep/.minute-symbol, /deep/.second-symbol {
      font-size: 26rpx;
      font-weight: 400;
      color: #fff;
      margin: 0 6rpx;
    }
  }
	/* #endif */
</style>
<style lang="scss">


	.home {
		width: 100%;
		height: 100vh;
		box-sizing: border-box;
		position: relative;
    background-repeat: no-repeat !important;
    background-size: 100% auto !important;
    overflow-y: hidden;


		//&--fill {
		//	width: 750rpx;
		//	height: 320rpx;
		//	// background: linear-gradient(0deg, #F5F5F5 2%, #FFFFFF 100%);
		//	position: absolute;
		//	left: 0;
		//	top: 0;
		//	z-index: -1;
		//}

		&--content {
			box-sizing: border-box;
			padding: 0 20rpx;
			//padding-top: 10rpx;
			padding-bottom: calc(300rpx + constant(safe-area-inset-bottom));
			padding-bottom: calc(300rpx + env(safe-area-inset-bottom));
			&--header {
				position: fixed;
				top: 0rpx;
				left: 0;
				width: 100%;
				// padding: 0 25rpx;
				box-sizing: border-box;
				z-index: 5;
        transition: all 0.5s;
			}
			&--tags {
				padding: 0 25rpx;
				$tagsMoreWith: 63rpx;
				display: flex;
				justify-content: flex-start;
				align-items: center;
        background-color: transparent;

				&--list {
					white-space: nowrap;

					&--has {
						width: calc(100% - #{$tagsMoreWith});
					}

					view {
						display: inline-block;
						font-size: 26rpx;
						font-weight: 400;
						//color: #fff;
						position: relative;
						margin-right: 52rpx;

						text {
							white-space: nowrap;
						}

						&.active {

							text:last-child {
                width: 28rpx;
                height: 28rpx;
                overflow: hidden;
								background: transparent;
                border-bottom-left-radius: 32rpx;
                border-bottom-right-radius: 0rpx;
                border-left: 6rpx solid;
                border-bottom: 6rpx solid;
								position: absolute;
								left: 50%;
								bottom: -16rpx;
								transform: translateX(-50%) rotate(-45deg);
							}
						}
					}
				}

				&--more {
					width: $tagsMoreWith;
					height: 100%;
					display: flex;
					flex-direction: column;
          justify-content: center;
          align-items: flex-end;


					image {
						width: 27rpx;
						height: 27rpx;
					}
          text:first-child{
            width: 28rpx;
            height: 4rpx;
            margin-bottom: 6rpx;
          }
          text:nth-child(2){
            width: 14rpx;
            height: 4rpx;
            margin-bottom: 6rpx;
          }
          text:nth-child(3){
            width: 24rpx;
            height: 4rpx;
          }
				}
			}
		}
	}

	.channel {
		margin-top: 35rpx;

		&--list {
			height: 350rpx;

			&--item {
				display: flex;
				flex-wrap: wrap;
				justify-content: flex-start;
				align-items: flex-start;

				&--channel {
					width: 138rpx;
					display: flex;
					flex-direction: column;
					align-items: center;

					image {
						width: 96rpx;
						height: 96rpx;
						border-radius: 50%;
					}

					view {
						font-size: 24rpx;
						font-weight: 500;
						color: #9A9A9A;
						margin-top: 20rpx;
					}

					;
				}
			}
		}

		&--dot {
			display: flex;
			justify-content: center;
			align-items: center;
			margin-top: 15rpx;

			&--index {
				width: 43rpx;
				height: 6rpx;
				background: #D4D8DE;
				border-radius: 3rpx;
			}

			&--active {
				background: #F2280C;
			}
		}
	}

	.to-top {
		width: 144rpx;
		height: 152rpx;
		position: fixed;
		right: 0;
		bottom: 200rpx;
	}


	.nav {
		width: 100%;
		overflow: hidden;
		position: fixed;
		top: 0;
		left: 0;
		z-index: 10;
		// background-color: #ffffff;
    transition: all 0.5s;
	}

	.nav-title {
		width: 100%;
		text-align: center;
		position: absolute;
		//bottom: 4rpx;
		left: 0;
		z-index: 10;
    display: flex;
    align-items: center;
    padding-left: 20rpx;
    box-sizing: border-box;
    &-right{
      //margin-left: 10rpx;
      width: 468rpx;
    }
	}

	.nav .home-logo {
		//width: 60%;
		height: 88rpx;
		//position: absolute;
		//top: 50%;
		//transform: translateY(-50%);
		//left: 0rpx;
		//padding: 0 24rpx;
		display: flex;
		align-items: center;
		justify-content: flex-start;

		image {
			width: 190rpx;
      height: auto;
		}

		view {
			display: block;
			width: calc(100% - 120rpx);
			overflow: hidden;
			text-overflow: ellipsis;
			white-space: nowrap;
			margin-left: 10rpx;
			text-align: left;
			font-size: 30rpx;
			color: #666;
			font-weight: bold;
		}
	}

	/deep/ image.mescroll-totop {
		right: 0 !important;
	}

	/deep/view.empty {
		top: 40vh;
	}

	.pop-ad {
		/deep/.uni-popup__wrapper-box {
			background: transparent !important;
      max-width: 100%!important;
      max-height: 100%!important;
      border-radius: 0!important;
		}
    /deep/ .uni-popup__wrapper.center{
      justify-content: flex-start;
      align-items: flex-start;
    }

		&-info {
			background: transparent;
      width: 100vw;
      height: 100vh;

			&-img {
        width: 100vw;
        height: 100vh;
			}

			&-close {
				width: 88rpx;
				height: 88rpx;
				//margin-top: 40rpx;
				display: block;
			}
		}
    &-time{
      position: absolute;
      right: 60rpx;
      bottom: 20rpx;
      width: 160rpx;
      line-height: 160rpx;
      height: 48rpx;
      background-color: rgba(0,0,0,0.5);
      border: 1px solid rgba(255, 255, 255, 0.2);
      font-size: 24rpx;
      border-radius: 98rpx;
      color: #fff;
      display: flex;
      align-items: center;
      justify-content: center;
      &-one{
        width: 45%;
        text-align: center;
        height: 48rpx;
        line-height: 48rpx;
      }
      &-op{
        width: 45%;
        text-align: center;
        height: 48rpx;
        line-height: 48rpx;
      }
    }
	}

	/deep/.uni-scroll-view ::-webkit-scrollbar {
		/* 隐藏滚动条，但依旧具备可以滚动的功能 */
		display: none;
		width: 0;
		height: 0;
		color: transparent;
		background: transparent;
	}

	/deep/::-webkit-scrollbar {
		display: none;
		width: 0;
		height: 0;
		color: transparent;
		background: transparent;
	}
</style>
