.head-wrap {
	background: #fff;
	position: fixed;
	width: calc(100%);
	//left: 24rpx;
	//top: 24rpx;
	z-index: 10;
	//border-top-left-radius: 30rpx;
	//border-top-right-radius: 30rpx;

	.search-header{
		width: 100%;
		overflow: hidden;
		z-index: 10;
		background-color: white;
		&-inner{
			position: absolute;
			left: 20rpx;
			display: flex;
			align-items: center;
			.iconfont{
				font-size: 32rpx;
				color: rgba(56, 56, 56, 1);
				margin-right: 20rpx;
				font-weight: bold;
			}
		}
	}

	.sort-wrap {
		display: flex;
		//border-bottom: 1px solid $ns-border-color-gray;
		padding: 10rpx 0;
		border-bottom: 2rpx solid #f8f8f8;
		box-sizing: border-box;
		> view {
			flex: 1;
			text-align: center;
			font-size: $ns-font-size-base;
			height: 60rpx;
			line-height: 60rpx;
		}
		.comprehensive-wrap {
			display: flex;
			justify-content: center;
			align-items: center;
			.iconfont-wrap {
				display: inline-block;
				margin-left: 10rpx;
				width: 40rpx;
				.iconfont {
					font-size: 32rpx;
					line-height: 1;
					margin-bottom: 5rpx;
				}
			}
		}
		.price-wrap {
			display: flex;
			justify-content: center;
			align-items: center;
			.iconfont-wrap {
				display: flex;
				justify-content: center;
				align-items: center;
				flex-direction: column;
				margin-left: 10rpx;
				width: 40rpx;
				margin-bottom: 12rpx;
				.iconfont {
					float: left;
					font-size: 32rpx;
					line-height: 1;
					height: 20rpx;
				}
			}
		}
		.screen-wrap {
			display: flex;
			justify-content: center;
			align-items: center;
			.iconfont-wrap {
				display: inline-block;
				margin-left: 10rpx;
				width: 40rpx;
				.iconfont {
					font-size: 36rpx;
					line-height: 1.5;
				}
			}
		}
	}
}

.goods-list {
	width: calc(100%);
	margin: 0 auto;
	 background: #fff;
	padding-bottom: 120rpx;
	box-sizing: border-box;
	border-bottom-left-radius: 30rpx;
	border-bottom-right-radius: 30rpx;
	padding-top: 24rpx;
	.goods-item{
		width: 100%;
		background: #fff;
		overflow: hidden;
		margin-bottom:24rpx;
		display: flex;
		padding: 0 24rpx;
		box-sizing: border-box;
		.image-wrap{
			width:256rpx;
			height: 256rpx;
			position: relative;
			border-radius: 20rpx;
			image{
				width: 100%;
				height: 100%;
				border-radius: 15rpx;
			}
			.over{
				width: 100rpx;
				height: 100rpx;
				position: absolute;
				left: 50%;
				top: 50%;
				transform: translate(-50%,-50%);
			}
		}
		.goods-info{
			//padding: 20rpx 16rpx;
			margin-left: 28rpx;
			max-width: 395rpx;
			margin-top: 8rpx;
			.goods-name{
				overflow: hidden;
				display: -webkit-box;
				-webkit-line-clamp: 2;
				-webkit-box-orient: vertical;
				word-break: break-all;
				margin-bottom: 20rpx;
				color: #333;
				font-size: 32rpx;
				line-height: 37rpx;
				height: 72rpx;
			}
			.goods-count{
				font-size: 24rpx;
				font-weight: 500;
				color: #BCC1C4;
			}
			.recommend-icon{
				padding: 5rpx 5rpx;
				font-size: 20rpx;
				color:#fff;
				background: linear-gradient(55deg, var(--custom-brand-color-80), var(--custom-brand-color));
				border-radius: 4rpx;
				margin-right: 10rpx;
			}
			.goods-tag{
				min-height: 28rpx;
				text{
					font-size: 18rpx;
					color: var(--custom-brand-color);
					display: inline-flex;
					justify-content: center;
					align-items: center;
					height: 32rpx;
					line-height: 32rpx;
					box-sizing: border-box;
					padding: 0 10rpx;
					background: rgba(255, 255, 255, 1);
					border: 1px solid var(--custom-brand-color);
					border-radius: 4rpx;
					margin-right: 10rpx;
				}
			}

			.price-wrap{
				display: flex;
				align-items: baseline;
				.price{
					font-size: 36rpx;
					font-weight: bold;
					color: var(--custom-brand-color);
					text{
						font-size:26rpx;
						font-weight: normal;
					}
				}
				.market-price{
					text-decoration: line-through;
					font-size:$ns-font-size-sm;
					color: #999;
					margin-left: 8rpx;
				}
			}
			.twoScroll_discounts{
				white-space: nowrap;
				line-height: 1;
				&-one{
					display: inline-flex;
					align-items: center;
					margin-right: 10rpx;
					&-name{
						//background: linear-gradient(127.234833981575deg, rgba(252, 47, 186, 1) 0%, rgba(251, 52, 171, 1) 10%, rgba(249, 66, 131, 1) 37%, rgba(248, 72, 114, 1) 48%, rgba(247, 75, 105, 1) 54%, rgba(243, 150, 41, 1) 100%);
						background-color: var(--custom-brand-color);
						font-size: 18rpx;
						height: 32rpx;
						line-height: 32rpx;
						border-top-left-radius: 4rpx;
						border-bottom-left-radius: 4rpx;
						color: white;
						padding: 0 6rpx;
					}
					&-value{
						color: var(--custom-brand-color);
						font-size: 18rpx;
						border-top: 1px solid var(--custom-brand-color);
						border-right: 1px solid var(--custom-brand-color);
						border-bottom: 1px solid var(--custom-brand-color);
						background-color: var(--custom-brand-color-10);
						box-sizing: border-box;
						height: 32rpx;
						line-height: 32rpx;
						padding: 0 6rpx;
						border-top-right-radius: 4rpx;
						border-bottom-right-radius: 4rpx;
					}
				}
				&-words{
					display: inline-flex;
					align-items: center;
					&-one{
						background: var(--custom-brand-color-10);
						border: 1px solid var(--custom-brand-color);
						box-sizing: border-box;
						font-size: 18rpx;
						height: 32rpx;
						line-height: 32rpx;
						border-radius: 4rpx;
						color: var(--custom-brand-color);
						padding: 0 6rpx;
						display: inline-block;
						margin-right: 10rpx;
					}
				}
			}
		}
	}

	&.largest {
		display: flex;
		flex-wrap: wrap;
		padding: 0 30rpx 0;
		background: transparent;
		.goods-item {
			flex-direction: column;
			width: calc(50% - 18rpx);
			padding: 0;
			margin-bottom: 32rpx;
			border-bottom: none;
			background: #ffffff;
			&:nth-child(2n + 1) {
				margin-right: 32rpx;
			}
			.goods-info {
				padding: 0 $ns-padding;
			}
		}
		.image-wrap {
			width: 100%;
			height: 330rpx;
			overflow: hidden;
		}
	}
}
.goods-empty{
	//padding-bottom: 100rpx;
	&-top{
		width: calc(100%);
		margin: 0 auto;
		background: #fff;
		margin-bottom: 20rpx;
	}
}
.category-list-wrap {
	height: 100%;
	.first {
		font-size: $ns-font-size-lg;
		font-weight: bold;
		display: block;
		// background: $page-color-base;
		padding: 20rpx;
	}
	.second {
		border-bottom: 2rpx solid $ns-border-color-gray;
		padding: 20rpx;
		display: block;

	}
	.third {
		padding: 0 20rpx 20rpx;
		overflow: hidden;
		> view {
			display: inline-block;
			margin-right: 20rpx;
			margin-top: 20rpx;
		}
		.uni-tag {
			padding: 0 20rpx;
		}
	}
}

.screen-wrap {
	.title {
		font-size: $ns-font-size-lg;
		padding: $ns-padding;
		background: #f6f4f5;
	}
	scroll-view {
		height: 85%;
		.item-wrap {
			border-bottom: 1px solid #f0f0f0;
			.label {
				font-size: $ns-font-size-lg;
				padding: $ns-padding;
				view {
					display: inline-block;
					font-size: 60rpx;
					height: 40rpx;
					vertical-align: middle;
					line-height: 40rpx;
				}
			}

			.list {
				margin: $ns-margin;
				overflow: hidden;
				> view {
					display: inline-block;
					margin-right: 25rpx;
					margin-bottom: 25rpx;
				}
				.uni-tag {
					padding: 0 $ns-padding;
					font-size: $ns-font-size-base;
					background: #f5f5f5;
				}
			}
			.price-wrap {
				display: flex;
				justify-content: center;
				align-items: center;
				padding: $ns-padding;
				input {
					flex: 1;
					background: #f5f5f5;
					height: 50rpx;
					padding: 15rpx 25rpx;
					line-height: 50rpx;
					font-size: 28rpx;
					border-radius: 50rpx;
					text-align: center;
					&:first-child {
						margin-right: 10rpx;
					}
					&:last-child {
						margin-left: 10rpx;
					}
				}
			}
		}
	}
	.footer {
		height: 90rpx;
		display: flex;
		justify-content: center;
		align-items: flex-start;
		display: flex;
		position: absolute;
		bottom: 0;
		width: 100%;
		.footer-box {
			width: 40%;
			height: 60rpx;
			background: $ns-bg-color-gray;
			border-top-left-radius: 30rpx;
			border-bottom-left-radius: 30rpx;
			text-align: center;
			line-height: 60rpx;
			color: $ns-text-color-gray;
		}
		.footer-box1 {
			width: 40%;
			height: 60rpx;
			border-top-right-radius: 30rpx;
			border-bottom-right-radius: 30rpx;
			text-align: center;
			line-height: 60rpx;
			color: #ffffff;
		}
	}
}
.footer.safe-area{
	padding-bottom: 0;
	padding-bottom: constant(safe-area-inset-bottom);
	padding-bottom: env(safe-area-inset-bottom);
}
