.head-wrap {
	background: #fff;
	position: fixed;
	width: 100%;
	left: 0;
	z-index: 1;

	.search-wrap {
		flex: 0.5;
		padding: 20rpx 20rpx 0;
		font-size: $ns-font-size-sm;
		display: flex;
		align-items: center;
		.input-wrap {
			flex: 1;
			margin: 0 20rpx;
			display: flex;
			justify-content: space-between;
			align-items: center;
			background: $uni-bg-color-grey;
			height: 80rpx;
			padding-left: 10rpx;
			border-radius: 40rpx;
			input {
				background: $uni-bg-color-grey;
				font-size: $ns-font-size-base;
				height: 50rpx;
				padding: 15rpx 25rpx 15rpx 40rpx;
				line-height: 50rpx;
				border-radius: 40rpx;
				width: 100%;
			}
			text {
				font-size: 40rpx;
				color: $ns-text-color-gray;
				width: 80rpx;
				text-align: center;
				margin-right: 20rpx;
			}
		}
	}

	.sort-wrap {
		display: flex;
		border-bottom: 1px solid $ns-border-color-gray;
		padding: 10rpx 0;
		> view {
			flex: 1;
			text-align: center;
			font-size: $ns-font-size-base;
			height: 60rpx;
			line-height: 60rpx;
		}
		.comprehensive-wrap {
			position: relative;
			.iconfont-wrap {
				display: inline-block;
				margin-left: 10rpx;
				width: 40rpx;
				.iconfont {
					font-size: 32rpx;
					height: 20rpx;
					line-height: 1;
				}
			}
		}
	}
}
.shop_li {
	width: 100%;
	background: #ffffff;
	border-radius: $ns-border-radius;
	margin-top: 15rpx;
	.shop-info {
		width: 100%;
		padding: $ns-padding;
		box-sizing: border-box;
		display: flex;
		align-items: flex-end;
		justify-content: space-between;
		position: relative;
		border-bottom: 2rpx solid $ns-border-color-gray;
	}
	.shop-image {
		width: 90rpx;
		height: 90rpx;
		border-radius: 50%;
		overflow: hidden;
		image {
			width: 100%;
			height: 100%;
		}
	}
	.shop-con {
		width: calc(100% - 90rpx - 20rpx);
		color: #ffffff;
	}
	.info-top {
		width: 100%;
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-bottom: 4rpx;

		.info-name {
			width: 60%;
			font-size: $ns-font-size-base;
			font-weight: bold;
			overflow: hidden;
			text-overflow: ellipsis;
			white-space: nowrap;

			.icongengduo {
				font-size: 26rpx;
				margin-top: 2rpx;
				font-weight: normal;
				font-weight: bold;
				margin-left: 10rpx;
			}
		}
	}
	.info-follow {
		display: inline-block;
		padding: 4rpx $ns-padding;
		font-size: $ns-font-size-sm;
		border: 1rpx solid $ns-border-color-gray;
		border-radius: 50rpx;
		position: absolute;
		right: $ns-margin;
		top: $ns-margin;
		color: #ffffff;

		.iconfont {
			margin-right: 10rpx;
			font-size: $ns-font-size-sm;
			color: #ffffff;
		}
	}
	.info-follow.active {
		background: #ffffff;
		.iconfont {
			margin-right: 0;
			color: #333333;
		}
	}
	.info-desc {
		width: 100%;
		display: flex;
		align-items: center;

		.desc-sign {
			padding: 0 $ns-padding;
			margin-right: $ns-margin;
			border-radius: 40rpx;
			color: #ffffff;
			font-size: 20rpx;
		}

		.desc-star {
			display: flex;
			align-items: center;
			font-size: 20rpx;
		}

		.fans {
			margin-left: $ns-margin;
			font-size: 20rpx;
		}
	}
	.shop-desc {
		width: 100%;
		padding: $ns-padding;
		box-sizing: border-box;
		line-height: 1.2;
		font-size: $ns-font-size-sm;
		color: $ns-text-color-gray;
		text-indent: 50rpx;
	}
}
.empty {
	margin-top: 100rpx;
}
