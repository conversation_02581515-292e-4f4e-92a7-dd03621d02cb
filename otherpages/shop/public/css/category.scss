.scroll-box.active{
	/* #ifdef MP */
	height: calc(100vh - 55px - 156rpx - 68rpx);
	top: 136rpx;
	/* #endif */
	/* #ifdef H5 */
	// height: calc(100vh - 50px - var(--status-bar-height) -55px);
	height: calc(100vh - 50px - var(--status-bar-height) - 50px - 156rpx - 68rpx);
	/* #endif */
	/* #ifdef APP-PLUS */
	height: calc(100vh - 44px - env(safe-area-inset-bottom) -55px - 68rpx);
	/* #endif */
}
.scroll-box {
	display: flex;
	position: fixed;
	left: 0;
	overflow: hidden;
	margin-top: 20rpx;

	/* #ifdef MP */
	height: calc(100vh - 55px - 156rpx);
	top: 136rpx;
	/* #endif */
	/* #ifdef H5 */
	// height: calc(100vh - 50px - var(--status-bar-height) -55px);
	height: calc(100vh - 50px - var(--status-bar-height) - 50px - 156rpx);
	/* #endif */
	/* #ifdef APP-PLUS */
	height: calc(100vh - 44px - env(safe-area-inset-bottom) -55px);
	/* #endif */
}

.cate-left {
	width: 160rpx;
	height: 100%;
	background: #ffffff;

	.left_list {
		width: 160rpx;
		height: 100rpx;
		display: flex;
		justify-content: center;
		align-items: center;
		padding: 10rpx 16rpx;
		box-sizing: border-box;
		line-height: 100%;
		position: relative;
		background: #ffffff;
		.left_list_box {
			width: 100%;
			color: $ns-text-color-black;
			display: -webkit-box;
			-webkit-box-orient: vertical;
			-webkit-line-clamp: 2;
			overflow: hidden;
			line-height: 1.3;
			text-align: center;
		}
	}

	.left_list.active {
		background: #ffffff;
		.left_list_box {
			width: 100%;
			color: $base-color;
		}
	}

	.left_list.active::before {
		content: '';
		position: absolute;
		top: 20%;
		left: 0;
		width: 10rpx;
		height: 60%;
		border-radius: 5rpx;
		background-color: $base-color;
	}
}

.cate-right {
	width: 590rpx;
	padding: 0rpx $ns-padding;
	box-sizing: border-box;
	height: calc(100% - 80px);

	.cate_image {
		width: 100%;
	}

	.cate-right-box {
		display: inline-block;
		overflow: hidden;
		margin-bottom: $ns-margin;
		margin-right: $ns-margin;

		.title {
			width: 100%;
			height: 74rpx;
			// background: #ffffff;
			font-size: $ns-font-size-base;
			font-weight: bold;
			display: flex;
			justify-content: center;
			align-items: center;
			position: relative;
		}

		.title:before {
			content: '';
			width: 1.53333rem;
			height: 0.06667rem;
			background-color: #d9d9d9;
			margin: 0 0.26667rem;
			display: inline-block;
			vertical-align: middle;
		}

		.title:after {
			content: '';
			width: 1.53333rem;
			height: 0.06667rem;
			background-color: #d9d9d9;
			margin: 0 0.26667rem;
			display: inline-block;
			vertical-align: middle;
		}

		.cate-goods-list {
			display: flex;
			flex-wrap: wrap;
			background: #ffffff;
			border-radius: 15rpx;
		}

		.cate-goods-li {
			width: 166rpx;
			height: 176rpx;
			margin-bottom: 20rpx;
			display: flex;
			justify-content: center;
			flex-direction: column;
			align-items: center;
			margin-top: 20rpx;

			.goods-pic {
				width: 128rpx;
				height: 128rpx;
			}

			.goods-name {
				width: 100%;
				height: 45rpx;
				text-align: center;
				line-height: 45rpx;
				font-size: $ns-font-size-sm;
			}
		}
	}
	.cate-right-box:nth-child(3n){
		margin-right: 0 !important;
	}
}

.emit-box {
	width: 100%;
	height: 600rpx;
	display: flex;
	flex-direction: column;
	justify-content: center;
	align-items: center;
	color: #999;
	font-size: $ns-font-size-base;
	.iconfont {
		color: #999;
		font-size: 120rpx;
	}
}
.empty {
	margin-top: 50rpx;
}
