import apiurls from "../../../../common/js/apiurls";
export default {
	data() {
		return {
			listStyle: '',
			loadingType: 'loading', //加载更多状态
			orderType: 'all',
			saleNumOrder: 'DESC', //1 销量从低到高 2销量从高到低
			priceOrder: 'DESC', //1 价格从低到高 2价格从高到低
			categoryList: [], //排序类型
			goodsList: [],
			order: 'all',
			sort: 'DESC',
			is_activity_goods: 0, //筛选活动商品，1为活动商品
			showCategory: false,
			showScreen: false,
			keyword: '',
			categoryId: 0,
			categoryLevel: 0,
			brandId: 0,
			attr: [],
			currAttr: [],
			minPrice: '',
			maxPrice: '',
			isFreeShipping: -1, //是否免邮
			brandList: [], //品牌筛选项
			showBrandMore: false,
			attributeList: [], //属性筛选项
			siteId: 0,

			top:80,
			statusBarHeight:8,
			navHeight: 40,
			couponId:0,
			platformcouponTypeId:0,

			entrance:'', //入口，判断显示标题
			categoryName:'',
			isLoadLike: false,  //是否加载推荐商品列表
			token:'',
			isShowSearch: false,
			isFocus: false
		}
	},

	onLoad(options) {
		if (!options.site_id) {
			this.$util.redirectTo('/otherpages/shop/home/<USER>', {}, 'redirectTo');
			return;
		}
		this.entrance = options.entrance || '';
		this.categoryName = options.category_name || '';
		this.siteId = options.site_id;

		this.categoryId = options.category_id || 0;
		this.categoryLevel = options.category_level || 0;
		this.brandId = options.brand_id || 0;
		this.keyword = options.keyword || '';
		this.loadCategoryList(this.categoryId);
		this.couponId = options.couponId || 0;
		this.platformcouponTypeId = options.platformcouponTypeId || 0;
		let res = uni.getSystemInfoSync()
		// #ifdef MP-WEIXIN
		// 参考文档  https://juejin.cn/post/7076705501764911118
		// 获取微信胶囊的位置信息 width,height,top,right,left,bottom
		const custom = uni.getMenuButtonBoundingClientRect()
		this.statusBarHeight = res.statusBarHeight
		// 导航栏高度(标题栏高度) = 胶囊高度 + (顶部距离 - 状态栏高度) * 2
		this.navHeight = custom.height + (custom.top - this.statusBarHeight) * 2
		// #endif
		this.top = res.screenWidth/750*80
	},
	onShow() {
		// 刷新多语言
		this.$langConfig.refresh();
		let title = this.categoryName;
		if(this.entrance && this.entrance == 'search'){
			title = '搜索'
		}
		uni.setNavigationBarTitle({ title:title })
		this.token = uni.getStorageSync('token')
	},
	methods: {
		goback(){
			this.$util.goBack()
		},
		// 滚动监听曝光
		async scrollView(e){
			if(!this.isLoadLike){
				await this.scrollTouch(e)
			}
		},
		scrolltolower(mescroll){
			if(this.isLoadLike){
				this.$refs.goodrecommend.scrollPage()
			}
		},
		getGoodsList(mescroll) {
			let data={
				page: mescroll.num,
				page_size: mescroll.size,
				goods_name: this.keyword,
				category_id: this.categoryId,
				category_level: this.categoryLevel,
				brand_id: this.brandId,
				min_price: this.minPrice,
				max_price: this.maxPrice,
				sf: this.order,
				st: this.sort,
				attr: this.currAttr.length > 0 ? JSON.stringify(this.currAttr) : "",
				site_id: this.siteId,
				coupon_type:this.couponId,
				platform_coupon_type:this.platformcouponTypeId,
				is_activity_goods: this.is_activity_goods
			}
			// if (this.siteId) {
			// 	data.shop_category_id=this.categoryId;
			// }else{
			// 	data.category_id = levelId;
			// 	data.category_level=this.categoryLevel;
			// }
			if(this.isFreeShipping>0){
				data.is_free_shipping=this.isFreeShipping;
			}
			if (this.$refs.loadingCover && mescroll.num == 1) this.$refs.loadingCover.show();
			this.$api.sendRequest({
				// url: '/api/goodssku/page',
				url: apiurls.goodsListUrl,
				data: data,
				success: res => {
					if (this.$refs.loadingCover) this.$refs.loadingCover.hide();
					let newArr = []
					let msg = res.message;
					if (res.code == 0 && res.data) {
						res.data.list = res.data.list.map(item=>{
							item.goods_image = this.$util.imageCdnResize(item.goods_image);
							return item;
						})
						newArr = res.data.list;
					} else {
						this.$util.showToast({
							title: msg
						})
					}
					mescroll.endSuccess(newArr.length);
					//设置列表数据
					if (mescroll.num == 1) this.goodsList = []; //如果是第一页需手动制空列表
					this.goodsList = this.goodsList.concat(newArr); //追加新数据

					// this.top = this.goodsList.length>0?190:110
					if(this.goodsList.length<1){
						this.isLoadLike = true
					}else{
						this.isLoadLike = false
					}
				},
				fail: res => {
					mescroll.endErr();
					if(this.goodsList.length<1){
						this.isLoadLike = true
					}
					if (this.$refs.loadingCover) this.$refs.loadingCover.hide();
				}
			});
		},
		bottomOut(){
			this.$refs.goodrecommend.getLikeList()
		},
		changeListStyle() {
			if (!this.listStyle) this.listStyle = 'largest';
			else this.listStyle = '';
		},
		//加载分类
		loadCategoryList(fid, sid) {
			this.$api.sendRequest({
				url: '/api/shopgoodscategory/tree',
				data: {
					site_id: this.siteId
				},
				success: res => {
					if (res.data != null) this.categoryList = res.data;
				}
			});
		},
		//筛选点击
		sortTabClick(tag) {
			if (tag == 'sale_num') {
				this.order = 'sale_num';
				this.sort = 'DESC';
				this.is_activity_goods = 0
			} else if (tag == 'price') {
				this.order = 'price';
				this.sort = 'DESC';
				this.is_activity_goods = 0
			} else if (tag == 'screen') {
				//筛选
				// this.showScreen = true;
				this.isShowSearch = true
				this.isFocus = true
				return;
			}else if(tag == 'is_activity_goods'){
				this.is_activity_goods = 1
			} else {
				this.order = 'all';
				this.sort = 'DESC';
				this.is_activity_goods = 0
			}

			if (this.orderType === tag && (tag !== 'price' && tag !== 'sale_num')) return;

			this.orderType = tag;
			if (tag === 'sale_num') {
				this.saleNumOrder = this.saleNumOrder === 'ASC' ? 'DESC' : 'ASC';
				this.sort = this.saleNumOrder;
			} else {
				this.saleNumOrder = '';
			}

			if (tag === 'price') {
				this.priceOrder = this.priceOrder === 'ASC' ? 'DESC' : 'ASC';
				this.sort = this.priceOrder;
			} else {
				this.priceOrder = '';
			}

			this.$refs.mescroll.refresh();
		},
		searchConfirm(e) {
			// this.search(e.type)
			let value = e.value;
			let diy_input_type = e.type
			if(diy_input_type == 'user_input'){
				this.keyword = value
				this.search(e.type)
			}else if(diy_input_type == 'associate'){
				switch (value.source){
					case 'goods':
						this.keyword = value.keyword_text
						this.search(diy_input_type)
						break;
					case 'seckill':
						this.$buriedPoint.diyReportSearchInteractionEvent({diy_input_type,diy_keyword:value.keyword_text.trim()})
						this.$util.diyCompateRedirectTo({
							wap_url: `/promotionpages/new_seckill/list/list?seckill_id=${value.source_id}`
						});
						break;
					case 'topic':
						this.$buriedPoint.diyReportSearchInteractionEvent({diy_input_type,diy_keyword:value.keyword_text.trim()})
						this.$util.diyCompateRedirectTo({
							wap_url: `/promotionpages/task/list/list?topic_id=${value.source_id}`
						})
						break;
					case 'micropage':
						this.$buriedPoint.diyReportSearchInteractionEvent({diy_input_type,diy_keyword:value.keyword_text.trim()})
						this.$util.diyCompateRedirectTo({
							wap_url: `/otherpages/diy/diy/diy?name=${value.micropage_name}`
						})
						break;
				}
			}
		},
		search(type) {
			this.$buriedPoint.diyReportSearchInteractionEvent({diy_input_type: type ? type : 'user_input',diy_keyword:this.keyword})
			this.isFocus = false
			this.isShowSearch = false
			this.$refs.mescroll.refresh();
		},
		clearSearch(){
			this.keyword = ''
			// this.$util.goBack()
			this.isFocus = false
			this.isShowSearch = false
			this.search()
		},

		selectedCategory(categoryId, categoryLevel) {
			this.keyword = '';
			this.categoryId = categoryId;
			this.categoryLevel = categoryLevel;
			this.$refs.mescroll.refresh();
			this.showCategory = false;
			//根据分类查询关联类型,查询关联品牌/属性
			this.$api.sendRequest({
				url: '/api/goodscategory/relevanceinfo',
				data: {
					category_id: this.categoryId
				},
				success: res => {
					let data = res.data;
					if (data) {
						if (data.brand_list) this.brandList = data.brand_list;
						if (data.attribute_list) this.attributeList = data.attribute_list;
					}
				}
			});

		},
		// 选择属性筛选项
		selectedAttr(attr_id, attr_value_id) {
			if (this.attr[attr_id] && this.attr[attr_id].attr_value_id == attr_value_id) {
				delete this.attr[attr_id];
			} else {
				this.attr[attr_id] = {
					attr_id: attr_id,
					attr_value_id: attr_value_id
				};
			}
			this.currAttr = [];
			for (let i in this.attr) this.currAttr.push(this.attr[i]);
		},
		//是否选中属性
		isSelectedAttr(attr_id, attr_value_id) {
			var res = false;
			for (let i in this.currAttr) {
				if (this.currAttr[i].attr_id == attr_id && this.currAttr[i].attr_value_id == attr_value_id) {
					res = true;
					break;
				}
			}
			return res;
		},
		screenData() {
			if (this.minPrice != '' || this.maxPrice != '') {

				if (!Number(this.minPrice)) {
					this.$util.showToast({
						title: '请输入最低价'
					});
					return;
				}
				if (!Number(this.maxPrice)) {
					this.$util.showToast({
						title: '最输入最高价'
					});
					return;
				}
				if (Number(this.minPrice) < 0 || Number(this.maxPrice) < 0) {
					this.$util.showToast({
						title: '筛选价格不能小于0'
					});
					return;
				}
				if (this.minPrice != '' && Number(this.minPrice) > Number(this.maxPrice)) {

					this.$util.showToast({
						title: '最低价不能大于最高价'
					});
					return;
				}
				if (this.maxPrice != '' && Number(this.maxPrice) < Number(this.minPrice)) {
					this.$util.showToast({
						title: '最高价不能小于最低价'
					});
					return;
				}

			}
			this.$refs.mescroll.refresh();
			this.showScreen = false;
		},
		imageError(index) {
			if(this.goodsList[index] && this.goodsList[index].goods_image){
				this.goodsList[index].goods_image = this.$util.getDefaultImage().default_goods_img;
				this.$forceUpdate();
			}
		},
		/**
		 *分享参数组装
		 */
		getSharePageParams() {
			return this.$util.unifySharePageParams('/otherpages/shop/home/<USER>', this.shareTitle || '先迈商城',
				'',{}, this.$util.img(this.shareImg) || this.$util.img('public/static/youpin/home_share.jpg'))
		}
	},
	/**
	 * 自定义分享内容
	 * @param {Object} res
	 */
	onShareAppMessage(res) {
		let { title, link, imageUrl, query } = this.getSharePageParams()
		return this.$buriedPoint.pageShare(link , imageUrl, title);
	},

}
