	// [data-theme] {
.fenxiao_index{
	position: relative;
	display: flex;
	flex-direction: column;
	align-items: center;
}
.fenxiao_index_header {
	width: 100%;
	position: relative;
	height: 260rpx;
	.member {
		width: 100%;
		height: 220rpx;
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding: 50rpx 40rpx;
		box-sizing: border-box;

		.member-pic {
			width: 101rpx;
			height: 101rpx;
			border-radius: 50%;
			border: 3rpx solid #ffffff;

			image {
				width: 100%;
				height: 100%;
				border-radius: 50%;
			}
		}

		.member-info {
			width: 466rpx;
			height: 120rpx;
			display: flex;
			flex-direction: column;
			justify-content: center;
			view {
				color: #ffffff;
			}
			.fenxiao_name{
				font-size: 34rpx;
			}
			.level_name {
				display: inline-block;
				border: 2rpx solid #fda918;
				color: #FFDC00;
				height: 34rpx;
				font-size: 20rpx;
				padding: 0rpx 9rpx 0rpx 10rpx;
				margin-left: 10rpx;
				line-height: 38rpx;
				border-radius: 8rpx;
			}
		}

		.member-tixian {
			width: 120rpx;
			height: 50rpx;
			border: 2rpx solid #ffffff;
			border-radius: 50rpx;
			display: flex;
			justify-content: center;
			align-items: center;
			font-size:$ns-font-size-sm;
			color: #ffffff;
		}
		.code{
			width: 30rpx;
			height: 30rpx;
			margin-right: $ns-margin;
		}
	}
}

.fenxiao_index_money{
	background-color: #FFFFFF;
	border-radius: 20rpx;
	display: flex;
	justify-content: center;
	align-items: center;
	width: 95%;
	position: absolute;
	top: 210rpx;
	
	.xian{
		height: 80rpx;
		border: 1rpx solid #F1F1F1;
	}
	
	.index-money-item{
		padding: 40rpx 0;
		flex: 1;
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		
		
		.item_money{
			color: #000000;
			font-size: 36rpx;
			line-height: 1;
		}
		.item_tit{
			padding-top: 26rpx;
			font-size: 20rpx;
			color: #838383;
		}
	}
}

.fenxiao_index_allmoney{
	display: flex;
	flex-direction: column;
	width: 95%;
	border-radius: 20rpx;
	background-color: #FFFFFF;
	position: absolute;
	top: 410rpx;
	
	.title-wrap{
		display: flex;
		align-items: center;
		border-bottom: 1rpx solid #f7f7f7;
		padding: 30rpx 40rpx;
		.title-img{
			width: 30rpx;
			height: 30rpx;
			display: flex;
			justify-content: center;
			align-items: center;
			margin-right: 10rpx;
			image{
				width: 100%;
				height: 100%;
			}
		}
		.title{
			color: #333333;
			font-size: $ns-font-size-base;
			line-height: 1;
		}
	}
	.all_money_wrap{
		display: flex;
		justify-content: center;
		align-items: center;
					padding: 25rpx 15rpx;
		.all-money{
			flex: 4;
			font-size: 26rpx;
		}
		.all-money{
			font-weight: 600;
			padding-left: 65rpx;
		}
		.xian{
			height: 80rpx;
			border: 1rpx solid #F1F1F1;
		}
		.all-money-item{
			flex: 3;
			display: flex;
			flex-direction: column;
			justify-content: space-between;
			align-items: center;
			
			.all-money-tit{
				color: $ns-text-color-gray;
				font-size: $ns-font-size-sm;
			}
			.all-money-num{
				
				color: #000000;
				font-size: 35rpx;
			}
		}
	}
}
.fenxiao_manu_list{
	display: flex;
	flex-direction: column;
	justify-content: center;
	width: 95%;
	border-radius: 20rpx;
	background-color: #FFFFFF;
	position: absolute;
	top: 675rpx;
	margin-bottom: 113rpx;
	.title-wrap{
		display: flex;
		align-items: center;
		border-bottom: 1rpx solid #f7f7f7;
		padding: 30rpx 40rpx;
		.title-img{
			width: 30rpx;
			height: 30rpx;
			display: flex;
			justify-content: center;
			align-items: center;
			margin-right: 10rpx;
			image{
				width: 100%;
				height: 100%;
			}
		}
		.title{
			color: #333333;
			font-size: $ns-font-size-base;
			line-height: 1;
		}
	}
	.content_manu {
		width: 100%;
		background: #ffffff;
		border-radius: 15rpx;
		box-sizing: border-box;
		display: flex;
		flex-wrap: wrap;
		padding-bottom: 10rpx;
		.manu-list {
			width: 24.5%;
			height: 150rpx;
			display: inline-block;
			padding: 0 $ns-padding;
			box-sizing: border-box;
			margin: 10rpx 0;
			.manu-list-box {
				width: 100%;
				height: 100%;
				display: flex;
				flex-direction: column;
				justify-content: center;
				align-items: center;
				image {
					width: 50rpx;
					height: 50rpx;
					margin-bottom: 15rpx;
				}
				text {
					font-size: $ns-font-size-sm;
				}
			}
		}
	}
}
.fenxiao_content {
	.content_manu {
		width: 100%;
		background: #ffffff;
		border-radius: 15rpx;
		box-sizing: border-box;
		display: flex;
		flex-wrap: wrap;
		.manu-list {
			width: 24.5%;
			height: 150rpx;
			display: inline-block;
			box-sizing: border-box;
			.manu-list-box {
				width: 100%;
				height: 100%;
				display: flex;
				flex-direction: column;
				justify-content: center;
				align-items: center;
				image {
					width: 60rpx;
					height: 60rpx;
					margin-bottom: 15rpx;
				}
				text {
					font-size: $ns-font-size-sm;
				}
			}
		}
	}
}
.empty {
	width: 100%;
	height: 400rpx;
	margin-top: 200rpx;
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	image {
		width: 300rpx;
		margin-bottom: 50rpx;
	}
	text {
		font-size: $ns-font-size-sm;
		font-weight: 600;
	}
	navigator {
		width: 300rpx;
		height: 70rpx;
		border-radius: 70rpx;
		text-align: center;
		line-height: 70rpx;
		color: #ffffff;
		margin-top: 30rpx;
	}
}
.member-level {
	width: 100%;
	height: 66rpx;
	padding: 0 $ns-padding;
	box-sizing: border-box;
	margin-top: 12rpx;
	.member-level-box {
		width: 100%;
		height: 100%;
		border-top-left-radius: 10px;
		border-top-right-radius: 10px;
		background: linear-gradient(to right, #1d1d1d, rgba(#1d1d1d, 0.4));
		padding: 0 $ns-padding;
		box-sizing: border-box;
		display: flex;
		justify-content: space-between;
		align-items: center;
		color: #ffd700;
		.iconv {
			font-size: 36rpx;
		}
		text {
			line-height: 1;
			display: flex;
			align-items: center;
			text {
				display: inline-block;
			}
		}
	}
}
// }