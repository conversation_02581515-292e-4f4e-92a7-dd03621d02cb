<template>
  <view class="receive-box">
    <!-- 优惠券信息 -->
    <view class="info">
      <view class="header" :style="{'background-image': `url(${$util.img(couponInfo.image)})`}">
        <view class="top">
          <text>￥</text>
          <view class="money">
            {{couponInfo.money}}
            <text>元</text>
          </view>
        </view>
        <view class="bottom">
          <view v-for="(item, index) in couponInfo.tags" :key="index">{{item}}</view>
        </view>
      </view>
      <view class="btn">
        <view>{{couponInfo.validity_type ? `领取日起${couponInfo.fixed_term}天有效`:`有效期至：${$util.timeStampTurnTime(couponInfo.end_time)}`}}</view>
        <view :class="couponStatus.can_receive || !token ? '':'disabeld'" @click="receiveFun">{{couponStatus.can_receive || !token ? '领取优惠券':couponStatus.notice_msg}}</view>
      </view>
    </view>
    <!-- 商品列表 -->
    <view class="content" v-if="goodList.length != 0 && !emptyShow">
      <view class="tip">{{couponInfo.at_least ? `- 以下商品可使用满${couponInfo.at_least}减${couponInfo.money}优惠券 -`:''}}</view>
      <view class="good-box">
        <view class="good-list" v-for="(item, index) in goodList" :key="index" @click="detailFun(item)">
          <view class="item-image">
            <image @error="errorFun(item)" :src="$util.img(item.goods_image.split(',')[0])" mode="" />
            <image :src="$util.img('public/static/youpin/product-sell-out.png')" class="over" v-if="item.goods_stock == 0"></image>
          </view>
          <view class="info-box">
            <view class="title">{{item.goods_name}}</view>
            <view class="sold hide-sales">已售{{item.sale_num}}件</view>
            <view class="price">
              <view><text>￥</text>{{item.retail_price}}</view>
              <view>￥{{item.market_price}}</view>
            </view>
          </view>
        </view>
      </view>
      <view class="empty-list-text" v-if="scrollLoading">没有更多内容</view>
    </view>
    <view class="empty" v-else>
      <image :src="$util.img('public/static/youpin/empty_coupon_good.png')"></image>
      <view>商品备货中，请稍候...</view>
    </view>
    <!-- 下拉加载动画 -->
    <ns-loading v-if="showLoading"></ns-loading>
    <!-- 返回顶部 -->
    <image :src="$util.img('public/static/youpin/to-top.png')" class="to-top" v-show="showTop" @click="scrollToTopNative"></image>

    <!-- 授权登录弹窗 -->
    <yd-auth-popup ref="ydauth"></yd-auth-popup>
    <ns-login ref="login"></ns-login>
    <uni-coupon-pop ref="couponPop"></uni-coupon-pop>

    <loading-cover ref="loadingCover"></loading-cover>
  </view>
</template>

<script>
  import scroll from '@/common/mixins/scroll-view.js'
  import scrollLoading from '@/common/mixins/scroll-loading.js'
  import toTop from '@/components/toTop/toTop.vue'
  import system from "../../../common/js/system";
  export default {
    components: {
      toTop
    },
    data() {
      return {
        goodList: [],
        tipList: [{name: '满100元可用'},{name: '满100元可用'},{name: '满100元可用'}],
        goodscoupon_type_id: '',
        couponInfo: {},
        couponStatus: {},
        otherGoods: false,  //其他商品
        limitGoods: false,   //是否是限定商品
        emptyShow: false,
        token: uni.getStorageSync('token') || ''
      }
    },
    onLoad({ goodscoupon_type_id }) {
      this.goodscoupon_type_id = goodscoupon_type_id
    },
    async onShow() {
      // 刷新多语言
			this.$langConfig.refresh();
      await system.wait_staticLogin_success();
      // this.showLoading = true
      this.goodListFun()
      this.goodsCouponReceivedNum()

      // #ifdef H5
      let share_data = this.$util.deepClone(this.getSharePageParams())
      let link = window.location.origin+this.$router.options.base+share_data.link.slice(1)
      share_data.link=link
      share_data.desc=share_data.title
    	share_data.title='先迈商城'
      await this.$util.publicShare(share_data);
      // #endif
    },
    methods: {
      getInit() {
        if(!this.otherGoods) {
          this.$api.sendRequest({
            url:this.$apiUrl.goodsCouponList,
            data: {
              page: this.page,
              page_size: this.page_size,
              goodscoupon_type_id: this.goodscoupon_type_id
            },
            success: res => {
              this.hideLoading()
              let data = res.data.list
              if(this.couponInfo.use_scenario != 1 && data.length == 0 && this.page == 1) {
                this.emptyShow = true
                if (this.$refs.loadingCover) this.$refs.loadingCover.hide();
                return false
              }
              if(data.length) {
                this.limitGoods = true
                this.page ++;
                this.goodList = this.goodList.concat(data); //追加新数据
                this.$buriedPoint.exposeGoods(data)
                if (this.$refs.loadingCover) this.$refs.loadingCover.hide();
              }else{
                if(!this.limitGoods){
                  this.otherGoods = true
                  this.goodsGuess()
                }else{
                  this.scrollLoading = true
                  if (this.$refs.loadingCover) this.$refs.loadingCover.hide();
                }
              }
            },
            fail: res => {
              if (this.$refs.loadingCover) this.$refs.loadingCover.hide();
            }
          });
        }else{
          this.goodsGuess()
        }
      },
      goodListFun() {
        this.$api.sendRequest({
          url:this.$apiUrl.goodsCouponTypeinfo,
          data: {
            goodscoupon_type_id: this.goodscoupon_type_id
          },
          success: res => {
            this.couponInfo = res.data
            this.hideLoading()
            this.getInit()
          },
          fail: res => {
            if (this.$refs.loadingCover) this.$refs.loadingCover.hide();
          }
        })
      },
      goodsGuess() {
        this.$api.sendRequest({
          url: this.$apiUrl.goodsListUrl,
          data: {
            page: this.page,
            page_size: this.page_size
          },
          success: res => {
            this.hideLoading()
            if (this.$refs.loadingCover) this.$refs.loadingCover.hide();
            let data = res.data.list
            if(data.length) {
              this.page ++;
              this.goodList = this.goodList.concat(data); //追加新数据
            }else{
              this.scrollLoading = true
            }
          }
        })
      },
      goodsCouponReceivedNum() {
        this.$api.sendRequest({
          url:this.$apiUrl.goodsCouponReceivedNum,
          data: {
            token: uni.getStorageSync('token'),
            goodscoupon_type_id: this.goodscoupon_type_id
          },
          success: res => {
            this.couponStatus = res.data
          }
        })
      },
      async receiveFun() {
        if (!uni.getStorageSync('token')) {
          this.$util.toShowLoginPopup(this,null,`/otherpages/goods/coupon_receive/coupon_receive?goodscoupon_type_id=${this.goodscoupon_type_id}`);
          return false
        }
        if(this.couponStatus.can_receive == 0) return false
        await this.$util.subscribeMessage({
					source:'goodscoupon',
					source_id:'',
					scene_type:'goodscoupon_group'
        })
        uni.showLoading({
          title: '加载中',
          mask: true
        });
        this.$api.sendRequest({
          url:this.$apiUrl.goodsCouponReceive,
          data: {
            token: uni.getStorageSync('token'),
            goodscoupon_type_id: this.goodscoupon_type_id
          },
          success: res => {
            uni.hideLoading();
            setTimeout(() => {
              this.$util.showToast({
                title: res.code == 0 ? '领取成功' : res.message,
                mask: true,
                duration: 2000,
                success: res => {
                  this.goodsCouponReceivedNum()
                }
              });
            }, 100)
          },
          fail: res => {
            uni.hideLoading();
          }
        })
      },
      detailFun(e) {
        uni.navigateTo({
          url:`/pages/goods/detail/detail?sku_id=${e.sku_id}`
        })
      },
      errorFun(e) {
        this.goodList.forEach(v => {
          if(v.goods_id == e.goods_id){
            v.goods_image = "https://youpin-dev.jiufuwangluo.com:8443/upload/default/default_img/goods.png"
          }
        })
      },
			getSharePageParams() {
        return this.$util.unifySharePageParams('/otherpages/goods/coupon_receive/coupon_receive','购物先领券，下单更省钱！',
            '',{goodscoupon_type_id:this.goodscoupon_type_id},this.$util.img('public/static/youpin/coupon_share.jpg'))
			}
    },
    mixins: [scroll, scrollLoading],
		// 分享
		onShareAppMessage(res) {
			let { title, link, imageUrl, query } = this.getSharePageParams()
			return this.$buriedPoint.pageShare(link, imageUrl, title);
		},
		// 分享到微信
		onShareTimeline(res){
			let { title, imageUrl, query } = this.getSharePageParams()
			return {
				title,
        imageUrl,
				query,
				success: res => {},
				fail: res => {}
			};
		}
  }
</script>

<style lang="scss" scoped>
.receive-box{
  padding: 20rpx 24rpx 110rpx;
  .info{
    // height: 374rpx;
    border-radius: 20rpx;
    background-color: #fff;
    .header{
      display: flex;
      flex-direction: column;
      width: 100%;
      height: 409rpx;
      background-size: cover;
      .top{
        display: flex;
        justify-content: center;
        align-items: baseline;
        color: #fff;
        padding: 87rpx 0 60rpx;
        font-size: 120rpx;
        line-height: 120rpx;
        .money{
          display: flex;
          align-items: flex-end;
          font-size: 120rpx;
          line-height: 120rpx;
          color: #fff;
          text{
            line-height: 100rpx;
          }
        }
        text{
          font-size: 64rpx;
          &:last-child{
            margin-left: 10rpx;
          }
        }
      }
      .bottom{
        flex: 1;
        display: flex;
        align-items: center;
        justify-content: center;
        view{
          background-color: #fff;
          font-size: 24rpx;
          line-height: 36rpx;
          color: #F2270C;
          padding: 6rpx 22rpx;
          border-radius: 8rpx;
          margin-right: 30rpx;
          &:last-child{
            margin-right: 0;
          }
        }
      }
    }
    .btn{
      display: flex;
      align-items: center;
      flex-direction: column;
      &>view:first-child{
        font-size: 28rpx;
        color: #333;
        line-height: 40rpx;
        text-align: center;
        margin: 24rpx 0 14rpx;
      }
      &>view:last-child{
        display: flex;
        justify-content: center;
        align-items: center;
        width: 400rpx;
        height: 80rpx;
        background: linear-gradient(0deg, #FB331D 0%, #FE5838 100%);
        border-radius: 40px;
        font-size: 30rpx;
        font-weight: 500;
        color: #fff;
        margin-bottom: 30rpx;
      }
      .disabeld{
        background: #CCCCCC !important;
      }
    }
  }
  .content{
    .tip{
      font-size: 32rpx;
      color: #333;
      margin-top: 56rpx;
      text-align: center;
      margin-bottom: 20rpx;
    }
    .good-box{
      display: flex;
      flex-wrap: wrap;
      margin-bottom: 20rpx;
      .good-list{
        width: 343rpx;
        background-color: #fff;
        border-radius: 10rpx;
        overflow: hidden;
        margin-top: 20rpx;
        &:nth-of-type(2n){
          margin-left: 16rpx;
        }
        image{
          display: inherit;
          width: 100%;
          height: 343rpx;
        }
        .item-image{
          position: relative;
          .over{
						width: 120rpx;
						height: 120rpx;
						position: absolute;
						left: 50%;
						top: 50%;
						transform: translate(-50%, -50%);
					}
        }
        .info-box{
          padding: 14rpx 16rpx 10rpx;
          .title{
            font-size: 26rpx;
            font-weight: 500;
            line-height: 40rpx;
            text-overflow: ellipsis;
            display: -webkit-box;
            -webkit-box-orient: vertical;
            -webkit-line-clamp: 2;
            overflow: hidden;
            min-height: 80rpx;
          }
          .sold{
            font-size: 24rpx;
            line-height: 36rpx;
            margin-top: 30rpx;
            color: #999;
          }
          .price{
            display: flex;
            align-items: center;
            margin-top: 4rpx;
            &>view:first-child{
              font-size: 36rpx;
              color: #F2270C;
              line-height: 48rpx;
              text{
                font-size: 26rpx;
              }
            }
            &>view:last-child{
              font-size: 24rpx;
              line-height: 48rpx;
              color: #999999;
              text-decoration: line-through;
              margin-left: 8rpx;
            }
          }
        }
      }
    }
  }
  .empty{
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 100rpx 0 0;
    image{
      width: 402rpx;
      height: 282rpx;
    }
    view{
      font-size: 32rpx;
      line-height: 44rpx;
      color: #999999;
      margin-top: 42rpx;
    }
  }
  .to-top{
    width: 144rpx;
    height: 152rpx;
    position: fixed;
    right: 0;
    bottom: 82rpx;
  }
}
</style>
