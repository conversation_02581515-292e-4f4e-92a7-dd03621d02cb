<template>
	<view>
		<view class="content">
<!--			<view class="wx-code">-->
<!--				<image :src="$util.img(path)" />-->
<!--			</view>-->
<!--			<view class="get-coupon">-->
<!--				<text class="coupon-name">{{ info.coupon_name }}</text>-->
<!--				<view class="ns-text-color coupon-price uni-bold">￥{{ info.money }}</view>-->
<!--				<text v-if="info.at_least > 0" class="ns-text-color-gray">满{{ info.at_least }}元使用</text>-->
<!--				<text v-else class="ns-text-color-gray">无门槛优惠券</text>-->
<!--				<view v-if="info.validity_type == 0" class="ns-text-color-gray">有效期至 {{ $util.timeStampTurnTime(info.end_time) }}</view>-->
<!--				<view v-else class="ns-text-color-gray">领取之日起{{ info.fixed_term }}天内有效</view>-->

<!--				<view class="get-span" @click="receiveGoodsCoupon(info.coupon_type_id)">-->
<!--					<button type="primary" v-if="isCanReceive">{{ button }}</button>-->
<!--					<button type="primary" disabled v-if="!isCanReceive">{{ button }}</button>-->
<!--				</view>-->

<!--				<view class="ns-text-color-gray ns-font-size-sm tips">注:扫描二维码或点击右上角进行分享</view>-->
<!--			</view>-->
    <view class="coupon">
      <view class="coupon-one" :class="{'coupon-one-invalid':!isCanReceive}" :style="{backgroundImage: isCanReceive ? `url(${$util.img(couponBg)})` : `url(${$util.img(couponInvalidBg)})`}">
        <view class="coupon-one-left">
          <view class="coupon-one-left-money"><text>￥</text>{{info.money}}</view>
          <view class="coupon-one-left-tip" v-if="info.type=='reward'">满{{info.at_least}}元使用</view>
          <view class="coupon-one-left-tip" v-if="info.type=='deduction'">{{info.type_name}}</view>
          <view class="coupon-one-left-tip" v-if="info.type=='fixed'">{{info.type_name}}</view>
        </view>
        <view class="coupon-one-right">
          <view class="coupon-one-right-title">{{info.type_name}}</view>
          <view class="coupon-one-right-date">有效期至{{info.end_time}}</view>
        </view>
      </view>
    </view>
    <view class="get-span" @click="receiveGoodsCoupon(info.coupon_type_id)">
      <button type="primary" v-if="isCanReceive">{{ button }}</button>
      <button type="primary" disabled v-if="!isCanReceive" :class="{'invalid':!isCanReceive}">{{ button }}</button>
    </view>
		</view>
		<loading-cover ref="loadingCover"></loading-cover>
	</view>
</template>

<script>
	import apiurls from "../../../common/js/apiurls";

  export default {
		components: {},
		data() {
			return {
				info: {},
				couponTypeId: 0,
				path: '',
				button: '点击领取优惠券',
				uid: 0,
				couponBtnSwitch: false, //获取优惠券防止重复提交
				memberId: 0,
				receivedNum: 0,
        couponBg:'public/static/youpin/coupon_bg.png',
        couponInvalidBg:'public/static/youpin/coupon_invail_bg.png'
			};
		},
		onLoad(data) {
			if (data.source_member) uni.setStorageSync('source_member', data.source_member);
			if (data.coupon_type_id) this.couponTypeId = data.coupon_type_id;

			// 小程序扫码进入
			if (data.scene) {
				var sceneParams = decodeURIComponent(data.scene);
				sceneParams = sceneParams.split('&');
				if (sceneParams.length) {
					sceneParams.forEach(item => {
						if (item.indexOf('coupon_type_id') != -1) this.couponTypeId = item.split('-')[1];
						if (item.indexOf('source_member') != -1) this.source_member = item.split('-')[1];
					});
				}
			}
		},
		onShow() {
			// 刷新多语言
			this.$langConfig.refresh();
			this.getCoupon();

			this.receivedNum = 0;

			if (uni.getStorageSync('token')) {
				this.getMemberId();
				this.getReceivedNum();
			}
		},
		onHide() {
			this.couponBtnSwitch = false;
		},
		methods: {
			getCoupon() {
				this.$api.sendRequest({
					url: apiurls.getCouponInfoUrl,
					data: {
						coupon_id: this.couponTypeId
					},
					success: res => {
            if (this.$refs.loadingCover) this.$refs.loadingCover.hide();
						let data = res.data;
						if (data) {
							this.info = data;
							this.buttonRefresh();
						} else {
							this.$util.showToast({
								title: res.message,
								success: () => {
									// setTimeout(() => {
									// 	uni.navigateBack({
									// 		delta: 1
									// 	});
									// }, 1000);
								}
							});
						}
					},
					fail: res => {
						if (this.$refs.loadingCover) this.$refs.loadingCover.hide();
					}
				});
			},
			receiveGoodsCoupon(couponTypeId) {
				if (!uni.getStorageSync('token')) {
          this.$util.redirectTo('/pages/member/index/index', {},'redirectTo');
					return;
				}

				if (!this.isCanReceive) return;

				if (this.couponBtnSwitch) return;
				this.couponBtnSwitch = true;

				if(this.info.status==3){
          this.$util.redirectTo('/otherpages/shop/home/<USER>', {},'redirectTo');
          this.couponBtnSwitch = false;
        }else{
          this.$api.sendRequest({
            url: apiurls.receivecouponUrl,
            data: {
              coupon_id: couponTypeId,
              get_type: 2 //获取方式:1订单2.直接领取3.活动领取
            },
            success: res => {
              var data = res.data;
              let msg = res.message;
              if (res.code == 0) {
                msg = '领取成功';
                this.receivedNum += 1;
                this.info.status=3
                this.buttonRefresh();
              }
              this.$util.showToast({
                title: msg
              });
              this.couponBtnSwitch = false;
            },
            fail: res => {
              this.couponBtnSwitch = false;
            }
          });
        }

			},
			getMemberId() {
				this.$api.sendRequest({
					url: '/api/member/id',
					success: res => {
						if (res.code >= 0) {
							this.memberId = res.data;
						}
					}
				});
			},
			getReceivedNum() {
				this.$api.sendRequest({
					url: '/coupon/api/coupon/receivedNum',
					data: {
						coupon_type_id: this.couponTypeId
					},
					success: res => {
						if (res.code >= 0) {
							this.receivedNum = res.data;
							this.buttonRefresh();
						}
					}
				});
			},
			buttonRefresh() {
				this.button = '点击领取优惠券';
				if(this.info.status==1){
          this.button = '点击领取优惠券';
        }else if(this.info.status==-1 || this.info.status==2){
          this.button = '活动已结束';
        }else if(this.info.status==3){
          this.button = '立即使用';
        }else if (this.info.count == this.info.lead_count) {
					this.button = '来迟了该优惠券已被领取完了';
				} else if (this.info.max_fetch == 0 || this.receivedNum == 0) {
					this.button = '点击领取优惠券';
				} else if (this.info.max_fetch > this.receivedNum) {
					this.button = '点击领取优惠券';
				} else {
					this.button = '该优惠券领取已达到上限';
				}
			}
		},
		computed: {
      //vueX页面主题
      themeStyle() {
        return 'theme-' + this.$store.state.themeStyle
      },
			isCanReceive: {
				get() {
          if(this.info.status==1){
            return true;
          }else if(this.info.status==-1 || this.info.status==2){
            return false;
          }else if(this.info.status==3){
            return true;
          } else if (this.info.count == this.info.lead_count) {
						return false;
					} else if (this.info.max_fetch == 0 || this.receivedNum == 0) {
						return true;
					} else if (this.info.max_fetch > this.receivedNum) {
						return true;
					} else {
						return false;
					}
				}
			}
		},
		/**
		 * 自定义分享内容
		 * @param {Object} res
		 */
		// onShareAppMessage(res) {
		// 	var title = '恭喜您获得一张' + this.info.coupon_name + '的优惠券';
		// 	// if (this.info.range_type == 1) {
		// 	// 	title += `,满${this.info.at_least}全场可用`;
		// 	// } else {
		// 	// 	title += `,满${this.info.at_least}部分商品可用`;
		// 	// }
		// 	var path = '/otherpages/goods/coupon_receive/coupon_receive?coupon_type_id=' + this.couponTypeId;
		// 	if (this.memberId) path += '&source_member=' + this.memberId;
		// 	return {
		// 		title: title,
		// 		// imageUrl: this.$util.img(this.goodsDetail.img_list[0].pic_cover_big),
		// 		path: path,
		// 		success: res => {},
		// 		fail: res => {}
		// 	};
		// }
	};
</script>

<style lang="scss">
	// [data-theme] {
		page {
			height: 100%;
		}

		.container {
			background: #fff;
			padding: 0;
			height: 100%;
		}

		.content {
			height: 100%;
      padding: 24rpx 24rpx 0 24rpx;
      box-sizing: border-box;
		}

		.wx-code {
			position: relative;
			margin: 0 auto;
			top: 34%;
			width: 320rpx;
			height: 320rpx;
			text-align: center;
			padding: 14rpx;
			box-shadow: 0 0 40rpx -2rpx;
		}

		.wx-code image {
			width: 320rpx;
			height: 320rpx;
		}

		.get-coupon {
			position: relative;
			margin: 0 auto;
			width: 560rpx;
			top: 37%;
			text-align: center;
			height: 366rpx;

			.coupon-name {
				font-size: $ns-font-size-lg;
			}
		}

		.get-span {
			display: flex;
      justify-content: center;
			border-radius: 6rpx;
			margin-top: 60rpx;
			margin-bottom: 20rpx;
			color: #fff;
      button{
        width: 654rpx;
        height: 80rpx;
        line-height: 80rpx;
        margin: 0;
        &.invalid{
          color: white;
        }
      }
		}

		.get-coupon .tips {
			height: 40rpx;
		}

		.coupon-price {
			font-size: 52rpx;
			height: 76rpx;
		}

		//button {
		//	box-shadow: 1px 2px 5px rgba(0, 0, 0, 0.3);
		//}
  button[type='primary'][disabled]{
    background: #CCCCCC!important;
  }
    .coupon{
      display: flex;
      justify-content: center;
      &-one{
        background-size: 100% 100%;
        height: 200rpx;
        display: flex;
        justify-content: space-between;
        align-items: center;
        &-left{
          width: 218rpx;
          height: 100%;
          display: flex;
          flex-direction: column;
          justify-content: center;
          align-items: center;
          &-money{
            font-size: 52rpx;
            font-weight: 500;
            color: #F2270C;
            text{
              font-size: 28rpx;
            }
          }
          &-tip{
            font-size: 24rpx;
            font-weight: 500;
            color: #F2270C;
          }
        }
        &-right{
          width: 484rpx;
          height: 100%;
          display: flex;
          flex-direction: column;
          align-items: flex-start;
          padding-left: 24rpx;
          box-sizing: border-box;
          &-title{
            font-size: 30rpx;
            font-weight: bold;
            color: #333333;
            margin-top: 30rpx;
            margin-bottom: 42rpx;
          }
          &-date{
            font-size: 22rpx;
            font-weight: 400;
            color: #999999;
          }
        }
      }
    }
    .coupon-one-invalid{
      .coupon-one-left-money{
        color: white;
      }
      .coupon-one-left-tip{
        color: white;
      }
    }
	// }
</style>
