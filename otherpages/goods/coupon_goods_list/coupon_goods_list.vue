<template>
  <view class="receive-box" :data-theme="themeStyle" :style="[themeColorVar]">
    <!-- #ifdef MP-WEIXIN -->
    <uni-nav-bar left-icon="back" :border="false" @clickLeft="$util.goBack" :fixed="true" :statusBar="true" backgroundColor="transparent" leftText="优惠券" color="#fff">
    </uni-nav-bar>
    <!-- #endif -->
    <!-- #ifdef H5 -->
    <uni-nav-bar left-icon="back" :border="false" @clickLeft="$util.goBack" :fixed="true" :statusBar="true" backgroundColor="transparent" leftText="优惠券" color="#fff" v-if="isOnXianMaiApp">
    </uni-nav-bar>
    <!-- #endif -->
    <view class="header">
      <image :src="$util.img('public/static/youpin/coupon_header.png')" class="header-bg"></image>
      <view class="header-title">满{{Number(couponInfo.at_least)}}减{{Number(couponInfo.money)}}</view>
      <view class="header-subtitle">{{couponInfo.use_scenario_text}}</view>
      <view class="header-time">
        <image :src="$util.img('public/static/youpin/calendar-icon.png')" class="header-time-icon"></image>
        有效期至：<text class="header-time-text">{{couponInfo.end_time}}</text>
      </view>
    </view>
    <!-- 商品列表 -->
    <view class="content" v-if="goodList.length != 0 && !emptyShow">
      <view class="good-box">
        <view class="good-list" v-for="(item, index) in goodList" :key="index" @click="detailFun(item)">
          <view class="item-image">
            <image @error="errorFun(item)" :src="$util.img(item.goods_image.split(',')[0])" mode="aspectFill" />
            <image :src="$util.img('public/static/youpin/product-sell-out.png')" class="over" v-if="item.goods_stock == 0"></image>
          </view>
          <view class="info-box">
            <view class="title">{{item.goods_name}}</view>
            <view class="info-box-info">
              <view class="info-box-info-left">
                <view class="info-box-info-left-market">￥{{item.market_price}}</view>
                <view class="info-box-info-left-price"><text class="info-box-info-left-price-symbol">￥</text>{{item.retail_price}}</view>
              </view>
              <view class="info-box-info-right">
                <image :src="$util.img('public/static/youpin/card-white.png')" class="info-box-info-right-img"></image>
              </view>
            </view>
            <!--              <view class="sold hide-sales">已售{{item.sale_num}}件</view>-->
            <!--              <view class="price">-->
            <!--                <view><text>￥</text>{{item.retail_price}}</view>-->
            <!--                <view>￥{{item.market_price}}</view>-->
            <!--              </view>-->
          </view>
        </view>
      </view>
      <view class="empty-list-text" v-if="scrollLoading">没有更多内容</view>
    </view>
    <view class="empty" v-else>
      <image :src="$util.img('public/static/youpin/empty_coupon_good.png')"></image>
      <view>商品备货中，请稍候...</view>
    </view>
    <!-- 下拉加载动画 -->
    <ns-loading v-if="showLoading"></ns-loading>
    <!-- 返回顶部 -->
    <image :src="$util.img('public/static/youpin/to-top.png')" class="to-top" v-show="showTop" @click="scrollToTopNative"></image>

    <!-- 授权登录弹窗 -->
    <yd-auth-popup ref="ydauth"></yd-auth-popup>
    <ns-login ref="login"></ns-login>
    <!-- <uni-coupon-pop ref="couponPop"></uni-coupon-pop> -->

    <loading-cover ref="loadingCover"></loading-cover>
  </view>
</template>

<script>
// #ifdef H5
import {isOnXianMaiApp} from "@/common/js/h5/appOP";
// #endif
import scroll from '@/common/mixins/scroll-view.js'
import scrollLoading from '@/common/mixins/scroll-loading.js'
import toTop from '@/components/toTop/toTop.vue'
import system from "../../../common/js/system";
import UniNavBar from "../../../components/uni-nav-bar/uni-nav-bar.vue";
import golbalConfig from "../../../common/mixins/golbalConfig";
export default {
  name: "coupon_goods_list",
  components: {
    UniNavBar,
    toTop
  },
  data() {
    return {
      goodList: [],
      goodscoupon_type_id: '',
      couponInfo: {},
      couponStatus: {},
      otherGoods: false,  //其他商品
      limitGoods: false,   //是否是限定商品
      emptyShow: false,
      token: uni.getStorageSync('token') || '',
      // #ifdef H5
      isOnXianMaiApp:isOnXianMaiApp,
      // #endif
    }
  },
  onLoad({ goodscoupon_type_id }) {
    this.goodscoupon_type_id = goodscoupon_type_id
  },
  async onShow() {
    // 刷新多语言
    this.$langConfig.refresh();
    await system.wait_staticLogin_success();
    // this.showLoading = true
    this.goodListFun()
    this.getInit()
  },
  methods: {
    getInit() {
      if(!this.otherGoods) {
        this.$api.sendRequest({
          url:this.$apiUrl.goodsCouponList,
          data: {
            page: this.page,
            page_size: this.page_size,
            goodscoupon_type_id: this.goodscoupon_type_id
          },
          success: res => {
            this.hideLoading()
            let data = res.data.list
            if(this.couponInfo.use_scenario != 1 && data.length == 0 && this.page == 1) {
              this.emptyShow = true
              if (this.$refs.loadingCover) this.$refs.loadingCover.hide();
              return false
            }
            if(data.length) {
              this.limitGoods = true
              this.page ++;
              this.goodList = this.goodList.concat(data); //追加新数据
              this.$buriedPoint.exposeGoods(data)
              if (this.$refs.loadingCover) this.$refs.loadingCover.hide();
            }else{
              if(!this.limitGoods){
                this.otherGoods = true
                this.goodsGuess()
              }else{
                this.scrollLoading = true
                if (this.$refs.loadingCover) this.$refs.loadingCover.hide();
              }
            }
          },
          fail: res => {
            if (this.$refs.loadingCover) this.$refs.loadingCover.hide();
          }
        });
      }else{
        this.goodsGuess()
      }
    },
    goodListFun() {
      this.$api.sendRequest({
        url:this.$apiUrl.goodsCouponTypeinfo,
        data: {
          goodscoupon_type_id: this.goodscoupon_type_id
        },
        success: res => {
          uni.hideLoading();
          this.couponInfo = res.data
          this.hideLoading()
        },
        fail: res => {
          if (this.$refs.loadingCover) this.$refs.loadingCover.hide();
        }
      })
    },
    goodsGuess() {
      this.$api.sendRequest({
        url: this.$apiUrl.goodsListUrl,
        data: {
          page: this.page,
          page_size: this.page_size
        },
        success: res => {
          this.hideLoading()
          if (this.$refs.loadingCover) this.$refs.loadingCover.hide();
          let data = res.data.list
          if(data.length) {
            this.page ++;
            this.goodList = this.goodList.concat(data); //追加新数据
          }else{
            this.scrollLoading = true
          }
        }
      })
    },
    detailFun(e) {
      uni.navigateTo({
        url:`/pages/goods/detail/detail?sku_id=${e.sku_id}`
      })
    },
    errorFun(e) {
      this.goodList.forEach(v => {
        if(v.goods_id == e.goods_id){
          v.goods_image = "https://youpin-dev.jiufuwangluo.com:8443/upload/default/default_img/goods.png"
        }
      })
    },
    /**
     *分享参数组装
     */
    getSharePageParams() {
      return this.$util.unifySharePageParams('/otherpages/shop/home/<USER>', this.shareTitle || '先迈商城',
          '',{}, this.$util.img(this.shareImg) || this.$util.img('public/static/youpin/home_share.jpg'))
    }
  },
  mixins: [scroll, scrollLoading,golbalConfig],
  /**
   * 自定义分享内容
   * @param {Object} res
   */
  onShareAppMessage(res) {
    let { title, link, imageUrl, query } = this.getSharePageParams()
    return this.$buriedPoint.pageShare(link , imageUrl, title);
  },
  // // 分享
  // onShareAppMessage(res) {
  //   var title = '优惠券活动商品,一起来购物吧';
  //   var path = `/otherpages/goods/coupon_goods_list/coupon_goods_list?goodscoupon_type_id=${this.goodscoupon_type_id}`;
  //   return {
  //     title: title,
  //     path: path,
  //     success: res => {},
  //     fail: res => {}
  //   };
  // },
  // // 分享到微信
  // onShareTimeline(res){
  //   return {
  //     title: '优惠券活动商品,一起来购物吧',
  //     query: {},
  //     success: res => {},
  //     fail: res => {}
  //   };
  // }
}
</script>

<style lang="scss" scoped>
.receive-box{
  min-height: 100vh;
  background: rgba(250, 250, 250, 1);
  .header{
    width: 100%;
    height: 582rpx;
    background: linear-gradient(180deg, var(--custom-brand-color) 0%, var(--custom-brand-color-80) 40.98%, rgba(252, 47, 33, 0.1) 83.34%, rgba(255, 30, 0, 0) 100%);
    position: relative;
    padding-top: 182rpx;
    box-sizing: border-box;
    &-bg{
      width: 722rpx;
      height: 198rpx;
      position: absolute;
      left: 28rpx;
      bottom: 202rpx;
    }
    &-title{
      font-size: 64rpx;
      font-weight: 500;
      line-height: 89.6rpx;
      color: rgba(255, 255, 255, 1);
      text-align: center;
      position: relative;
    }
    &-subtitle{
      font-size: 32rpx;
      font-weight: 400;
      letter-spacing: 12rpx;
      line-height: 44.8rpx;
      color: rgba(255, 255, 255, 1);
      text-align: center;
      position: relative;
    }
    &-time{
      position: relative;
      margin: 0 auto;
      margin-top: 36rpx;
      width: 710rpx;
      height: 80rpx;
      font-size: 28rpx;
      font-weight: 400;
      line-height: 32.82rpx;
      color: rgba(56, 56, 56, 1);
      display: flex;
      align-items: center;
      padding-left: 36rpx;
      box-sizing: border-box;
      background-color: white;
      border-radius: 80rpx;
      &-icon{
        width: 36rpx;
        height: 36rpx;
        margin-right: 14rpx;
      }
      &-text{
        font-size: 28rpx;
        font-weight: 400;
        line-height: 32.82rpx;
        color: var(--custom-brand-color);
      }
    }
  }
  .content{
    padding: 0 20rpx;
    box-sizing: border-box;
    margin-top: -128rpx;
    .good-box{
      display: flex;
      flex-wrap: wrap;
      justify-content: space-between;
      align-items: flex-start;
      .good-list{
        width: 344rpx;
        background-color: #fff;
        border-radius: 20rpx;
        overflow: hidden;
        margin-top: 20rpx;
        image{
          display: inherit;
          width: 100%;
          height: 344rpx;
          border-radius: 20rpx;
        }
        .item-image{
          position: relative;
          .over{
						width: 150rpx;
						height: 150rpx;
						position: absolute;
						left: 50%;
						top: 50%;
						transform: translate(-50%, -50%);
					}
        }
        .info-box{
          padding: 20rpx;
          box-sizing: border-box;
          .title{
            font-size: 32rpx;
            font-weight: 400;
            line-height: 44rpx;
            color: rgba(56, 56, 56, 1);
            text-overflow: ellipsis;
            display: -webkit-box;
            -webkit-box-orient: vertical;
            -webkit-line-clamp: 2;
            overflow: hidden;
            min-height: 88rpx;
          }
          &-info{
            display: flex;
            justify-content: space-between;
            align-items: center;
            &-left{
              display: flex;
              flex-direction: column;
              align-items: flex-start;
              &-market{
                font-size: 28rpx;
                font-weight: 400;
                line-height: 32.82rpx;
                text-decoration-line: line-through;
                color: rgba(166, 166, 166, 1);
              }
              &-price{
                font-size: 32rpx;
                font-weight: 700;
                line-height: 37.5rpx;
                color: var(--custom-brand-color);
                &-symbol{
                  font-size: 24rpx;
                }
              }
            }
            &-right{
              width: 120rpx;
              height: 60rpx;
              border-radius: 100rpx;
              background: var(--custom-brand-color);
              display: flex;
              justify-content: center;
              align-items: center;
              &-img{
                width: 40rpx;
                height: 40rpx;
              }
            }
          }
        }
      }
    }
  }
  .empty{
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 100rpx 0 0;
    image{
      width: 402rpx;
      height: 282rpx;
    }
    view{
      font-size: 32rpx;
      line-height: 44rpx;
      color: #999999;
      margin-top: 42rpx;
    }
  }
  .to-top{
    width: 144rpx;
    height: 152rpx;
    position: fixed;
    right: 0;
    bottom: 82rpx;
  }
}

/deep/ .uni-navbar{
  position: fixed;
  z-index: 999;
}
</style>
