<template>
	<view :class="themeStyle" :style="[themeColorVar]">
		<view class="content" :style="{paddingTop: (navHeight+statusBarHeight) + 'px'}">
<!--			<view class="cate-search">-->
<!--				<view class="search-box">-->
<!--					<text class="iconfont iconIcon_search"></text>-->
<!--					<input class="uni-input" maxlength="50" v-model="inputValue" confirm-type="search" @focus="inputFocus" @input="inputInput"-->
<!--					 @confirm="search('user_input')" :placeholder="$lang('inputPlaceholder')" placeholder-style="color:#CCCCCC" />-->
<!--          <text class="search-close iconfont iconclose" @click.stop="clearSearch()" v-if="inputValue"></text>-->
<!--				</view>-->
<!--				<view class="text" @click="back()">取消</view>-->
<!--			</view>-->
      <view class="search-header" :style="{ height: (navHeight+statusBarHeight) + 'px'}">
        <view class="search-header-inner" :style="{'top': statusBarHeight+'px',height: navHeight+'px', lineHeight: navHeight+'px'}">
          <text class="iconfont iconback_light" @click="$util.goBack"></text>
          <diy-associate-search :associate-top="(navHeight+statusBarHeight) + 'px'" @confirm="searchConfirm"></diy-associate-search>
        </view>
      </view>
			<view class="search-content">
				<!-- 历史搜索 -->
				<view class="history" v-if="historyList.length">
					<view class="history-box">
						<view class="history-top">
              <view class="title ns-font-size-base"><image :src="history_svg" class="history-top-img"></image>{{ $lang('history') }}</view>
              <image :src="$util.img('public/static/youpin/clear.png')" class="history-top-clear" @click="openPopup('all')"></image>
						</view>
						<view class="history-bottom">
							<view class="history-li" v-for="(item, index) in historyList" :key="index" @click="otherSearch(item,'history')"
							 @longpress.stop="openPopup('item',item)" v-if="index<15">
								<button type="primary" class="btn-disabled overtext-hidden-one" size="mini">{{ item }}</button>
							</view>
						</view>
					</view>
				</view>
				<!--热门词-->
				<view class="history" v-if="hotWordsList.length">
					<view class="history-box">
						<view class="history-top">
							<view class="title ns-font-size-base"><image :src="hot_svg" class="history-top-img"></image>推荐搜索</view>
							<!--              <view class="icon iconfont iconicon7" @click="deleteHistoryList"></view>-->
						</view>
						<view class="history-bottom">
							<view class="history-li" v-for="(item, index) in hotWordsList" :key="index" @click="otherSearch(item.word,'recommend')">
								<button type="primary" class="btn-disabled" size="mini">{{ item.word }}</button>
							</view>
						</view>
					</view>
				</view>
			</view>
			<view><nsGoodsRecommend ref="goodrecommend"></nsGoodsRecommend></view>
		</view>

		<!-- 提示弹窗 -->
		<diy-uni-popup ref="popup" :text="type=='all'?'确定要删除所有搜索历史吗？':'确定要删除该搜索历史吗？'" @confirm="type=='all'?deleteHistoryList():deleteItem(selectItem)"></diy-uni-popup>
    <!-- 返回顶部 -->
    <to-top v-if="showTop" @toTop="scrollToTopNative()"></to-top>
    <loading-cover ref="loadingCover"></loading-cover>
	</view>
</template>

<script>
	import apiurls from '@/common/js/apiurls.js'
	import uniPopup from '@/components/uni-popup/uni-popup.vue';
	import nsGoodsRecommend from '@/components/ns-goods-recommend/ns-goods-recommend.vue';
  import toTop from '@/components/toTop/toTop.vue'
  import scroll from '@/common/mixins/scroll-view.js';
  import globalConfig from "../../../common/mixins/golbalConfig";
  import system from "../../../common/js/system";
  import diyAssociateSearch from "../../components/diy-associate-search/diy-associate-search.vue";
	export default {
		components: {
			uniPopup,
			nsGoodsRecommend,
      toTop,
      diyAssociateSearch
		},
    mixins: [scroll,globalConfig],
		data() {
			return {
				inputValue: '', //搜索框的值
				historyList: [], //历史搜索记录
				searchList: [], //搜索发现列表
				showSearch: true, //是否展示搜索发现
				hotWordsList: [], //热门搜索
				type: 'all',
				selectItem: '',
        history_svg: '',
        hot_svg: '',
        statusBarHeight:8,
        navHeight: 40,
        search_report_list:[]
			};
		},
		async onLoad(options) {
      await system.wait_staticLogin_success()
      // #ifdef MP-WEIXIN
      // 参考文档  https://juejin.cn/post/7076705501764911118
      // 获取微信胶囊的位置信息 width,height,top,right,left,bottom
      const custom = uni.getMenuButtonBoundingClientRect()
      let res = uni.getSystemInfoSync()
      this.statusBarHeight = res.statusBarHeight
      // 导航栏高度(标题栏高度) = 胶囊高度 + (顶部距离 - 状态栏高度) * 2
      this.navHeight = custom.height + (custom.top - this.statusBarHeight) * 2
      // #endif

			if (options.keyword) this.inputValue = options.keyword;
			uni.getStorageSync('search') ? '' : uni.setStorageSync('search', []);
      let color = this.$util.colorToHex(this.$store.state.themeColorVar['--custom-brand-color']).slice(1);
      this.history_svg = encodeURI(this.$util.img( `api/website/svgChangeFillColor?svg_name=history&color=${color}`))
      this.hot_svg = encodeURI(this.$util.img( `api/website/svgChangeFillColor?svg_name=hot&color=${color}`))
			await this.goodsHotWords()
      if (this.$refs.loadingCover) this.$refs.loadingCover.hide();
		},
		async onShow() {
      await system.wait_staticLogin_success()
			// 刷新多语言
			this.$langConfig.refresh();
			this.findHistoryList();
		},
		computed: {
			//vueX页面主题
			themeStyle() {
				return 'theme-' + this.$store.state.themeStyle
			}
		},
    onReachBottom(){
      this.$refs.goodrecommend.scrollPage()
    },
		methods: {
			//获取历史搜索记录
			findHistoryList() {
				this.historyList = uni.getStorageSync('search').reverse();
			},
			//删除所有历史记录
			deleteHistoryList() {
				uni.setStorageSync('search', []);
				this.findHistoryList();
				this.$refs.popup.closePopup()
			},
			openPopup(type, e) {
				this.type = type;
				if (e) this.selectItem = e;
				this.$refs.popup.open()
			},
			//删除历史记录的某一项
			deleteItem(e) {
				let array = uni.getStorageSync('search');
				let newArr = array.filter(v => {
					return v != e;
				});
				uni.setStorageSync('search', newArr);
				this.findHistoryList();
				this.$refs.popup.closePopup()
			},
			//input框获取焦点事件
			inputFocus(e) {
				this.showScroll = false;
				if (this.inputValue.trim() != '') this.dataList = [];
			},
			//input框输入事件
			inputInput(e) {
				if (e.detail.value) {}
			},
      clearSearch(){
        this.inputValue = ''
      },
			//点击其他列表搜索
			otherSearch(e,type) {
				this.inputValue = e;
				this.search(type);
			},
			back() {
				uni.navigateBack({
					delta: 1
				});
			},
      searchConfirm(e) {
        let value = e.value;
        let diy_input_type = e.type
        if(diy_input_type == 'user_input'){
          this.inputValue = value
          this.search(e.type)
        }else if(diy_input_type == 'associate'){
          switch (value.source){
            case 'goods':
              this.inputValue = value.keyword_text.trim()
              this.search(diy_input_type)
              break;
            case 'seckill':
              this.$buriedPoint.diyReportSearchInteractionEvent({diy_input_type,diy_keyword:value.keyword_text.trim()})
              this.$util.diyCompateRedirectTo({
                wap_url: `/promotionpages/new_seckill/list/list?seckill_id=${value.source_id}`
              });
              break;
            case 'topic':
              this.$buriedPoint.diyReportSearchInteractionEvent({diy_input_type,diy_keyword:value.keyword_text.trim()})
              this.$util.diyCompateRedirectTo({
                wap_url: `/promotionpages/task/list/list?topic_id=${value.source_id}`
              })
              break;
            case 'micropage':
              this.$buriedPoint.diyReportSearchInteractionEvent({diy_input_type,diy_keyword:value.keyword_text.trim()})
              this.$util.diyCompateRedirectTo({
                wap_url: `/otherpages/diy/diy/diy?name=${value.micropage_name}`
              })
              break;
          }
        }
      },
			//搜索
			search(type) {
				if (this.inputValue.trim() != '') {
					this.showScroll = true;

					// 对历史搜索处理,判断有无,最近搜索显示在最前
					let historyList = uni.getStorageSync('search');
					let array = [];
					if (historyList.length) {
						array = historyList.filter(v => {
							return v != this.inputValue.trim();
						});
						array.push(this.inputValue.trim());
					} else {
						array.push(this.inputValue.trim());
					}
					uni.setStorageSync('search', array);
					let shop_id = uni.getStorageSync('shop_id');
          this.$buriedPoint.diyReportSearchInteractionEvent({diy_input_type:type,diy_keyword:this.inputValue.trim()})
          if(type == 'history' || type == 'recommend'){
            this.searchReport(type,this.inputValue.trim(),this.inputValue.trim())
          }
					this.$util.redirectTo('/otherpages/shop/list/list', {
						keyword: this.inputValue.trim(),
						entrance: 'search',
						site_id: shop_id
					});
				} else {
					this.$util.showToast({
						title: '搜索内容不能为空哦'
					});
				}
			},
			async goodsHotWords() {
        try{
          let res = await this.$api.sendRequest({
            url: apiurls.goodsHotWordsUrl,
            async: false
          })
          if (res.code == 0) {
            this.hotWordsList = res.data
          }
        }catch (e) {

        }

			},
      /**
       *分享参数组装
       */
      getSharePageParams() {
        return this.$util.unifySharePageParams('/otherpages/shop/home/<USER>', this.shareTitle || '先迈商城',
            '',{}, this.$util.img(this.shareImg) || this.$util.img('public/static/youpin/home_share.jpg'))
      },
      /**
       * 搜索上报
       * @param source  来源：search直接搜索，guess联想点击搜索，history历史搜索
       * @param search_text  搜索词
       * @param source_search_text  原搜索词，联想点击前的搜索词
       * @returns {Promise<void>}
       */
      async searchReport(source="",search_text, source_search_text){
        let key = `${source}-${search_text}`
        if(this.search_report_list.includes(key)){
          return
        }
        this.search_report_list.push(key)
        try {
          let res = await this.$api.sendRequest({
            url: apiurls.searchReportUrl,
            data:{
              source,
              search_text,
              source_search_text
            },
            async: false
          })
        }catch (e) {

        }
      }
		},
    /**
     * 自定义分享内容
     * @param {Object} res
     */
    onShareAppMessage(res) {
      let { title, link, imageUrl, query } = this.getSharePageParams()
      return this.$buriedPoint.pageShare(link , imageUrl, title);
    },
	};
</script>

<style lang="scss">
	// [data-theme] {
	.content {
		width: 100vw;
		/* #ifdef MP */
		height: 100vh;
		/* #endif */
		/* #ifdef H5 */
		height: calc(100vh - 44px - 50px - env(safe-area-inset-bottom) - var(--status-bar-height));
		/* #endif */
		/* #ifdef APP-PLUS */
		height: calc(100vh - 44px - env(safe-area-inset-bottom));
		/* #endif */
		background: #ffffff;
	}

	.cate-search {
		width: 100%;
		background: #ffffff;
		padding: 20rpx 0;
		padding-left: 30rpx;
		box-sizing: border-box;
		display: flex;

		input {
			font-size: $ns-font-size-base;
			height: 60rpx;
			line-height: 60rpx;
			width: calc(100% - 140rpx);
		}

		text {
			font-size: 40rpx;
			color: $ns-text-color-gray;
			width: 64rpx;
			text-align: center;

		}

		.text {
			color: #333;
			font-size: 28rpx;
			width: 115rpx;
			text-align: center;
		}

		.search-box {
			width: 605rpx;
			height: 60rpx;
			background: $uni-bg-color-grey;
			display: flex;
			align-items: center;
			border-radius: 40rpx;
      position: relative;
		}
	}
  .search-header{
    width: 100%;
    overflow: hidden;
    position: fixed;
    top: 0;
    left: 0;
    z-index: 10;
    background-color: white;
    &-inner{
      position: absolute;
      left: 20rpx;
      display: flex;
      align-items: center;
      .iconfont{
        font-size: 32rpx;
        color: rgba(56, 56, 56, 1);
        margin-right: 20rpx;
        font-weight: bold;
      }
    }
  }

	.search-content {
		box-sizing: border-box;
		background: #ffffff;
	}

	.history {
		width: 100%;
		padding: 0 30rpx;
		margin-bottom: 20rpx;
		box-sizing: border-box;

		.history-box {
			width: 100%;
			height: 100%;
			background: #ffffff;
			border-radius: 15rpx;
			padding: 10rpx 0;
			box-sizing: border-box;
			overflow: hidden;

			.history-top {
				width: 100%;
				height: 60rpx;
				display: flex;
				justify-content: space-between;
				align-items: center;
				margin-bottom: 10rpx;
        &-img{
          width: 32rpx;
          height: 32rpx;
          margin-right: 12rpx;
        }
				.title {
          font-size: 32rpx;
          font-weight: 400;
          line-height: 37.5rpx;
          color: rgba(56, 56, 56, 1);
          display: flex;
          align-items: center;
				}
        &-clear{
          width: 44rpx;
          height: 44rpx;
        }
			}

			.history-bottom {
				width: 100%;
        max-height: 144rpx;
        overflow: hidden;
				.history-li {
					display: inline-block;
					margin-right: 20rpx;
					margin-bottom: 15rpx;

					button[type='primary'] {
            background: rgba(250, 250, 250, 1)!important;
            font-size: 28rpx;
            font-weight: 400;
            height: 56rpx;
            line-height: 56rpx;
            color: rgba(128, 128, 128, 1);
            max-width: 680rpx;
					}
				}
			}
		}

		.hidden-show {
			width: 100%;
			height: 70rpx;
			text-align: center;
			line-height: 70rpx;
		}
	}

	.search-alike {
		width: 100%;
		height: calc(100vh - 100rpx);

		padding: 0 $ns-padding;
		box-sizing: border-box;

		.alike-box {
			width: 100%;
			height: 100%;
			background: #ffffff;
			border-radius: $ns-padding;
			overflow: hidden;
		}

		.alike-li {
			width: 100%;
			height: 80rpx;
			padding: 0 $ns-padding;
			box-sizing: border-box;
			border-bottom: 2rpx solid #f5f5f5;
			display: flex;
			justify-content: space-between;
			align-items: center;
		}

		.iconjiantou-copy-copy-copy {
			font-size: 40rpx;
			transform: rotate(15deg);
		}
	}
  .search-close{
    width: 42rpx!important;
    height: 42rpx!important;
    line-height: 42rpx!important;
    background-color: #999;
    color: #fff!important;
    border-radius: 50%;
    font-size: 20rpx!important;
    z-index: 10;
    position: absolute;
    top: 50%;
    right: 20rpx;
    transform: translateY(-50%);
  }
	// }
</style>
