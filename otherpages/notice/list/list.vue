<template>
	<view :class="themeStyle">
		<mescroll-uni @getData="getData" refs="mescroll">
			<block slot="list">
				<view class="notice-list" v-if="dataList.length">
					<view class="notice-item" @click="jumpDetail(item.id)" v-for="(item, index) in dataList" :key="index">
						<view class="title-info">
							<text class="title" :class="item.is_top == 1 && index == 0 ? 'new ns-text-before' : ''">{{ item.title }}</text>
							<text class="release-time">{{ $util.timeStampTurnTime(item.create_time, 1) }}</text>
						</view>
						<view class="more">
							<text class="detail">{{ item.info }}</text>
							<view class="iconfont iconright"></view>
						</view>
					</view>
				</view>
				<view v-else class="cart-empty"><ns-empty :text="$lang('emptyText')" :isIndex="!1"></ns-empty></view>
				<loading-cover ref="loadingCover"></loading-cover>
			</block>
		</mescroll-uni>
	</view>
</template>

<script>
export default {
	data() {
		return {
			dataList: []
		};
	},
	onLoad() {},
	onShow() {
		// 刷新多语言
		this.$langConfig.refresh();
	},
	computed: {
		//vueX页面主题
		themeStyle() {
			return 'theme-' + this.$store.state.themeStyle;
		}
	},
	methods: {
		getData(mescroll) {
			this.$api.sendRequest({
				url: '/api/notice/page',
				data: {
					page_size: mescroll.size,
					page: mescroll.num
				},
				success: res => {
					let newArr = [];
					let msg = res.message;
					if (res.code == 0 && res.data) {
						newArr = res.data.list;
					} else {
						this.$util.showToast({
							title: msg
						});
					}
					mescroll.endSuccess(newArr.length);
					//设置列表数据
					if (mescroll.num == 1) this.dataList = []; //如果是第一页需手动制空列表
					this.dataList = this.dataList.concat(newArr); //追加新数据
					this.dataList.forEach(item => {
						item.info = item.content.replace(/<[^>]+>/g, '');
					});
					if (this.$refs.loadingCover) this.$refs.loadingCover.hide();
				},
				fail: res => {
					mescroll.endErr();
					if (this.$refs.loadingCover) this.$refs.loadingCover.hide();
				}
			});
		},
		jumpDetail(e) {
			this.$util.redirectTo('/otherpages/notice/detail/detail?notice_id=' + e);
		}
	},

	onShareAppMessage(res) {
		var title = '公告';
		var path = '/otherpages/notice/list/list';
		return {
			title: title,
			path: path,
			success: res => {},
			fail: res => {}
		};
	}
};
</script>

<style lang="scss">
/deep/ .fixed {
	position: relative;
	top: 0;
}
.cart-empty {
	position: relative;
	top: -50%;
	padding-top: 154px;
}
.notice-list {
	.notice-item {
		margin: $ns-margin;
		background: #fff;
		border-radius: 10rpx;
		padding: 32rpx 34rpx 23rpx 34rpx;
		box-sizing: border-box;
		display: flex;
		flex-direction: column;
		align-items: center;
		line-height: 1;

		.title-info {
			width: 100%;
			display: flex;
			justify-content: space-between;
			align-items: center;
			border-bottom: 1px solid #f3f3f3;
			padding-bottom: 15rpx;
			overflow: hidden;
			.title {
				flex: 4;
				color: #000;
				overflow: hidden;
				text-overflow: ellipsis;
				white-space: nowrap;
				display: block;
				align-items: center;
				font-size: $ns-font-size-base;

				&.new::after {
					font-family: 'iconfont';
					content: '\e600';
					font-size: 43rpx;
					line-height: 1;
					margin-left: 14rpx;
					vertical-align: middle;
				}
			}

			.release-time {
				flex: 3;
				overflow: hidden;
				text-overflow: ellipsis;
				white-space: nowrap;
				margin-left: 20rpx;
				color: $ns-text-color-gray;
				text-align: right;
				font-size: $ns-font-size-sm;
			}
		}

		.content {
			margin-top: 10rpx;
			display: inline-block;
			white-space: nowrap;
			overflow: hidden;
			text-overflow: ellipsis;
			width: 100%;
			color: $ns-text-color-gray;
			font-size: 20rpx;
			padding-bottom: 30rpx;
		}

		.more {
			width: 100%;
			display: flex;
			justify-content: space-between;
			padding-top: 24rpx;
			font-size: $ns-font-size-sm;
			align-items: center;

			.detail {
				width: 505rpx;
				color: #838383;
				font-size: $ns-font-size-sm;
				display: -webkit-box;
				-webkit-box-orient: vertical;
				-webkit-line-clamp: 1;
				overflow: hidden;
			}
		}
	}
}
</style>
