.page-title{
  width: 360rpx;
  overflow:hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  -o-text-overflow:ellipsis;
  text-align: center;
}
.diy-wrap {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
}
.diy-wrap-scroll{
  /deep/ .mescroll-uni-fixed{
    padding-bottom: 300rpx!important;
  }
}
/deep/ .mescroll-upwarp{
  min-height: 0;
  padding: 0;
}
.page-content{
  padding: 0 20rpx;
  .goods-list{
    position: initial;
  }
  .swiper-box{
    display: block;
    margin-top: 24rpx;
  }
}
/* 标题栏 */
.custom {
  background: #fff;
  display: flex;
  align-items: center;
  justify-content: center;
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;

  .iconfont {
    font-size: $ns-font-size-base + 12;
    color: #333;
    font-weight: bold;
    position: absolute;
    left: 20rpx;
  }

  .custom-navbar {
    display: flex;
    // border-radius: 30rpx;
    // background: #FFF4F4;
    width: 360rpx;
    align-items: center;

    .navbar-item {
      height: 60rpx;
      line-height: 60rpx;
      width: 100%;
      text-align: center;
      color: #333333;
      font-size: $ns-font-size-base + 2;
      // &.active{
      // 	background:$base-color;
      // 	color: #FFFFFF;
      // 	border-radius: 30rpx;
      // }
    }
  }
}
