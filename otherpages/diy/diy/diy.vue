<template>
	<view>
		<view class="diy-wrap" :style="[{ background: bgColor },themeColorVar]">
			<!-- #ifdef MP-WEIXIN -->
			<ns-navbar ref="nsNavbarRef" :title="pageTitle" :background="{ background:'#fff'}" :title-color="'#000000'" :border-bottom="false" z-index="1001" :customBack="customBack"></ns-navbar>
			<!-- #endif -->
			<!-- #ifdef H5 -->
			<uni-nav-bar left-icon="back" :border="false" @clickLeft="appGoBack" :fixed="true" :statusBar="false"
                   v-if="isOnXianMaiApp">
				<template>
					<view class="page-title">{{pageTitle}}</view>
				</template>
			</uni-nav-bar>
			<!-- #endif -->
			<mescroll-uni
				ref="mescroll"
				@getData="getData"
        :class="[themeStyle, openBottomNav && $util.getPlatform() == 'weapp'? 'diy-wrap-scroll': '']"
				class="diy-wrap"
        :top="topHeight+'px'"
        :bottom="openBottomNav ? '55px':'0'"
				@listenRefresh="listenRefresh"
        @scroll="scroll"
			>
				<block slot="list">
					<view class="index_bg" :style="'background:' + 'url(' + $util.img(bgUrl) + ') no-repeat 0 0/100%'">
						<view class="page-content" :class="Bulge?'active':''">
							<view v-for="(item, index) in diyData.value" :key="index">

								<!-- 搜索 -->
								<template v-if="item.controller == 'Search'">
									<diy-search :searchObj="searchObj" :sbgc="sbgc"></diy-search>
								</template>

								<!-- 广告 -->
								<template v-if="item.controller == 'ImageAds'">
									<diy-advertising-swiper class="swiper-box" :config="item" :ads="item.list" :imageError="imageError"></diy-advertising-swiper>
								</template>

								<!-- 频道模块 -->
								<template v-if="item.controller == 'GraphicNav'">
									<diy-channel-area :channels="item.list" :scrollSetting="item.scrollSetting">
									</diy-channel-area>
								</template>

                <!-- 幻灯片组件  -->
                <template v-if="item.controller == 'SlideShow'">
                  <diy-slide-show :value="item"></diy-slide-show>
                </template>

								<!-- 活动广告位 -->
								<template v-if="item.controller == 'ActivityAds'">
									<diy-activity-ads :value="item"></diy-activity-ads>
								</template>

								<!--  拼团专区  -->
								<template v-if="item.controller == 'Pintuan'">
									<diy-pintuan :value="{ backgroundColor:'#ffffff',item}" :dataList="pintuanList">
									</diy-pintuan>
								</template>

								<!-- 迈豆专区 -->
								<template v-if="item.controller == 'Maidou'">
									<diy-maidou :value="{item}" :dataList="maidou"
										:site-id="shop_id" @finish="listenRefresh"></diy-maidou>
								</template>

								<!-- 秒杀模块  -->
								<template v-if="item.controller == 'Seckill'">
									<diy-seckill :value="{ backgroundColor:'#ffffff',item}" :dataList="seckill"
										:site-id="shop_id" @finish="listenRefresh"></diy-seckill>
								</template>

                <!-- 直播卡片模块  -->
                <template v-if="item.controller == 'LiveInfo'">
                  <diy-live :value="item" :site-id="shop_id" ref="diyLiveRef">
                    <template slot="liveSubscribe">
                      <!-- #ifdef MP-WEIXIN -->
                      <subscribe
                          :width="86"
                          :height="32"
                          :room-id="item.room_id"
                          :font-size="12"
                          :color="'transparent'"
                          :background-color="'transparent'"
                          :custom-params="{}"
                          @subscribe="onSubscribe($event,item)"
                      >
                      </subscribe>
                      <!-- #endif -->
                    </template>
                  </diy-live>
                </template>

                <!--  发现好物  -->
                <template v-if="item.controller == 'Seeding'">
                  <diy-seeding :value="{ backgroundColor:'#ffffff',item}">
                  </diy-seeding>
                </template>

                <!--      商品专题模块        -->
                <template v-if="item.controller == 'ProductTopic'">
                  <diy-product-topic :config="item" :top-height="topHeight" :show-top="topHeight" :scroll-top="scrollTop" ref="diyProductTopicRef" @scrollToPoint="scrollToY"></diy-product-topic>
                </template>

                <!--      新品专区模块        -->
                <template v-if="item.controller == 'NewProductArea'">
                  <diy-new-product-area :config="item"></diy-new-product-area>
                </template>

								<!-- 店主推荐  -->
								<template v-if="item.controller == 'ShoperRecommand'">
									<diy-recommend-product :recommendGoods="recommend_goods" :imageError="imageError">
									</diy-recommend-product>
								</template>

								<!-- 推荐商品 -->
								<template v-if="item.controller == 'GoodsList'">
									<diy-goods-list class="goods-list" :products="products"></diy-goods-list>
								</template>

								<template v-if="item.controller == 'Text'">
									<!-- 文本 -->
									<diy-text :value="item" :site-id="shop_id"></diy-text>
								</template>

								<template v-if="item.controller == 'TextNav'">
									<!-- 文本导航 -->
									<diy-text-nav :value="item" :site-id="shop_id"></diy-text-nav>
								</template>

								<template v-if="item.controller == 'Notice'">
									<!-- 公告 -->
									<diy-notice :value="item" :site-id="shop_id"></diy-notice>
								</template>

<!--								<template v-if="item.controller == 'Bargain' && addonIsExit.bargain">-->
<!--									&lt;!&ndash; 砍价 &ndash;&gt;-->
<!--									<diy-bargain :value="item"></diy-bargain>-->
<!--								</template>-->

								<template v-if="item.controller == 'Title'">
									<!-- 顶部标题 -->
									<diy-title :value="item" :site-id="shop_id"></diy-title>
								</template>

								<template v-if="item.controller == 'RichText'">
									<!-- 富文本 -->
									<diy-rich-text :value="item"></diy-rich-text>
								</template>

								<template v-if="item.controller == 'PopWindow'">
									<!-- 弹出广告 -->
									<view @touchmove.prevent.stop class="PopWindow">
										<uni-popup ref="uniPopup" type="center" class="wap-floating" :maskClick="false">
											<view class="image-wrap"><image :src="$util.img(item.image_url)" mode="widthFix" @click="redirectTo(item.link)"></image></view>
											<text class="iconfont iconroundclose" @click="closeNum"></text>
										</uni-popup>
									</view>
								</template>

								<template v-if="item.controller == 'HorzLine'">
									<!-- 辅助线 -->
									<diy-horz-line :value="item"></diy-horz-line>
								</template>

								<template v-if="item.controller == 'HorzBlank'">
									<!-- 辅助空白 -->
									<diy-horz-blank :value="item"></diy-horz-blank>
								</template>

<!--								<template v-if="item.controller == 'AdminCoupon' && addonIsExit.coupon">-->
<!--									&lt;!&ndash; 优惠券 &ndash;&gt;-->
<!--									<diy-coupon :value="item" :site-id="shop_id"></diy-coupon>-->
<!--								</template>-->

								<template v-if="item.controller == 'RubikCube'">
									<!-- 魔方、橱窗 -->
									<diy-rubik-cube :value="item" :site-id="shop_id"></diy-rubik-cube>
								</template>

								<template v-if="item.controller == 'Video'">
									<!-- 视频 -->
                  <!-- #ifdef MP-WEIXIN -->
                  <diy-video :value="item">
                    <template v-slot:tx_video="{vid,height,borderRadius,isMuted}">
                      <player-component :vid="vid" :height="height" :autoplay="true" :usePoster="true" :loop="true" :muted="isMuted" :border-radius="borderRadius"></player-component>
                    </template>
                  </diy-video>
                  <!-- #endif -->
								</template>

<!--								<template v-if="item.controller == 'Groupbuy' && addonIsExit.groupbuy">-->
<!--									&lt;!&ndash; 团购 &ndash;&gt;-->
<!--									<diy-groupbuy :value="item" :site-id="shop_id"></diy-groupbuy>-->
<!--								</template>-->

<!--								<template v-if="item.controller == 'ShopInfo'">-->
<!--									&lt;!&ndash; 店铺信息 &ndash;&gt;-->
<!--									<diy-shop-info :value="item" :site-id="shop_id" :type="1"></diy-shop-info>-->
<!--								</template>-->

								<template v-if="item.controller == 'ShopSearch'">
									<!-- 店内搜索 -->
									<diy-shop-search :value="item" :site-id="shop_id"></diy-shop-search>
								</template>

								<template v-if="item.controller == 'RankList'">
									<!-- 店铺排行榜 -->
									<diy-shop-rank-list :value="item" :site-id="shop_id"></diy-shop-rank-list>
								</template>

								<template v-if="item.controller == 'ShopStore'">
									<!-- 门店列表 -->
									<diy-shop-store :value="item" :site-id="shop_id"></diy-shop-store>
								</template>

<!--								<template v-if="item.controller == 'GoodsCategory'">-->
<!--									&lt;!&ndash; 商品分类 &ndash;&gt;-->
<!--									<diy-goods-level-category :value="item" :bottom="windowHeight"></diy-goods-level-category>-->
<!--								</template>-->

<!--								<template v-if="item.controller == 'Wholesale' && addonIsExit.wholesale">-->
<!--									&lt;!&ndash; 商品批发 &ndash;&gt;-->
<!--									<diy-whole-sale :value="item"></diy-whole-sale>-->
<!--								</template>-->

<!--								<template v-if="item.controller == 'FenxiaoGoodsList' && addonIsExit.fenxiao">-->
<!--									&lt;!&ndash; 分销商品列表 &ndash;&gt;-->
<!--									<diy-fenxiao-goods-list :value="item" :site-id="shop_id"></diy-fenxiao-goods-list>-->
<!--								</template>-->
							</view>
							<!-- 站点关闭展示 -->
							<view @touchmove.prevent.stop class="PopWindow">
								<uni-popup ref="uniPopup" type="center" class="wap-floating" :maskClick="false">
									<view class="popup-box">
										<view class="close_title ns-margin-top">站点关闭</view>
										<view class="close_content">
											<scroll-view scroll-y="true" class="close_content_box">{{ webSiteInfo ? webSiteInfo.close_reason : '' }}</scroll-view>
										</view>
									</view>
								</uni-popup>
							</view>

							<loading-cover ref="loadingCover"></loading-cover>
						</view>
					</view>
				</block>
			</mescroll-uni>

			<diy-bottom-nav type="shop" :site-id="shop_id" v-if="openBottomNav"></diy-bottom-nav>
		</view>
	</view>
</template>

<script>
import system from "@/common/js/system.js";
import diy from "./js/diy.js";
import appInlineH5 from "@/common/mixins/appInlineH5";
import golbalConfig from "../../../common/mixins/golbalConfig";
// #ifdef H5
import {isOnXianMaiApp} from "@/common/js/h5/appOP";
// #endif

import diySearch from '@/otherpages/components/diy-search/diy-search.vue'
import diyAdvertisingSwiper from "@/otherpages/components/diy-advertising-swiper/diy-advertising-swiper"
import diyActivityAds from "@/otherpages/components/diy-activity-ads/diy-activity-ads"
import diyChannelArea from "@/otherpages/components/diy-channel-area/diy-channel-area"
import diyPintuan from '@/otherpages/components/diy-pintuan/diy-pintuan.vue'
import diyMaidou from "@/otherpages/components/diy-maidou/diy-maidou"
import diySeckill from '@/otherpages/components/diy-seckill/diy-seckill.vue'
import diyRecommendProduct from "@/otherpages/components/diy-recommend-product/diy-recommend-product"
import diyGoodsList from '@/otherpages/components/diy-goods-list/diy-goods-list.vue'
import diyTagProduct from "@/otherpages/components/diy-tag-product/diy-tag-product"
import uniPopup from '@/components/uni-popup/uni-popup.vue';


import diyHorzBlank from '@/components/diy-horz-blank/diy-horz-blank.vue';
import diyHorzLine from '@/components/diy-horz-line/diy-horz-line.vue';
// import diyCoupon from '@/components/diy-coupon/diy-coupon.vue';
import diyImgAds from '@/otherpages/components/diy-img-ads/diy-img-ads.vue';
import diyGraphicNav from '@/otherpages/components/diy-graphic-nav/diy-graphic-nav.vue';
import diyNotice from '@/otherpages/components/diy-notice/diy-notice.vue';
import diyRichText from '@/otherpages/components/diy-rich-text/diy-rich-text.vue';
import diyRubikCube from '@/otherpages/components/diy-rubik-cube/diy-rubik-cube';
import diyTextNav from '@/otherpages/components/diy-text-nav/diy-text-nav.vue';
import diyTitle from '@/otherpages/components/diy-title/diy-title.vue';
import diyVideo from '@/otherpages/components/diy-video/diy-video.vue';
import diyBottomNav from '@/components/diy-bottom-nav/diy-bottom-nav.vue';
// import diyBargain from '@/components/diy-bargain/diy-bargain.vue';
// import diyGroupBuy from '@/components/diy-groupbuy/diy-groupbuy.vue';
import diyShopRankList from '@/otherpages/components/diy-shop-rank-list/diy-shop-rank-list';
// import diyShopInfo from '@/components/diy-shop-info/diy-shop-info';
import nsNavbar from '@/components/ns-navbar/ns-navbar.vue'
import diyShopStore from '@/otherpages/components/diy-shop-store/diy-shop-store';
// import diyFenxiaoGoodsList from '@/components/diy-fenxiao-goods-list/diy-fenxiao-goods-list';
// import diyGoodsLevelCategory from '@/components/diy-goods-level-category/diy-goods-level-category.vue';
// import diyWholeSale from '@/components/diy-wholesale/diy-wholesale.vue'
import diyProductTopic from '@/otherpages/components/diy-product-topic/diy-product-topic.vue';
import diyNewProductArea from '@/otherpages/components/diy-new-product-area/diy-new-product-area.vue';
import diyLive from '@/otherpages/components/diy-live/diy-live.vue';
import diySeeding from "@/otherpages/components/diy-seeding/diy-seeding.vue";
import diySlideShow from "@/otherpages/components/diy-slide-show/diy-slide-show.vue";
import UniNavBar from "../../../components/uni-nav-bar/uni-nav-bar.vue";
export default {
	mixins: [diy, appInlineH5,golbalConfig],
  components:{
    UniNavBar,
    diySearch,            //搜索
    diyAdvertisingSwiper, //轮播
    diyActivityAds,       //活动广告
    diyChannelArea,       //图文导航
    diyPintuan,           //拼团
    diyMaidou,            //迈豆
    diySeckill,           //秒杀
    diyRecommendProduct,  //店主推荐
    diyGoodsList,         //商品列表
    diyTagProduct,        //类目商品列表
    uniPopup,             //弹窗

    diyHorzBlank,
    diyHorzLine,
    // diyCoupon,
    diyImgAds,
    diyGraphicNav,
    diyNotice,
    diyRichText,
    diyRubikCube,
    diyTextNav,
    diyTitle,
    diyVideo,
    diyBottomNav,
    // diyGroupBuy,
    diyShopRankList,
    // diyShopInfo,
    // diyBargain,
    diyShopStore,
    // diyFenxiaoGoodsList,
    // diyGoodsLevelCategory,
    nsNavbar,
    // diyWholeSale,
    diyProductTopic,
    diyNewProductArea,
    diyLive,
    diySeeding,
    diySlideShow
  },
	data() {
		return {
			diyData: {
				global: {},
				value: []
			},
			webSiteInfo: null,
			name: '',
			siteId: 0,
			city: '',
			windowHeight: 0,
			Bulge:false,
      shop_id: '',
			statusBarHeight: 0,
			navHeight: 0,
      bgUrl: '',
      shareTitle: '',
      shareImg: '',
			searchObj: null, // 搜索框
			sbgc:'',
      topHeight: 0,
      scrollTop:0,
			// #ifdef H5
			isOnXianMaiApp:isOnXianMaiApp,
			// #endif
		};
	},
	computed: {
		// 使用对象展开运算符将此对象混入到外部对象中
		themeStyle() {
			return 'theme-' + this.$store.state.themeStyle;
		},
		addonIsExit() {
			return this.$store.state.addonIsExit;
		},
		bgColor() {
			let str = '';
			if (this.diyData && this.diyData.global) {
				str = this.diyData.global.bgColor;
			}
			return str;
		},
		pageTitle(){
			let str = '微页面';
			if (this.diyData && this.diyData.global) {
				str = this.diyData.global.title;
			}
			return str;
		},
		openBottomNav(){
			let Bloon=false;
			if (this.diyData && this.diyData.global) {
				Bloon = this.diyData.global.openBottomNav;
			}
			return Bloon;
		},
		navBar(){
			let str="#ffffff"
			if(this.bgUrl){
				str='url(' + this.$util.img(this.bgUrl) + ') no-repeat 0 0/100%'
			}else if(this.bgColor){
				str=this.bgColor;
			}else{
				str="rgba(0,0,0,0)"
			}
			return str;
		}
	},
	onLoad(data) {
		this.name = data.name || '';
		this.siteId = data.site_id || 0;
		this.getHeight();
		if (this.name) {
			this.$langConfig.refresh();
			// this.refresh();

			if (data.source_member) uni.setStorageSync('source_member', data.source_member);
			// 小程序扫码进入
			if (data.scene) {
				var sceneParams = decodeURIComponent(data.scene);
				sceneParams = sceneParams.split('&');
				if (sceneParams.length) {
					sceneParams.forEach(item => {
						if (item.indexOf('source_member') != -1) uni.setStorageSync('source_member', item.split('-')[1]);
					});
				}
			}
      // #ifdef MP-WEIXIN
      // 参考文档  https://juejin.cn/post/7076705501764911118
      // 获取微信胶囊的位置信息 width,height,top,right,left,bottom
      const custom = uni.getMenuButtonBoundingClientRect()
      let res = uni.getSystemInfoSync()
      this.statusBarHeight = res.statusBarHeight
      // 导航栏高度(标题栏高度) = 胶囊高度 + (顶部距离 - 状态栏高度) * 2
      // this.navHeight = custom.height + (custom.top - this.statusBarHeight) * 2
      this.navHeight = this.$refs.nsNavbarRef.navbarHeight;
      // #endif
      // #ifdef H5
      if(this.isOnXianMaiApp){
        this.navHeight= 44;
      }else{
        this.navHeight = 0
      }
      // #endif
		} else {
			this.$util.redirectTo('/pages/index/index/index', {}, 'reLaunch');
		}
	},
	async onShow() {
    // 分享静默登录
    await system.wait_staticLogin_success();

    let shop_id = uni.getStorageSync('shop_id');
    this.shop_id = shop_id;

	},
	methods: {
    // 监听订阅事件用于获取订阅状态
    onSubscribe(e,item) {
      // console.log("房间号：", e.detail.room_id);
      // console.log("订阅用户openid", e.detail.openid);
      // console.log("是否订阅", e.detail.is_subscribe);
      this.$refs.diyLiveRef[0].changeSubscribe(e.detail.room_id)

    },
		isBulge(e){
			this.Bulge=e;
		},
		navigateBack() {
			let currentPage = getCurrentPages();
			if(currentPage.length>1){
				uni.navigateBack({
					delta:1
				})
			}else{
				this.$util.redirectTo('/pages/index/index/index')
			}
		},
		getHeight() {
			var self = this;
			let res = uni.getSystemInfoSync();
      self.windowHeight = res.windowHeight - 57;
      if (self.iphoneX) {
        self.windowHeight = self.windowHeight - 33;
      }
		},
		getDiyInfo() {
			this.$api.sendRequest({
				url: '/api/diyview/info',
				data: {
					name: this.name,
					site_id: this.siteId
				},
				success: async res => {
					if (res.code != 0 || !res.data) {
						if (this.$refs.loadingCover) this.$refs.loadingCover.hide();
						this.diyData = [];
						return;
					}
					this.diyData = res.data;
					if (this.diyData.value) {
						this.diyData = JSON.parse(this.diyData.value);
						// console.log(this.diyData);
						uni.setNavigationBarTitle({
							title: this.diyData.global.title
						});
            this.bgUrl = this.diyData.global.bgUrl
            this.shareTitle = this.diyData.global.shareTitle
            this.shareImg = this.diyData.global.shareImg
            this.topHeight = this.statusBarHeight+this.navHeight
						for (var i = 0; i < this.diyData.value.length; i++) {
							if (this.diyData.value[i].controller == 'PopWindow') {
								setTimeout(() => {
									if (uni.getStorageSync('diy_wap_floating_layer') != null && uni.getStorageSync('diy_wap_floating_layer') != '') {
										var wap_floating_cookie = JSON.parse(uni.getStorageSync('diy_wap_floating_layer'));
										if (wap_floating_cookie.closeNum < 3) {
											this.$refs.uniPopup[0].open();
										}
									} else {
										// this.$refs.uniPopup[0].open();
									}
								}, 500);
								break;
							}
						}
						this.diyData.value.length > 0 && this.diyData.value.forEach((it,index) => {
							if (it.type == 'SEARCH') {
								this.sbgc = it.backgroundColor
								this.searchObj = it
							}
							it.key = Math.floor(Math.random()*10) + String(index)

							if(it.type == "IMAGE_ADS") {
								let list = []
								it.list.map(v => {
									list.push({
										banner_url: v.link,
										image_url: v.imageUrl,
										banner_name: v.title,
									})
								})
								it.list = list
							}
						})
					}
          // #ifdef H5
          let share_data = this.$util.deepClone(this.getSharePageParams())
          let link = window.location.origin+this.$router.options.base+share_data.link.slice(1)
          share_data.link=link
          share_data.desc=share_data.title
          share_data.title='先迈商城'
          await this.$util.publicShare(share_data);
          // #endif
					uni.stopPullDownRefresh();
					if (this.$refs.loadingCover) this.$refs.loadingCover.hide();
				},
				fail: res => {
					if (this.$refs.loadingCover) this.$refs.loadingCover.hide();
				}
			});
		},
		closeNum() {
			this.$refs.uniPopup[0].close();
			var closeNum = 1;
			if (uni.getStorageSync('diy_wap_floating_layer')) {
				var wap_floating_cookie = JSON.parse(uni.getStorageSync('diy_wap_floating_layer'));
				closeNum = ++wap_floating_cookie.closeNum;
			}
			uni.setStorageSync(
				'diy_wap_floating_layer',
				JSON.stringify({
					closeNum
				})
			);
		},
		refresh() {
			this.getDiyInfo();
			this.getWebSitefo();
			this.getDefaultImg();

			if (uni.getStorageSync('city')) {
				this.city = uni.getStorageSync('city').title;
			} else {
				// this.getLocation();
			}
		},
		redirectTo(link) {
			this.$util.diyRedirectTo(link);
		},
		getWebSitefo() {
			this.webSiteInfo = uni.getStorageSync('web_site_info');
			if (this.webSiteInfo) this.webSiteInfo = JSON.parse(this.webSiteInfo);
			this.$api.sendRequest({
				url: '/api/website/info',
				success: res => {
					let data = res.data;
					if (data) {
						this.webSiteInfo = data;
						if (this.webSiteInfo.wap_status) return;
						this.$refs.uniPopup.open();
						uni.setStorageSync('web_site_info', JSON.stringify(this.webSiteInfo));
					}
				}
			});
		},
		// 获取默认图
		getDefaultImg() {
			this.$api.sendRequest({
				url: '/api/config/defaultimg',
				success: res => {
					let data = res.data;
					if (res.code == 0 && data) {
						uni.setStorageSync('default_img', JSON.stringify(data));
					}
				}
			});
		},
		/**
		 * 获取定位城市
		 */
		// getLocation() {
		// 	const map = new WxMap({
		// 		key: this.$config.mpKey
		// 	});
		// 	uni.getLocation({
		// 		type: 'gcj02',
		// 		success: res => {
		// 			map.reverseGeocoder({
		// 				location: {
		// 					latitude: res.latitude,
		// 					longitude: res.longitude
		// 				},
		// 				success: res => {
		// 					this.$api.sendRequest({
		// 						url: '/api/address/citybyname',
		// 						data: {
		// 							city: res.result.address_component.city
		// 						},
		// 						success: res => {
		// 							if (res.data) {
		// 								this.city = res.data.title;
		// 								uni.setStorageSync('city', res.data);
		// 							}
		// 						}
		// 					});
		// 				},
		// 				fail: res => {}
		// 			});
		// 		}
		// 	});
		// },
    // 分享参数
    getSharePageParams() {
      return this.$util.unifySharePageParams('/otherpages/diy/diy/diy',this.shareTitle ? this.shareTitle : '先迈商城',
      '',{name:this.name}, this.shareImg)
		},
    // 分享给朋友
		onShareAppMessage(res) {
      let shareType = res.target && res.target.dataset && res.target.dataset.shareType
      if(shareType && shareType=='liveRoom'){
        let { title, link, imageUrl, query } = this.$refs.diyLiveRef[0].toShareAppMessage(res)
        return this.$buriedPoint.pageShare(link , imageUrl, title);
      }else{
        let { title, link, imageUrl, query } = this.getSharePageParams()
        return this.$buriedPoint.pageShare(link , imageUrl, title);
      }
		},
		// 分享到微信
		onShareTimeline(res){
			let { title, imageUrl, query } = this.getSharePageParams()
			return {
				title,
        imageUrl,
				query,
				success: res => {},
				fail: res => {}
			};
		}
	}
};
</script>
<style lang="scss">
/* #ifdef MP-WEIXIN */
/*diy-seckill组件中计数器样式， 微信小程序这样写是因在自定义组件中做/deep/样式穿透是不生效，需要在页面上做样式样式穿透*/
.last-time {
  padding: 4rpx 0;
  font-size: 20rpx !important;

  .clockrun {
    /deep/ .custom {
      display: flex;
    }

    /deep/ .custom :nth-child(odd) {
      background-color: var(--custom-brand-color);
      width: 40rpx;
      height: 40rpx;
      line-height: 40rpx;
      color: white;
      border-radius: 6upx;
      font-size: 22rpx;
      text-align: center;
      overflow: hidden;
    }

    /deep/ .custom :nth-child(even) {
      padding: 0 6upx;
      color: var(--custom-brand-color);
    }
  }


}
.seckill-two-item-image-area-clockrun {
  /deep/ .custom {
    display: flex;
  }
  /deep/.day {
    font-size: 26rpx;
    font-weight: 400;
    color: #fff;
  }

  /deep/.day-symbol {
    font-size: 26rpx;
    font-weight: 400;
    color: #fff;
    margin: 0 6rpx;
  }

  /deep/.hour, /deep/.minute, /deep/.second {
    font-size: 26rpx;
    font-weight: 400;
    color: #fff;
    display: flex;
    justify-content: center;
    align-items: center;
  }

  /deep/.hour-symbol, /deep/.minute-symbol, /deep/.second-symbol {
    font-size: 26rpx;
    font-weight: 400;
    color: #fff;
    margin: 0 6rpx;
  }
}
/* #endif */
</style>
<style lang="scss" scoped>
  @import './css/diy.scss';

	.index_bg {
		width: 100%;
		height: 100%;
    background-size: 100% auto;
    background-repeat: no-repeat;
	}

	.head-nav {
		width: 100%;
		height: var(--status-bar-height);
	}

	.head-nav.active {
		padding-top: 40rpx;
	}

	.head-return {
		padding-left: 30rpx;
		padding-right: 30rpx;
		height: 90rpx;
		line-height: 90rpx;
		text-align: center;
		color: #fff;
		font-weight: 600;
		font-size: $ns-font-size-lg;
		display: flex;
		justify-content: space-between;

		text {
			display: inline-block;
			margin-right: 10rpx;
		}
	}

	.wap-floating {
		.image-wrap {
			width: 480rpx;

			image {
				width: 100%;
				border-radius: 40rpx;
			}
		}

		text {
			display: block;
			font-size: 60rpx;
			color: #ffffff;
			text-align: center;
		}
	}
	.fenxiao-menu {
		margin: 24rpx;
		height: 124rpx;
		display: flex;
		background-color: #ffffff;
		align-items: center;

		.menu-item {
			flex: 1;
			display: flex;
			flex-direction: column;
			padding-left: 36rpx;

			.menu-item-tit {
				font-size: 24rpx;
				color: #000000;
			}

			text {
				color: #838383;
				font-size: 20rpx;
			}
		}

		.shu {
			width: 1rpx;
			height: 92rpx;
			border: 1rpx solid #eaeaea;
		}
	}
	.PopWindow {
		.wap-floating,
		.uni-popup__wrapper-box {
			.image-wrap {
				width: 480rpx;

				image {
					width: 100%;
					border-radius: 40rpx;
				}
			}

			text {
				display: block;
				font-size: 60rpx;
				color: #ffffff;
				text-align: center;
			}
		}

		.uni-popup__wrapper-box {
			text-align: center;
			overflow: initial !important;
			background: none !important;
			vertical-align: middle;
			background: none;
		}
	}

	::-webkit-scrollbar {
		width: 0;
		height: 0;
		color: transparent;
	}

	.popup-box {
		width: 450rpx;
		background: #ffffff;
		border-radius: $ns-border-radius;
		overflow: hidden;

		.close_title {
			width: 100%;
			text-align: center;
			height: 70rpx;
			line-height: 70rpx;
			font-size: $ns-font-size-base;
		}

		.close_content {
			width: 100%;
			max-height: 500rpx;
			padding: $ns-padding;
			box-sizing: border-box;
		}

		.close_content_box {
			width: 100%;
			max-height: 460rpx;
			line-height: 1.3;
		}
	}
</style>

<style scoped>
.wap-floating >>> .uni-popup__wrapper.uni-custom .uni-popup__wrapper-box {
	background: none !important;
}

.poster-layer /deep/ .uni-popup__wrapper-box {
	max-height: initial !important;
}

/deep/ .sku-layer .uni-popup__wrapper-box {
	overflow-y: initial !important;
}

/deep/ .uni-popup__wrapper.uni-custom .uni-popup__wrapper-box {
	max-height: unset !important;
}
/deep/ .search_product{
  padding-top: 20rpx;
  box-sizing: border-box;
}
</style>
