export default {
  data() {
    return {
      ads: [],          // 轮播数据
      pintuanList: [],  // 拼团数据
      seckill: {},      // 秒杀数据
      maidou: {},       // 迈豆
      products: [],     // 推荐商品列表
      recommend_goods: [], //店主推荐
      exposeStatus: true,
      exposeProducts: false,
    }
  },
  methods: {
    //图片错误显示默认图
    imageError(data, index) {
      if (data instanceof Object && data[index] && data[index].goods_image) {
        data[index].goods_image = this.$util.getDefaultImage().default_goods_img;
      }
      if (data instanceof Object && data[index] && data[index].image_url) {
        data[index].image_url = this.$util.getDefaultImage().default_goods_img;
      }
      this.$forceUpdate();
    },
    /*
    * 根据广告位获取广告图片
    * @param sign 广告位置
    * */
    async getAdInfo() {
      let res = await this.$api.sendRequest({
        url: this.$apiUrl.specialBannerUrl,
        async: false,
        data: {
          sign: 'index-1'
        }
      })
      if (res.code != 0) {
        return false
      }
      // this.ads = res.data.list;
    },
    // 拼团数据
    async getPintuanGoodsList() {
      let res = await this.$api.sendRequest({
        url: this.$apiUrl.pintuanGoodsList,
        async: false,
        data: {
          page_size: 5
        }
      })
      if (res.code != 0) {
        uni.showToast({
          title: res.message,
          mask: true,
          icon: "none",
          duration: 3000
        });
        return
      }
      this.pintuanList = res.data.list;
    },
    // 秒杀接口
    async getHomeApiFun() {
      let res = await this.$api.sendRequest({
        url: this.$apiUrl.homeUrl,
        async: false,
        data: {},
      });
      if (res.code != 0) {
        uni.showToast({
          title: res.message,
          mask: true,
          icon: "none",
          duration: 3000
        });
        return
      }
      //秒杀
      res.data.seckill.action_time = res.data.seckill.action_time ? Math.abs(res.data.seckill.action_time) *1000 : 0
      this.seckill = Object.prototype.toString.call(res.data.seckill).slice(8, -1) == 'Object' ? res.data.seckill : {};
      // 迈豆
      this.maidou = res.data.maidou;
      // 店主推荐
      this.recommend_goods = res.data.recommend_goods;
    },
    async getData(mescroll) {
      let goods_list = this.diyData.value.filter(item=>{
        return item.controller == 'GoodsList'
      })
      if(goods_list.length == 0){
        mescroll.endSuccess(0)
      }else{
        await this.getGoodsList(mescroll)
      }
    },
    // 推荐商品
    async getGoodsList(mescroll) {
      mescroll.optUp.toTop.src = this.$util.img('public/static/youpin/to-top.png');
      mescroll.optUp.toTop.width = "144rpx";

      let res = await this.$api.sendRequest({
        url: this.$apiUrl.goodsListUrl,
        async: false,
        data: {
          shop_id: this.shop_id,
          page_size: mescroll.size,
          page: mescroll.num,
        },
      });
      if (res.code != 0) {
        uni.showToast({
          title: res.message,
          mask: true,
          icon: "none",
          duration: 3000
        });
        return
      }

      let newArr = res.data.list;
      mescroll.endSuccess(newArr.length);
      //设置列表数据
      if (mescroll.num == 1) this.products = []; //如果是第一页需手动制空列表
      this.products = this.products.concat(newArr); //追加新数据

      this.exposeProducts && this.$buriedPoint.exposeGoods(newArr, 'sku_id')
    },
    listenRefresh() {
      this.getDiyInfo(); // 微页面排版
      // this.getAdInfo();  // 轮播接口
      let pintuan_controller = this.diyData.value.filter(item=>{
        return item.controller == 'Pintuan'
      })
      if(pintuan_controller.length>0){
        this.getPintuanGoodsList(); //拼团接口
      }
      let seckill_controller = this.diyData.value.filter(item=>{
        return item.controller == 'Seckill' || item.controller == 'Maidou' || item.constructor == 'ShoperRecommand'
      })
      if(seckill_controller.length>0){
        this.getHomeApiFun(); //秒杀，迈豆接口
      }
    },
    // 监听滚动触发埋点
    scroll(e) {
      this.scrollTop = e.scrollTop
      if (e.scrollTop >= 200 && this.exposeStatus) {
        this.exposeStatus = false
        // 拼团
        this.$buriedPoint.exposeGoods(this.pintuanList ?? [], 'goods_id')
        // 迈豆
        this.$buriedPoint.exposeGoods(this.maidou.list ?? [], 'sku_id')
        // 秒杀
        this.$buriedPoint.exposeGoods(this.seckill.list ?? [], 'sku_id')
      }

      if (e.scrollTop >= 1000 && !this.exposeProducts) {
        this.$buriedPoint.exposeGoods(this.products, 'sku_id')
        this.exposeProducts = true
      }
    },
    // 返回监听
    customBack() {
      this.$util.goBack();
    },
    scrollToY(y){
      this.$refs.mescroll.toScrollPointY(y)
    }
  },
}
