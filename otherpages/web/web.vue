<template>
	<view>
<!--		<view>{{ $lang('kefu') }}</view>-->
<!--		<view class="iconfont iconshang navigate-back" @click="navigateBack"></view>-->
		<web-view :src="src" @message="postMessage" @load="loadpage" @error="binderror" v-if="src"></web-view>
    <yd-auth-popup ref="ydauth"></yd-auth-popup>
    <ns-login ref="login"></ns-login>
	</view>
</template>

<script>
	import {scenePare} from "../../common/js/scene_handle";
  import apiurls from "../../common/js/apiurls";
  import system from "../../common/js/system";

  export default {
		data() {
			return {
				src: '',
        tmpSrc:"",
        encodesrc:"",
        shareData:{}
			};
		},
		async onLoad(event) {
      await system.wait_staticLogin_success();
      // 小程序扫码进入
      if (event.scene) {
        scenePare(false,event);
      }
      this.tmpSrc = event.src;
      if(event.encodesrc){
        try{
          let res = await this.$api.sendRequest({
            url: apiurls.decryptDataUrl,
            async: false,
            data:{
              key:event.encodesrc
            }
          })
          if(res.code==0){
            this.tmpSrc=res.data.decrypt_data;
          }
        }catch (e) {

        }
      }
      let mini_token=uni.getStorageSync('token');
      if(mini_token){
        try{
          await system.checkToken();
        }catch (e) {
          mini_token=''
        }
      }
      let src=this.tmpSrc;
      try{
        src = decodeURIComponent(src)
        let urls=src.split('?');
        let params_dict={}
        if(src.indexOf('/spa/app')!=-1){
          if(mini_token){
            params_dict['mini_token']=mini_token
          }
        }
        // #ifdef MP-WEIXIN
        if(mini_token){
          let userInfo=await this.getMemberInfo()
          params_dict['openid']=userInfo.weapp_openid || ''
          params_dict['avatar']=userInfo.headimg ? encodeURIComponent(userInfo.headimg) : ''
          params_dict['nickname']=userInfo.nickname || ''
          params_dict['unionid']=userInfo.wx_unionid || ''
        }
        // 防伪码页面
        if(src.indexOf('/h5scrm/wechat')!=-1){
          if(!params_dict['openid']){
            this.$util.toShowLoginPopup(this,null,`/otherpages/web/web?src=${this.tmpSrc}`);
            return
          }
        }
        // #endif
        let params_str = Object.keys(params_dict).map(item => `${item}=${params_dict[item]}`).join('&')
        if(urls.length>1){
          urls[1]+=`&${params_str}`;
        }else{
          urls[1]=params_str;
        }
        src=urls.join('?');
      }catch(err){}
      this.src = src
		},
    async onShow(){

    },
		methods: {
			navigateBack() {
				uni.navigateBack({
					delta: 1
				});
			},
      postMessage(e){
        // console.log('message',e.detail)
        let shareData=e.detail.data.length>0 ? e.detail.data[e.detail.data.length-1] : {}
        this.shareData=shareData;
      },
      loadpage(e){
        // console.log('load',e)
      },
      binderror(e){
        // console.log('binderror',e)
      },
      // 获取会员基础信息
      async getMemberInfo() {
        let memberInfo={}
        try {
          let res = await this.$api.sendRequest({
            url: '/api/member/info',
            async: false
          });
          if (res.code >= 0 && res.data) {
            memberInfo = res.data;
            memberInfo.phone = memberInfo.mobile
            if(memberInfo.mobile) {
              memberInfo.mobile = memberInfo.mobile.substr(0,3)+"****"+memberInfo.mobile.substr(7);
            }
          }
        }catch (e) {

        }
        return memberInfo
      },
      /**
       *分享参数组装
       */
      getSharePageParams() {
        return this.$util.unifySharePageParams('/otherpages/web/web',this.shareData.title ? this.shareData.title : '先迈商城',
            '',{src:this.shareData.link ? decodeURIComponent(this.shareData.link) : decodeURIComponent(this.tmpSrc)},this.shareData.imgUrl ? this.shareData.imgUrl : '')
      }
		},
    onShareAppMessage(res){
      let { title, link, imageUrl, query } = this.getSharePageParams()
      return this.$buriedPoint.pageShare(link , imageUrl, title);
    }
	};
</script>

<style lang="scss">
	.navigate-back {
		position: absolute;
		top: 34rpx;
		left: 34rpx;
		z-index: 5;
		font-size: $ns-font-size-lg;
	}
</style>
