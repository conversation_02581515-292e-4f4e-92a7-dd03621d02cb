/**
 * app内嵌本项目页面时的混合方法
 */
// #ifdef H5
import {goClosePage, isOnXianMaiApp} from "../js/h5/appOP";
// #endif
export default {
    onReady(){
        // #ifdef H5
        this.appCurrentPages=getCurrentPages().length;
        // #endif
    },
    methods:{
        appGoBack(){
            // app内打开h5,  销毁页面之前是否关闭app的web-view
            // #ifdef H5
            if(isOnXianMaiApp){
                if(this.appCurrentPages <= 1){
                    goClosePage('0');
                }else{
                    uni.navigateBack();
                }
            }else{
                uni.navigateBack();
            }
            // #endif
        }
    }

}
