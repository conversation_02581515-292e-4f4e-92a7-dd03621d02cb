/**
 * 页面在屏幕显示区域的商品对接微信web分析
 * 在需要暴露的元素节点上增加类型名 expose_goods_index 并且增加节点属性data-expose_goods_sku 值为sku_id
 */
export default {
    data(){
        return{
            systemInfo:null,
            scrollPosition:0,
            isEmit:true,
            postendSkuId:[],  //已经暴露过的sku_id
            tmpSkuId:[],  //需要曝光的sku_id
        }
    },
    onLoad(){
        this.systemInfo=uni.getSystemInfoSync();
    },
    async onReady(){
        await this.againDealWith();
    },
    async onPageScroll(e){
        await this.scrollTouch(e)
    },
    methods:{
		// 首次显示或切换tab显示触发
		async againDealWith(status = false){
			if(status){ // 切换tab时清空已曝光的
				this.postendSkuId = []
			}
			setTimeout(async ()=>{
			    await this.getExposeGoods();
			},2000)
		},
		// 滚动触发处理
		async scrollTouch(e){
			let new_point=e.scrollTop;
			if(Math.abs(new_point-this.scrollPosition) > 50 && this.isEmit){
			    this.isEmit=false;
			    this.scrollPosition=new_point;
			    await this.getExposeGoods();
			    this.isEmit=true;
			}
		},
        /**
         * 检查商品是的在屏幕显示区域内
         * @param item
         * @returns {boolean}
         */
        checkNoneInScreen(item){
            if(item.top >-1 && item.top<=this.systemInfo.screenHeight && item.left >-1 && item.left<=this.systemInfo.screenWidth){
                return true;
            }else{
                return false;
            }
        },
        /**
         * 获取屏幕显示区域内暴露的商品
         * @returns {Promise<unknown>}
         */
        getExposeGoods(){
            const query = uni.createSelectorQuery();
            return new Promise((resolve, reject) => {
                query.selectAll('.expose_goods_index').boundingClientRect(data=>{
                    for (let i = 0; i < data.length; i++) {
                        if(this.checkNoneInScreen(data[i])){
                            let sku_id=data[i].dataset['expose_goods_sku'];
                            if(this.postendSkuId.indexOf(sku_id)==-1 && this.tmpSkuId.indexOf(sku_id)==-1){
                                this.tmpSkuId.push(sku_id);
                            }
                        }
                    }
                    this.toExposes();
                    resolve();
                }).exec();
            })
        },
        /**
         * 调用接口曝光
         */
        toExposes(){
            let arr_tmep=[];
            for (let i = 0; i < this.tmpSkuId.length; i++) {
                arr_tmep.push({sku_id:this.tmpSkuId[i]})
            }
            this.postendSkuId=this.postendSkuId.concat(this.tmpSkuId);
            this.$buriedPoint.exposeGoods(arr_tmep, 'sku_id')
            this.tmpSkuId=[];
        }
    }
}
