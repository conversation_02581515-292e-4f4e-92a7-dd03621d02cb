export default {
	data() {
		return {
			showTop: false,
			scrollTop: 0,
			oldLocation: 0
		}
	},
	methods: {
		scrollToTopNative() {
			uni.pageScrollTo({
				duration: 200,
				scrollTop: 0
			});
		}
	},
	onReachBottom() {
		// if(this.$refs.goodrecommend) this.$refs.goodrecommend.getLikeList(10)
	},
	onPageScroll(e) {
		this.oldLocation = e.scrollTop;
		if (e.scrollTop > 200) {
			!this.showTop ? this.showTop = true : '';
		} else {
			this.showTop ? this.showTop = false : '';
		}
		if (e.scrollTop > 100) {
			!this.isShowDetailTab ?this.isShowDetailTab=true : '';
		} else {
			this.isShowDetailTab ? this.isShowDetailTab=false : '';
		}
	}
}
