/*访问统计类型函数*/
import apiurls from "../js/apiurls";

export default {
    /**
     * 店铺首页访问统计
     * @param that
     */
    shopInterview(that){
        let shop_id=uni.getStorageSync('shop_id');
        let token=uni.getStorageSync('token');
        let is_shopkeeper=that.$store.state.is_shopkeeper;
        let scene_id=that.$store.state.scene;
        let data={
            shop_id,
            token,
            scene_id
        }
        if(is_shopkeeper){
            data=Object.assign(data,{is_shopkeeper})
        }
        if(token && shop_id){
            that.$api.sendRequest({
                url: apiurls.browseUrl,
                data
            });
            if(is_shopkeeper){
                that.$store.dispatch('writeIsShopkeeper',null);
            }
        }
    }
}

