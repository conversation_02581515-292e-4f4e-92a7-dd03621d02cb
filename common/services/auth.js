import weixinAuth from "../js/weixinAuth";

export default {
    /**
     * 解密数据
     * @param {Object} e
     */
    async ecryptData(e) {
        let detail = e.detail
        if (detail.errMsg.search(':ok') == -1) {
            return Promise.reject('用户拒绝授权')
        }

        let allPromise = [];
        let promiseResult = []
        allPromise.push(weixinAuth.getLoginCode())

        switch (detail.errMsg) {
            case 'getUserInfo:ok':
                allPromise.push(weixinAuth.getUserInfo())
                promiseResult = await Promise.all(allPromise)
                break
            case 'getUserProfile:ok':
                allPromise.push(weixinAuth.getUserProfile())
                promiseResult = await Promise.all(allPromise)
                break
            case 'getPhoneNumber:ok':
                promiseResult = await Promise.all(allPromise)
                break
            case 'login:ok':
                promiseResult = await Promise.all(allPromise)
                break;
            default:
                return Promise.reject('暂不处理' + detail.errMsg)
        }

        // 登录信息
        let [loginError, loginResult] = promiseResult[0]
        if (loginError) {
            return Promise.reject(loginError)
        }
        // 附加授权相关
        if (promiseResult.length == 2) {
            let [authError, authResult] = promiseResult[1]
            if (authError) {
                return Promise.reject(authError)
            }
            detail = authResult
        }
        // 统一解密
        return Promise.resolve([loginResult.code, detail]);
    },
    /**
     * 获取手机号
     * @param {Object} e
     */
    async getPhoneNumber(e) {
        return this.ecryptData(e)
    },
    /**
     * 获取用户匿名信息
     * @param {Object} e
     */
    async getUserInfo(e) {
        return this.ecryptData(e)
    },
    /**
     * 获取用户信息
     */
    async getUserProfile() {
        let e = {
            detail: {
                errMsg: 'getUserProfile:ok'
            }
        }
        return this.ecryptData(e)
    },
    /**
     * 获取登录的code
     * @returns {Promise<[*, *]>}
     */
    async getCode(){
        let e = {
            detail: {
                errMsg: 'login:ok'
            }
        }
        return this.ecryptData(e)
    }
}
