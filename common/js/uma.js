/**
 * 友盟统计
 */

const appKey='6127642f870a7a610a4f6224';   //玖富主体 6127642f870a7a610a4f6224   先迈主体 6127572510c4020b03efbd50
// import uma from 'umtrack-wx';
// #ifdef MP-WEIXIN
let uma=require('umtrack-wx');
uma.init({
    appKey, //由友盟分配的APP_KEY
    // 使用Openid进行统计，此项为false时将使用友盟+uuid进行用户统计。
    // 使用Openid来统计微信小程序的用户，会使统计的指标更为准确，对系统准确性要求高的应用推荐使用Openid。
    useOpenid: true,
    // 使用openid进行统计时，是否授权友盟自动获取Openid，
    // 如若需要，请到友盟后台"设置管理-应用信息"(https://mp.umeng.com/setting/appset)中设置appId及secret
    autoGetOpenid: true,
    debug: true, //是否打开调试模式
    uploadUserInfo: true // 自动上传用户信息，设为false取消上传，默认为false
});
// #endif

// 适配vue插件如此可通过Vue.use(uma)来安装
uma.install = function (Vue) {
    Vue.prototype.$uma = uma;
}
module.exports= uma;
