/*
 * creat by 何健 on 2020年09月27日
 * h5跳转到app的scheme url
 * */
//获取访问的user-agent
let ua = navigator.userAgent.toLowerCase() || window.navigator.userAgent.toLowerCase();
let is_youpin = /youpin/i.test(ua);
let is_xianmai = /xianmai/i.test(ua);
let prefix = "xm://xianmai";
// if (is_xianmai) {
// 	prefix = "xm://xianmai";
// } else {
// 	prefix = "yp://youpin";
// }

export const appLoginUrl = `${prefix}?position=login`; /*登录页面*/
export const webUrl = `${prefix}?web=` /*跳转到H5页面*/
export const authenticationUrl = `${prefix}?position=authentication` /*实名认证*/
export const withdrawUrl = `${prefix}?position=withdraw` /*可用余额体现*/
export const openBrowser = `${prefix}?openBrowser=` /*使用手机原生浏览器访问链接*/
export const singleProductJoiningUrl = `${prefix}?position=wantToJoinIn`; /*个人开店加盟*/
export const mywallet = `${prefix}?position=mywallet` /*我的钱包*/
export const appHomeUrl = `${prefix}?action=home`; /*网站首页*/
export const myMessageUrl = `${prefix}?position=myMessage`; /*我的消息*/
export const category = `${prefix}?action=category`; /*首页分类*/
