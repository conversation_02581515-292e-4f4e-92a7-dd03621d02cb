/*
* creat by 何健 on 2020年09月27日
* 工具类函数
* */


//获取访问的user-agent
let ua = navigator.userAgent.toLowerCase() || window.navigator.userAgent.toLowerCase();
//判断user-agent
export let isIOS = /(iPhone|iPad|iPod|iOS)/i.test(ua); //苹果家族
export let isAndroid = /(android|nexus)/i.test(ua); //安卓家族
let isXianMai=/xianmai/i.test(ua); //是否在app里面
let isYouPin=/youpin/i.test(ua);
export const isOnXianMaiApp=(isXianMai || isYouPin)
let MiniProgramId="gh_949e4c92bf03";  //先迈小程序的id
if(window.location.host=="youpin-dev.jiufuwangluo.com" || window.location.host=="youpin-dev.jiufuwangluo.com:8443" || window.location.host=="youpin-dev.jiufuwangluo.com:4443"){
    MiniProgramId="gh_039d38216563";  //柚品小程序的id
}

/**
 * 配置app默认项
 * @param hide {boolean} 是否隐藏app原生的头部导航
 * @param bounces {boolean}  是否关闭app页面弹性滑动 此属性只有ios有 android没有
 * @param nativeNav {boolean} 是否配置原生导航颜色  true 是  false 不配置
 */
export function setAppData({hide,bounces,nativeNav}){
    var hideNavJson     = {'hide': '1'};
    if(isAndroid){
        if(hide){
            try {
                window.android.hideNavigationBar(JSON.stringify(hideNavJson));
            }catch (e) {
                console.log(e)
            }
        }
        if(nativeNav){
            try {
                window.android.setStatusBarStyle(JSON.stringify({'style':'1'}));
                window.android.setNavigationBarColor(JSON.stringify({'color': '#ffffff'} ));
            }catch (e) {
                console.log(e)
            }
        }
        // window.android.webviewBounces(JSON.stringify({'status': '0'}));
    }
    if(isIOS){
        setupWebViewJavascriptBridge(function(bridge) {
            if(hide){
                // 隐藏
                bridge.callHandler('hideNavigationBar', hideNavJson, function(response) {});
            }
            if(bounces){
                bridge.callHandler('webviewBounces', {'status': '0'}, function(response) {});
            }
            if(nativeNav){
                bridge.callHandler('setStatusBarStyle', {'style':'1'}, function(response) {});
                bridge.callHandler('setNavigationBarColor', {'color': '#ffffff'}, function(response) {});
            }
        })
    }
}

/**
 * 设置原生导航栏和状态栏背景颜色
 * @param color {String} 颜色值
 */
export function changeNavBgColor(color) {
    if(isAndroid){
        try {
            window.android.setNavigationBarColor(JSON.stringify({'color': color} ));
        }catch (e) {

        }
    }
    if(isIOS){
        setupWebViewJavascriptBridge(function(bridge){
            bridge.callHandler('setNavigationBarColor', {'color': color}, function(response) {});
        })
    }
}


/**
 * 获取app的token
 * @param callback {function}获取token成功回调函数，回调带一个token参数
 * @param errorCallback  {function} 获取token失败回调函数
 */
export function updateLoginPageData(callback,errorCallback){
    let token="";
    if(isIOS){
        setupWebViewJavascriptBridge((bridge)=> {
            bridge.callHandler('getLoginData', {}, (response) => {
                // 获取userToken
                // if(response.hasOwnProperty('token')){
                //     token = response.token;
                // }
                token = response.token;
                callback(token)
                // if(callback && typeof callback=="function"){
                //     callback(token)
                // }
            });
        });

    } else if(isAndroid){
        try{
            let userToken = window.android.getLoginData(JSON.stringify({}));
            let response = JSON.parse(userToken);
            if(response.hasOwnProperty('token')){
                token = response.token;
            }
            if(callback && typeof callback=="function"){
                callback(token)
            }
        }catch (e) {
            if (errorCallback && typeof errorCallback == "function") {
                errorCallback();
            }
        }

    }
}

/**
 * 调用原生复制
 * @param text {string} {'text': '复制的内容'}
 * @param callback {function}  成功回调函数
 */
export function schemeCopy(text,callback) {
    if(isIOS){
        setupWebViewJavascriptBridge(function(bridge) {
            bridge.callHandler('textCopy', {'text': text}, function(response) {
                if(callback && typeof callback=='function'){
                    callback();
                }
            });
        });
    }else if(isAndroid){
        window.android.textCopy(JSON.stringify({'text': text}));
        if(callback && typeof callback=='function'){
            callback();
        }
    }
}

/**
 * 分享调用app原生页面
 * @param shareJson {Object} app原生页面url
 */
export function shareSchemeGo(shareJson) {
    if(isIOS){
        setupWebViewJavascriptBridge(function(bridge) {
            bridge.callHandler('share', shareJson, function(response) {
            });
        });
    }else if(isAndroid){
        window.android.share(JSON.stringify(shareJson));
    }
}

/**
 * 分享小程序路径，调用app原生页面
 * @param shareJson
 */
export function shareMiniProgramSchemeGo(shareJson){
    shareJson['userName']=MiniProgramId;
    if(!shareJson['thumbImage']){
        shareJson['thumbImage']=window.location.origin+'/public/static/youpin/default-img.png'
    }
    if(isIOS){
        setupWebViewJavascriptBridge(function(bridge) {
            bridge.callHandler('shareMiniProgram', shareJson, function(response) {
            });
        });
    }else if(isAndroid){
        window.android.shareMiniProgram(JSON.stringify(shareJson));
    }
}

/**
 * 跳转到app原生页面
 * @param appUrl {string} app原生页面url
 */
export function schemeGo(appUrl) {
    if(isIOS){
        setupWebViewJavascriptBridge(function(bridge) {
            bridge.callHandler('handleScheme', {'scheme':appUrl}, function(response) {
            });
        });
    }else if(isAndroid){
        window.android.handleScheme(JSON.stringify({'scheme':appUrl}));
    }
}

/**
 * 保存图片
 * @param ImageUrl {string}  图片的url
 * @param callback {function} 保存成功的回调函数
 */
export function schemeSaveImage(ImageUrl,callback) {
    if(isIOS){
        setupWebViewJavascriptBridge(function(bridge) {
            bridge.callHandler('saveImage', {'url':ImageUrl}, function(response) {
                if(callback && typeof callback=='function'){
                    callback();
                }
            });
        });
    }else if(isAndroid){
        window.android.saveImage(JSON.stringify({'url':ImageUrl}));
        if(callback && typeof callback=='function'){
            callback();
        }
    }
}

// iOS与JS桥接方法
export function setupWebViewJavascriptBridge(callback) {
    if (window.WebViewJavascriptBridge) {
        return callback(WebViewJavascriptBridge);
    }
    if (window.WVJBCallbacks) {
        return window.WVJBCallbacks.push(callback);
    }
    window.WVJBCallbacks = [callback];
    let WVJBIframe = document.createElement('iframe');
    WVJBIframe.style.display = 'none';
    WVJBIframe.src = 'https://__bridge_loaded__';
    document.documentElement.appendChild(WVJBIframe);
    setTimeout(function () {
        document.documentElement.removeChild(WVJBIframe)
    }, 0)
}

/*弹窗消失后跳转原生页面
*  @param type {String} '0'表示普通的关闭，webview为新开页面时，使用此方式关闭； '1'表示移除view上面的webview，当webview不是新开页面，仅在当前页面addView时，使用此方式关闭
*  @param callback {Function} 回调函数
* */
export function goClosePage(type,callback){
    if(!type){
        type='0';
    }
    if(isAndroid){
        try {
            window.android.closeWebView(JSON.stringify({'type':type}));
            if(callback && typeof callback=='function'){
                callback();
            }
        }catch (e) {
            console.log(e)
        }
    }
    else if(isIOS){
        setupWebViewJavascriptBridge(function(bridge) {
            bridge.callHandler('closeWebView', {'type':type}, function(response) {
                if(callback && typeof callback=='function'){
                    callback();
                }
            });
        })
    }
}

/*百度事件统计
*  @param type {String} type：1:点击事件统计 2:页面时长开始统计 3:页面时长结束统计
*  @param event {String} 事件名称
*  @param pageName {String} 页面名称
* */
export function baiduStatistics(type,event,pageName,callback){
    if(isAndroid){
        try {
            window.android.baiduStatistics(JSON.stringify({'type':type,'event':event,'pageName':pageName}));
            if(callback && typeof callback=='function'){
                callback();
            }
        }catch (e) {
            console.log(e)
        }
    }
    else if(isIOS){
        setupWebViewJavascriptBridge(function(bridge) {
            bridge.callHandler('baiduStatistics', {'type':type,'event':event,'pageName':pageName}, function(response) {
                if(callback && typeof callback=='function'){
                    callback();
                }
            });
        })
    }
}


/**
 * app唤起柚品小程序
 * @param path {String} 小程序的启动路径
 * @param callback {Function}  成功回调函数
 */
export function launchMiniProgram(path,callback){
    let data={
        userName:MiniProgramId,
        path
    }
    if(isAndroid){
        try {
            window.android.launchMiniProgram(JSON.stringify(data));
            if(callback && typeof callback=='function'){
                callback();
            }
        }catch (e) {
            console.log(e)
        }
    }
    else if(isIOS){
        setupWebViewJavascriptBridge(function(bridge) {
            bridge.callHandler('launchMiniProgram', data, function(response) {
                if(callback && typeof callback=='function'){
                    callback();
                }
            });
        })
    }
}


export function relocation(path,callback){
    if(isAndroid){
        try {
            window.android.relocation(path);
            if(callback && typeof callback=='function'){
                callback();
            }
        }catch (e) {
            console.log(e)
        }
    }
    else if(isIOS){
        setupWebViewJavascriptBridge(function(bridge) {
            bridge.callHandler('relocation', path, function(response) {
                if(callback && typeof callback=='function'){
                    callback();
                }
            });
        })
    }
}

// app内嵌webview窗口高度
export function updateContentHeight(height){
    if(isAndroid){
        try {
            window.android.updateContentHeight(JSON.stringify({ "height":height}));
        }catch (e) {
            console.log(e)
        }
    }
    else if(isIOS){

    }
}

/**
 * 判断手机是否安装了微信app
 * @param callback {function} 成功回调函数
 */
export function checkIsWXAppInstalled(callback){
    let data={}
    let status=0;
    if(isAndroid){
        try {
            let statusData = window.android.isWXAppInstalled(JSON.stringify(data));
            let response = JSON.parse(statusData);
            if(response.hasOwnProperty('status')){
                status = parseInt(response.status);
            }
            if(callback && typeof callback=="function"){
                callback(status)
            }
        }catch (e) {
            console.log(e)
        }
    }
    else if(isIOS){
        setupWebViewJavascriptBridge(function(bridge) {
            bridge.callHandler('isWXAppInstalled', data, function(response) {
                if(callback && typeof callback=='function'){
                    status=parseInt(response.status);
                    callback(status);
                }
            });
        })
    }
}
