/*微信小程序日志上报到微信开发平台*/
const log = wx.getRealtimeLogManager ? wx.getRealtimeLogManager() : null
const isRelease=process.env.NODE_ENV!='development' ? true :false;  //是否是线上环境

module.exports =  {
    debug() {
        if (!log) return;
        if(!isRelease) return;
        log.debug.apply(log, arguments)
    },
    info() {
        if (!log) return;
        if(!isRelease) return;
        log.info.apply(log, arguments)
    },
    warn() {
        if (!log) return;
        if(!isRelease) return;
        log.warn.apply(log, arguments)
    },
    error() {
        if (!log) return;
        if(!isRelease) return;
        log.error.apply(log, arguments)
    },
    setFilterMsg(msg) { // 从基础库2.7.3开始支持
        if (!log || !log.setFilterMsg) return
        if (typeof msg !== 'string') return
        log.setFilterMsg(msg)
    },
    addFilterMsg(msg) { // 从基础库2.8.1开始支持
        if (!log || !log.addFilterMsg) return
        if (typeof msg !== 'string') return
        log.addFilterMsg(msg)
    }
}
