/*柚品新urls*/
export default {
    // system
    getPhoneUrl:"/api/login/getPhoneNumber",  /*授权手机号*/
    loginUrl:"/api/login/loginByWx", /*登录*/
    staticLoginUrl:"/api/login/wxSnsapiBaseLogin", /*静默登录*/
	wechatAuthUrl:'/api/login/wechatAuth', /*微信授权登陆 | 手机验证码登陆*/
    browseUrl:'/api/website/browse',  /*店铺会员访问日志接口*/
    checkLoginTokenUrl:'/api/login/checkLoginToken',  /*登录凭证检验接口*/
    checkShopStatusUrl:'/api/shop/checkShopStatus',  /*店铺状态检查接口*/
	registerAggrementUrl:'/api/register/registerAggrement', /*用户登录/注册协议列表*/
	aggrementInfoUrl:'/api/register/aggrementInfo', /*注册协议详情*/
	sendMobileCodeUrl:'/api/member/sendMobileCode', /*短信接口*/
    decryptDataUrl:'/shopapi/inviteOpenShop/decryptData',  /*获取解密数据*/
    getOutLinkUrl:'/api/website/getOutLink',  /*获取升级vip页面的链接*/
    getSubscribeScene:'/api/WeAppNotice/getSubscribeScene', /*获取消息模板接口*/
    subscribeUrl:'/api/WeAppNotice/subscribe',  /*消息订阅接口*/
    newCommQrcodeUrl:'/api/Website/newCommQrcode', /*小程序二维码*/
    jssdkconfigUrl:'/wechat/api/wechat/jssdkconfig',  /*获取公众号配置参数*/
	publicShareParamsUrl:'/wechat/api/wechat/share',  /*公众号分享参数获取*/
    enterpriseWxContactCsUrl:'/api/EnterpriseWx/contactCs', /*全局企微客服*/
    getIncomeInfoUrl:'/api/memberaccount/getIncomeInfo',  /*未读收益接口*/
    getIncomeConfigUrl:'/api/Memberaccount/getIncomeConfig', /*收益配置接口(用于收益json动画的配置)*/
    getDominantColorUrl : '/api/website/getDominantColor', /*获取图片的主颜色*/

    // product
    homeUrl:"/api/website/index",  /*店铺首页接口*/
    getAppNotice: "/api/website/getAppNotice", /*店铺首页app消息红点接口*/
    goodsListUrl:'/api/goods/goodsList', /*商品列表*/
    orderBuyUrl:'/api/order/orderBuy', /*再次购买*/
    goodsServiceDesc:"/api/goods/goodsServiceDesc", /*商品服务说明*/
    shopGoodsCateTreeUrl:'/api/goodscategory/shopGoodsCateTree',  /*店铺商品分类*/
	updateCartUrl:'/api/cart/updateCart', /*修改购物车规格*/
	goodsHotWordsUrl:'/api/goods/goodsHotWords', /*热门搜索*/
    goodsGuessUrl:'/api/goods/goodsGuess', /*猜你喜欢商品列表*/
    specialBannerUrl:'/api/index/specialBanner', /*广告图片接口*/
    specialBannerReportByClickUrl:'/api/index/specialBannerReportByClick', /*广告图片点击上报*/
    searchGuessUrl:'/api/Search/guess', /*关键词联想接口*/
    searchHitUrl:'/api/Search/hit', /*联想词点击上报接口*/
    searchReportUrl:'/api/Search/report', /*搜索上报*/
    deliveryMoneyUrl:'/api/goods/deliveryMoney', /*运费计算接口*/
    moreGoodiesUrl:'/api/goods/moreGoodies', /*商品详情--更多好物商品*/


    //order
    orderCoupon:'/coupon/api/usercoupon/orderCoupon', //获取可用不可用优惠券列表
    periodOrder:'/api/periodOrder/payment', //周期购确认订单页接口--wjj


    //member
    memberAuthenticationInfo:"/api/member/checkMemberAuth",//我的-获取实名认证
    memberSaveAuthenticationInfo:"/api/member/memberAuth",//我的-保存实名认证
    memberBenefitList:"/api/member/benefitList",//我的-分销客收益列表
    memberWechatAddr:"/api/readme/wechatAddr",//我的-微信地址使用说明接口
    bankListUrl:'/api/memberbankaccount/bankList', //银行列表
    bankCardInfoUrl: '/api/memberbankaccount/getBankNameByCardNo',  //根据卡号获取银行名字
    memberBankUrl:"/api/memberbankaccount/memberBank", //会员绑定的银行卡
    editBankConfirmUrl:"/api/memberbankaccount/editBankConfirm", //编辑银行卡确认
    editBankUrl:'/api/memberbankaccount/editBank', //编辑银行卡
    addBankUrl:'/api/memberbankaccount/addBank', //新增绑定银行卡
    memberwithdrawUrl:'/api/memberwithdraw/info',  //提现申请页接口
    memberaccountDetailUrl:'/api/memberaccount/detail',  //资金明细详情接口
    memberCouponListUrl:'/coupon/api/coupon/memberpage', //优惠券领取列表接口
    getCouponInfoUrl:'/api/coupon/getCouponInfo', //优惠券详情接口
    receivecouponUrl:'/api/coupon/receivecoupon',  //优惠券领取接口
	serviceUrl:'/api/kf/info',  //客服接口
	issignUrl:'/api/membersignin/issign', // 是否已签到判断接口
    signinUrl:'/api/membersignin/signin', // 用户签到接口
	enentListUrl:'/api/ShareBuyActivity/userActivityList', // 分享赚活动参与列表接口
	kanjiaListUrl:'/api/barginActivity/memberbargainlist', // 砍价活动参与列表接口
	pintuanOrderNum:'/api/PintuanOrder/num',//拼团订单数量
    historyBrowsing:'/api/Member/historyBrowsing', //我的足迹
    delHistoryBrowsing: '/api/Member/delHistoryBrowsing', //足迹删除
    editMemberInfoUrl:'/api/member/editMemberInfo', // 修改用户信息接口
    recrodSoterCheckUrl:'/api/WxCommon/recrodSoterCheck', // 生物验证-记录小程序结果返回
    xmArticleUrl: '/api/member/xmArticle', /*获取的文章  参数 code_name =3    1=关于我们，2=保障体系，3=荣誉认证，4=任务单结算保障制度，5=违规做单管理条例*/
    goodscouponNumInfoUrl:'/goodscoupon/api/Goodscoupon/num_info', /*个人中心-优惠券数量*/
    usershareexperienceNumInfoUrl:'/api/Usershareexperience/num_info', /*个人中心-种草数量*/
    memberaccountSelectTypesUrl:'/api/Memberaccount/selectTypes', /*我的钱包筛选项*/
    memberaccountWalletInfoUrl:'/api/Memberaccount/walletInfo', /*我的钱包明细接口*/
    memberRecommendUrl:'/api/Member/recommend',  /*粉丝列表接口*/
    fansDataStatisticsUrl:'/api/Member/fansDataStatistics',   /*粉丝数据统计*/
    signinDetailUrl:'/membersignin/api/Signin/detail', /*签到商品奖励-签到详情*/
    signinUserUrl:'/membersignin/api/Signin/sign', /*签到商品奖励-用户签到*/
    signinCompleteLogsUrl:'/membersignin/api/Signin/completeLogs',  /*签到商品奖励-用户签到完成记录*/
    addressParseUrl:'/api/Express/addressParse', /*快递地址解析*/
    checkpaypasswordUrl:'/api/member/checkpaypassword', /*检测支付密码*/
    ShopInfoUrl: '/api/ShopManage/ShopInfo', /*店铺管理-店铺信息*/
    saleStatisticsUrl:'/api/ShopManage/saleStatistics', /*店铺管理-业绩统计（消费情况）*/
    SaleGoodsOrderUrl:'/api/ShopManage/SaleGoodsOrder', /*店铺管理-商品销量排行*/
    childListUrl:'/api/ShopManage/childList', /*店铺管理-粉丝列表*/
    saleOrderDataUrl:'/api/ShopManage/saleOrderData', /*店铺管理-消费情况订单数据*/
    performanceUrl:'/api/ShopManage/performance', /*店铺管理 - 业绩中心*/
    getAuthCodeUrl: '/api/Register/getAuthCode', /*APP注册--小程序获取验证码*/

    // 跨境相关接口
    cbecRealNameAuthInfoUrl:'/cbec_xyhc/api/CbecRealNameAuth/info', /*跨境实名认证信息*/
    cbecRealNameAuthEditUrl:'/cbec_xyhc/api/CbecRealNameAuth/edit', /*跨境实名认证编辑*/

    // 贡献值
    xmLeaguePointsUrl: '/leaguePoints/api/MemberLeaguePoint/index', /*加盟任务积分详情*/
    xmInOutRecordsUrl:'/leaguePoints/api/MemberLeaguePoint/inOutRecords', /*加盟任务-用户贡献值进出记录*/
    exchangeListUrl:'/leaguePoints/api/ExchangeLeaguePoint/exchangeList', /*兑换优惠券列表*/
    exchangeUrl:'/leaguePoints/api/ExchangeLeaguePoint/exchange', /*兑换优惠券*/

    //开通VIP掌柜
    memberOpenShopInfo: '/api/memberOpenShop/getOpenInfo', //开店页面接口
    memberGetopenShop: '/api/memberOpenShop/openShop', //用户开店接口
    memberCheckShopInfoByMobile: '/api/memberOpenShop/checkShopInfoByMobile',//检查手机号开店情况接口

    //分销客
    fenxiaokeDistributorUrl:'/api/readme/distributor', //分销客--申请分销客说明接口
    fenxiaokeApplyDistributorUrl:'/api/member/applyDistributor', //分销客--申请为分销客接口
    bookNoticeUrl:'/api/AppletMsg/bookNotice',  //消息订阅通知接口

    //周期购
    orderCycleManage:'/api/member/periodbuyordermanage', // 周期购订单管理接口
    orderCycleList:'/api/member/periodbuyorderlist', // 周期购订单列表接口
    cyclePurchaseListUrl:'/api/periodbuy/goodsList', //周期购商品列表|搜索列表
    periodbuyDetailUrl:'/api/periodbuy/detail', //周期购商品详情接口

    //秒杀
    getSeckillList: '/api/seckill/lists',// 秒杀活动列表
    getSeckillGoodsDetail:'/api/seckill/seckillGoodsDetail', //秒杀活动商品详情接口
    getSeckillSubmitorder:'/api/seckillorder/submitorder',// 秒杀订单提交
    getSeckillPayment:'/api/seckillorder/payment',// 秒杀确认订单页接口
	seckillGroup:'/h5api/goods/seckillGroup', //秒杀分组（通用接口）

    //分享赚活动
    ShareBuyActivitySuccessDetail:'/api/ShareBuyActivity/applyInfo',  //分享赚活动申请成功详情接口
	shareBuyApplyUrl:'/api/ShareBuyActivity/apply',//分享赚活动提交申请接口
	shareBuyApplyActivityDetail:'/api/ShareBuyActivity/applyActivity',//分享赚活动申请详情页接口
    shareBuyActivityInfo:'/api/ShareBuyActivity/activityInfo', //分享赚活动商品详情接口

    //大转盘
    getTurntableActivityInfo:'/api/LargeTurntable/turntableActivityInfo',//大转盘活动详情接口
    getMmemberAwardList:'/api/LargeTurntable/getMmemberAwardList',//会员中奖记录接口
    getTurntableLottery:'/api/LargeTurntable/turnTableLottery', //大转盘抽奖接口
	getMemberAwardPage:'/api/LargeTurntable/getMemberAwardPage',//会员中奖--我的礼品接口

	// 砍价
	bargain:"/api/BarginActivity/bargain",//用户帮砍接口
	launch:"/api/BarginActivity/launch",//用户发起砍价接口
	recordList:"/api/BarginActivity/record",//用户帮砍记录接口
	bargainActivityInfo:"/api/BarginActivity/activityInfo",//砍价活动详情接口



    //先迈参数转玖富小程序参数
    transformParam:"/api/website/transformParam",

	//迈豆专区
	maidouLidt:"/api/Maidou/goodsList",//迈豆专区商品列表
	getMaidouBanner:"/api/Maidou/page",//迈豆专区
	myShopData:"/api/shop/myShopStatistics",//我的店铺接口
	upgradeShop:"/api/member/upgradeShop",//会员申请开店详情接口
	shopDoc:"/api/readme/shopDoc",//店主介绍
	applyUpgradeShop:"/api/member/applyUpgradeShop",//申请开店
	changePayPassword:"/api/member/changePayPassword",//设置支付密码
	freezeMaidouList:"/api/memberaccount/freezeMaidouList",//冻结迈豆流水接口
	maidoulist:"/api/memberaccount/maidoulist",//我的迈豆流水接口
	recommandGoodList:"/api/Goods/recommandGoodList",// 推荐商品
	mymaidou:"/api/memberaccount/mymaidou",//我的迈豆接口

    //拼团活动
    pintuanGoodsDetail:"/api/Pintuan/goodsDetail",  //拼团商品详情接口
    pintuanGoodsList:"/api/Pintuan/goodsList",  //拼团商品列表
    pintuanGroupList:"/api/Pintuan/groupList",  //商品开团列表
	ptGroupDetailUrl:"/api/Pintuan/groupDetail", // 参团详情
	pintuanRuleUrl:'/api/Pintuan/pintuanRule', // 拼团规则
	inviteAdv:'/api/Member/inviteAdv',//邀请有礼
    pintuanShareReportUrl:'/api/Pintuan/share',  /*点击拼团分享按钮上报（含拼团商品详情页、邀请参团页）*/

    //任务专区
    topicGoodsListUrl:'/api/topic/goodsList', //任务专区列表
    getTopicGoodsListUrl:'/api/topic/getTopicGoodsList',  /*任务专区接口（新的--跟老的没区别）*/
    checkTopicIdsUrl: '/api/topic/checkTopicIds',  /*检测专题ids是否有效*/

    //新品专区
    newProductAreaGoodsList:'/api/NewProductArea/goodsList', /*新品专区商品列表*/

    // 问卷活动
    questionnaireActivityFormUrl:'/questionnaireActivity/api/questionnaire/index',  /*获取问卷表单--活动详情*/
    questionnaireActivitySubmitUrl:'/questionnaireActivity/api/questionnaire/submit',  /*获取问卷表单--提交表单*/
    questionnaireActivityDetailUrl:'/questionnaireActivity/api/questionnaire/detail',  /*获取问卷表单--表单详情*/
    questionnaireActivityEditDetailUrl:'/questionnaireActivity/api/questionnaire/editDetail',  /*获取问卷表单--编辑的表单回显详情*/
    questionnaireActivityEditUrl:'/questionnaireActivity/api/questionnaire/edit',  /*获取问卷表单--提交编辑过的表单*/
    questionnaireActivityListsUrl:'/questionnaireActivity/api/questionnaire/lists',  /*获取问卷表单--我的问卷提交记录*/
    questionnaireActivityChildListsUrl:'/questionnaireActivity/api/questionnaire/childLists',  /*获取问卷表单--好友提交问卷的记录*/




    //编译成h5使用的接口
    h5LoginUrl:'/api/login/h5Login',  /*h5先迈商城账号密码以及手机验证码登陆*/
    h5RegisterUrl:'/api/login/h5Register', /*h5注册*/
    retrievePasswordUrl:'/api/login/retrievePassword', /*找回密码*/
    wechatCustomPageUrl:'/api/pay/wechatCustomPage', /*公众号支付结果，小票接口*/
    h5wxSnsapiBaseUrl:'/api/pay/wxSnsapiBase',  /*静默授权获取openid*/
    h5PayPageUrl:'/api/pay/h5PayPage', /*小程序h5收银台*/
    getYoupinMemberTokenUrl:'/api/member/getYoupinMemberToken',  /*先迈token换取柚品h5 MemberToken*/

    // 退货退款
    applyOnlyRefund:'/api/orderrefund/applyOnlyRefund',  //提交售后（仅退款）
    orderrefund: '/api/orderrefund/return',  //退款信息详情
    applyReturn: '/api/orderrefund/applyReturn',  //提交售后（退货退款）
    exchange: '/api/orderrefund/exchange',  //售后页面（换货）详情
    applyExchange: '/api/orderrefund/applyExchange',  //提交售后页面（换货）
    detail: '/api/orderrefund/detail',  //售后详情
    delivery: '/api/orderrefund/delivery',  //提交物流信息
    cancel: '/api/orderrefund/cancel',  //撤销申请
    confirm: '/api/orderrefund/confirm',  //确认收货
    logistics: '/api/order/logistics',  //确认收货
    checkstatus: '/api/orderrefund/checkstatus',  //验证售后状态


    //优惠券指定商品列表
    goodsCouponList: '/goodscoupon/api/goodscoupon/goodsList',
    //优惠券类型详情
    goodsCouponTypeinfo: '/goodscoupon/api/goodscoupon/typeinfo',
    //用户商品优惠券列表
    memberpage: '/goodscoupon/api/goodscoupon/memberpage',
    //领取优惠券
    goodsCouponReceive: '/goodscoupon/api/goodscoupon/receive',
    //是否可以领取优惠券
    goodsCouponReceivedNum: '/goodscoupon/api/goodscoupon/receivedNum',


    //咨询文章
    usershareexperienceListUrl:'/api/Usershareexperience/list', /*资讯文章列表*/
    usershareexperienceContent:'/api/Usershareexperience/content', /*资讯文章详情*/
    usershareexperienceAddView:'/api/Usershareexperience/addView', /*资讯文章新增浏览*/
    usershareexperienceLike:'/api/Usershareexperience/like', /*资讯文章点赞*/
    usershareexperienceTransmit:'/api/Usershareexperience/transmit', /*资讯文章分享上报*/

    //直播
    getPlaybacks: '/broadcast/api/live/getPlaybacks',   //直播回放接口
    livePage: '/broadcast/api/live/roomList',   //直播回放接口
    getMemberByopenid: '/api/member/getMemberByopenid',  //share_openid换取recommend_member_id
    roominfo: '/broadcast/api/live/roominfo',  //直播间详情
    getLiveInfoUrl:'/api/topic/getLiveInfo', /*直播卡片中直播间详情信息*/

    // 多包裹列表
    getPackageList: '/api/order/getPackageList',

    // 多折扣优惠活动
    multipleDiscountinfo: '/multipleDiscount/api/multipleDiscount/info',  //多件折扣活动详情
    multipleDiscountgoodsList: '/multipleDiscount/api/multipleDiscount/goodsList',  //多件折扣活动商品列表

    // 优惠券弹窗信息
    use_remind: '/goodscoupon/api/goodscoupon/use_remind',


    // 种草功能
    // 个人主页发布内容
    usershareexperienceLaunch: '/api/Usershareexperience/launch',
    usershareexperienceEditLaunch: '/api/Usershareexperience/editLaunch',
    buyList: '/api/goods/buyList', //购买记录商品列表
    footprintList: '/api/goods/footprintList', //足迹商品列表
    usePublishGoodsUrl: '/api/Usershareexperience/usePublishGoods', // 用户未发布种草的商品

    //个人主页面
    personInfoUrl:'/api/Usershareexperience/personInfo', /*发现个人主页个人信息*/
    discoverListUrl:'/api/Usershareexperience/discoverList', /*“发现”-个人主页发布列表*/
    delLaunchUrl:'/api/Usershareexperience/delLaunch', /*删除发布种草内容*/

    //种草详情页面
    usershareexperienceVideoListUrl:'/api/Usershareexperience/videoList', /*视频列表页面*/
    commentListUrl:'/api/usershareexperience/commentList', /*种草文章评论列表*/
    publishCommentUrl:'/api/usershareexperience/publishComment', /*发表评论*/
    commentReplyUrl:'/api/usershareexperience/commentReply', /*回复评论列表*/

    //商品套餐组合销售
    comboDetailUrl:'/combo/api/combo/detail',  /*组合套餐详情*/
    comboSkuInfoUrl:'/combo/api/combo/info',  /*套餐sku选择后的详情*/
    comboCheapPriceUrl:'/combo/api/combo/cheapPrice', /*计算组合总优惠接口*/

    //企业微信素材
    materialDetailUrl:'/api/Material/detail',  /*素材详情（企微工具）*/

    //公共组件接口
    driftMessageUrl: '/api/Index/driftMessage',  /*滚动消息*/
    getSignDriftMessageUrl:'/api/Index/getSignDriftMessage', /*签到气泡显示消息*/
}
