import Config from '../config.js'
import JSEncrypt from 'jsencrypt'
import Util from '../util.js'
import store from '../../../store'
import {H5ReportSentry, WechatReportSentry} from '../report-sentry'
// #ifdef MP-WEIXIN
const wxLogger =require('../wx-logger');
// #endif


// #ifdef H5
const app_type = Util.isWeiXin() ? 'wechat' : 'h5';
const app_type_name = Util.isWeiXin() ? '微信公众号' : 'H5';
// #endif

// #ifdef MP-WEIXIN
const app_type = 'weapp';
const app_type_name = '微信小程序';
// #endif

// #ifdef MP-ALIPAY
const app_type = 'aliapp';
const app_type_name = '支付宝小程序';
// #endif

// #ifdef MP-BAIDU
const app_type = 'baiduapp';
const app_type_name = '百度小程序';
// #endif

// #ifdef MP-TOUTIAO
const app_type = 'MP-TOUTIAO';
const app_type_name = '头条小程序';
// #endif

// #ifdef MP-QQ
const app_type = 'MP-QQ';
const app_type_name = 'QQ小程序';
// #endif

// #ifdef APP-PLUS
const app_type = 'app';
const app_type_name = 'APP';
// #endif

//微信小程序直播插件
// #ifdef MP-WEIXIN
let livePlayer = requirePlugin('live-player-plugin');
// #endif

//  直播间分享卡片处理
const sceneList = [1007, 1008, 1014, 1044, 1045, 1046, 1047, 1048, 1049, 1073, 1154, 1155]

let verification_list = []; //弹验证码的url

async function getLiveCustomParams(scene_json) {
	let res = {}
	if (sceneList.includes(scene_json.scene)) {
		// 此判断只适合在直播间内通过分享卡片出来才会执行，链接分享不执行
		try {
			res = await livePlayer.getShareParams()
		}catch (e) {

		}
	}
	return res
}

export default {
	async sendRequest(params) {
    /*
    ** 此步骤相当于request拦截请求
    */
		var method = params.data != undefined ? 'POST' : 'GET', // 请求方式
			data = {
				app_type,
				app_type_name,
				project_type:Config.project_type,  //1先迈项目 0柚品项目
				version:Config.version,
			};
		var url = Config.baseUrl + params.url ; // 请求路径
		if(params.is_xm_url){
			delete params.is_xm_url;
			if(process.env.NODE_ENV === 'production'){
				url = Config.imgDomainXianmai + params.url
			}else{
				// #ifdef H5
				url = '/xm_apis' + params.url
				// #endif
				// #ifdef MP-WEIXIN
				url = Config.imgDomainXianmai + params.url
				// #endif
			}
		}
		// token
		if (uni.getStorageSync('token')) data.token = uni.getStorageSync('token');
		// 城市id
		if (uni.getStorageSync('city')) data.web_city = uni.getStorageSync('city').id;
		// 参数
		if (params.data != undefined) Object.assign(data, params.data);
		// 分享详情获取share_shop_id都要带上
		if (store.state.share_shop_id) data.share_shop_id = store.state.share_shop_id;
		// 店铺id
		data.shop_id = data.shop_id || uni.getStorageSync('shop_id');
		const region = uni.getStorageSync('region');
		const storeID = uni.getStorageSync('storeID');
		const region_storeID_create_time = uni.getStorageSync('region_storeID_create_time');
		//当前打开的页面
		let current_page_full_Path='';
		let current_page_Path='';
		let pages_arr = getCurrentPages().reverse();
		try{
			if(pages_arr && pages_arr.length>0){
				current_page_full_Path=pages_arr[0].$page.fullPath;
				current_page_Path = pages_arr[0].route
			}
		}catch (e) {
		}
		// 场景值
		const scene_json = uni.getLaunchOptionsSync()
		const weapp_scene = JSON.stringify(scene_json)
		let share_in_page = ''
		let query_dict = Util.GetRequestQuery(current_page_full_Path)
		let custom_params = query_dict.custom_params  //直播自定参数， 直播插件分享卡片出来的
		let live_custom_params = uni.getStorageSync('live_custom_params')
		if(custom_params) custom_params = JSON.parse(decodeURIComponent(custom_params))
		if(!custom_params){
			let res = await getLiveCustomParams(scene_json)  // 直播插件中分享二位码方式取query参数
			if(res.custom_params) custom_params = res.custom_params
		}
		if(query_dict.share_in_page){  //当前页面路径参数带有分享标记，冷热启动进入的第一个页面
			share_in_page = scene_json.query.share_in_page
		}
		if(custom_params instanceof Object && custom_params.share_in_page){
			share_in_page = custom_params.share_in_page
			if(pages_arr.length>1){
				// 判断上一级页面是不是直播插件页面，主要用途是插件页面跳入商品页面时
				if(pages_arr[1]==null){
					share_in_page = 0
				}
			}
		}
		let default_header = {
			'content-type': 'application/x-www-form-urlencoded;application/json',
			'weapp-referer':current_page_full_Path,
		}
		if(params.header && Object.prototype.toString.call(params.header) === '[object Object]' && Object.keys(params.header).length){
			default_header = Object.assign(params.header,default_header);
		}
		if(store.state.wechatCodeBill) default_header['wechat-code-bill']=store.state.wechatCodeBill
		if(region) default_header.region = region
		if(storeID) default_header.storeID = storeID
		if(region_storeID_create_time) default_header['region-storeID-create-time'] = region_storeID_create_time
		if(weapp_scene) default_header['weapp-scene'] = weapp_scene
		if(share_in_page) default_header['share-in-page'] = share_in_page
		if(share_in_page) default_header['share-in-page-visit-time'] = new Date().getTime()  //进页面的访问时间
		if(custom_params){
			default_header['live-custom-params'] = JSON.stringify(custom_params)
			if(live_custom_params){
				if(live_custom_params instanceof Object){
					if(JSON.stringify(live_custom_params)!=JSON.stringify(custom_params)){
						uni.setStorageSync('live_custom_params',custom_params)
					}
				}else{
					uni.setStorageSync('live_custom_params',custom_params)
				}
			}else{
				uni.setStorageSync('live_custom_params',custom_params)
			}
		}else{
			if(live_custom_params){
				default_header['live-custom-params'] = JSON.stringify(live_custom_params)
			}
		}
		// 请求参数加密
		if (Config.apiSecurity) {
			let jsencrypt = new JSEncrypt();
				jsencrypt.setPublicKey(Config.publicKey);
			let encrypt = encodeURIComponent(jsencrypt.encryptLong(JSON.stringify(data)));
			data = {
				encrypt,
				app_type,
				app_type_name
			};
    }


    /*
    ** 此方法用于响应response拦截请求
    */
    // 响应拦截http协议为Status Code为200的方法
    const interceptorResponseSuccess = (res,response) =>{
		if(typeof res == "string"){
			// #ifdef H5
			try{
				JSON.parse(res)
			}catch (e) {
				H5ReportSentry.captureMessage('api body json解析失败', {
					contexts: {
						request: {
							url: url,
							method: method,
							data: data,
							header: default_header,
						},
						response: {
							statusCode: response.statusCode,
							body: res, // 响应体
							header: response.header
						},
					},
				});
			}
			// #endif
		}
		let page_list = ['', '/pages/index/index/index']
		let codeType = store.state.wechatVerCode.codeType || 'http'
		if(res.code == -10030){
			if(!verification_list.includes(params.url)){
				verification_list.push(params.url)
			}
			if(res.data && res.data.is_refresh==1){
				codeType = 'httpAndRefresh'
			}
			if(!page_list.includes(current_page_full_Path.split('?')[0])){
				Util.toWechatVerificationCode(codeType)
			}
		}
		if(res.code==0){
			if(verification_list.length>0){
				if(verification_list.includes(params.url)){
					store.dispatch('writeWechatCodeBill')
					verification_list = []
				}
			}else{
				store.dispatch('writeWechatCodeBill')
			}
		}
        // 状态码为-10009时，移除storage保存的token值
        if(res.code == -10009){
			uni.removeStorageSync('token'); return
		}
		if(res.code == -20002){
			Util.toShowRealNamePopup()
		}
        // 除了状态码为0成功外，其它状态码均视为请求成功，做提示方法
        /* if(res.code != undefined && res.code != 0){
          Util.showToast({
            title: res.message ? res.message : '请求数据有问题',
            icon: 'none',
          })
        } */
		// 页面数据请求错误，需要跳回到首页
		if(res.code == -100){
			setTimeout(()=>{
				Util.redirectTo('/pages/index/index/index', {}, 'reLaunch');
			},3000)
		}
    }

    // 响应拦截http协议为StatusCode为404、500等的状态、或者请求失败网络错误的方法
    const interceptorResponseFail = (res) =>{
		if(res && res.errMsg == 'request:ok'){
			if(res && (res.statusCode == 404 || res.statusCode == 500)){
				Util.showToast({
					title: '网络故障，请联系客服处理',
					icon: 'none',
				})
				// #ifdef MP-WEIXIN
				WechatReportSentry.captureException(url+'，请求错误，'+res.errMsg+' '+res.statusCode)
				wxLogger.error('http response error',{url,method,data,responseStatusCode:res.statusCode});
				wxLogger.setFilterMsg(`http error`);
				// #endif
				// #ifdef H5
				H5ReportSentry.captureException(url+'，请求错误，'+res.errMsg+' '+res.statusCode)
				// #endif
				return false
			}
			if(res && res.statusCode == 403){
				// #ifdef MP-WEIXIN
				WechatReportSentry.captureException(url+'，请求错误，'+res.errMsg+' '+res.statusCode)
				wxLogger.error('http response error',{url,method,data,responseStatusCode:res.statusCode});
				wxLogger.setFilterMsg(`http error`);
				// #endif
				// #ifdef H5
				H5ReportSentry.captureException(url+'，请求错误，'+res.errMsg+' '+res.statusCode)
				// #endif
				if(current_page_full_Path.split('?')[0]!='/otherpages/store/store_empty/store_empty'){
					Util.redirectTo('/otherpages/store/store_empty/store_empty?status=403', {}, 'redirectTo')
				}
				return false
			}
			/* if(res.statusCode != 200 ){
				// 请求失败,请求头StatusCode不是200的情况下
				Util.showToast({
					title: '请求错误：'+res.statusCode,
					icon: 'none',
				});
			} */
		}else{
			// if(res && res.errMsg.indexOf('request:fail') > -1 && res.errMsg.length > 'request:fail'.length ){
			// 	// #ifdef MP-WEIXIN
			// 	WechatReportSentry.captureException(url+'，网络错误，'+res.errMsg)
			// 	// #endif
			// 	// #ifdef H5
			// 	H5ReportSentry.captureException(url+'，网络错误，'+res.errMsg)
			// 	// #endif
			// }
			if (res.errMsg === 'request:fail abort') {
				// 专门处理 request:fail abort
				return true
			}else if(res && res.errMsg == 'request:fail'){
				Util.showToast({
					title: '网络错误，请检查手机是否联网',
					icon: 'none',
				})
				return false
			}
		}
    }

		if (params.async === false) {
			//同步
			return new Promise((resolve, reject) => {
				uni.request({
					url: url,
					method: method,
					data: data,
					header: default_header,
					dataType: params.dataType || 'json',
					responseType: params.responseType || 'text',
					success: (res) => {
						interceptorResponseSuccess(res.data,res)
						// #ifdef MP-WEIXIN
						WechatReportSentry.consoleLog(JSON.stringify({request:{url,method,data,header: default_header},response:{statusCode:res.statusCode,data:res.data}}))
						// #endif
						resolve(res.data);
					},
					fail: (res) => {
						let err = interceptorResponseFail(res)
						// #ifdef MP-WEIXIN
						WechatReportSentry.consoleLog(JSON.stringify({request:{url,method,data,header: default_header},response:{statusCode:res.statusCode,data:res.data}}))
						// #endif
						if(err){
							// 对于 request:fail abort，返回特定格式，避免未处理拒绝
							resolve({code: -1,message: '请求被中止',errMsg: res.errMsg})
						}else{
							reject(res);
						}
					},
					complete: (res) => {
					}
				});
			});
		} else {

			//异步
			uni.request({
				url: url,
				method: method,
				data: data,
				header: default_header,
				dataType: params.dataType || 'json',
				responseType: params.responseType || 'text',
				success: (res) => {
					interceptorResponseSuccess(res.data,res)
					typeof params.success == 'function' && params.success(res.data);
					// #ifdef MP-WEIXIN
					WechatReportSentry.consoleLog(JSON.stringify({request:{url,method,data,header:default_header},response:{statusCode:res.statusCode,data:res.data}}))
					// #endif
				},
				fail: (res) => {
					interceptorResponseFail(res)
					typeof params.fail == 'function' && params.fail(res);
					// #ifdef MP-WEIXIN
					WechatReportSentry.consoleLog(JSON.stringify({request:{url,method,data,header:default_header},response:{statusCode:res.statusCode,data:res.data}}))
					// #endif
				},
				complete: (res) => {
					typeof params.complete == 'function' && params.complete(res);
				}
			});
		}
	}
}
