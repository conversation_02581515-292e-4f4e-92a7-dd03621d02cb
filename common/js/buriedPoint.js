import Http from './request/http.js'
import util from "./util";

export default {

	/**
	 * 分享埋点（触发按钮弹窗即可）
	 * @param {String} path  分享路径和参数
	 * @param {String} imageUrl  图片地址
	 * @param {String} title  分享名称
	 * @param {Boolean} sharePage  分享来源；默认为false为普通页;  true 为商品详情页
	 * @param {Object} goodsParam  商品参数对象； goods_id '' 或 sku_ids []
	 */
	pageShare( path, imageUrl='', title='', sharePage=false, goodsParam={} ) {

		let shareObj = {
			"share_image_url": imageUrl,
			"share_title": title?title:'youpin_shop',
			"share_to": 1,
			"share_from": 2,
			"is_goods_page": sharePage?1:0,
		}
		goodsParam=Object.assign(goodsParam,{behavior_report_event:'wxdata_page_share'});
		// #ifdef MP-WEIXIN
		if(sharePage){
			Http.sendRequest({
				url: '/api/Analysis/goodsList',
				data:goodsParam,
				success: res => {
					let it = res.data[0]

					Object.assign(shareObj,{
					  "spu_id": it.spu_id,
					  "spu_name": it.spu_name,
					  // "spu_standard_catg_id": it.spu_standard_catg_id,
					  "spu_catg_first_id": it.spu_catg_first_id,
					  "spu_catg_first_name": it.spu_catg_first_name,
					  "spu_catg_second_id": it.spu_catg_second_id,
					  "spu_catg_second_name": it.spu_catg_second_name
					})

					if(wx.reportEvent){
						wx.reportEvent("wxdata_page_share", shareObj)
					}
				}
			})
		}else {
			if(wx.reportEvent){
				wx.reportEvent("wxdata_page_share", shareObj)
			}
		}
		// #endif

		let targetObj = {
			path,
			success: res => {}
		}
		if(imageUrl != '') targetObj.imageUrl = imageUrl;
		if(title != '') targetObj.title = title;
		targetObj.title = util.shareTitleAddNickname(targetObj.title)
		return targetObj;

	},

	/**
	 * 结算埋点（进入订单确认页）
	 * @param {Object} param
	 */
	submitOrderContent( params) {
		// #ifdef MP-WEIXIN
		Http.sendRequest({
			url: '/api/Analysis/goodsList',
			data: {
				sku_ids: JSON.stringify(params.sku_ids),
				behavior_report_event:'wxdata_submit_order_content'
			},
			success: res => {
				if(res.data && Array.isArray(res.data) && res.data.length>0){
					res.data.map((it,index) => {

						if(wx.reportEvent){
							wx.reportEvent("wxdata_submit_order_content", {
								"spu_id": it.spu_id,
								"spu_name": it.spu_name,
								"sku_id": it.sku_id,
								"sku_name": it.sku_name,
								"price_original": it.price_original,
								"price_current": it.price_current,
								"sku_count": Number(params.num[index]),
								"is_goods_page": params.pages,
								// "spu_standard_catg_id": it.spu_standard_catg_id,
								"spu_catg_first_id": it.spu_catg_first_id,
								"spu_catg_first_name": it.spu_catg_first_name,
								"spu_catg_second_id": it.spu_catg_second_id,
								"spu_catg_second_name": it.spu_catg_second_name
							})
						}
					})

				}

			}
		})
		// #endif

	},

	/**
	 * 商品曝光埋点
	 * @param {Array} list  目标数组
	 * @param {String} id   传递的id值
	 * @param {Number} len  曝光的限制数量  默认0不限制
	 */
	exposeGoods( list , id = 'sku_id' , len=0) {
		if(list.length > 0){

			let idlist = []
			let num = len != 0 ? len:list.length

			for(let i = 0; i < num; i++){
				if(list[i] && list[i][id]) idlist.push(list[i][id])
			}
			// #ifdef MP-WEIXIN
			Http.sendRequest({
				url: '/api/Analysis/goodsList',
				data:{[id == 'sku_id'?'sku_ids':'goods_ids']:JSON.stringify(idlist),behavior_report_event:'wxdata_expose_goods'},
				success: res => {
					if(res.data && Array.isArray(res.data) && res.data.length > 0){
						res.data.map(item => {
							if(wx.reportEvent){
								wx.reportEvent("wxdata_expose_goods", {
									"spu_id": item.spu_id,
									"spu_name": item.spu_name,
									"sku_id": item.sku_id,
									"sku_name": item.sku_name,
									"price_original": item.price_original,
									"price_current": item.price_current,
									// "spu_standard_catg_id": item.spu_standard_catg_id,
									"spu_catg_first_id": item.spu_catg_first_id,
									"spu_catg_first_name": item.spu_catg_first_name,
									"spu_catg_second_id": item.spu_catg_second_id,
									"spu_catg_second_name": item.spu_catg_second_name
								})
							}
						})
					}
				}
			})
			// #endif
		}

	},


	/**
	 * 商品点击埋点
	 * @param {Object} param  ；
	 */
	clickGoods( params ) {
		let data = {behavior_report_event:'wxdata_click_goods'}

		params.sku_id && (data.sku_ids = JSON.stringify([params.sku_id]))
		params.goods_id && (data.goods_id = params.goods_id)
		// #ifdef MP-WEIXIN
		Http.sendRequest({
			url: '/api/Analysis/goodsList',
			data,
			success: res => {

				let item = res.data[0]
				if(item && item.spu_id && item.sku_id && item.spu_name && item.sku_name){
					if(wx.reportEvent){
						wx.reportEvent("wxdata_click_goods", {
							"spu_id": item.spu_id,
							"spu_name": item.spu_name,
							"sku_id": item.sku_id,
							"sku_name": item.sku_name,
							"price_original": item.price_original,
							"price_current": item.price_current,
							// "spu_standard_catg_id": item.spu_standard_catg_id,
							"spu_catg_first_id": item.spu_catg_first_id,
							"spu_catg_first_name": item.spu_catg_first_name,
							"spu_catg_second_id": item.spu_catg_second_id,
							"spu_catg_second_name": item.spu_catg_second_name
						})
					}
				}
			}
		})
		// #endif
	},

	/**
	 * 商品浏览埋点
	 * @param {Object} param  ；
	 */
	browseGoods( params ) {
		let data = {behavior_report_event:'wxdata_browse_goods'}

		params.sku_id && (data.sku_ids = JSON.stringify([params.sku_id]))
		params.id && (data.goods_id = params.id)
		// #ifdef MP-WEIXIN
		Http.sendRequest({
			url: '/api/Analysis/goodsList',
			data,
			success: res => {
				let item = res.data[0]
				if(item && item.spu_id && item.sku_id && item.spu_name && item.sku_name){
					if(wx.reportEvent){
						wx.reportEvent("wxdata_browse_goods", {
							"spu_id": item.spu_id,
							"spu_name": item.spu_name,
							"sku_id": item.sku_id,
							"sku_name": item.sku_name,
							"price_original": item.price_original,
							"price_current": item.price_current,
							// "spu_standard_catg_id": item.spu_standard_catg_id,
							"spu_catg_first_id": item.spu_catg_first_id,
							"spu_catg_first_name": item.spu_catg_first_name,
							"spu_catg_second_id": item.spu_catg_second_id,
							"spu_catg_second_name": item.spu_catg_second_name
						})
					}
				}
			}
		})
		// #endif
	},


	/**
	 * 商品加购减购埋点
	 * @param {Object} params
	 */
	purchaseGoods( params ) {
		// #ifdef MP-WEIXIN
		Http.sendRequest({
			url: '/api/Analysis/goodsList',
			data: {
				sku_ids: JSON.stringify(typeof params.id == 'object' ? params.id : [params.id]),
				behavior_report_event:'wxdata_purchase_goods'
			},
			success: res => {

				if(res.data && Array.isArray(res.data) && res.data.length>0){

					res.data.map((it,index) => {
						if(params.action_num[index] != 0){
							if(wx.reportEvent){
								wx.reportEvent("wxdata_purchase_goods", {
									"spu_id": it.spu_id,
									"spu_name": it.spu_name,
									"sku_id": it.sku_id,
									"sku_name": it.sku_name,
									"price_original": it.price_original,
									"price_current": it.price_current,
									"action_type": params.action_type,
									"action_num": params.action_num[index],
									"is_goods_page": params.is_goods_page,
									// "spu_standard_catg_id": it.spu_standard_catg_id,
									"spu_catg_first_id": it.spu_catg_first_id,
									"spu_catg_first_name": it.spu_catg_first_name,
									"spu_catg_second_id": it.spu_catg_second_id,
									"spu_catg_second_name": it.spu_catg_second_name
								})
							}
						}
					})
				}

			}
		})
		// #endif
	},


	/**
	 * 订单状态变更埋点
	 * @param {Object} param
	 */
	orderStatus( params) {
		let data = {behavior_report_event: params.status==250 ? 'wxdata_order_status_close':'wxdata_order_status'}

		params.order_id && (data.order_id = params.order_id)
		params.order_no && (data.order_no = params.order_no)
		params.out_trade_no && (data.out_trade_no = params.out_trade_no)

		// 区分商品和拼团的接口    不传默认为商品
		let url = params.orderType ? '/api/Analysis/pinOrderRelevant':'/api/Analysis/orderRelevant'
		// #ifdef MP-WEIXIN
		Http.sendRequest({
			url,
			data,
			success: res => {
				if(res.data && Array.isArray(res.data) && res.data.length>0){
					res.data.map( item => {
						if(wx.reportEvent){
							wx.reportEvent("wxdata_order_status", {
								"spu_id": item.spu_id,
								"spu_name": item.spu_name,
								"sku_id": item.sku_id,
								"sku_name": item.sku_name,
								"price_original": item.price_original,
								"price_current": item.price_current,
								"sku_count": item.sku_count,
								"sku_pay_amt": item.sku_pay_amt,
								"order_amt": item.order_amt,
								"pay_amt": item.pay_amt,
								"order_id": item.order_id,
								"order_status": params.status,
								// "spu_standard_catg_id": item.spu_standard_catg_id,
								"spu_catg_first_id": item.spu_catg_first_id,
								"spu_catg_first_name": item.spu_catg_first_name,
								"spu_catg_second_id": item.spu_catg_second_id,
								"spu_catg_second_name": item.spu_catg_second_name
							})
						}
					})
				}
			}
		})
		// #endif
	},

	/**
	 * 发起退款埋点
	 * @param {Object} param
	 */
	refundAll( param ) {
		param=Object.assign(param,{behavior_report_event:'wxdata_refund_status'})
		// #ifdef MP-WEIXIN
		Http.sendRequest({
			url: '/api/Analysis/orderRefundRelevant',
			data:param,
			success: res => {
				let item = res.data
				if(wx.reportEvent){
					wx.reportEvent("wxdata_refund", {
						"spu_id": item.spu_id,
						"spu_name": item.spu_name,
						"sku_id": item.sku_id,
						"sku_name": item.sku_name,
						"price_original": item.price_original,
						"price_current": item.price_current,
						"sku_count": item.sku_count,
						"sku_refund_amt": item.sku_refund_amt,
						"refund_amt": item.refund_amt,
						"order_id": item.order_id,
						"refund_order_id": item.refund_order_id,
						// "spu_standard_catg_id": item.spu_standard_catg_id,
						"spu_catg_first_id": item.spu_catg_first_id,
						"spu_catg_first_name": item.spu_catg_first_name,
						"spu_catg_second_id": item.spu_catg_second_id,
						"spu_catg_second_name": item.spu_catg_second_name
					})
				}
			}
		})
		// #endif


	},


	/***************************************************以下是自定义微信web分析事件上报*******************************************************/
	/**
	 * 事件名字：广告事件
	 * 事件说明： 用户与广告的交互
	 * 用户与广告的交互类型，如开屏广告展示 (display)、广告点击 (click)、点击跳过 (jump) 等
	 * @param {Object} param
	 * */
	diyReportAdEvent(param){
		// #ifdef MP-WEIXIN
		const region = uni.getStorageSync('region');
		const storeID = uni.getStorageSync('storeID');
		if(!param.diy_ad_location_type){
			//当前打开的页面
			let current_page_full_Path='';
			try{
				let pages_arr = getCurrentPages().reverse();
				if(pages_arr && pages_arr.length>0){
					current_page_full_Path=pages_arr[0].$page.fullPath;
				}
			}catch (e) {
			}
			if(current_page_full_Path.indexOf('/otherpages/shop/home/<USER>')!=-1){
				param.diy_ad_location_type = 'home'
			}else if(current_page_full_Path.indexOf('/otherpages/diy/diy/diy')!=-1){
				param.diy_ad_location_type = 'diy'
			}
		}
		if(wx.reportEvent){
			wx.reportEvent("diy_event_ad_event", {
				"diy_storeid": storeID,
				"diy_region": region,
				"diy_ad_location_type": param.diy_ad_location_type,
				"diy_material_path": param.diy_material_path,
				"diy_ad_type": param.diy_ad_type,
				"diy_target_page": param.diy_target_page,
				"diy_ad_id": param.diy_ad_id,
				"diy_action_type": param.diy_action_type
			})
		}
		// #endif
	},
	/**
	 * 事件名字：普通商品交互
	 * 事件说明： 商品页的行为
	 * 加入购物车 (add_cart)，立即购买 (purchase)，分享 (share)
	 * @param param
	 */
	diyReportGroupInteractionEvent(param={sku_id:null,goods_id:null,diy_action_type:null,is_goods_page:0}){
		// #ifdef MP-WEIXIN
		let data = {behavior_report_event:'diy_event_group_interaction'}

		param.sku_id && (data.sku_ids = JSON.stringify([param.sku_id]))
		param.goods_id && (data.goods_id = param.goods_id)
		if(wx.reportEvent){
			Http.sendRequest({
				url: '/api/Analysis/goodsList',
				data,
				success: res => {
					let item = res.data[0]
					if(item && item.spu_id && item.sku_id && item.spu_name && item.sku_name){
						const region = uni.getStorageSync('region');
						const storeID = uni.getStorageSync('storeID');
						let event_data = {
							"diy_storeid": storeID,
							"diy_region": region,
							"diy_product_name": item.spu_name,
							"diy_product_id": item.spu_id,
							"diy_action_type": param.diy_action_type,
							"action_num": 1,
							// "action_type": action_type,
							"is_goods_page": param.is_goods_page,
							"price_current": item.price_current,
							"price_original": item.price_original,
							"sku_id": item.sku_id,
							"sku_name": item.sku_name,
							"spu_catg_first_id": item.spu_catg_first_id,
							"spu_catg_first_name": item.spu_catg_first_name,
							"spu_catg_second_id": item.spu_catg_second_id,
							"spu_catg_second_name": item.spu_catg_second_name,
							"spu_id": item.spu_id,
							"spu_name": item.spu_name,
							// "spu_standard_catg_id": ""
						}
						if(param.diy_action_type == 'add_cart') event_data.action_type=0;
						wx.reportEvent("diy_event_group_interaction", event_data)
					}
				}
			})
		}
		// #endif
	},
	/**
	 * 事件名字：拼团商品交互
	 * 事件说明： 拼团商品页的行为
	 * 全部拼团 (view_all)，参团 (join_group)，开团 (start_group)，单独购买 (individual_purchase)，了解规则 (understand_rules)，分享 (share)
	 * @param param
	 */
	diyReportGroupBuyingInteractionEvent(param={sku_id:null,goods_id:null,diy_group_activity_id:null,diy_group_buying_id:null,diy_action_type:null,is_goods_page:0}){
		// #ifdef MP-WEIXIN
		let data = {behavior_report_event:'diy_event_group_buying_interaction'}

		param.sku_id && (data.sku_ids = JSON.stringify([param.sku_id]))
		param.goods_id && (data.goods_id = param.goods_id)
		if(wx.reportEvent){
			Http.sendRequest({
				url: '/api/Analysis/goodsList',
				data,
				success: res => {
					let item = res.data[0]
					if(item && item.spu_id && item.sku_id && item.spu_name && item.sku_name){
						const region = uni.getStorageSync('region');
						const storeID = uni.getStorageSync('storeID');
						wx.reportEvent("diy_event_group_buying_interaction", {
							"diy_storeid": storeID,
							"diy_region": region,
							"diy_group_activity_id": param.diy_group_activity_id,
							"diy_group_buying_id": param.diy_group_buying_id,
							"diy_product_name": item.spu_name,
							"diy_product_id": item.spu_id,
							"diy_action_type": param.diy_action_type,
							"action_num": 1,
							// "action_type": 6,
							"is_goods_page": param.is_goods_page,
							"price_current": item.price_current,
							"price_original": item.price_original,
							"sku_id": item.sku_id,
							"sku_name": item.sku_name,
							"spu_catg_first_id": item.spu_catg_first_id,
							"spu_catg_first_name": item.spu_catg_first_name,
							"spu_catg_second_id": item.spu_catg_second_id,
							"spu_catg_second_name": item.spu_catg_second_name,
							"spu_id": item.spu_id,
							"spu_name": item.spu_name,
							// "spu_standard_catg_id": ""
						})
					}
				}
			})
		}
		// #endif
	},
	/**
	 * 事件名字：分享有礼交互
	 * 事件说明： 分享有礼页面的行为
	 * 参团 (join_group)，开团 (start_group)，分享 (share)
	 * @param param
	 */
	diyReportShareRewardsInteractionEvent(param={sku_id:null,goods_id:null,diy_group_activity_id:null,diy_group_buying_id:null,diy_action_type:null,is_goods_page:0}){
		// #ifdef MP-WEIXIN
		let data = {behavior_report_event:'diy_event_share_rewards_interaction'}

		param.sku_id && (data.sku_ids = JSON.stringify([param.sku_id]))
		param.goods_id && (data.goods_id = param.goods_id)
		if(wx.reportEvent){
			Http.sendRequest({
				url: '/api/Analysis/goodsList',
				data,
				success: res => {
					let item = res.data[0]
					if(item && item.spu_id && item.sku_id && item.spu_name && item.sku_name){
						const region = uni.getStorageSync('region');
						const storeID = uni.getStorageSync('storeID');
						wx.reportEvent("diy_event_share_rewards_interaction", {
							"diy_storeid": storeID,
							"diy_region": region,
							"diy_group_activity_id": param.diy_group_activity_id,
							"diy_group_buying_id": param.diy_group_buying_id,
							"diy_product_name": item.spu_name,
							"diy_product_id": item.spu_id,
							"diy_action_type": param.diy_action_type,
							"action_num": 1,
							// "action_type": 6,
							"is_goods_page": param.is_goods_page,
							"price_current": item.price_current,
							"price_original": item.price_original,
							"sku_id": item.sku_id,
							"sku_name": item.sku_name,
							"spu_catg_first_id": item.spu_catg_first_id,
							"spu_catg_first_name": item.spu_catg_first_name,
							"spu_catg_second_id": item.spu_catg_second_id,
							"spu_catg_second_name": item.spu_catg_second_name,
							"spu_id": item.spu_id,
							"spu_name": item.spu_name,
							// "spu_standard_catg_id": ""
						})
					}
				}
			})
		}
		// #endif
	},
	/**
	 * 事件名字：拼团详情页
	 * 事件说明： 拼团详情页的行为
	 * 立即参团 (join_now)，返回首页 (return_home)，邀请参团 (invite_to_join)
	 * @param param
	 */
	diyReportGroupDetailInteractionEvent(param={sku_id:null,goods_id:null,diy_group_activity_id:null,diy_group_buying_id:null,diy_action_type:null,is_goods_page:0}){
		// #ifdef MP-WEIXIN
		let data = {behavior_report_event:'diy_event_group_detail_interaction'}

		param.sku_id && (data.sku_ids = JSON.stringify([param.sku_id]))
		param.goods_id && (data.goods_id = param.goods_id)
		if(wx.reportEvent){
			Http.sendRequest({
				url: '/api/Analysis/goodsList',
				data,
				success: res => {
					let item = res.data[0]
					if(item && item.spu_id && item.sku_id && item.spu_name && item.sku_name){
						const region = uni.getStorageSync('region');
						const storeID = uni.getStorageSync('storeID');
						wx.reportEvent("diy_event_group_detail_interaction", {
							"diy_storeid": storeID,
							"diy_region": region,
							"diy_group_activity_id": param.diy_group_activity_id,
							"diy_group_buying_id": param.diy_group_buying_id,
							"diy_product_name": item.spu_name,
							"diy_product_id": item.spu_id,
							"diy_action_type": param.diy_action_type,
							"action_num": 1,
							// "action_type": 6,
							"is_goods_page": param.is_goods_page,
							"price_current": item.price_current,
							"price_original": item.price_original,
							"sku_id": item.sku_id,
							"sku_name": item.sku_name,
							"spu_catg_first_id": item.spu_catg_first_id,
							"spu_catg_first_name": item.spu_catg_first_name,
							"spu_catg_second_id": item.spu_catg_second_id,
							"spu_catg_second_name": item.spu_catg_second_name,
							"spu_id": item.spu_id,
							"spu_name": item.spu_name,
							// "spu_standard_catg_id": ""
						})
					}
				}
			})
		}
		// #endif
	},
	/**
	 * 事件名字：客服按钮交互
	 * 事件说明： 用户与客服按钮的交互行为
	 * 点击按钮 (click_button)，关闭二维码 (close_qrcode)，识别二维码 (scan_qrcode)
	 * @param param
	 */
	diyReportCustomerServiceInteractionEvent(param){
		// #ifdef MP-WEIXIN
		const region = uni.getStorageSync('region');
		const storeID = uni.getStorageSync('storeID');
		//当前打开的页面
		let current_page_full_Path='';
		try{
			let pages_arr = getCurrentPages().reverse();
			if(pages_arr && pages_arr.length>0){
				current_page_full_Path=pages_arr[0].$page.fullPath;
			}
		}catch (e) {
		}
		if(wx.reportEvent){
			wx.reportEvent("diy_event_customer_service_interaction", {
				"diy_storeid": storeID,
				"diy_region": region,
				"diy_source_page": current_page_full_Path,
				"diy_action_type": param.diy_action_type
			})
		}
		// #endif
	},
	/**
	 * 事件名字：授权交互
	 * 事件说明： 登录授权过程中的交互
	 * 弹出授权 (popup_authorization)，确认授权 (confirm_authorization)，取消授权 (cancel_authorization)
	 * 授权手机号 (phone_authorization)，授权微信资料 (wechat_authorization)
	 * @param param
	 */
	diyReportAuthorizationInteractionEvent(param){
		// #ifdef MP-WEIXIN
		const region = uni.getStorageSync('region');
		const storeID = uni.getStorageSync('storeID');
		if(wx.reportEvent){
			wx.reportEvent("diy_event_authorization_interaction", {
				"diy_storeid": storeID,
				"diy_region": region,
				"diy_authorization_type": param.diy_authorization_type,
				"diy_action_type": param.diy_action_type
			})
		}
		// #endif
	},
	/**
	 * 事件名字：搜索交互
	 * 事件说明： 搜索页面的交互行为
	 * 用户进入搜索页面的来源，如首页 (home)，分类页 (category_page)
	 * 用户输入(user_input)，历史搜索(history)，推荐搜索(recommend)
	 * @param param
	 */
	diyReportSearchInteractionEvent(param){
		// #ifdef MP-WEIXIN
		const region = uni.getStorageSync('region');
		const storeID = uni.getStorageSync('storeID');
		let source_page = ''
		try{
			let pages_arr = getCurrentPages().reverse();
			if(pages_arr && pages_arr.length>0){
				// current_page_full_Path=pages_arr[0].$page.fullPath;
				for (let i = 0; i < pages_arr.length; i++) {
					if(pages_arr[i].$page.fullPath.indexOf('/otherpages/shop/category/category')!=-1){
						source_page = 'category_page'
						break;
					}else if(pages_arr[i].$page.fullPath.indexOf('/otherpages/diy/diy/diy')!=-1){
						source_page = 'diy'
						break;
					}else if(pages_arr[i].$page.fullPath.indexOf('/otherpages/shop/home/<USER>')!=-1){
						source_page = 'home'
						break;
					}
				}
			}
		}catch (e) {
		}
		if(wx.reportEvent){
			wx.reportEvent("diy_event_search_interaction", {
				"diy_storeid": storeID,
				"diy_region": region,
				"diy_input_type": param.diy_input_type,
				"diy_keyword": param.diy_keyword,
				"diy_source_page": source_page
			})
		}
		// #endif
	},
	/**
	 * 事件名字：分类检索交互
	 * 事件说明： 分类检索页面的交互行为
	 * 用户点击的分类名称
	 * @param param
	 */
	diyReportCategoryRetrievalInteractionEvent(param){
		// #ifdef MP-WEIXIN
		const region = uni.getStorageSync('region');
		const storeID = uni.getStorageSync('storeID');
		if(wx.reportEvent){
			wx.reportEvent("diy_event_category_retrieval_interaction", {
				"diy_storeid": storeID,
				"diy_region": region,
				"diy_clicked_category_name": param.diy_clicked_category_name
			})
		}
		// #endif
	},
	/**
	 * 事件名字：装修组件交互
	 * 事件说明： 装修组件中的交互行为
	 * 专题(topics)、广告(advertise)、公告(announcement)、图文(graphics and text)、图片(picture)、迈豆(maidou)、秒杀(flash sale)、拼团(group_buying)
	 * @param param
	 */
	diyReportDecorationComponentInteractionEvent(param){
		// #ifdef MP-WEIXIN
		const region = uni.getStorageSync('region');
		const storeID = uni.getStorageSync('storeID');
		let diy_component_name =''
		let diy_component_id = ''
		//当前打开的页面
		let current_page_full_Path='';
		try{
			let pages_arr = getCurrentPages().reverse();
			if(pages_arr && pages_arr.length>0){
				current_page_full_Path=pages_arr[0].$page.fullPath;
				diy_component_name = pages_arr[0].$holder.navigationBarTitleText
			}
		}catch (e) {
		}
		if(current_page_full_Path.indexOf('/otherpages/shop/home/<USER>')!=-1){
			diy_component_id = 'home'
			diy_component_name = diy_component_name || '首页'
		}else if(current_page_full_Path.indexOf('/otherpages/diy/diy/diy')!=-1){
			let theQuery = util.GetRequestQuery(current_page_full_Path)
			diy_component_id= theQuery.name
		}
		if(wx.reportEvent){
			wx.reportEvent("diy_event_decoration_component_interaction", {
				"diy_storeid": storeID,
				"diy_region": region,
				"diy_text": param.diy_text,
				"diy_link": param.diy_link,
				"diy_image": param.diy_image,
				"diy_component_id": diy_component_id,
				"diy_component_name": diy_component_name,
				"diy_template_name": param.diy_template_name
			})
		}
		// #endif
	},
	/**
	 * 事件名字：专题页面交互
	 * 事件说明： 专题页面的交互行为
	 * 点击商品 (click_goods)、展示 (display)
	 * @param param
	 */
	diyReportTopicPageInteractionEvent(param){
		// #ifdef MP-WEIXIN
		const region = uni.getStorageSync('region');
		const storeID = uni.getStorageSync('storeID');
		if(wx.reportEvent){
			wx.reportEvent("diy_event_topic_page_interaction", {
				"diy_storeid": storeID,
				"diy_region": region,
				"diy_product_link": param.diy_product_link,
				"diy_activity_title": param.diy_activity_title,
				"diy_template_name": param.diy_template_name,
				"diy_product_name": param.diy_product_name,
				"diy_action_type": param.diy_action_type
			})
		}
		// #endif
	},
	/**
	 * 事件名字：单页交互
	 * 事件说明： 定制的活动页面、落地页等通用页面的交互行为
	 * 用户的交互类型，如点击 (click)、展示 (display)
	 * @param param
	 */
	diyReportCustomPageInteractionEvent(param){
		// #ifdef MP-WEIXIN
		const region = uni.getStorageSync('region');
		const storeID = uni.getStorageSync('storeID');
		if(wx.reportEvent){
			wx.reportEvent("diy_event_custom_page_interaction", {
				"diy_storeid": storeID,
				"diy_region": region,
				"diy_click_image": param.diy_click_image,
				"diy_click_link": param.diy_click_link,
				"diy_click_text": param.diy_click_text,
				"diy_page_identifier": param.diy_page_identifier,
				"diy_page_title": param.diy_page_title,
				"diy_page_name": param.diy_page_name,
				"diy_action_type": param.diy_action_type
			})
		}
		// #endif
	},
}
