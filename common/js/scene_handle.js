/*
* 扫码进入小程序的参数处理
* */




/*
* 小程序跳转时，url的query参数与扫码进入小程序的映射表
* key 是scene值  value是query参数
* */
let query_scene_map={
    's':'shop_id',  //店铺id
    'm':'share_member_id', //分销客分享id
    'q':'is_shopkeeper', //否掌柜从app个人中心页面分享出来的
    'r':'recommend_member_id', //获取上级推荐的人的id
    'u':'sku_id',  //sku的id
    'b':'source_member',
    'p':'preview',
    'a':'share_activity_id',
    'l':'share_apply_id',
    'i':'id',
    'e':'period_id', //周期购商品详情页参数
    'v':'invitation_shop_id', //邀请成为店主页面参数
    't':'open_type', //邀请成为店主页面参数
    'wc':'encodesrc', //web页面的链接参数
    'rd':'room_id',  //直播间id
    'c':'combo_id', //优惠组合id
    'mi':'material_id', //素材id
    // 'n':'region', //投放渠道id(web分析)
    // 'd':'storeID', //投放区域id(web分析)
}

/*
* 把scene的值塞入到query中
* */
function scene_to_query(sceneKey,sceneValue,data,isLaunch){
    if (query_scene_map.hasOwnProperty(sceneKey)){
        let key=query_scene_map[sceneKey];
        if(isLaunch){
            data.query[key]=sceneValue;
        }else{
            data[key]=sceneValue;
        }

    }
}

/*
* 扫码进入小程序参数解析
* @param isLaunch  是否是小程序启动是调用，主要是options参数处理的差异，false是一般页面调用，重新初始化系统
* @param data  小程序参数
* */
export function scenePare(isLaunch,data) {
    let sceneDict={}; //扫二维进入小程序的参数
    if(Object.keys(data).length<1){
        return  sceneDict;
    }
    let sceneStr=isLaunch ? data.query.scene : data.scene;
    if (sceneStr) {
        var sceneParams = decodeURIComponent(sceneStr);
        sceneParams = sceneParams.split('&');
        if (sceneParams.length) {
            sceneParams.forEach(item => {
                let oneItme=item.split('=');
                if(oneItme.length>1){
                    sceneDict[oneItme[0]]=oneItme[1];
                }
            });
        }
    }
    for (const sceneKey in sceneDict) {
        scene_to_query(sceneKey,sceneDict[sceneKey],data,isLaunch);
    }
    return sceneDict
}

/*
* query参数键值对转换成scene参数
* @queryDict param 是query参数的键值对
* */
export function query_to_scene(queryDict){
    let scene="";
    let sceneArr=[];
    for (const queryKey in queryDict) {
        for (const sceneKey in query_scene_map) {
            if(query_scene_map[sceneKey]==queryKey){
                sceneArr.push(`${sceneKey}=${queryDict[queryKey]}`)
                break;
            }
        }
    }
    scene=sceneArr.join('&');
    return scene
}
