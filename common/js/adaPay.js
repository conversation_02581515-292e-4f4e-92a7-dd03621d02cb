!function(e,t){"object"==typeof exports&&"object"==typeof module?module.exports=t():"function"==typeof define&&define.amd?define([],t):"object"==typeof exports?exports.AdaPay=t():e.AdaPay=t()}(this,function(){return function(e){function t(r){if(n[r])return n[r].exports;var u=n[r]={i:r,l:!1,exports:{}};return e[r].call(u.exports,u,u.exports,t),u.l=!0,u.exports}var n={};return t.m=e,t.c=n,t.i=function(e){return e},t.d=function(e,n,r){t.o(e,n)||Object.defineProperty(e,n,{configurable:!1,enumerable:!0,get:r})},t.n=function(e){var n=e&&e.__esModule?function(){return e.default}:function(){return e};return t.d(n,"a",n),n},t.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},t.p="/",t(t.s=1)}([function(e,t,n){"use strict";function r(e,t){var n=s(e);if(n)return void t(n);"alipay_qr"===e.pay_channel||"wx_qr"===e.pay_channel?a(e,function(e){t(e)}):"wx_lite"===e.pay_channel?a(e,function(e){t(e)}):t({msg:"暂不支持该方式"})}function u(e,t){requestPayResult(e.query_url).then(function(e){t(e)}).catch(function(e){l.default.payResult.unknown.result_info={},t(l.default.payResult.unknown)})}function a(e,t){var n=JSON.parse(e.expend.pay_info);wx.requestPayment({timeStamp:n.timeStamp,nonceStr:n.nonceStr,package:n.package,signType:n.signType,paySign:n.paySign,success:function(n){l.default.payResult.succeeded.result_info=e,t(l.default.payResult.succeeded)},fail:function(n){l.default.payResult.failed.result_info=e,t(l.default.payResult.failed)}})}function s(e){var t=l.default.channelConfig;return"succeeded"!==e.status?l.default.chackMsg.orderError:t.indexOf(e.pay_channel)<0?l.default.chackMsg.channelError:"false"===e.prod_mode?l.default.chackMsg.otherError:void 0}var o=n(2),l=function(e){return e&&e.__esModule?e:{default:e}}(o);e.exports={doPay:r,query:u}},function(e,t,n){"use strict";function r(e,t){return a.default.doPay(e,t)}var u=n(0),a=function(e){return e&&e.__esModule?e:{default:e}}(u);e.exports={doPay:r,version:"1.0.4"}},function(e,t,n){"use strict";var r={};e.exports=r,r.channelConfig=["alipay_qr","alipay_wap","wx_lite"],r.chackMsg={orderError:"发起支付失败",channelError:"支付渠道参数错误",amountError:"支付金额参数错误",queryUrlError:"支付结果url参数未知"},r.payStatus={succeeded:"succeeded",failed:"failed",pending:"pending",timeout:"timeout",cancel:"unknown",unknown:"unknown",paramError:"paramError"},r.payResult={succeeded:{result_status:r.payStatus.succeeded,result_message:"订单支付成功",result_info:{}},failed:{result_status:r.payStatus.failed,result_message:"订单支付失败",result_info:{}},pending:{result_status:r.payStatus.pending,result_message:"订单支付中",result_info:{}},timeout:{result_status:r.payStatus.timeout,result_message:"订单支付超时",result_info:{}},cancel:{result_status:r.payStatus.cancel,result_message:"支付取消",result_info:{}},unknown:{result_status:r.payStatus.unknown,result_message:"订单结果未知",result_info:{}},paramError:{result_status:r.payStatus.paramError,result_message:"参数错误",result_info:{}}}}])});
//# sourceMappingURL=adaPay.js.map