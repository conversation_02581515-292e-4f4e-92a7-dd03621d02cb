import Vue from 'vue'
import Config from './config.js'
import AdaPay from "./adaPay";
import Http from './request/http.js'
import apiUrl from './apiurls'
import { Weixin } from "./wx-jssdk";
import uniCopy from "./uni-copy";
// #ifdef H5
import {
	checkIsWXAppInstalled,
	goClosePage,
	isIOS,
	isOnXianMaiApp,
	launchMiniProgram,
	schemeGo,
	updateLoginPageData
} from "./h5/appOP";
import { appHomeUrl, webUrl } from "./h5/appSchemeUrl";
// #endif

import buriedPoint from './buriedPoint.js'
import util from "../../components/uni-calendar/util";

/*精准计时器,低时间差*/
class AdjustingInterval {
	/*
	* @param {function} workFunc 每次定时执行的函数
	* @param  {int} interval 定时器间隔时间 单位毫秒
	* @param {int} remainder 定时器需要运行的时间 单位秒
	* @param {function} completeFunc 定时器运行完成的后执行的函数
	* */
	constructor(workFunc, interval, remainder, completeFunc) {
		this.workFunc = workFunc;
		this.interval = interval;
		this.remainder = remainder;
		this.completeFunc = completeFunc;
		this.count = 0;  //定时器运行重复的次数
		this.startTime = 0;
		this.intervalObj = null;
	}
	start() {
		this.count = 0;
		this.startTime = new Date().getTime();
		this.intervalObj = setTimeout(() => {
			this.step();
		}, this.interval);
	}
	step() {
		if (this.remainder && this.count >= this.remainder) {
			if (typeof this.completeFunc == 'function') {
				this.completeFunc();
			}
			this.stop();
			return
		}
		if (typeof this.workFunc == 'function') {
			this.workFunc();
		}
		this.count++;
		let offset = new Date().getTime() - (this.startTime + this.count * 1000);
		let nextTime = this.interval - offset;
		if (nextTime < 0) nextTime = 0;
		this.intervalObj = setTimeout(() => {
			this.step();
		}, nextTime);
	}
	stop() {
		clearTimeout(this.intervalObj);
	}
}

export default {
	/**
	 * 获取path的query参数
	 * @param url {String} path路径
	 * @returns {Object} 返回query参数
	 * @constructor
	 */
	GetRequestQuery(url) {
		let theQuery = new Object();
		let url_list = url.split('?');
		if (url_list.length < 2) {
			return theQuery
		}
		let str = url_list[1];
		let strs = str.split("&");
		for (let i = 0; i < strs.length; i++) {
			theQuery[strs[i].split("=")[0]] = unescape(strs[i].split("=")[1]);
		}
		return theQuery;
	},
	/**
	 * 页面跳转
	 * @param {string} to 跳转链接 /pages/idnex/index
	 * @param {Object} param 参数 {key : value, ...}
	 * @param {string} mode 模式
	 */
	redirectTo(to, param, mode) {
		let url = to;
		const old_region = uni.getStorageSync('region');
		const old_storeID = uni.getStorageSync('storeID');
		let theQuery = this.GetRequestQuery(url)
		if(!(param instanceof Object)){
			param = {}
		}
		if(param['region'] && param['region']!=old_region){
			uni.setStorageSync('region', param['region'])
		}
		if(param['storeID'] && param['storeID']!=old_storeID){
			uni.setStorageSync('storeID', param['storeID'])
		}
		if(theQuery.region && old_region!=theQuery.region){
			param['region'] = theQuery.region
			uni.setStorageSync('region', param['region'])
		}
		if(theQuery.storeID && old_storeID!=theQuery.storeID){
			param['storeID'] = theQuery.storeID
			uni.setStorageSync('storeID', param['storeID'])
		}
		if(!param['region']){
			if(old_region) param['region'] = old_region;
		}
		if(!param['storeID']){
			if(old_storeID) param['storeID'] = old_storeID;
		}
		if(param['region'] || param['storeID']){
			if(old_region!=param['region'] || old_storeID!=param['storeID']){
				uni.setStorageSync('region_storeID_create_time',new Date().getTime())
			}
		}
		if (param != undefined) {
			// #ifdef H5
			if (param.back) {
				param.back = encodeURIComponent(param.back);
			}
			// #endif
			Object.keys(param).forEach(function (key) {
				if (url.indexOf('?') != -1) {
					url += "&" + key + "=" + param[key];
				} else {
					url += "?" + key + "=" + param[key];
				}
			});
		}
		// #ifdef H5
		if (isOnXianMaiApp) {
			if (url.indexOf('otherpages/shop/home/<USER>') != -1) {
				schemeGo(appHomeUrl);
				return;
			}
		}
		let queryObject = this.GetRequestQuery(url);
		if ((!queryObject['shop_id'] && url.indexOf('shop_id') == -1) || (queryObject['shop_id'] != uni.getStorageSync('shop_id'))) {
			queryObject['shop_id'] = uni.getStorageSync('shop_id');
		}
		if (!queryObject.hasOwnProperty('recommend_member_id')) {
			let recommend_member_id = uni.getStorageSync('member_id')
			if (recommend_member_id) {
				queryObject['recommend_member_id'] = recommend_member_id;
			}
		}
		let original_url = url.split('?')[0];
		url = original_url + '?' + Object.keys(queryObject).map((item) => item + '=' + queryObject[item]).join('&');
		// #endif
		let productUrl = ['/promotionpages/pintuan/detail/detail', '/pages/goods/detail/detail', '/promotionpages/new_seckill/detail/detail']
		if (productUrl.includes(to.split("?")[0])) {
			let objs = this.GetRequestQuery(url);
			buriedPoint.clickGoods(objs)
		}

		switch (mode) {
			case 'tabbar':
				// 跳转到 tabBar 页面，并关闭其他所有非 tabBar 页面。
				uni.switchTab({
					url: url
				})
				break;
			case 'redirectTo':
				// 关闭当前页面，跳转到应用内的某个页面。
				uni.redirectTo({
					url: url
				});
				break;
			case 'reLaunch':
				// 关闭所有页面，打开到应用内的某个页面。
				uni.reLaunch({
					url: url
				});
				break;
			default:
				// #ifdef MP-WEIXIN
				let arr = getCurrentPages().reverse();
				arr = arr.filter(item=>item)
				for (let i = 0; i < arr.length; i++) {
					if (url == arr[i].$page.fullPath) {
						uni.navigateBack({
							delta: i
						});
						return;
					}
				}
				// 微信小程序路由层级最大为10层
				if(arr.length>=10){
					uni.redirectTo({
						url: url
					});
					break;
				}
				// #endif
				// 保留当前页面，跳转到应用内的某个页面
				uni.navigateTo({
					url: url
				});
		}
	},
	/**
	 * 图片路径转换
	 * @param {String} img_path 图片地址
	 * @param {Object} params 参数，针对商品、相册里面的图片区分大中小，size: big、mid、small
	 */
	img(img_path, params) {
		var path = "";
		if (img_path != undefined && img_path != "") {
			if (params && img_path != this.getDefaultImage().default_goods_img) {
				// 过滤默认图
				let arr = img_path.split(".");
				let suffix = arr[arr.length - 1];
				arr.pop();
				arr[arr.length - 1] = arr[arr.length - 1] + "_" + params.size;
				arr.push(suffix);
				img_path = arr.join(".");
			}
			if (img_path.indexOf("http://") == -1 && img_path.indexOf("https://") == -1) {
				if (img_path.indexOf('attachment') == 0) {
					path = Config.imgDomainXianmai + "/" + img_path;
				} else {
					path = Config.imgDomain + "/" + img_path;
				}

			} else {
				path = img_path;
			}
		}
		return path;
	},
	/**
	 * 获取默认图
	 * @param {Object} link
	 */
	getDefaultImage() {
		let defaultImg = uni.getStorageSync('default_img');
		if (defaultImg) {
			defaultImg = JSON.parse(defaultImg);
			defaultImg.default_goods_img = this.img(defaultImg.default_goods_img);
			defaultImg.default_headimg = this.img(defaultImg.default_headimg);
			defaultImg.default_shop_img = this.img(defaultImg.default_shop_img);
			return defaultImg;
		} else {
			return {
				default_goods_img: '',
				default_headimg: '',
				default_shop_img: ''
			};
		}
	},
	/**
	 * 时间戳转日期格式
	 * @param {Object} timeStamp
	 */
	timeStampTurnTime(timeStamp) {
		if (timeStamp != undefined && timeStamp != "" && timeStamp > 0) {
			var date = new Date();
			date.setTime(timeStamp * 1000);
			var y = date.getFullYear();
			var m = date.getMonth() + 1;
			m = m < 10 ? ('0' + m) : m;
			var d = date.getDate();
			d = d < 10 ? ('0' + d) : d;
			var h = date.getHours();
			h = h < 10 ? ('0' + h) : h;
			var minute = date.getMinutes();
			var second = date.getSeconds();
			minute = minute < 10 ? ('0' + minute) : minute;
			second = second < 10 ? ('0' + second) : second;
			return y + '-' + m + '-' + d + ' ' + h + ':' + minute + ':' + second;
		} else {
			return "";
		}
	},
	/**
	 * 倒计时
	 * @param {Object} seconds 秒
	 */
	countDown(seconds) {
		let [day, hour, minute, second] = [0, 0, 0, 0]
		if (seconds > 0) {
			day = Math.floor(seconds / (60 * 60 * 24))
			hour = Math.floor(seconds / (60 * 60)) - (day * 24)
			minute = Math.floor(seconds / 60) - (day * 24 * 60) - (hour * 60)
			second = Math.floor(seconds) - (day * 24 * 60 * 60) - (hour * 60 * 60) - (minute * 60)
		}
		if (day < 10) {
			// day = '0' + day
		}
		if (hour < 10) {
			// hour = '0' + hour
		}
		if (minute < 10) {
			// minute = '0' + minute
		}
		if (second < 10) {
			// second = '0' + second
		}
		return {
			d: day,
			h: hour,
			i: minute,
			s: second
		};
	},
	/**
	 * 数值去重
	 * @param {Array} arr 数组
	 * @param {string} field 字段
	 */
	unique(arr, field) {
		const res = new Map();
		return arr.filter((a) => !res.has(a[field]) && res.set(a[field], 1));
	},
	/**
	 * 判断值是否在数组中
	 * @param {Object} elem
	 * @param {Object} arr
	 * @param {Object} i
	 */
	inArray: function (elem, arr) {
		return arr == null ? -1 : arr.indexOf(elem);
	},
	/**
	 * 获取某天日期
	 * @param {Object} day
	 */
	getDay: function (day) {
		var today = new Date();
		var targetday_milliseconds = today.getTime() + 1000 * 60 * 60 * 24 * day;
		today.setTime(targetday_milliseconds);

		const doHandleMonth = function (month) {
			var m = month;
			if (month.toString().length == 1) {
				m = "0" + month;
			}
			return m
		}

		var tYear = today.getFullYear();
		var tMonth = today.getMonth();
		var tDate = today.getDate();
		var tWeek = today.getDay();
		var time = parseInt(today.getTime() / 1000);
		tMonth = doHandleMonth(tMonth + 1);
		tDate = doHandleMonth(tDate);

		const week = ['周日', '周一', '周二', '周三', '周四', '周五', '周六']
		return {
			't': time,
			'y': tYear,
			'm': tMonth,
			'd': tDate,
			'w': week[tWeek]
		};
	},
	/**
	 * 图片选择加上传
	 * @param number num
	 * @param {Object} params
	 * @param {Object} callback
	 * @param {Array} sourceType  ['album', 'camera']
	 * @param {Array} sizeType ['original', 'compressed']
	 * return array
	 */
	upload: function (num, params, callback, sourceType=['album', 'camera'],sizeType=['compressed']) {
		var data = {
			token: uni.getStorageSync('token'),
			private_key: Config.privateKey
		}
		data = Object.assign(data, params);
		var imgs_num = num;
		var _self = this;
		uni.chooseImage({
			count: imgs_num,
			sizeType, //可以指定是原图还是压缩图，默认二者都有
			sourceType: sourceType ? sourceType:['album', 'camera'], //从相册或者拍照
			success: async function (res) {
				const tempFilePaths = res.tempFilePaths;
				console.log(res);
				// typeof callback == 'function' && callback(tempFilePaths);
				uni.showLoading({
					title: '上传中',
					mask: true
				});

				var _data = data;
				var imgs = [];
				try{
					for (var i = 0; i < tempFilePaths.length; i++) {
						var path = await _self.upload_file_server(tempFilePaths[i], _data, params.path);
						imgs.push(path);
					}
					if(tempFilePaths.length == imgs.length) {
						uni.hideLoading()
					}
					typeof callback == 'function' && callback(imgs);
				}catch (e) {

				}
			}
		});
	},
	//上传图片
	upload_file_server(tempFilePath, data, path) {
		var _self = this;
		return new Promise((resolve, reject) => {
			uni.uploadFile({
				url: Config.baseUrl + '/api/upload/' + path,
				filePath: tempFilePath,
				name: 'file',
				formData: data,
				success: function (res) {
					var path_str = JSON.parse(res.data);
					if (path_str.code >= 0) {
						resolve(path_str.data.pic_path);
					} else {
						uni.hideLoading();
						_self.showToast({
							title: path_str.message
						})
						reject(path_str);
					}
				},
				fail: function (res) {
					console.log(res);
					uni.hideLoading();
					reject(res);
				}
			});
		});
	},
	//上传视频
	upload_video_server(tempFilePath, data, path) {
		var _self = this;
		return new Promise((resolve, reject) => {
			uni.uploadFile({
				url: Config.baseUrl + '/api/upload/' + path,
				filePath: tempFilePath,
				name: 'file',
				formData: data,
				success: function (res) {
					uni.hideLoading();
					var path_str = JSON.parse(res.data);
					if (path_str.code >= 0) {
						resolve(path_str.data);
					} else {
						_self.showToast({
							title: path_str.data
						})
						reject("error");
					}
				},
				fail: function (res) {
					_self.showToast({
						title: res
					})
					uni.hideLoading();
				}
			});

		});
	},
	editVideo(params, callback) {
		// #ifdef MP-WEIXIN
		uni.openVideoEditor({
			filePath: params.files,
			success: async function (res) {
				var data = await _self.upload_video_server(res.tempFilePat, {}, params.path);
				typeof callback == 'function' && callback(data);
			}
		})
	// #endif
	},
	// 上传视频处理器
	uploadVideo: function(params, callback) {
		var _self = this;
		uni.chooseVideo({
			sourceType: ['album'],
			compressed: false,
			success: async function (res) {
				if(res.duration < 100) {
					uni.showLoading({
						title: '视频处理中',
						mask:true
					});
					// console.log('chooseVideo', res);
						// 获取视频信息 用于压缩的传值bitrate，fps
						uni.getVideoInfo({
							src: res.tempFilePath,
							success: async function (e) {
								console.log('getVideoInfo', e);
								// #ifdef MP-WEIXIN
								// 压缩视频处理
								uni.compressVideo({
									src: res.tempFilePath,
									quality: 'high',  // 'low', 'medium', 'high'
									bitrate: e.bitrate,
									fps: e.fps,
									success: async function (compress) {
										console.log('compress', res);
										uni.hideLoading();
										// 剪切视频处理
										uni.openVideoEditor({
											filePath: compress.tempFilePath,
											success: async function (editor) {
												console.log('editor', res);
												uni.showLoading({
													title: '上传中',
													mask:true
												});
												var data = await _self.upload_video_server(editor.tempFilePath, {}, params.path);
												console.log('data', data);
												typeof callback == 'function' && callback({
													...data,
													duration: (editor.duration/1000).toFixed(2),
													video_metadata: e
												});
											},
											fail: function (res) {
												console.log('openVideoEditor fail', res);
												uni.hideLoading();
											}
										})
									},
									fail: function (res) {
										console.log('compressVideo fail', res);
										uni.hideLoading();
									}
								})
								// #endif

								// #ifdef H5
								uni.hideLoading();
								uni.showLoading({
									title: '上传中',
									mask:true
								});
								var data = await _self.upload_video_server(res.tempFilePath, {}, params.path);
								typeof callback == 'function' && callback({
									...data,
									duration: res.duration,
									video_metadata: e
								});
								// #endif
							},
							fail: function (res) {
								console.log('getVideoInfo fail', res);
								uni.hideLoading();
							}
						})
				}else{
					_self.showToast({
						title: '时长太长啦，试试其他视频吧'
					})
				}
			},
			fail: res => {
				console.log('fail', res)
			},
			complete: res => {
				console.log('complete', res)
			}
		})
	},
	/**
	 * 复制
	 * @param {Object} value
	 * @param {Object} callback
	 */
	copy(value, callback) {
		// #ifdef H5
		uniCopy({
			content: value,
			success: (res) => {
				this.showToast({
					title: '复制成功'
				});
				typeof callback == 'function' && callback();
			},
			error: (e) => {
				this.showToast({
					title: '复制失败'
				});
				typeof callback == 'function' && callback();
			}
		})
		// #endif

		// #ifdef MP || APP-PLUS
		uni.setClipboardData({
			data: value,
			success: () => {
				typeof callback == 'function' && callback();
			}
		});
		// #endif
	},
	/**
	 * 是否是微信浏览器
	 */
	isWeiXin() {
		// #ifndef H5
		return false;
		// #endif
		var ua = navigator.userAgent.toLowerCase();
		if (ua.match(/MicroMessenger/i) == "micromessenger") {
			return true;
		} else {
			return false;
		}
	},

	/**
	 * 显示消息提示框
	 *  @param {Object} params 参数
	 */
	showToast(params = {}) {
		params.title = params.title || "";
		params.icon = params.icon || "none";
		params.position = params.position || 'bottom';
		uni.showToast(params);
	},
	/**
	 * 检测苹果X以上的手机
	 */
	isIPhoneX() {
		let res = uni.getSystemInfoSync();
		if (res.model.search('iPhone X') != -1) {
			return true;
		}
		return false;
	},
	/**
	 * 深度拷贝对象
	 * @param {Object} obj
	 */
	deepClone(obj) {
		const isObject = function (obj) {
			return typeof obj == 'object';
		}

		if (!isObject(obj)) {
			throw new Error('obj 不是一个对象！')
		}
		//判断传进来的是对象还是数组
		let isArray = Array.isArray(obj)
		let cloneObj = isArray ? [] : {}
		//通过for...in来拷贝
		for (let key in obj) {
			cloneObj[key] = isObject(obj[key]) ? this.deepClone(obj[key]) : obj[key]
		}
		return cloneObj
	},
	/**
	 * 重置TabBar
	 */
	refreshBottomNav() {
		var bottomNav = uni.getStorageSync("bottom_nav");
		bottomNav = JSON.parse(bottomNav);
		for (var i = 0; i < bottomNav.list.length; i++) {
			var item = bottomNav.list[i];
			var obj = {
				index: i
			};
			obj.text = item.title;
			obj.iconPath = this.img(item.iconPath);
			obj.selectedIconPath = this.img(item.selectedIconPath);
			if (bottomNav.type == 1) {
				// 图文
			} else if (bottomNav.type == 2) {
				// 图片
			} else if (bottomNav.type == 3) {
				// 文字
			}
			uni.setTabBarItem(obj);
		}
	},
	/**
	 * 自定义模板的跳转链接
	 * @param {Object} link
	 */
	diyRedirectTo(link, mode) {
		if (link == null || link == '' || !link.wap_url) return;
		if (link.wap_url.indexOf('http') != -1) {
			// #ifdef MP-WEIXIN
			this.redirectTo('/otherpages/web/web?src=' + encodeURIComponent(link.wap_url), {}, mode);
			// #endif
			// #ifdef H5
			if (isOnXianMaiApp) {
				// this.redirectTo('/otherpages/web/web?src=' + encodeURIComponent(link.wap_url), {}, mode);
				schemeGo(webUrl + link.wap_url);
			} else {
				window.location.href = link.wap_url;
			}
			// #endif
		} else {
			if (link.shop_id) {
				this.redirectTo(link.wap_url, { shop_id: link.shop_id }, mode);
			} else {
				this.redirectTo(link.wap_url, {}, mode);
			}
		}
	},
	/**
	 * 兼容app跳转处理
	 */
	diyCompateRedirectTo(link) {
		if (link == null || link == '' || !link.wap_url) return;
		let { wap_url, shop_id } = link
		// 包含域名直接访问
		if(wap_url.indexOf('http') != -1){
			this.diyRedirectTo(link)
		}else{
			// #ifdef H5
			if (isOnXianMaiApp) {
				if(wap_url.indexOf('xm://')!=-1){
					schemeGo(wap_url);
					return;
				}
				wap_url=uni.getStorageSync('appOrigin') + wap_url.substr(1);
				let queryObject = this.GetRequestQuery(wap_url);
				queryObject['shop_id'] = uni.getStorageSync('shop_id');
				let recommend_member_id=uni.getStorageSync('member_id');
				if (recommend_member_id) {
					queryObject['recommend_member_id'] = recommend_member_id;
				}
				let original_url = wap_url.split('?')[0];
				let url = original_url + '?' + Object.keys(queryObject).map((item) => item + '=' + queryObject[item]).join('&');
				// app手动加域名处理
				this.diyRedirectTo({
					wap_url: url,
					shop_id
				})
			}else{
				this.diyRedirectTo(link)
			}
			// #endif
			// #ifdef MP-WEIXIN
			this.diyRedirectTo(link)
			// #endif
		}
	},

	/**
	 * 判断手机是否为iphoneX系列
	 */
	uniappIsIPhoneX() {
		let isIphoneX = false;
		let systemInfo = uni.getSystemInfoSync();
		// #ifdef MP || APP-PLUS
		if (systemInfo.model.search('iPhone X') != -1) {
			isIphoneX = true;
		}
		// #endif

		// #ifdef H5
		var u = navigator.userAgent;
		var isIOS = !!u.match(/\(i[^;]+;( U;)? CPU.+Mac OS X/); //ios终端
		if (isIOS) {
			if (systemInfo.screenWidth == 375 && systemInfo.screenHeight == 812 && systemInfo.pixelRatio == 3) {
				isIphoneX = true;
			} else if (systemInfo.screenWidth == 414 && systemInfo.screenHeight == 896 && systemInfo.pixelRatio == 3) {
				isIphoneX = true;
			} else if (systemInfo.screenWidth == 414 && systemInfo.screenHeight == 896 && systemInfo.pixelRatio == 2) {
				isIphoneX = true;
			}
		}
		// #endif
		return isIphoneX;
	},
	/**
	 * 判断当前页面栈是否存在，如果存在，则返回该页面栈，否则跳转到该页面
	 * @param {String} url 参数
	 */
	jumpPage(url) {
		let jump = true;
		let arr = getCurrentPages().reverse();
		for (let i = 0; i < arr.length; i++) {
			if (url.indexOf(arr[i].route) != -1) {
				jump = false;
				uni.navigateBack({
					delta: i
				});
				break;
			}
		}
		if (jump) {
			this.$util.diyRedirectTo(url);
		}
	},
	/**
	 * 自定义返回事件
	 */
	goBack() {
		if (getCurrentPages().length > 1) {
			uni.navigateBack()
		} else {
			this.redirectTo('/pages/index/index/index');
		}
	},
	/**
	 * 加密添加星星符号
	 * @param {String,Number} val 值
	 * @param {Number} front 前面保留几位
	 * @param {Number} after 后面保留几位
	 */
	addStar(val, front, after) {
		return val.substring(0, front) + '*'.repeat(val.length - (front + after)) + val.substring(val.length - after)
	},
	/**
	 * 是否是空对象
	 * @param obj
	 * @returns {boolean}
	 */
	isEmptyObject(obj) {
		for (let key in obj) {
			return false;
		}
		return true;
	},
	AdjustingInterval,
	/**
	 * 初始化公众号jssdk配置
	 * @returns {Promise<Weixin>}
	 * @constructor
	 */
	InitJsSDKconfig() {
		let wxJS = new Weixin();
		return new Promise(async (resolve, reject) => {
			if (this.isWeiXin()) {
				try {
					let jssdkRes = await Http.sendRequest({
						url: apiUrl.jssdkconfigUrl,
						data: {
							url: location.href
						},
						async: false
					});
					wxJS.init(jssdkRes.data);
				} catch (e) {

				}
			}
			resolve(wxJS)
		})
	},
	/**
	 * 公众号微信分享接口
	 * @param shareData 公众号分享参数
	 * @param callback 分享参数设置成功的回调函数
	 * @returns {Promise<void>}
	 */
	async publicShare(shareData = { title: '先迈商城', desc: '', link: '', imageUrl: this.img('public/static/youpin/home-logo.png') }, callback) {
		if (this.isWeiXin()) {
			Vue.prototype.wxJS.setShareData(shareData, callback);
		}
	},
	/**
	 * 封装支付方式
	 * @param pay_type {String} 支付类型 有 newpay  adapay unionpay cpcnpay
	 * @param pay_info {Object} 支付信息
	 * @param success_callback {Function} 成功回到函数
	 * @param fail_callback {Function} 失败的回到函数
	 * @param error_callback {Function} 错误的回调函数
	 */
	wechatPay(pay_type, pay_info, success_callback, fail_callback, error_callback) {
		// #ifdef MP-WEIXIN
		if (pay_type == 'newpay' || pay_type=='unionpay' || pay_type=='cpcnpay') {

			pay_info = Object.assign(pay_info, {
				success: (res) => {
					if (success_callback && typeof success_callback == 'function') {
						success_callback(res);
					}
				},
				fail: (err) => {
					if (fail_callback && typeof fail_callback == 'function') {
						fail_callback(err);
					}

				}
			})
			uni.requestPayment(pay_info);
		} else {

			AdaPay.doPay(pay_info, (result) => {
				if (result.result_status == 'succeeded') {
					if (success_callback && typeof success_callback == 'function') {
						success_callback(result);
					}
				} else if (result.result_status == 'failed') {
					if (fail_callback && typeof fail_callback == 'function') {
						fail_callback(result);
					}
				} else if (result.result_status == 'cancel') {
					if (fail_callback && typeof fail_callback == 'function') {
						fail_callback(result);
					}
				} else {
					if (error_callback && typeof error_callback == 'function') {
						error_callback(result);
					}
				}
			});
		}
		// #endif
		// #ifdef H5
		if (this.isWeiXin()) {
			// this.InitJsSDKconfig().then((wxJS)=>{
			// 	wxJS.pay(pay_info,success_callback,fail_callback);
			// });
			WeixinJSBridge.invoke('getBrandWCPayRequest', { ...pay_info }, (res) => {
				if (res.err_msg == "get_brand_wcpay_request:ok") {
					// 使用以上方式判断前端返回,微信团队郑重提示：
					//res.err_msg将在用户支付成功后返回ok，但并不保证它绝对可靠。
					if (success_callback && typeof success_callback == 'function') {
						success_callback(res)
					}
				} else {
					if (fail_callback && typeof fail_callback == 'function') {
						fail_callback(res)
					}
				}
			})
		} else {
			let to_call = () => {
				setTimeout(() => {
					uni.showModal({
						title: '提示',
						content: '订单正在支付中，可点击查看订单支付状态',
						confirmText: '查看订单',
						showCancel: false,
						success: (res) => {
							if (res.confirm) {
								if (success_callback && typeof success_callback == 'function') {
									success_callback(res)
								}
							} else if (res.cancel) {
								if (fail_callback && typeof fail_callback == 'function') {
									fail_callback(res)
								}
							}
						},
						fail: (err) => {
							if (error_callback && typeof error_callback == 'function') {
								error_callback(err);
							}
						}
					});
				}, 2000)
			}
			let not_wx_tip = () => {
				uni.showModal({
					title: '提示',
					content: '请先安装微信，再选择微信支付',
					confirmText: '确定',
					showCancel: false,
					success: (res) => {
					},
					fail: (err) => {

					}
				});
			}
			let to_pay = () => {
				let query_obj = this.GetRequestQuery(pay_info.wxAppLink);
				query_obj['openType'] = 'app';
				let url = pay_info.wxAppLink.split('?')[0];
				url = url + '?' + Object.keys(query_obj).map((item) => item + '=' + query_obj[item]).join('&');
				launchMiniProgram(url, () => {
					to_call();
				});
			}
			if (isOnXianMaiApp) {
				if (isIOS) {
					let isCheck = false;
					checkIsWXAppInstalled(status => {
						isCheck = true;
						if (status) {
							to_pay();
						} else {
							not_wx_tip();
						}
					})
					setTimeout(() => {
						if (!isCheck) {
							to_pay();
						}
					}, 1000)
				} else {
					to_pay();
				}

			} else {
				window.location.href = pay_info.openlink;
				to_call();
			}

		}
		// #endif
	},
	/**
	 * 获取微信订阅模板
	 * @param type {string} 场景类型  order_pay_before 订单支付前
	 open_pintuan_before 开团前
	 join_pintuan_before 参团前
	 order_refund_before 订单发起退款前
	 * @returns {Promise<*[]>} 订阅消息模板数组
	 */
	async getSubscribeScene(type) {
		let data = {
			notice_scene_group: type
		}
		let templates = [];
		// #ifdef MP-WEIXIN
		try {
			let res = await Http.sendRequest({
				url: apiUrl.getSubscribeScene,
				data,
				async: false,
			});
			if (res.code == 0) {
				templates = res.data;
			}
		} catch (e) {

		}
		// #endif
		return templates;
	},
	/**
	 * 发送订阅模板id到后台的单独接口
	 * @param push_data
	 * @returns {Promise<void>}
	 */
	async subscribeMessageMethod(push_data) {
		// #ifdef MP-WEIXIN
		try {
			let scene = push_data.scene && JSON.parse(push_data.scene);
			if (push_data.source && scene && scene.length > 0) {
				// if(scene && scene.length>0){   //领取优惠券后的订阅没有source
				await Http.sendRequest({
					url: apiUrl.subscribeUrl,
					data: push_data,
					async: false
				});
			}
		} catch (e) {

		}
		// #endif
	},
	/**
	 * 订阅微信信息
	 * @param params {Object}  消息参数
	 * @param isNotSend {Boolean} 是否立即发送订阅消息给后台
	 * @returns {Promise<void>}
	 */
	async subscribeMessage(params, isNotSend) {
		let scene_type = params.scene_type;  //场景类型
		let source = params.source;  //来源：order订单 ，pintuan_order拼团订单
		let source_id = params.source_id;  //来源主键id  ource和source_id参数用来使消息细分到具体某个订单，或者拼团订单。
		let templates = [];
		if (scene_type) {
			templates = await this.getSubscribeScene(scene_type);
		}
		let template_keys = templates.map(item => item.template_key);
		let push_data = {}
		// #ifdef MP-WEIXIN
		return new Promise((resolve, reject) => {
			uni.requestSubscribeMessage({
				tmplIds: template_keys,
				success: async (res) => {
					let accepet = [];
					if (res.errCode) {
						reject(res)
					} else {
						for (const accepetKey in res) {
							if (template_keys.indexOf(accepetKey) != -1 && res[accepetKey] == 'accept') {
								accepet = accepet.concat(templates.filter((item) => {
									return item.template_key == accepetKey;
								}))
							}
						}
						let scene_list = accepet.map((item) => item.scene);
						let push_data = {
							source,
							source_id,
							scene: JSON.stringify(scene_list),
						}
						if (scene_list.length > 0 && !isNotSend) {
							await this.subscribeMessageMethod(push_data);
						}
						resolve(push_data);
					}

				},
				fail: (err) => {
					resolve(push_data);
				}
			})
		})
		// #endif
		// #ifdef H5
		return push_data
		// #endif
	},
	/**
	 * 设置用户数据
	 * @param userInfo {Object} 需要写到localStorage
	 */
	setUserInfo(userInfo = {}) {
		let keys = Object.keys(userInfo);
		for (let i = 0; i < keys.length; i++) {
			if (userInfo[keys[i]] != undefined && userInfo[keys[i]] != '') {
				uni.setStorageSync(keys[i], userInfo[keys[i]])
			}
		}
	},
	/**
	 * 清除用户信息
	 * @returns {Promise<void>}
	 */
	async clearUserInfo() {
		uni.removeStorageSync('token');
		uni.removeStorageSync('member_id');
		uni.removeStorageSync('is_distributor');
		uni.removeStorageSync('is_shopping_status');
		uni.removeStorageSync('shop_name');
		uni.removeStorageSync('is_shopper');
		uni.removeStorageSync('nickname');
		uni.removeStorageSync('mobile');
	},
	/**
	 * 弹出登录框
	 * @param that {Object} 页面this对象
	 * @param callback {Function} 回调函数
	 * @param redirectToUrl 登录后跳转链接
	 */
	toShowLoginPopup(that, callback, redirectToUrl) {
		if (!uni.getStorageSync('token')) {
			// #ifdef MP-WEIXIN
			if (callback && typeof callback == 'function') {
				that.$refs.ydauth.init(callback);
			} else {
				that.$refs.ydauth.init(() => {
					that.$util.redirectTo(redirectToUrl, {}, 'reLaunch');
				});
			}
			// #endif
			// #ifdef H5
			setTimeout(() => {
				that.$refs.login && that.$refs.login.open(redirectToUrl);
			}, 0)
			// #endif
		}
	},
	/**
	 * 弹出登录框
	 * @param that {Object} 页面this对象
	 */
	toShowCouponPopup(that) {
		if (uni.getStorageSync('token')) {
			setTimeout(() => {
				that.$refs.couponPop && that.$refs.couponPop.open();
			}, 0)
		}
	},
	/**
	 * app内打开h5页面，自动获取app token转换成小程序token
	 * @returns {Promise<unknown>}
	 */
	transformYoupinMemberToken() {
		return new Promise((resolve, reject) => {
			if (isOnXianMaiApp) {
				updateLoginPageData(async (token) => {
					let data = { token };
					let mini_token = "";
					try {
						let res = await Http.sendRequest({
							url: apiUrl.getYoupinMemberTokenUrl,
							data,
							async: false,
						});
						if (res.code == 0) {
							mini_token = res.data.token;
						}
					} catch (e) {

					}
					resolve(mini_token)
				}, (err) => {
					reject(err)
				})
			} else {
				resolve('');
			}

		})

	},
	/**
   * @description: 函数节流
   * @param {Function} func 回调
   * @param {number} delay 间隔
   */
	throttle(func, delay) {
		let run = true
		return () => {
			if (!run) {
				return // 如果开关关闭了，那就直接不执行下边的代码
			}
			run = false // 持续触发的话，run一直是false，就会停在上边的判断那里
			setTimeout(() => {
				func()
				run = true // 定时器到时间之后，会把开关打开，我们的函数就会被执行
			}, delay)
		}
	},
	/**
	* @description: 函数防抖
	* @param {Function} func 回调
	* @param {number} delay 间隔
	*/
	debounce(func, delay) {
		let timeout
		return () => {
			clearTimeout(timeout) // 如果持续触发，那么就清除定时器，定时器的回调就不会执行。
			timeout = setTimeout(() => {
				func()
			}, delay)
		}
	},
	/**
	 * 保存网络图片或者视频到手机相册，只是在小程序上可用，h5不可用
	 * @param fileList {Array} 元素是string 资源的网络路径
	 * @param resourceType {Number} 0是图片资源 1是视频资源
	 * @param successCallback {Function} 保存成功回调函数
	 * @param errorCallback {Function}  保存失败回调函数
	 */
	downloadFilesSavePhotosAlbum(fileList,resourceType,successCallback,errorCallback) {
		//是否有权限保存图片或视频
		let writePhotosAlbum=(successFun, failFun)=>{
			uni.getSetting({
				success(res) {
					if (!res.authSetting['scope.writePhotosAlbum']) {
						uni.authorize({
							scope: 'scope.writePhotosAlbum',
							success: function () {
								successFun && successFun()
							},
							fail: function (res) {
								uni.hideLoading()
								uni.showModal({
									title: '提示',
									content: "小程序需要您的微信授权保存图片或者视频，是否重新授权？",
									showCancel: true,
									cancelText: "否",
									confirmText: "是",
									success: function (res2) {
										if (res2.confirm) { //用户点击确定'
											uni.openSetting({
												success: (res3) => {
													if (res3.authSetting['scope.writePhotosAlbum']) {
														//已授权
														successFun && successFun()
													} else {
														failFun && failFun()
													}
												}
											})
										} else {
											failFun && failFun()
										}
									}
								});
							}
						})
					} else {
						successFun && successFun()
					}
				}
			})
		}
		// 下载
		let download=(url) =>{
			return new Promise((resolve, reject) => {
				uni.downloadFile({
					url: url,
					success: function (res) {
						let temp = res.tempFilePath
						if(resourceType==0){
							uni.saveImageToPhotosAlbum({
								filePath: temp,
								success: function (res) {
									resolve(res)
								},
								fail: function (err) {
									reject(res)
								}
							})
						}else{
							uni.saveVideoToPhotosAlbum({
								filePath: temp,
								success: function (res) {
									resolve(res)
								},
								fail: function (err) {
									reject(res)
								}
							})
						}

					},
					fail: function (err) {
						reject(err)
					}
				})
			})
		}
		// 队列
		let queue=(urls)=> {
			let promise = Promise.resolve()
			urls.forEach((url, index) => {
				promise = promise.then(() => {
					return download(url)
				})
			})
			return promise
		}

		// 获取保存到相册权限
		writePhotosAlbum(
			function success() {
				uni.showLoading({
					title: '加载中',
					mask: true
				})
				// 调用保存图片promise队列
				queue(fileList)
					.then(res => {
						uni.hideLoading()
						uni.showToast({
							title: '下载完成'
						})
						if(successCallback && typeof successCallback=='function'){
							successCallback();
						}
					})
					.catch(err => {
						uni.hideLoading()
						if(errorCallback && typeof errorCallback=='function'){
							errorCallback();
						}
					})
			},
			function fail() {
				uni.showToast({
					title: '您拒绝了保存到相册'
				})
			}
		)
	},
	/**
	 * 在html的head标签中加入外链js文件
	 * @param url
	 */
	addScript(url) {
		let head = document.getElementsByTagName('head')[0];
		let script = document.createElement('script');
		script.type = 'text/javascript';
		script.src = url;
		head.appendChild(script);
	},

	/**
	 *注入接入百度统计代码
	 */
	baiduStatistics() {
		var _hmt = _hmt || [];
		window._hmt=_hmt;
		(function() {
			var hm = document.createElement("script");
			hm.src = "https://hm.baidu.com/hm.js?2a7db1249273319f5fe5bef4a8a931e2";
			var s = document.getElementsByTagName("script")[0];
			s.parentNode.insertBefore(hm, s);
		})();
	},
	sentrySrc(){
		// let head = document.getElementsByTagName('head')[0];
		// let script = document.createElement('script');
		// script.type = 'text/javascript';
		// script.crossorigin = 'anonymous';
		// script.src = 'https://sentry.jiufuwangluo.com/js-sdk-loader/1c04cf0ed610fb04a0935ba2e6834e8e.min.js';
		// head.appendChild(script);

		let shead = document.getElementsByTagName('head')[0];
		let sscript = document.createElement('script');
		sscript.type = 'text/javascript';
		sscript.crossorigin = 'anonymous';
		sscript.src = this.img('https://www.xianmai88.com/static/youpin/sentry.min.js');
		shead.appendChild(sscript);
	},
	/**
	 * 获取图片信息，比如宽高
	 * @param url {String} 图片的网络路径
	 * @returns {Promise<unknown>}
	 */
	getImageInfo(url){
		return new Promise((resolve, reject)=>{
			uni.getImageInfo({
				src:url,
				success:(image)=>{
					resolve(image)
				},
				fail:(err)=>{
					reject(err)
				}
			})
		})
	},
	/*获取编译的平台*/
	getPlatform(){
		let platform = undefined;
		// #ifdef MP-WEIXIN
		platform = 'weapp'  //微信小程序
		// #endif
		// #ifdef H5
		platform = 'h5'
		// #endif
		// #ifdef MP-ALIPAY
		platform = 'aliapp'  //支付宝小程序
		// #endif
		// #ifdef MP-BAIDU
		platform = 'baiduapp' //百度小程序
		// #endif
		// #ifdef MP-TOUTIAO
		platform = 'MP-TOUTIAO'  //头条小程序
		// #endif
		// #ifdef MP-QQ
		platform = 'MP-QQ'  //QQ小程序
		// #endif
		// #ifdef APP-PLUS
		platform = 'app' //APP
		// #endif
		return platform
	},
    /**
     * 跳转商品详情，普通商品、拼团、秒杀的跳转优先级
     * @param item {Object} 商品信息
     * @returns {boolean}
     */
    toProductDetail(item,callback) {
        let sku_id = item.sku_id;
        if(item.promotion && Object.keys(item.promotion).length) {
            this.diyCompateRedirectTo({
                wap_url: `/promotionpages/pintuan/detail/detail?id=${item.promotion.pintuan_goods_id}&sku_id=${sku_id}`
            });
			if(callback && typeof callback=='function'){
				callback(`/promotionpages/pintuan/detail/detail?id=${item.promotion.pintuan_goods_id}&sku_id=${sku_id}`)
			}
            return false
        }else if (item.is_seckill == 1) {
            this.diyCompateRedirectTo({
                wap_url: `/promotionpages/new_seckill/detail/detail?sku_id=${sku_id}`
            });
			if(callback && typeof callback=='function'){
				callback(`/promotionpages/new_seckill/detail/detail?sku_id=${sku_id}`)
			}
        }else{
			this.diyCompateRedirectTo({
				wap_url: `/pages/goods/detail/detail?sku_id=${sku_id}`
			});
			if(callback && typeof callback=='function'){
				callback(`/pages/goods/detail/detail?sku_id=${sku_id}`)
			}
		}
    },
    /**
     *  分享参数组装(注意需要分享的那一刻再调此方法)
     * @param path {String} 路径，不带参数
     * @param title {String} 分享标题
     * @param desc {String} 分享描述
     * @param shareParams {Object} 页面分享额外的参数
     * @param imageUrl {String}  分享信息中的图片
     * @returns {{imgUrl, link, title, desc, querys: string}}
     */
    unifySharePageParams(path,title,desc,shareParams={},imageUrl){
        if(!(shareParams instanceof Object)){
            shareParams = {}
        }
		shareParams.share_in_page = 1  //分享时，带上分享标志
        let shop_id = uni.getStorageSync('shop_id');
        let recommend_member_id = uni.getStorageSync('member_id');
        const region = uni.getStorageSync('region');
        const storeID = uni.getStorageSync('storeID');

        if(shop_id) shareParams['shop_id'] = shop_id;
        if(recommend_member_id) shareParams['recommend_member_id'] = recommend_member_id;
        if(region) shareParams['region'] = region;
        if(storeID) shareParams['storeID'] = storeID;

        let querys_list = []
        for (const key in shareParams) {
            querys_list.push(`${key}=${shareParams[key]}`)
        }
        let querys = querys_list.join('&')
		let link = path
		if(querys){
			link = link + `?${querys}`
		}
		title = this.shareTitleAddNickname(title)
        let share_data={
            title,
            desc,
			path,
            link,
            query:querys,
			imageUrl
        }
        return share_data;
    },
	/**
	 * 统一处理定义跳转直播页面，并返回链接
	 * @param room_id 房间号
	 * @param is_jump_plugin  true 是否直接跳转到直播插件的播放页 false 跳转当前小程序的直播中转页面
	 * @returns {string}
	 */
    livePlayerPageUrl(room_id,is_jump_plugin=false){
		const params = {
			shop_id: uni.getStorageSync("shop_id"),
			recommend_member_id: uni.getStorageSync("member_id"),
		}
		const region = uni.getStorageSync('region');
		const storeID = uni.getStorageSync('storeID');
		if(region){
			params.region = region
		}
		if(storeID){
			params.storeID = storeID
		}
		if(room_id){
			params.room_id = room_id
		}
		params.share_in_page = 1  //分享时，带上分享标志
		let customParams = encodeURIComponent(
			JSON.stringify(params)
		);
		if(is_jump_plugin){
			return `plugin-private://wx2b03c6e691cd7370/pages/live-player-plugin?room_id=${room_id}&custom_params=${customParams}`
		}else{
			return `/pages/live-player-plugin?room_id=${room_id}`
		}
	},
	/**
	 * 统一处理定义跳转腾讯云验证码页面，并返回是否跳转成功
	 * @param codeType {String} 验证码类型
	 * @returns {Promise<boolean>}  是否跳到去验证码页面
	 */
	async toWechatVerificationCode(codeType){
		function checkYpWechatVerificationCode(){
			return new Promise((resolve, reject)=>{
				setInterval(()=>{
					const pageList = getCurrentPages().reverse()
					if(pageList.length>0){
						const currentPageInstance = pageList[0].$vm;
						if(currentPageInstance &&
							currentPageInstance.$refs &&
							currentPageInstance.$refs.ypWechatVerificationCode &&
							currentPageInstance.$refs.ypWechatVerificationCode.toShowVerCode){
							resolve()
						}
					}
				},50)
			})
		}
		if(codeType){
			await Vue.prototype.$store.dispatch('writeWechatCodeBill')
			if(this.getPlatform()=='weapp'){
				await checkYpWechatVerificationCode();
				const currentPageInstance = getCurrentPages().reverse()[0].$vm;
				currentPageInstance.$refs.ypWechatVerificationCode.toShowVerCode(codeType)
			}else if(this.getPlatform() == 'h5'){
				Vue.prototype.$bus.$emit('toShowVerCode',codeType)
			}
			return true
		}
		return false
	},
	/**
	 * 验证成功后处理
	 * @param codeType 验证码类型
	 * @param ticket 验证的票据
	 * @returns {Promise<void>}
	 */
	async successWechatVerificationCode(codeType, ticket){
		await Vue.prototype.$store.dispatch('writeWechatCodeBill',ticket)
		await Vue.prototype.$store.dispatch('writeWechatVerCode',{verifyState:true,codeType})
		if(['httpAndRefresh'].includes(codeType)){
			this.reload()
		}
	},
	/**
	 * 注入腾讯云验证码的js
	 */
	tencentCaptchaJS() {
		var _hmt = _hmt || [];
		window._hmt=_hmt;
		(function() {
			var hm = document.createElement("script");
			hm.src = "https://turing.captcha.qcloud.com/TCaptcha.js";
			var s = document.getElementsByTagName("script")[0];
			s.parentNode.insertBefore(hm, s);
		})();
	},
	// 通过获取该页面实例执行其内部的生命周期方法来刷新页面
	reload() {
		const currentPage = getCurrentPages().pop();
		if (currentPage) {
			let pageUrl =currentPage['$page']['fullPath'] //当前页面路径(带参数);
			this.redirectTo(pageUrl,{}, 'redirectTo');
		}
	},
	// 显示实名验证的提示弹窗
	async toShowRealNamePopup(){
		function checkDiyRealNamePopup(){
			return new Promise((resolve, reject)=>{
				setInterval(()=>{
					const pageList = getCurrentPages().reverse()
					if(pageList.length>0){
						const currentPageInstance = pageList[0].$vm;
						if(currentPageInstance &&
							currentPageInstance.$refs &&
							currentPageInstance.$refs.diyRealNamePopup &&
							currentPageInstance.$refs.diyRealNamePopup.open){
							resolve()
						}
					}
				},50)
			})
		}
		if(this.getPlatform()=='weapp'){
			await checkDiyRealNamePopup();
			const currentPageInstance = getCurrentPages().reverse()[0].$vm;
			currentPageInstance.$refs.diyRealNamePopup.open()
		} else if(this.getPlatform() == 'h5'){
			Vue.prototype.$bus.$emit('toShowRealNamePopup')
		}
	},
	/**
	 * 将RGB或HEX颜色转换为RGBA颜色数组，透明度从10%到100%
	 * @param {string} color - RGB颜色值，格式为 "rgb(r, g, b)" 或 HEX颜色值，格式为 "#rrggbb" 或 "#rgb"
	 * @returns {string[]} 包含从10%到100%透明度的RGBA颜色数组
	 */
	generateRGBAColors(color) {
		let r, g, b;

		// 如果颜色是HEX格式
		if (color.startsWith('#')) {
			// 处理短格式#rgb
			if (color.length === 4) {
				r = parseInt(color[1] + color[1], 16);
				g = parseInt(color[2] + color[2], 16);
				b = parseInt(color[3] + color[3], 16);
			}
			// 处理标准格式#rrggbb
			else if (color.length === 7) {
				r = parseInt(color.substr(1, 2), 16);
				g = parseInt(color.substr(3, 2), 16);
				b = parseInt(color.substr(5, 2), 16);
			}
		}
		// 如果颜色是RGB格式
		else if (color.startsWith('rgb')) {
			const rgbValues = color.match(/\d+/g).map(Number);
			[r, g, b] = rgbValues;
		}

		// 生成10%到100%透明度的RGBA颜色数组
		const rgbaColors = [];
		for (let i = 10; i <= 100; i += 10) {
			const alpha = i / 100;
			const rgba = `rgba(${r}, ${g}, ${b}, ${alpha})`;
			rgbaColors.push(rgba);
		}

		return rgbaColors;
	},
	/**
	 * rgb或者rgba转16进制
	 * @param color
	 * @returns {null|string}
	 */
	colorToHex(color) {
		// 检查是否是十六进制格式
		if (color.startsWith('#')) {
			return color.toUpperCase(); // 如果已经是十六进制，直接返回
		}

		// 处理 rgb 和 rgba 格式
		const rgbaMatch = color.match(/rgba?\((\d+),\s*(\d+),\s*(\d+)(?:,\s*([\d.]+))?\)/);
		if (rgbaMatch) {
			const r = parseInt(rgbaMatch[1]);
			const g = parseInt(rgbaMatch[2]);
			const b = parseInt(rgbaMatch[3]);
			const a = rgbaMatch[4] !== undefined ? parseFloat(rgbaMatch[4]) : 1; // 透明度默认为 1

			const hex = ((1 << 24) | (r << 16) | (g << 8) | b).toString(16).slice(1).toUpperCase();
			if (a === 1) {
				return `#${hex}`;
			} else {
				const alphaHex = Math.round(a * 255).toString(16).padStart(2, '0').toUpperCase();
				return `#${hex}${alphaHex}`;
			}
		}

		// 输入格式无法识别
		return null;
	},
	rgbaToRgb(rgba) {
		// 使用正则表达式提取 RGBA 中的 R、G、B 值
		const result = rgba.match(/rgba?\((\d+),\s*(\d+),\s*(\d+)/);

		if (result) {
			// 提取 R、G、B 值并返回 RGB 字符串
			const r = result[1];
			const g = result[2];
			const b = result[3];
			return `rgb(${r}, ${g}, ${b})`;
		} else {
			throw new Error("Invalid RGBA format");
		}
	},
	/**
	 * 生成指定长度的随机字符串
	 * @param length {int} 长度
	 * @returns {string}
	 */
	generateRandomString(length) {
		let result = '';
		const characters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
		const charactersLength = characters.length;
		for (let i = 0; i < length; i++) {
			result += characters.charAt(Math.floor(Math.random() * charactersLength));
		}
		return result;
	},
	/**
	 * cdn上图片追加远程图片处理参数
	 * @param image_url {string} 图片路径
	 * @param resize_params {Object} 处理参数
	 * @returns {string}
	 */
	imageCdnResize(image_url,resize_params={image_process:'resize,w_400','x-oss-process':'image/resize,w_400'}){
		if(!image_url){
			return image_url;
		}
		let param_list = [];
		for (const paramsKey in resize_params) {
			param_list.push(`${paramsKey}=${resize_params[paramsKey]}`)
		}
		if(image_url.indexOf('?') > -1){
			image_url += `&${param_list.join('&')}`
		}else{
			image_url += `?${param_list.join('&')}`
		}
		return image_url;
	},
	/**
	 * 广告图片点击上报
	 * @param id
	 */
	async specialBannerReportByClick(id){
		await Http.sendRequest({
			url: apiUrl.specialBannerReportByClickUrl,
			data: {
				id
			},
			async: false
		});
	},
	// 分享语标题增加昵称并防止重复添加
	shareTitleAddNickname(title) {
		let nickname = uni.getStorageSync('nickname');
		let mobile = uni.getStorageSync('mobile');
		if(nickname){
			nickname = nickname.length > 4 ? '...'+nickname.slice(-4) : nickname
		}else{
			if(mobile){
				nickname = mobile.length > 4 ? mobile.slice(-4) : mobile
				nickname = '尾号' + nickname
			}
		}
		nickname = `【${nickname}】`
		if(nickname && title.indexOf(nickname) == -1){
			title = nickname+title
		}
		return title
	},
	//获取客服链接并且跳转
	async getCustomerService(){
		buriedPoint.diyReportCustomerServiceInteractionEvent({diy_action_type:'click_online_service'})
		let res = await Http.sendRequest({
			url:apiUrl.serviceUrl,
			async: false
		})
		if(res.data && res.data.url){
			if(this.getPlatform() == 'weapp'){
				wx.openCustomerServiceChat({
					extInfo: {url: res.data.url},
					corpId: res.data.corpId,
					success(res) {}
				})
			}else{
				this.diyRedirectTo({wap_url:res.data.url});
			}
		}
	},
}
