/**
 * 柚品公共验证等方法
 */
import apiurls from "./apiurls.js";
import request from "./request/http.js"
import statistics from "../mixins/statistics";
import {scenePare} from "./scene_handle.js"
import util from "./util";
import AuthService from "../../common/services/auth";
import store from "../../store";
import {WechatReportSentry} from "./report-sentry";
// #ifdef H5
import {goClosePage, isOnXianMaiApp, schemeGo} from "./h5/appOP";
import {webUrl} from "./h5/appSchemeUrl";
// #endif
const getDefaultImg = () =>{
    return new Promise((resolve ,reject)=>{
        request.sendRequest({
            url: '/api/config/defaultimg',
            success: res => {
                let data = res.data;
                if (res.code == 0 && data) {
                    uni.setStorageSync('default_img', JSON.stringify(data));
                }
                resolve();
            },
            fali:err=>{
             reject()
            }
        });
    })

}

//微信小程序直播插件
// #ifdef MP-WEIXIN
let livePlayer = requirePlugin('live-player-plugin');
// #endif

export default {

	/**
	 * 检测token
	 */
	checkToken(query_shop_id){
    return new Promise(async (relove,reject) => {
      let token=uni.getStorageSync('token');
      const setStorageSyncShopId = (id)=>{
        if(id){
          uni.setStorageSync('shop_id',Number(id));
        }
      }


      if(token){
        //检测店铺token有效性
        let res = await request.sendRequest({
              url: apiurls.checkLoginTokenUrl,
              async:false,
              data:{
                  token
              }
        })
        if(res.code==0){
            let shop_id=res.data.shop_id;
            let member_id=res.data.member_id;
            let is_distributor=res.data.is_distributor;
            let is_shopping_status=res.data.is_shopping_status;
            let shop_name = res.data.shop_name;
            let is_shopper = res.data.is_shopper;  //是否为店主，1：是，0：否
            let nickname = res.data.nickname;
            let mobile = res.data.mobile;
            util.setUserInfo({shop_id,member_id,is_distributor,is_shopping_status,shop_name,is_shopper,nickname,mobile});
            relove(res)
        }else{
          await util.clearUserInfo();
          setStorageSyncShopId(query_shop_id);
          reject(res)
        }
      }else{
        setStorageSyncShopId(query_shop_id);
        relove();
      }
    })
  },
  // 商品分享埋点
  goodsShare(goods_id){
	  return new Promise(async (relove,reject) => {
		  let res = await request.sendRequest({
		    url: '/api/goods/share',
			async:false,
		  	data:{
		  		goods_id,
				token:uni.getStorageSync('token')
		  	}
		  })
		  if(res.code == 0){
			relove(res)
		  }else{
			reject(res)
		  }
	  })

  },
    // 获取钱包未读收益，（给底部导航用）
    async getIncomeInfo(){
        try{
            let res = await request.sendRequest({
                url: apiurls.getIncomeInfoUrl,
                async:false,
                data:{}
            })
            if(res.code==0){
                if(res.data){
                    await store.dispatch('writeIncomeInfo', res.data)
                }
            }
        }catch (e) {

        }
    },
    // 用户拼团页面（拼团商品详情页，拼团分享页），分享动作，上报给后台
    pintuanShareActionReport(paramsData){
        return new Promise(async (relove,reject) => {
            let res = await request.sendRequest({
                url: apiurls.pintuanShareReportUrl,
                async:false,
                data:paramsData
            })
            if(res.code == 0){
                relove(res)
            }else{
                reject(res)
            }
        })

    },

    /*静默登录*/
    async staticLogin(){
        let [code,_]=await AuthService.getCode();
        let params={
            code,
        };
        let recommend_member_id=uni.getStorageSync('recommend_member_id');
        if(recommend_member_id){
            params=Object.assign(params,{recommend_member:recommend_member_id})
        }
        try{
            let res=await request.sendRequest({
                url:apiurls.staticLoginUrl,
                async:false,
                data:params
            });
            if(res.code==0){

                let token=res.data.token;
                let shop_id=res.data.shop_id;
                let member_id=res.data.member_id;
                let is_distributor=res.data.is_distributor;
                let shop_name=res.data.site_name;
                let is_shopper = res.data.is_shopper;  //是否为店主，1：是，0：否
                let is_shopping_status=res.data.is_shopping_status;
                util.setUserInfo({shop_id,member_id,is_distributor,is_shopping_status,shop_name,is_shopper,token});
            }
        }catch (e) {

        }
    },

    /*等待静默登录完成*/
    wait_staticLogin_success(){
        return new Promise((resolve ,reject)=>{
            let intervalObj=setInterval(()=>{
                if(uni.getStorageSync('staticLogin_success')){
                    clearInterval(intervalObj);
                    resolve();
                }
            },50)
        })
    },

  /*
    系统初始化参数处理、例如参数、店铺、分销客处理
    @param data  小程序参数
    @param that  是this
    @param isLaunch  是否是小程序启动是调用，主要是options参数处理的差异，false是一般页面调用，重新初始化系统
   */
  async AppVueInit(data,that,isLaunch){
      uni.setStorageSync('staticLogin_success',false)  //初始化静默授权标志位
      //购物车数量
      // that.$store.dispatch('getCartNumber').then((e) => {})
      //全局主题色
      that.$store.dispatch('getThemeStyle')
      that.$store.dispatch('getThemeColor')
      //全局插件
      that.$store.dispatch('getAddonIsexit')
      //进货单数量
      // that.$store.dispatch('getWholeSaleNumber')
      //判断当前是否为开发状态
      that.$api.sendRequest({
          url: '/api/shop/isshow',
          success: res => {
              that.$store.state.Development = res.data
          }
      });
      try{
          await getDefaultImg();
      }catch (e) {

      }

      // #ifdef MP
      // 小程序端自动登录
      // if (!uni.getStorageSync('token') && !uni.getStorageSync('loginLock') && !uni.getStorageSync('unbound')) {
      // 	this.getCode(() => {
      // 		if (Object.keys(this.authInfo).length) {
      // 			this.$api.sendRequest({
      // 				url: '/api/login/auth',
      // 				data: this.authInfo,
      // 				success: res => {
      // 					if (res.code >= 0) {
      // 						uni.setStorage({
      // 							key: 'token',
      // 							data: res.data.token,
      // 							success: () => {}
      // 						});
      // 					} else {
      // 						uni.setStorage({
      // 							key: 'unbound',
      // 							data: 1,
      // 							success: () => {}
      // 						});
      // 					}
      // 				}
      // 			})
      // 		}
      // 	});
      // }
      // #endif

      // 小程序扫码进入
      let sceneDict={}; //扫二维进入小程序的参数
      sceneDict=scenePare(isLaunch,data);
      let query_shop_id=null;
      let share_member_id=null;
      let is_shopkeeper=null;
      let recommend_member_id=null;
      let region = null;
      let storeID = null;
      let old_region = uni.getStorageSync('region')
      let old_storeID = uni.getStorageSync('storeID')
      if(isLaunch){
          query_shop_id= data.query && data.query.shop_id;
          share_member_id=data.query && data.query.share_member_id;
          is_shopkeeper= data.query && data.query.is_shopkeeper; //是否掌柜从app个人中心页面分享出来的
          recommend_member_id=data.query && data.query.recommend_member_id;   //获取上级推荐的人的id（登录的时候用到）
          region = data.query && data.query.region; //投放渠道id(web分析)
          storeID = data.query && data.query.storeID; //投放区域id(web分析)
          let scene=data.scene; //小程序进入的场景
          that.$store.dispatch('writeIsShopkeeper',is_shopkeeper);
          that.$store.dispatch('writeScene',scene);
          region && uni.setStorageSync('region', region);
          storeID && uni.setStorageSync('storeID', storeID);
          if(region || storeID){
              if(old_region!=region || old_storeID!=storeID){
                  uni.setStorageSync('region_storeID_create_time',new Date().getTime())
              }
          }
      }else{
          query_shop_id= data.shop_id;
      }

      // #ifdef MP-WEIXIN
      if(isLaunch){
          if (data.query && data.query.share_openid) {
              //  直播间分享海报处理（经过三只手机测试，海报query必有share_openid，卡片在第一次query为空，经过getShareParams()获取后，有时query带上参数，有时query还是为空，需要再通过getShareParams()获取）
              try{
                  let res = await that.$api.sendRequest({
                      url: apiurls.getMemberByopenid,
                      async:false,
                      data: {
                          openid: data.query.share_openid
                      },
                  })
                  if(res.code == 0) {
                      recommend_member_id = res.data.member_id;
                      query_shop_id= res.data.shop_id;
                  }
                  if(data.query.custom_params){
                      let custom_params = JSON.parse(decodeURIComponent(data.query.custom_params))
                      region = custom_params.region
                      storeID = custom_params.storeID
                      region && uni.setStorageSync('region', region);
                      storeID && uni.setStorageSync('storeID', storeID);
                      if(region || storeID){
                          if(old_region!=region || old_storeID!=storeID){
                              uni.setStorageSync('region_storeID_create_time',new Date().getTime())
                          }
                      }
                  }
              }catch{}
          }else {
              //  直播间分享卡片处理
              const sceneList = [1007, 1008, 1014, 1044, 1045, 1046, 1047, 1048, 1049, 1073, 1154, 1155]
              if (sceneList.includes(data.scene)) {
                  // 此判断只适合在直播间内通过分享卡片出来才会执行，链接分享不执行
                  try{
                      let res = await livePlayer.getShareParams()
                      let custom_params = res.custom_params
                      if(custom_params != '') {
                          if(custom_params.recommend_member_id != '') {
                              recommend_member_id = custom_params.recommend_member_id;
                              query_shop_id= custom_params.shop_id;
                              region = custom_params.region
                              storeID = custom_params.storeID
                              region && uni.setStorageSync('region', region);
                              storeID && uni.setStorageSync('storeID', storeID);
                              if(region || storeID){
                                  if(old_region!=region || old_storeID!=storeID){
                                      uni.setStorageSync('region_storeID_create_time',new Date().getTime())
                                  }
                              }
                          }
                      }else if (data.query.share_openid) {
                          // 防止漏传custom_params
                          try{
                              let res = await that.$api.sendRequest({
                                  url: apiurls.getMemberByopenid,
                                  async:false,
                                  data: {
                                      openid: data.query.share_openid
                                  },
                              })
                              if(res.code == 0) {
                                  recommend_member_id = res.data.member_id;
                                  query_shop_id= res.data.shop_id;
                              }
                          }catch{}
                      }
                  }catch{}
              }
          }
      }
      // #endif

		// #ifdef H5
      if(isOnXianMaiApp){
          //在app中启动时，先删除本地存储数据
          util.clearUserInfo();
          uni.removeStorageSync('shop_id');
          uni.removeStorageSync('recommend_member_id');
      }
		// #endif

      //存储上级推荐人的id 或者是分享拼团页面的上级人id
      if(recommend_member_id && (recommend_member_id!=uni.getStorageSync('member_id'))){
          uni.setStorageSync('recommend_member_id',recommend_member_id);
      }
      //记录分享者的分销客id、店铺id
      if(query_shop_id && share_member_id){
          that.$store.dispatch('writeShareMemberId',Number(share_member_id));
          that.$store.dispatch('writeShareShopId',Number(query_shop_id));
      }
      if(query_shop_id){
          //埋点shop_id，url链接传参
          that.$store.dispatch('writeBuriedShopId',Number(query_shop_id))
      }

      //如果为传店铺id和本地都无店铺id就设置默认店铺
      if(!query_shop_id && !uni.getStorageSync('shop_id')){
          query_shop_id=110;
      }
      // console.log("query_shop_id",query_shop_id)
      query_shop_id && uni.setStorageSync('shop_id',query_shop_id);

      // #ifdef H5
      try{
          //app内转换token
          let transform_token= await util.transformYoupinMemberToken();
          if(transform_token){
              util.setUserInfo({token:transform_token});
          }
      }catch (e) {

      }
      // #endif

      //验证token
      try{
          await this.checkToken(query_shop_id);
      }catch (e) {

      }
	  // #ifdef MP-WEIXIN
	  try{
	      //静默登录
	      if(!uni.getStorageSync('token')){
	          await this.staticLogin();
	      }
	  }catch (e) {

	  }
	  // #endif
      let shop_id=uni.getStorageSync('shop_id');
      // console.log("shop_id",shop_id);
      if(shop_id){
          //检测店铺id有效性
          let res=await that.$api.sendRequest({
              url: apiurls.checkShopStatusUrl,
              async:false,
              data: {
                  shop_id,
              },
          })
          if(res && res instanceof Object){
              if(res.code!=0){
                  uni.removeStorageSync('shop_id');
              }else{
                  let shop_name=res.data.site_name;
                  let shop_id=res.data.site_id;
                  uni.setStorageSync('shop_name',shop_name);
                  uni.setStorageSync('shop_id',shop_id);
              }
          }
      }
      // #ifdef MP-WEIXIN
      let token = uni.getStorageSync('token')
      let member_id = uni.getStorageSync('member_id')
      WechatReportSentry.configureScope(token,member_id)
      // #endif
      await this.getIncomeInfo()
	// #ifdef H5
      //判断启动h5页面，url参数是否正确，不正确重新加载页面
      //data有可能为空的
      //h5的onshow触发的时候会出现data是空对象，uniapp的开发工具编辑的问题，3.2.16版本data是正常有数据，后面的版本是会data为空的
      // let is_redirect=Object.keys(data).length>0 ? Object.keys(data.query).length<1 || !data.query['shop_id'] || (uni.getStorageSync('token') && (query_shop_id!=uni.getStorageSync('shop_id') || recommend_member_id!=uni.getStorageSync('member_id'))) :false;
      let is_redirect=false;
      // 在app中不重定向
      if(!isOnXianMaiApp){
          if(Object.keys(data).length>0){
              if(Object.keys(data.query).length<1 || !data.query['shop_id']){
                  is_redirect=true;
              }
              if(uni.getStorageSync('shop_id')){
                  if(query_shop_id!=uni.getStorageSync('shop_id')){
                      is_redirect=true;
                  }
              }
              if(uni.getStorageSync('token')){
                  if(query_shop_id!=uni.getStorageSync('shop_id') || recommend_member_id!=uni.getStorageSync('member_id')){
                      is_redirect=true;
                  }
              }
          }else{
              is_redirect=false;
          }
      }
      let redirect_whitelist=['/pages/login/login/login','/pages/login/register/register', '/pluginspages/wechatVerificationCode/wechatVerificationCode']; //不重定向的白名单
      // if(isOnXianMaiApp){
      //     redirect_whitelist.push('/pages/order/list/list');  //在app内订单不重定向
      // }
      let is_whitelist=!!(redirect_whitelist.filter((item)=>window.location.href.indexOf(item)!=-1).length);
      if(is_redirect && !is_whitelist){
          let theQuery=util.GetRequestQuery(window.location.href);
          if(theQuery.hasOwnProperty('shop_id')){
              delete theQuery['shop_id'];
          }
          if(theQuery.hasOwnProperty('recommend_member_id')){
              delete theQuery['recommend_member_id'];
          }

          if(!theQuery.hasOwnProperty('shop_id')){
              theQuery['shop_id']=uni.getStorageSync('shop_id');
          }
          if(!theQuery.hasOwnProperty('recommend_member_id')){
              let recommend_member_id=uni.getStorageSync('member_id')
              if(recommend_member_id){
                  theQuery['recommend_member_id']=recommend_member_id;
              }
          }
          const region = uni.getStorageSync('region');
          const storeID = uni.getStorageSync('storeID');
          if(!theQuery.hasOwnProperty('region') && region){
              theQuery['region']=region;
          }
          if(!theQuery.hasOwnProperty('storeID') && storeID){
              theQuery['storeID']=storeID;
          }
          let url=window.location.href;
          let original_url=url.split('?')[0];
          url=original_url+'?'+Object.keys(theQuery).map((item)=>item+'='+theQuery[item]).join('&');
          window.location.href=url;
          return;
      }
	// #endif
      // #ifdef H5
      setTimeout(()=>{
          statistics.shopInterview(that);
      },4000)
      // #endif
      // #ifdef MP-WEIXIN
      statistics.shopInterview(that);
      // #endif
      uni.setStorageSync('staticLogin_success',true)  //静默授权完成
  }

}
