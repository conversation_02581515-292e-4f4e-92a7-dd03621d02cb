/**
 * 微信授权接口封装
 */
export const DEFAULT_AUTH_TYPE = 'weixin'
export default {
    async getLoginCode(provider = DEFAULT_AUTH_TYPE){
        return uni.login({
            provider,
        })
    },
    async getUserProfile(provider = DEFAULT_AUTH_TYPE){
        return uni.getUserProfile({
            desc: '用于用户登录', // 声明获取用户个人信息后的用途，后续会展示在弹窗中，请谨慎填写
            provider,
        })
    },
    async getUserInfo(provider = DEFAULT_AUTH_TYPE){
        return uni.getUserInfo({
            provider: 'weixin',
        })
    },

}
