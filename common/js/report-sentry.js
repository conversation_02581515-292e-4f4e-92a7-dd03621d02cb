/*接入哨兵系统*/
// #ifdef H5
import Vue from 'vue';
// #endif

// 参考文档 https://github.com/alexayan/sentry-mina
// #ifdef MP-WEIXIN
import * as sentry from "sentry-mina";
// #endif
import config from "./config";
const is_report = !!config.project_type && process.env.NODE_ENV === 'production'  //是否上报
export class WechatReportSentry{
    constructor() {
        this.dsn = 'https://<EMAIL>/4'
	}

    run(){
		// #ifdef MP-WEIXIN
		if(!is_report){
			return
		}
		// config Sentry
		sentry.init({
		    dsn: this.dsn,
			environment: process.env.NODE_ENV,
			release:config.version,
			normalizeDepth: 5,
			sendDefaultPii: true,
			integrations:[
				// 捕获并记录 setTimeout, setInterval 内的异常
				new sentry.Integrations.TryCatch(),
				new sentry.Integrations.Breadcrumbs({
					console: true,
					realtimeLog: false, // https://developers.weixin.qq.com/miniprogram/dev/framework/realtimelog/
					request: true,
					navigation: true,
					api: true,
					lifecycle: true,
					unhandleError: true
				}),
				// 将 Sentry 事件数据记录到小程序 LogManager
				new sentry.Integrations.LogManager({
					level: 1  // 取值为0表示是否会把 App、Page 的生命周期函数和 wx 命名空间下的函数调用写入日志，取值为1则不会
				})
			]
		})
		// sentry.configureScope(scope => {
		// 	scope.setUser({ id: '4711' });
		// 	scope.setTag('user_mode', 'admin');
		// 	scope.setExtra('battery', 0.7);
		// 	// scope.clear();
		// });
		// #endif
    }
	static captureException(error){
		// #ifdef MP-WEIXIN
		if(!is_report){
			return
		}
		sentry.captureException(error); // 直接上报错误对象
		// #endif
	}

	static configureScope(token,member_id){
		// #ifdef MP-WEIXIN
		if(!is_report){
			return
		}
		sentry.configureScope(scope => {
			scope.setUser({id:member_id});
			scope.setExtra('token',token)
			scope.setExtra('member_id',member_id)
			// scope.clear();
		});
		// #endif
	}

	static consoleLog(params){
		// #ifdef MP-WEIXIN
		if(!is_report){
			return
		}
		console.log(params)
		// #endif
	}

}


export class H5ReportSentry{
	constructor() {
		this.dsn = 'https://<EMAIL>/6'
	}
	static waitSentry(){
		return new Promise((resolve ,reject)=>{
			let intervalObj=setInterval(()=>{
				if(window.Sentry){
					clearInterval(intervalObj);
					resolve();
				}
			},50)
		})
	}

	async run(){
		if(!is_report){
			return
		}
		// #ifdef H5
		await H5ReportSentry.waitSentry();
		Sentry.init({
			Vue,
			dsn: this.dsn,
			integrations: [
				Sentry.browserTracingIntegration({  }),
				Sentry.replayIntegration(),
			],
			environment: process.env.NODE_ENV,
			sampleRate: 1.0,
			tracesSampleRate: 1.0, // 设置 Tracing 的采样率
			normalizeDepth: 5,
			sendDefaultPii: true,
			tracesSampler: samplingContext => {
				// 自定义采样逻辑
				if (samplingContext.transactionContext.op === 'pageload') {
					// 针对页面加载事件，设定较高的采样率
					return 1.0;
				} else if (samplingContext.transactionContext.op === 'http') {
					// 针对 HTTP 请求事件，设定较低的采样率
					return 0.5;
				}
				// 默认的采样率
				return 0.1;
			},
			beforeSend(event, hint) {

				let token = uni.getStorageSync('token')
				let member_id = uni.getStorageSync('member_id')
				// 添加用户信息
				event.user = {
					token,
					member_id
				};

				return event;
			},
		});
		// #endif
	}
	static async captureException(error){
		// #ifdef H5
		if(!is_report) {
			return
		}
		await H5ReportSentry.waitSentry();
		Sentry.captureException(error); // 直接上报错误对象
		// #endif
	}
	static async captureMessage(title, options){
		// #ifdef H5
		if(!is_report) {
			return
		}
		await H5ReportSentry.waitSentry();
		Sentry.captureMessage(title, options); // 直接上报错误对象
		// #endif
	}
}
