const path = require('path')
let UNI_PLATFORM = process.env.UNI_PLATFORM
let integrity = false
if(UNI_PLATFORM == 'h5'){
    integrity = false  //h5编译模式，开启SRI的js完整性检验模式
}
module.exports = {
    integrity,
    configureWebpack: {
        module: {
            rules: [{
                test: /\.vue$/,
                use: {
                    loader: path.resolve(__dirname, "./node_modules/vue-inset-loader")
                },
            }]
        },
    }
}
